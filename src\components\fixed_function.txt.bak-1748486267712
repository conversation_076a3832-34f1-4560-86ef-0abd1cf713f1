// Initialize WebSocket for YOLO visualization
const initializeYoloWebSocket = () => {
  if (wsYoloConnection && wsYoloConnection.readyState === WebSocket.OPEN) {
    console.log('YOLO WebSocket宸茶繛鎺ワ紝鏃犻渶閲嶆柊鍒濆鍖?);
    return;
  }
  
  if (wsYoloConnection && wsYoloConnection.readyState === WebSocket.CONNECTING) {
    console.log('YOLO WebSocket姝ｅ湪杩炴帴涓紝璇风◢鍊?..');
    return;
  }
  
  console.log('创建YOLO WebSocket连接 - 连接到YOLO图像处理后端');
  addApiLog(`INFO:qwen-vl-api:正在连接YOLO图像处理后端: ${YOLO_BACKEND_URL}`);
  
  try {
    // 创建新的WebSocket连接到YOLO后端
    wsYoloConnection = new WebSocket(YOLO_BACKEND_URL);

    wsYoloConnection.onopen = () => {
      console.log('YOLO WebSocket连接已建立?);
      addApiLog(`INFO:qwen-vl-api:已连接到YOLO图像处理后端`);
      
      // 鍙戦€佹ā鍨嬮厤缃俊鎭?
      wsYoloConnection.send(JSON.stringify({
        action: 'configure',
        model: 'yolo11n-seg.pt',
        config: {
          segmentation: true,
          confidence: 0.25,
          visualize: true,
          img_size: 640
        }
      }));
      
      // 开始YOLO处理
      if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
        captureAndProcessFrame();
      }
    };
    
    wsYoloConnection.onerror = (error) => {
      console.error('YOLO WebSocket错误:', error);
      addApiLog(`ERROR:qwen-vl-api:YOLO鍥惧儚澶勭悊鍚庣杩炴帴澶辫触`);
    };
    
    wsYoloConnection.onclose = () => {
      console.log('YOLO WebSocket连接已关闭?);
      addApiLog(`INFO:qwen-vl-api:YOLO鍥惧儚澶勭悊鍚庣杩炴帴宸插叧闂璥);
      wsYoloConnection = null;
    };
    
    wsYoloConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.error) {
          console.error('YOLO鏈嶅姟鍣ㄨ繑鍥為敊璇?', data.error);
          return;
        }
        
        // 处理YOLO检测结果?
        if (data.processed_frame) {
          // 纭繚澶勭悊鍚庣殑甯ф暟鎹槸瀹屾暣鐨凞ata URL
          let processedFrameDataUrl = data.processed_frame;
          if (!processedFrameDataUrl.startsWith('data:image')) {
            processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
          }
          
          // 鍦╟anvas涓婃樉绀哄鐞嗗悗鐨勫抚
          if (detectionCanvasElement.value) {
            const processedImage = new Image();
            processedImage.onload = () => {
              const ctx = detectionCanvasElement.value.getContext('2d');
              if (ctx) {
                ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
                ctx.drawImage(
                  processedImage,
                  0, 0,
                  detectionCanvasElement.value.width,
                  detectionCanvasElement.value.height
                );
              }
            };
            processedImage.src = processedFrameDataUrl;
          }
          
          // 鏇存柊妫€娴嬬粨鏋?
          if (data.objects && data.objects.length > 0) {
            latestDetections.value = data.objects;
          }
          
          // 继续处理下一帧?
          if (videoPlayerElement.value && !videoPlayerElement.value.paused && useYoloDetection.value) {
            requestAnimationFrame(captureAndProcessFrame);
          }
        }
      } catch (error) {
        console.error('处理YOLO WebSocket消息时出错?', error);
      }
    };
  } catch (error) {
    console.error('杩炴帴鍒癥OLO鍚庣鏃跺嚭閿?', error);
    addApiLog(`ERROR:qwen-vl-api:杩炴帴鍒癥OLO鍥惧儚澶勭悊鍚庣澶辫触: ${error.message}`);
  }
}; 

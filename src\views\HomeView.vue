<script setup lang="ts">
import { ref, reactive, inject, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 获取更新检测结果的方法
const updateDetectionResults = inject('updateDetectionResults')

// 上传状态
const uploadStatus = ref('idle') // idle, uploading, success, error
const imageUrl = ref('')
const detectionResults = ref(null)
const uploadProgress = ref(0)
const fileInputRef = ref(null)

// 上传配置
const uploadConfig = reactive({
  accept: 'image/jpeg,image/png,image/heic',
  maxSize: 100 * 1024 * 1024, // 100MB
})

// 处理文件上传
const handleFileUpload = (event: any) => {
  const file = event.target.files[0]
  if (!file) return
  
  // 检查文件类型
  if (!uploadConfig.accept.includes(file.type)) {
    ElMessage.error('不支持的文件格式，请上传JPG/PNG/HEIC格式图片')
    return
  }
  
  // 检查文件大小
  if (file.size > uploadConfig.maxSize) {
    ElMessage.error('文件过大，请上传小于100MB的图片')
    return
  }
  
  // 显示预览
  const reader = new FileReader()
  reader.onload = (e) => {
    imageUrl.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
  
  // 上传文件
  uploadStatus.value = 'uploading'
  uploadProgress.value = 0
  
  // 模拟上传进度
  const progressInterval = setInterval(() => {
    uploadProgress.value += Math.random() * 10
    if (uploadProgress.value >= 100) {
      uploadProgress.value = 100
      clearInterval(progressInterval)
      
      // 模拟处理延迟
      setTimeout(() => {
        uploadStatus.value = 'success'
        // 模拟检测结果
        detectionResults.value = {
          vehicles: [
            { type: '小型汽车', confidence: 0.95, position: { x: 120, y: 150, width: 200, height: 100 }, risk: 'low' },
            { type: '小型汽车', confidence: 0.92, position: { x: 350, y: 200, width: 180, height: 90 }, risk: 'low' },
            { type: '卡车', confidence: 0.87, position: { x: 500, y: 180, width: 250, height: 150 }, risk: 'medium' }
          ],
          summary: '检测到3辆车辆，其中包括2辆小型汽车和1辆卡车。'
        }
        
        // 发送检测结果到父组件或状态管理
        emitDetectionResults()
      }, 500)
    }
  }, 200)
}

// 拖拽上传
const handleDrop = (event: any) => {
  event.preventDefault()
  const file = event.dataTransfer.files[0]
  if (file) {
    const dummyEvent = { target: { files: [file] } }
    handleFileUpload(dummyEvent)
  }
}

const handleDragOver = (event: any) => {
  event.preventDefault()
}

// 发送检测结果
const emitDetectionResults = () => {
  // 使用inject获取的方法更新检测结果
  if (updateDetectionResults) {
    updateDetectionResults(detectionResults.value)
  }
}

// 重置上传
const resetUpload = () => {
  uploadStatus.value = 'idle'
  imageUrl.value = ''
  detectionResults.value = null
  uploadProgress.value = 0
  
  // 清空检测结果
  if (updateDetectionResults) {
    updateDetectionResults(null)
  }
}

// 模拟历史记录
const recentUploads = ref([
  { id: 1, thumbnail: 'https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?w=100&h=60&fit=crop&auto=format', time: new Date(Date.now() - 3600000 * 2) },
  { id: 2, thumbnail: 'https://images.unsplash.com/photo-1566143260803-b6fad8a4c05d?w=100&h=60&fit=crop&auto=format', time: new Date(Date.now() - 3600000 * 5) },
  { id: 3, thumbnail: 'https://images.unsplash.com/photo-1568605117036-5fe5e7bab0b7?w=100&h=60&fit=crop&auto=format', time: new Date(Date.now() - 3600000 * 8) },
  { id: 4, thumbnail: 'https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=100&h=60&fit=crop&auto=format', time: new Date(Date.now() - 3600000 * 24) },
  { id: 5, thumbnail: 'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?w=100&h=60&fit=crop&auto=format', time: new Date(Date.now() - 3600000 * 48) }
])

// 选择历史图片
const selectHistoryImage = (item) => {
  // 模拟加载历史图片
  imageUrl.value = item.thumbnail.replace('w=100&h=60&fit=crop', 'w=800&h=600&fit=crop')
  uploadStatus.value = 'success'
  
  // 模拟检测结果
  detectionResults.value = {
    vehicles: [
      { type: '小型汽车', confidence: 0.95, position: { x: 120, y: 150, width: 200, height: 100 }, risk: 'low' },
      { type: '小型汽车', confidence: 0.92, position: { x: 350, y: 200, width: 180, height: 90 }, risk: 'low' },
      { type: '卡车', confidence: 0.87, position: { x: 500, y: 180, width: 250, height: 150 }, risk: 'medium' }
    ],
    summary: '检测到3辆车辆，其中包括2辆小型汽车和1辆卡车。'
  }
  
  // 发送检测结果
  emitDetectionResults()
}

// 导出结果
const exportResults = () => {
  ElMessage.success('检测结果已导出')
}

// 动画效果
onMounted(() => {
  // 可以添加一些初始化动画
})
</script>

<template>
  <div class="home-container">
    <div class="section-header">
      <h1 class="page-title">交通图像分析</h1>
      <div class="header-actions">
        <el-tooltip content="查看教程" placement="top">
          <el-button type="info" plain><el-icon><VideoPlay /></el-icon> 使用教程</el-button>
        </el-tooltip>
        <el-tooltip content="API文档" placement="top">
          <el-button type="info" plain><el-icon><Document /></el-icon> API文档</el-button>
        </el-tooltip>
      </div>
    </div>
    
    <el-card class="upload-card">
      <!-- 上传区域 -->
      <div 
        class="upload-area" 
        :class="{ 
          'is-active': uploadStatus === 'uploading',
          'is-success': uploadStatus === 'success',
          'is-error': uploadStatus === 'error'
        }"
        @drop="handleDrop"
        @dragover="handleDragOver"
      >
        <div v-if="uploadStatus === 'idle'" class="upload-placeholder">
          <div class="upload-icon-container">
            <el-icon class="upload-icon"><Upload /></el-icon>
          </div>
          <div class="upload-text">
            <h3>拖拽图片到此处或点击上传</h3>
            <p class="upload-tip">支持JPG/PNG/HEIC格式，最大100MB</p>
            <el-button type="primary" @click="fileInputRef.click()">
              <el-icon><Plus /></el-icon> 选择图片
            </el-button>
            <input 
              ref="fileInputRef" 
              type="file" 
              class="file-input" 
              :accept="uploadConfig.accept"
              @change="handleFileUpload" 
            />
          </div>
        </div>
        
        <div v-else-if="uploadStatus === 'uploading'" class="upload-loading">
          <el-progress 
            type="circle" 
            :percentage="Math.floor(uploadProgress)" 
            :status="uploadProgress < 100 ? 'exception' : 'success'"
            :stroke-width="6"
          />
          <p class="loading-text">{{ uploadProgress < 100 ? '正在上传图像...' : '正在分析图像...' }}</p>
          <p class="loading-subtext">请稍候，这可能需要几秒钟时间</p>
        </div>
        
        <div v-else-if="uploadStatus === 'success'" class="upload-result">
          <div class="image-container">
            <img :src="imageUrl" alt="上传图片" class="preview-image" />
            
            <!-- 检测框叠加层 -->
            <div class="detection-overlay" v-if="detectionResults">
              <div 
                v-for="(vehicle, index) in detectionResults.vehicles" 
                :key="index"
                class="detection-box"
                :class="`risk-${vehicle.risk}`"
                :style="{
                  left: `${vehicle.position.x}px`,
                  top: `${vehicle.position.y}px`,
                  width: `${vehicle.position.width}px`,
                  height: `${vehicle.position.height}px`
                }"
              >
                <span class="detection-label">
                  {{ vehicle.type }} ({{ (vehicle.confidence * 100).toFixed(0) }}%)
                </span>
              </div>
            </div>
          </div>
          
          <div class="action-buttons">
            <el-button type="primary" @click="resetUpload">
              <el-icon><RefreshRight /></el-icon> 重新上传
            </el-button>
            <el-button type="success" @click="exportResults">
              <el-icon><Download /></el-icon> 导出结果
            </el-button>
            <el-dropdown>
              <el-button type="info">
                更多操作 <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item><el-icon><Share /></el-icon> 分享结果</el-dropdown-item>
                  <el-dropdown-item><el-icon><EditPen /></el-icon> 编辑标注</el-dropdown-item>
                  <el-dropdown-item><el-icon><Printer /></el-icon> 打印报告</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        
        <div v-else-if="uploadStatus === 'error'" class="upload-error">
          <el-icon class="error-icon"><CircleClose /></el-icon>
          <h3>图像处理失败</h3>
          <p>可能是由于图像格式不支持或服务器暂时不可用</p>
          <el-button type="primary" @click="resetUpload">重新上传</el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 最近上传 -->
    <div class="recent-uploads-section">
      <div class="section-header">
        <h3>最近上传</h3>
        <el-button type="primary" text>查看全部</el-button>
      </div>
      
      <div class="thumbnail-list">
        <div 
          class="thumbnail-item" 
          v-for="item in recentUploads" 
          :key="item.id"
          @click="selectHistoryImage(item)"
        >
          <div class="thumbnail-image">
            <img :src="item.thumbnail" alt="历史图片" />
            <div class="thumbnail-overlay">
              <el-icon><View /></el-icon>
            </div>
          </div>
          <span class="thumbnail-time">{{ item.time.toLocaleString() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.upload-card {
  flex: 1;
  margin-bottom: 24px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  transition: all 0.3s;
}

.upload-card:hover {
  box-shadow: var(--shadow-lg);
}

.upload-area {
  height: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-md);
  transition: all 0.3s;
  padding: 20px;
  background-color: rgba(247, 250, 252, 0.5);
}

.upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(96, 165, 250, 0.05);
}

.upload-area.is-active {
  border-color: var(--primary-color);
  background-color: rgba(96, 165, 250, 0.05);
}

.upload-area.is-success {
  border-color: var(--success-color);
  border-style: solid;
}

.upload-area.is-error {
  border-color: var(--danger-color);
  border-style: solid;
}

.upload-placeholder {
  text-align: center;
  max-width: 500px;
}

.upload-icon-container {
  margin-bottom: 20px;
  background-color: rgba(96, 165, 250, 0.1);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.upload-icon {
  font-size: 36px;
  color: var(--primary-color);
}

.upload-text h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.upload-tip {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}

.file-input {
  display: none;
}

.upload-loading {
  text-align: center;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.loading-subtext {
  margin-top: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

.error-icon {
  font-size: 64px;
  color: var(--danger-color);
  margin-bottom: 16px;
}

.upload-error h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.upload-error p {
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.upload-result {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  border-radius: var(--radius-md);
  background-color: #000;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.detection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.detection-box {
  position: absolute;
  border: 2px solid;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 5px rgba(0, 198, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(0, 198, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 198, 255, 0.5);
  }
}

.detection-box.risk-low {
  border-color: var(--success-color);
}

.detection-box.risk-medium {
  border-color: var(--warning-color);
}

.detection-box.risk-high {
  border-color: var(--danger-color);
  animation: danger-pulse 1.5s infinite;
}

@keyframes danger-pulse {
  0% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
  }
}

.detection-label {
  position: absolute;
  top: -25px;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.detection-box.risk-low .detection-label {
  background-color: var(--success-color);
}

.detection-box.risk-medium .detection-label {
  background-color: var(--warning-color);
}

.detection-box.risk-high .detection-label {
  background-color: var(--danger-color);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px 0;
}

.recent-uploads-section {
  margin-top: auto;
  padding-top: 20px;
}

.recent-uploads-section h3 {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  color: var(--text-primary);
}

.thumbnail-list {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding: 16px 0;
}

.thumbnail-item {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.2s;
}

.thumbnail-item:hover {
  transform: translateY(-4px);
}

.thumbnail-image {
  width: 120px;
  height: 70px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
  margin-bottom: 8px;
  box-shadow: var(--shadow-sm);
}

.thumbnail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s;
}

.thumbnail-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s;
}

.thumbnail-overlay .el-icon {
  color: white;
  font-size: 24px;
}

.thumbnail-item:hover .thumbnail-overlay {
  opacity: 1;
}

.thumbnail-item:hover img {
  transform: scale(1.1);
}

.thumbnail-time {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}
</style>

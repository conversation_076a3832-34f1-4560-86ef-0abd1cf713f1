<template>
  <div class="video-monitor-container">
    <!-- 主内容区 -->
    <div class="card-container main-panel">
      <div class="card-header">
        <span class="card-title">视频监测</span>
        <div class="card-tools">
          <label for="video-upload" class="upload-button hover-lift" v-if="!isStreaming">
            <i class="upload-icon-small"></i>
            上传视频
          </label>
          <input 
            type="file" 
            id="video-upload" 
            accept="video/*" 
            @change="handleVideoUpload" 
            class="upload-input"
            v-if="!isStreaming"
          />
          <button class="toggle-button hover-lift" @click="toggleWebcam" v-if="!isStreaming">
            开启摄像头
          </button>
          <button class="stop-button hover-lift" @click="stopStreaming" v-if="isStreaming">
            停止监测
          </button>
        </div>
      </div>
      
      <div class="card-body">
        <div class="upload-area hover-lift" v-if="!videoSrc && !isWebcamActive">
          <label for="video-upload" class="upload-label">
            <div class="upload-icon pulse">+</div>
            <div>点击上传视频或开启摄像头</div>
          </label>
        </div>
        <div class="video-container" v-else>
          <video 
            ref="videoElement" 
            id="video-player" 
            class="video-player" 
            :src="videoSrc" 
            autoplay
            muted
            @loadedmetadata="onVideoLoaded"
            v-show="!isWebcamActive"
          ></video>
          <video
            ref="webcamElement"
            id="webcam-player"
            class="video-player"
            autoplay
            muted
            playsinline
            v-show="isWebcamActive"
          ></video>
          <canvas ref="detectionCanvas" class="detection-canvas"></canvas>
        </div>
      </div>
      
      <div class="card-footer">
        <div class="video-controls" v-if="videoSrc && !isStreaming">
          <button class="control-button" @click="playPauseVideo">
            <i :class="isPlaying ? 'pause-icon' : 'play-icon'"></i>
            {{ isPlaying ? '暂停' : '播放' }}
          </button>
          <button class="start-monitor-button" @click="startMonitoring" :disabled="isLoading">
            {{ isLoading ? '处理中...' : '开始实时监测' }}
          </button>
        </div>
        
        <div class="streaming-info" v-if="isStreaming">
          <div class="streaming-status">
            <div class="status-indicator"></div>
            <span>实时监测中</span>
          </div>
          <div class="streaming-time">已监测: {{ formattedStreamingTime }}</div>
        </div>
      </div>
    </div>
    
    <!-- 右边栏 -->
    <div class="result-panel">
      <div class="risk-analysis-header">
        <div class="risk-analysis-icon"></div>
        <span class="risk-analysis-title">风险统计</span>
      </div>

      <!-- 实时检测结果面板 -->
      <div class="detection-results" v-if="isStreaming">
        <!-- 风险事件统计 -->
        <div class="risk-events-container high-risk" v-if="highRiskEvents.length > 0">
          <div class="risk-summary">
            <div class="risk-count danger">{{ highRiskEvents.length }}</div>
            <div class="risk-label">高风险事件</div>
          </div>

          <div class="risk-events">
            <div 
              class="risk-event-item high-risk" 
              v-for="(event, index) in highRiskEvents.slice(0, 5)" 
              :key="'high-' + index"
            >
              <div class="event-time">{{ event.time }}</div>
              <div class="event-content">
                <div class="event-type">{{ event.category }}</div>
                <div class="event-description">{{ event.event }}</div>
              </div>
              <div class="event-risk-level risk-high">高风险</div>
            </div>
          </div>
        </div>

        <!-- 低风险事件 -->
        <div class="risk-events-container low-risk" v-if="lowRiskEvents.length > 0">
          <div class="risk-summary">
            <div class="risk-count warning">{{ lowRiskEvents.length }}</div>
            <div class="risk-label">低风险事件</div>
          </div>

          <div class="risk-events">
            <div 
              class="risk-event-item low-risk" 
              v-for="(event, index) in lowRiskEvents.slice(0, 5)" 
              :key="'low-' + index"
            >
              <div class="event-time">{{ event.time }}</div>
              <div class="event-content">
                <div class="event-type">{{ event.category }}</div>
                <div class="event-description">{{ event.event }}</div>
              </div>
              <div class="event-risk-level risk-low">低风险</div>
            </div>
          </div>
        </div>

        <!-- 无风险事件 -->
        <div class="safe-container" v-if="detectionEvents.length === 0">
          <div class="safe-icon">✓</div>
          <div class="safe-message">安全</div>
          <div class="safe-description">未检测到任何风险事件</div>
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="stats-panel-horizontal" v-if="isStreaming">
        <div class="stats-item">
          <div class="stats-icon person-icon"></div>
          <div class="stats-info">
            <div class="stats-label">人员</div>
            <div class="stats-value">{{ personCount }}</div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-icon vehicle-icon"></div>
          <div class="stats-info">
            <div class="stats-label">车辆</div>
            <div class="stats-value">{{ vehicleCount }}</div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-icon machine-icon"></div>
          <div class="stats-info">
            <div class="stats-label">设备</div>
            <div class="stats-value">{{ machineCount }}</div>
          </div>
        </div>
      </div>

      <!-- 提示板 -->
      <div class="instruction-panel" v-if="!isStreaming">
        <div class="instruction-icon"></div>
        <h3 class="instruction-title">使用说明</h3>
        <ul class="instruction-list">
          <li>上传视频文件或开启摄像头</li>
          <li>点击"开始实时监测"启动分析</li>
          <li>系统将自动检测视频中的风险事件</li>
          <li>右侧面板将显示检测结果及风险统计</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { detectObjects } from '../services/DetectionService';

// 视频源和状态
const videoSrc = ref('');
const videoElement = ref<HTMLVideoElement | null>(null);
const webcamElement = ref<HTMLVideoElement | null>(null);
const detectionCanvas = ref<HTMLCanvasElement | null>(null);
const isPlaying = ref(false);
const isWebcamActive = ref(false);
const isStreaming = ref(false);
const isLoading = ref(false);
const streamStartTime = ref(0);
const detectionInterval = ref<number | null>(null);

// 检测结果
const detectionEvents = ref<any[]>([]);
// 统计数据
const personCount = ref(0);
const vehicleCount = ref(0);
const machineCount = ref(0);

// 高风险和低风险事件计算属性
const highRiskEvents = computed(() => {
  return detectionEvents.value.filter(event => event.risk_level === 'high');
});

const lowRiskEvents = computed(() => {
  return detectionEvents.value.filter(event => event.risk_level === 'low');
});

// 格式化流媒体时间
const formattedStreamingTime = computed(() => {
  if (!streamStartTime.value) return '00:00:00';
  
  const totalSeconds = Math.floor((Date.now() - streamStartTime.value) / 1000);
  const hours = Math.floor(totalSeconds / 3600).toString().padStart(2, '0');
  const minutes = Math.floor((totalSeconds % 3600) / 60).toString().padStart(2, '0');
  const seconds = (totalSeconds % 60).toString().padStart(2, '0');
  
  return `${hours}:${minutes}:${seconds}`;
});

// 处理视频上传
function handleVideoUpload(event: Event) {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    videoSrc.value = URL.createObjectURL(file);
    isWebcamActive.value = false;
  }
}

// 视频加载完成
function onVideoLoaded() {
  console.log('视频加载完成');
}

// 播放/暂停视频
function playPauseVideo() {
  if (!videoElement.value) return;
  
  if (videoElement.value.paused) {
    videoElement.value.play();
    isPlaying.value = true;
  } else {
    videoElement.value.pause();
    isPlaying.value = false;
  }
}

// 切换摄像头
async function toggleWebcam() {
  try {
    if (!isWebcamActive.value) {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (webcamElement.value) {
        webcamElement.value.srcObject = stream;
        isWebcamActive.value = true;
        videoSrc.value = ''; // 清除文件视频源
      }
    } else {
      stopWebcam();
    }
  } catch (error) {
    console.error('无法访问摄像头:', error);
    alert('无法访问摄像头，请检查权限设置');
  }
}

// 停止摄像头
function stopWebcam() {
  if (webcamElement.value && webcamElement.value.srcObject) {
    const stream = webcamElement.value.srcObject as MediaStream;
    const tracks = stream.getTracks();
    
    tracks.forEach(track => track.stop());
    webcamElement.value.srcObject = null;
    isWebcamActive.value = false;
  }
}

// 开始监测
function startMonitoring() {
  if ((!videoSrc.value && !isWebcamActive.value) || isStreaming.value) return;
  
  isStreaming.value = true;
  streamStartTime.value = Date.now();
  detectionEvents.value = [];
  
  // 每隔2秒进行一次检测
  detectionInterval.value = window.setInterval(detectFrame, 2000);
}

// 停止监测
function stopStreaming() {
  if (detectionInterval.value) {
    clearInterval(detectionInterval.value);
    detectionInterval.value = null;
  }
  
  isStreaming.value = false;
  
  if (isWebcamActive.value) {
    stopWebcam();
  }
}

// 检测当前帧
async function detectFrame() {
  try {
    isLoading.value = true;
    
    // 获取视频当前帧
    const canvas = document.createElement('canvas');
    const video = isWebcamActive.value ? webcamElement.value : videoElement.value;
    
    if (!video || video.videoWidth === 0) return;
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 在canvas上绘制当前视频帧
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // 获取帧的base64数据
    const imageData = canvas.toDataURL('image/jpeg');
    
    // 生成检测提示词
    const customPrompt = `
    1. 详细分析图像中的安全风险，重点关注以下几类目标：
       - 人员：识别所有人员，用蓝色框标注，特别关注是否佩戴安全帽、反光背心的工人（标记为高风险），摔倒的工人（标记为高风险）
       - 车辆：识别所有车辆，用深蓝色框标注，特别关注危险驾驶行为（标记为高风险）
       - 机械：识别所有机械设备，用绿色框标注，特别关注不安全操作（标记为高风险）
    
    2. 对每个检测到的目标，提供以下信息：
       - 类别：人员/车辆/机械等
       - 事件：具体事件描述
       - 风险等级：高风险(high)/低风险(low)/安全(safe)
       - 置信度：0-1之间的数值
       - 边界框坐标：bbox_2d格式
       - 标签：用于显示的文本标签
    
    3. 提供图像的整体安全风险分析，使用简洁描述。
    
    4. 识别并分别列出图像中的高风险事件和低风险事件。
    `;
    
    // 调用检测API
    const modelId = 'qwen2.5-vl-7b-instruct'; // 使用轻量模型以提高速度
    const selectedOptions = ['person_count', 'person_wear', 'vehicle_count', 'vehicle_status', 'equipment_status'];
    
    const results = await detectObjects(imageData, selectedOptions, customPrompt, modelId);
    
    // 绘制检测框
    drawDetectionBoxes(results);
    
    // 添加检测到的风险事件到列表
    const time = new Date().toLocaleTimeString();
    
    // 更新统计数据
    updateStatistics(results);
    
    if (results.high_risk_events && results.high_risk_events.length > 0) {
      results.high_risk_events.forEach((event: any) => {
        if (event.risk_level === 'high') {
          detectionEvents.value.unshift({
            time,
            category: event.category || '未知类别',
            event: event.event || '未知事件',
            risk_level: 'high'
          });
        }
      });
    }
    
    if (results.low_risk_events && results.low_risk_events.length > 0) {
      results.low_risk_events.forEach((event: any) => {
        if (event.risk_level === 'low') {
          detectionEvents.value.unshift({
            time,
            category: event.category || '未知类别',
            event: event.event || '未知事件',
            risk_level: 'low'
          });
        }
      });
    }
    
    // 限制事件列表长度
    if (detectionEvents.value.length > 50) {
      detectionEvents.value = detectionEvents.value.slice(0, 50);
    }
    
  } catch (error) {
    console.error('检测失败:', error);
  } finally {
    isLoading.value = false;
  }
}

// 更新统计数据
function updateStatistics(results: any) {
  // 统计人员数量
  const persons = results.objects?.filter((obj: any) => 
    obj.category === '人员' || 
    (obj.category && (obj.category.includes('person') || obj.category.includes('worker')))
  ) || [];
  personCount.value = persons.length;
  
  // 统计车辆数量
  const vehicles = results.objects?.filter((obj: any) => 
    obj.category === '车辆' || 
    (obj.category && (obj.category.includes('vehicle') || obj.category.includes('car')))
  ) || [];
  vehicleCount.value = vehicles.length;
  
  // 统计机械设备数量
  const machines = results.objects?.filter((obj: any) => 
    obj.category === '机械' || obj.category === '设备' || 
    (obj.category && (obj.category.includes('machine') || obj.category.includes('equipment')))
  ) || [];
  machineCount.value = machines.length;
}

// 绘制检测框
function drawDetectionBoxes(results: any) {
  if (!detectionCanvas.value) return;
  
  const canvas = detectionCanvas.value;
  const video = isWebcamActive.value ? webcamElement.value : videoElement.value;
  
  if (!video) return;
  
  // 调整canvas尺寸匹配视频
  canvas.width = video.clientWidth;
  canvas.height = video.clientHeight;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // 清除canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // 首先检查是否有分割可视化图像URL（YOLOv11-seg生成的）
  if (results.visualized_image_url || (results.use_visualized_image && results.visualized_image_url)) {
    console.log('检测到分割可视化图像，使用掩码图像显示:', results.visualized_image_url);
    const segImageElement = new Image();
    segImageElement.crossOrigin = "anonymous";  // 添加跨域支持
    
    segImageElement.onload = function() {
      console.log('分割图像加载成功，尺寸:', segImageElement.width, 'x', segImageElement.height);
      // 图像加载完成后绘制到画布上
      ctx.drawImage(segImageElement, 0, 0, canvas.width, canvas.height);
    };
    
    segImageElement.onerror = function(e) {
      console.error('分割图像加载失败:', e);
      // 加载失败时回退到普通检测框绘制
      drawFallbackDetectionBoxes(results, ctx, video);
    };
    
    // 设置图像源 - 使用绝对路径，添加时间戳防止缓存
    const cacheBuster = new Date().getTime();
    segImageElement.src = `http://localhost:8000${results.visualized_image_url}?t=${cacheBuster}`;
    return; // 直接使用可视化图像，不再单独绘制检测框
  }
  
  // 如果没有可视化图像，则继续使用常规检测框绘制
  drawFallbackDetectionBoxes(results, ctx, video);
}

// 回退方案：使用普通检测框绘制
function drawFallbackDetectionBoxes(results: any, ctx: CanvasRenderingContext2D, video: HTMLVideoElement) {
  // 获取视频的实际尺寸
  const videoWidth = video.videoWidth || results.input_width || 1920;
  const videoHeight = video.videoHeight || results.input_height || 1080;
  
  // 获取视频的显示尺寸
  const displayWidth = video.clientWidth;
  const displayHeight = video.clientHeight;
  
  // 定义颜色映射
  const colors: Record<string, string> = {
    'high': 'rgba(255, 0, 0, 0.5)', // 高风险红色
    'low': 'rgba(255, 193, 7, 0.5)', // 低风险黄色
    'person': 'rgba(0, 123, 255, 0.5)', // 人员蓝色
    'vehicle': 'rgba(111, 66, 193, 0.5)', // 车辆紫色
    'machine': 'rgba(40, 167, 69, 0.5)', // 机械设备绿色
    'default': 'rgba(128, 128, 128, 0.5)' // 默认灰色
  };
  
  // 绘制高风险事件
  if (results.high_risk_events && results.high_risk_events.length > 0) {
    results.high_risk_events.forEach((event: any) => {
      if (event.bbox_2d && event.bbox_2d.length === 4) {
        const [x1, y1, x2, y2] = event.bbox_2d;
        
        // 坐标转换
        const displayX1 = (x1 * displayWidth);
        const displayY1 = (y1 * displayHeight);
        const displayX2 = (x2 * displayWidth);
        const displayY2 = (y2 * displayHeight);
        
        // 绘制边框
        ctx.strokeStyle = colors.high;
        ctx.lineWidth = 3;
        ctx.fillStyle = colors.high.replace('0.5', '0.2');
        ctx.strokeRect(displayX1, displayY1, displayX2 - displayX1, displayY2 - displayY1);
        ctx.fillRect(displayX1, displayY1, displayX2 - displayX1, displayY2 - displayY1);
        
        // 绘制标签
        ctx.fillStyle = colors.high;
        ctx.font = '14px "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif';
        const label = event.event || '高风险';
        ctx.fillRect(displayX1, displayY1 - 20, ctx.measureText(label).width + 10, 20);
        ctx.fillStyle = 'white';
        ctx.fillText(label, displayX1 + 5, displayY1 - 5);
      }
    });
  }
  
  // 绘制低风险事件
  if (results.low_risk_events && results.low_risk_events.length > 0) {
    results.low_risk_events.forEach((event: any) => {
      if (event.bbox_2d && event.bbox_2d.length === 4) {
        const [x1, y1, x2, y2] = event.bbox_2d;
        
        // 坐标转换 - 直接使用归一化坐标，这些坐标已经是0-1范围
        const displayX1 = (x1 * displayWidth);
        const displayY1 = (y1 * displayHeight);
        const displayX2 = (x2 * displayWidth);
        const displayY2 = (y2 * displayHeight);
        
        // 绘制边框
        ctx.strokeStyle = colors.low;
        ctx.lineWidth = 3;
        ctx.fillStyle = colors.low.replace('0.5', '0.2');
        ctx.strokeRect(displayX1, displayY1, displayX2 - displayX1, displayY2 - displayY1);
        ctx.fillRect(displayX1, displayY1, displayX2 - displayX1, displayY2 - displayY1);
        
        // 绘制标签
        ctx.fillStyle = colors.low;
        ctx.font = '14px "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif';
        const label = event.event || '低风险';
        ctx.fillRect(displayX1, displayY1 - 20, ctx.measureText(label).width + 10, 20);
        ctx.fillStyle = 'white';
        ctx.fillText(label, displayX1 + 5, displayY1 - 5);
      }
    });
  }
  
  // 绘制DINO检测结果（如果有的话）
  if (results.dino_detections && results.dino_detections.length > 0) {
    results.dino_detections.forEach((detection: any) => {
      if (detection.bbox && detection.bbox.length === 4) {
        const [x1, y1, x2, y2] = detection.bbox;
        const category = detection.category || "default";
        const risk = detection.risk_level || "default";
        
        // 选择颜色
        let color = colors.default;
        if (risk === "high") color = colors.high;
        else if (risk === "low" || risk === "medium") color = colors.low;
        
        // 坐标转换
        const displayX1 = (x1 * displayWidth);
        const displayY1 = (y1 * displayHeight);
        const displayX2 = (x2 * displayWidth);
        const displayY2 = (y2 * displayHeight);
        
        // 绘制边框
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.fillStyle = color.replace('0.5', '0.1');
        ctx.strokeRect(displayX1, displayY1, displayX2 - displayX1, displayY2 - displayY1);
        ctx.fillRect(displayX1, displayY1, displayX2 - displayX1, displayY2 - displayY1);
        
        // 绘制标签
        ctx.fillStyle = color;
        ctx.font = '12px "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif';
        const label = detection.label || category;
        const score = detection.score ? ` ${(detection.score * 100).toFixed(0)}%` : '';
        const displayLabel = `${label}${score}`;
        ctx.fillRect(displayX1, displayY1 - 18, ctx.measureText(displayLabel).width + 8, 18);
        ctx.fillStyle = 'white';
        ctx.fillText(displayLabel, displayX1 + 4, displayY1 - 4);
      }
    });
  }
}

// 组件挂载时
onMounted(() => {
  // 初始化视频组件引用
  videoElement.value = document.getElementById('video-player') as HTMLVideoElement;
  webcamElement.value = document.getElementById('webcam-player') as HTMLVideoElement;
  detectionCanvas.value = document.getElementById('detection-canvas') as HTMLCanvasElement;
});

// 组件卸载时
onUnmounted(() => {
  // 停止监测
  if (detectionInterval.value) {
    clearInterval(detectionInterval.value);
  }
  
  // 停止摄像头
  stopWebcam();
  
  // 释放视频资源
  if (videoSrc.value) {
    URL.revokeObjectURL(videoSrc.value);
  }
});
</script>

<style scoped>
.video-monitor-container {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 15px;
  height: 100%;
  width: 100%;
}

/* 主面板 */
.main-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 结果面板 */
.result-panel {
  width: 320px;
  background: linear-gradient(135deg, #001529, #002140);
  overflow-y: auto;
  min-width: 320px;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
  height: 100%;
}

.result-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.7), transparent);
  z-index: 1;
}

.risk-analysis-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.risk-analysis-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 5px rgba(0, 168, 255, 0.5));
  position: relative;
  z-index: 1;
}

.risk-analysis-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  position: relative;
  z-index: 1;
  letter-spacing: 1px;
}

/* 保留原来的样式部分 */
.card-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(0, 33, 64, 0.5);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
  display: flex;
  align-items: center;
}

.card-body {
  flex: 1;
  padding: 20px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-footer {
  padding: 15px 20px;
  background: rgba(0, 21, 41, 0.4);
  border-top: 1px solid rgba(0, 168, 255, 0.3);
}

/* 添加新的右边栏样式 */
.detection-results {
  padding: 15px;
  overflow-y: auto;
}

.risk-events-container {
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(0, 21, 41, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(0, 168, 255, 0.2);
  transition: all 0.3s ease;
}

.risk-events-container.high-risk {
  border-color: rgba(245, 34, 45, 0.3);
  background: linear-gradient(135deg, rgba(245, 34, 45, 0.05), rgba(0, 21, 41, 0.5));
}

.risk-events-container.low-risk {
  border-color: rgba(250, 173, 20, 0.3);
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.05), rgba(0, 21, 41, 0.5));
}

.risk-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.2);
}

.risk-count {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
}

.risk-count.danger {
  color: #f5222d;
  text-shadow: 0 0 15px rgba(245, 34, 45, 0.5);
}

.risk-count.warning {
  color: #faad14;
  text-shadow: 0 0 15px rgba(250, 173, 20, 0.5);
}

.risk-label {
  font-size: 14px;
  color: #bdc3c7;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.risk-event-item {
  display: flex;
  flex-direction: column;
  padding: 10px;
  background: rgba(0, 21, 41, 0.3);
  border-radius: 8px;
  margin-bottom: 10px;
  transition: transform 0.3s;
  border-left: 3px solid transparent;
}

.risk-event-item:hover {
  transform: translateY(-2px);
}

.risk-event-item.high-risk {
  border-left-color: #f5222d;
}

.risk-event-item.low-risk {
  border-left-color: #faad14;
}

.event-time {
  font-size: 12px;
  color: #bdc3c7;
  margin-bottom: 5px;
}

.event-content {
  margin-bottom: 8px;
}

.event-type {
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 4px;
}

.event-description {
  font-size: 13px;
  color: #bdc3c7;
}

.event-risk-level {
  align-self: flex-start;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.risk-high {
  background: rgba(245, 34, 45, 0.2);
  color: #f5222d;
  border: 1px solid rgba(245, 34, 45, 0.3);
}

.risk-low {
  background: rgba(250, 173, 20, 0.2);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.safe-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 15px;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05), rgba(0, 21, 41, 0.3));
  border-radius: 8px;
  border: 1px solid rgba(82, 196, 26, 0.3);
  margin: 15px 0;
}

.safe-icon {
  font-size: 36px;
  color: #52c41a;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(82, 196, 26, 0.5);
}

.safe-message {
  font-size: 24px;
  font-weight: bold;
  color: #52c41a;
  margin-bottom: 10px;
}

.safe-description {
  font-size: 14px;
  color: #bdc3c7;
  text-align: center;
}

.stats-panel-horizontal {
  display: flex;
  justify-content: space-around;
  background: rgba(0, 21, 41, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin: 15px;
  border: 1px solid rgba(0, 168, 255, 0.2);
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stats-icon {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.person-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
}

.vehicle-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236f42c1'%3E%3Cpath d='M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.85 6h10.29l1.08 3.11H5.77L6.85 6zM19 17H5v-5h14v5z'%3E%3C/path%3E%3C/svg%3E");
}

.machine-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2328a745'%3E%3Cpath d='M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'%3E%3C/path%3E%3C/svg%3E");
}

.stats-info {
  display: flex;
  flex-direction: column;
}

.stats-label {
  font-size: 12px;
  color: #bdc3c7;
}

.stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
}

.instruction-panel {
  background: rgba(0, 21, 41, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin: 15px;
  border: 1px solid rgba(0, 168, 255, 0.2);
}

.instruction-icon {
  width: 40px;
  height: 40px;
  margin: 0 auto 15px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.instruction-title {
  text-align: center;
  font-size: 18px;
  margin-bottom: 15px;
  color: #1890ff;
}

.instruction-list {
  list-style-type: none;
  padding: 0;
}

.instruction-list li {
  padding: 8px 0;
  border-bottom: 1px dashed rgba(0, 168, 255, 0.2);
  color: #bdc3c7;
  position: relative;
  padding-left: 20px;
}

.instruction-list li:last-child {
  border-bottom: none;
}

.instruction-list li::before {
  content: ">";
  position: absolute;
  left: 0;
  color: #1890ff;
  font-weight: bold;
}

/* 保留原来的样式 */
.upload-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #003a8c;
  border-radius: 12px;
  background-color: rgba(0, 33, 64, 0.3);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 60px;
  transition: all 0.3s;
  position: relative;
  z-index: 1;
}

.upload-icon {
  font-size: 64px;
  margin-bottom: 20px;
  color: #1890ff;
  transition: transform 0.3s;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
}

.upload-input {
  display: none;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  background-color: #000;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #000;
}

.detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.video-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.control-button, .start-monitor-button, .stop-button, .upload-button, .toggle-button {
  padding: 10px 20px;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-button {
  background: rgba(0, 21, 41, 0.6);
  color: #ffffff;
  border: 1px solid rgba(0, 168, 255, 0.3);
}

.control-button:hover {
  background: rgba(0, 58, 140, 0.5);
}

.start-monitor-button {
  background: linear-gradient(to right, #0c8eff, #0058ff);
  color: white;
  box-shadow: 0 4px 15px rgba(12, 142, 255, 0.3);
  flex: 1;
}

.start-monitor-button:hover {
  background: linear-gradient(to right, #0a6fcc, #0046cc);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(12, 142, 255, 0.5);
}

.start-monitor-button:disabled {
  background: linear-gradient(to right, #b3b3b3, #8c8c8c);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  opacity: 0.7;
}

.stop-button {
  background: linear-gradient(to right, #ff4d4f, #cf1322);
  color: white;
  box-shadow: 0 4px 15px rgba(207, 19, 34, 0.3);
}

.stop-button:hover {
  background: linear-gradient(to right, #ff7875, #f5222d);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(207, 19, 34, 0.5);
}

.upload-button {
  background: linear-gradient(to right, #0c8eff, #0058ff);
  color: white;
  box-shadow: 0 4px 10px rgba(12, 142, 255, 0.2);
}

.upload-button:hover {
  background: linear-gradient(to right, #0a6fcc, #0046cc);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(12, 142, 255, 0.3);
}

.toggle-button {
  background: rgba(12, 142, 255, 0.15);
  color: #0c8eff;
  box-shadow: 0 4px 10px rgba(12, 142, 255, 0.1);
  border: 1px solid rgba(12, 142, 255, 0.3);
}

.toggle-button:hover {
  background: rgba(12, 142, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(12, 142, 255, 0.2);
}

.play-icon, .pause-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background-size: contain;
  background-repeat: no-repeat;
}

.play-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
}

.pause-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M6 19h4V5H6v14zm8-14v14h4V5h-4z'/%3E%3C/svg%3E");
}

.upload-icon-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4v6zm-4 2h14v2H5v-2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.streaming-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.streaming-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #f5222d;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 34, 45, 0.6);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 34, 45, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 34, 45, 0);
  }
}

.streaming-time {
  font-weight: bold;
  color: #ffffff;
}
</style> 
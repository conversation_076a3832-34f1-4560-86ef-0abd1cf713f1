<template>
  <div :class="['chat-message', message.sender === 'bot' ? 'bot-message' : 'user-message']">
    <div class="message-avatar">
      <div :class="['avatar-icon', message.sender === 'bot' ? 'bot-avatar' : 'user-avatar']"></div>
    </div>
    <div class="message-content">
      <div class="message-header">
        <div class="message-sender">{{ message.sender === 'bot' ? '智能顾问' : '您' }}</div>
        <div class="message-time">{{ formatTime(message.timestamp) }}</div>
      </div>
      <div class="message-text" v-html="formatMessage(message.text)"></div>
      
      <div class="message-actions" v-if="message.sender === 'bot'">
        <button class="action-button copy-button" @click="copyMessage" title="复制">
          <span class="action-icon copy-icon"></span>
        </button>
        <button class="action-button tts-button" @click="textToSpeech" title="语音播放">
          <span class="action-icon tts-icon"></span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ChatMessage {
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

const props = defineProps<{
  message: ChatMessage
}>();

// 格式化时间
const formatTime = (date: Date): string => {
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};

// 格式化消息，支持简单的Markdown
const formatMessage = (text: string): string => {
  let formattedText = text
    // 加粗
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="chat-link">$1</a>')
    // 换行
    .replace(/\n/g, '<br>');
    
  return formattedText;
};

// 复制消息
const copyMessage = () => {
  navigator.clipboard.writeText(props.message.text)
    .then(() => {
      // 可以添加一个成功的通知
      console.log('消息已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
    });
};

// 文本转语音
const textToSpeech = () => {
  // 仅在浏览器支持语音合成API时执行
  if ('speechSynthesis' in window) {
    const utterance = new SpeechSynthesisUtterance(props.message.text);
    utterance.lang = 'zh-CN'; // 设置语言为中文
    window.speechSynthesis.speak(utterance);
  } else {
    console.error('当前浏览器不支持语音合成');
  }
};
</script>

<style scoped>
.chat-message {
  display: flex;
  margin-bottom: 20px;
  max-width: 90%;
}

.bot-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 10px;
  flex-shrink: 0;
  background: rgba(0, 58, 140, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.avatar-icon {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.bot-avatar {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M20 9V7c0-1.1-.9-2-2-2h-3c0-1.66-1.34-3-3-3S9 3.34 9 5H6c-1.1 0-2 .9-2 2v2c-1.66 0-3 1.34-3 3s1.34 3 3 3v4c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-4c1.66 0 3-1.34 3-3s-1.34-3-3-3zM7.5 11.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5S9.83 13 9 13s-1.5-.67-1.5-1.5zM16 17H8v-2h8v2zm-1-4c-.83 0-1.5-.67-1.5-1.5S14.17 10 15 10s1.5.67 1.5 1.5S15.83 13 15 13z'%3E%3C/path%3E%3C/svg%3E");
}

.user-avatar {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
}

.message-content {
  background: rgba(0, 33, 64, 0.7);
  padding: 15px;
  border-radius: 12px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.bot-message .message-content {
  border-top-left-radius: 0;
  border-left: 3px solid rgba(0, 168, 255, 0.6);
}

.user-message .message-content {
  background: rgba(0, 58, 140, 0.7);
  border-top-right-radius: 0;
  border-right: 3px solid #1890ff;
  text-align: right;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-sender {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
}

.bot-message .message-sender {
  color: #1890ff;
}

.user-message .message-sender {
  color: #40a9ff;
}

.message-time {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.message-text {
  color: #ffffff;
  line-height: 1.6;
  font-size: 14px;
  word-break: break-word;
}

.user-message .message-text {
  color: #ffffff;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.message-content:hover .message-actions {
  opacity: 1;
}

.action-button {
  background: rgba(0, 33, 64, 0.7);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.action-button:hover {
  background: rgba(0, 58, 140, 0.7);
  transform: translateY(-2px);
}

.action-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.copy-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z'/%3E%3C/svg%3E");
}

.tts-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z'/%3E%3C/svg%3E");
}

.chat-link {
  color: #40a9ff;
  text-decoration: underline;
  transition: all 0.3s ease;
}

.chat-link:hover {
  color: #69c0ff;
  text-decoration: none;
}
</style> 
# Traffic Eyes Ezviz Cloud Camera Integration
# Simple script to start only the Ezviz cloud camera monitoring

Write-Host "Starting Traffic Eyes Ezviz Cloud Camera Integration..." -ForegroundColor Green

# Set environment variables to avoid OpenCV errors
$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"

# Create necessary directories
$dirs = @("static_ezviz", "logs")
foreach ($d in $dirs) {
    if (-not (Test-Path $d)) {
        Write-Host "Creating directory: $d" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $d -Force | Out-Null
    }
}

# Check Python environment
$py = "python"
try {
    $ver = & $py --version 2>&1
    Write-Host "Python version: $ver" -ForegroundColor Green
} catch {
    Write-Host "Python not detected. Please install Python 3.8 or later." -ForegroundColor Red
    exit 1
}

# Install required packages
$pkgs = @("fastapi", "uvicorn", "redis", "websocket-client", "numpy", "opencv-python", "ultralytics", "pillow")
$toInstall = @()
foreach ($p in $pkgs) {
    $res = & $py -m pip show $p 2>&1
    if (-not $res) { $toInstall += $p }
}
if ($toInstall.Count -gt 0) {
    Write-Host "Installing missing packages: $($toInstall -join ', ')" -ForegroundColor Yellow
    & $py -m pip install $toInstall
}

# Configure Redis
Write-Host "Configuring Redis..." -ForegroundColor Cyan
& $py configure_redis.py

# Check if YOLO model exists
$modelPath = "yolov8x-seg.pt"
if (-not (Test-Path $modelPath)) {
    Write-Host "Downloading YOLO model..." -ForegroundColor Cyan
    & $py -c "from ultralytics import YOLO; YOLO('yolov8x-seg.pt')"
} else {
    Write-Host "YOLO model found: $modelPath" -ForegroundColor Green
}

# Start the Ezviz cloud camera processor
Write-Host "Starting Ezviz cloud camera processor (8082)..." -ForegroundColor Cyan
$port = 8082
$cmd = "$py ezviz_yolo_processor.py --port $port --model 'yolov8x-seg.pt'"
Write-Host "Executing: $cmd" -ForegroundColor Yellow

# Launch browser
Start-Process "http://localhost:$port" -WindowStyle Normal

# Start the processor
Invoke-Expression $cmd 
"""
YOLOv11分割跟踪识别系统 - 用于实时视频处理
使用YOLOv11进行目标检测、分割和跟踪
"""

import cv2
import numpy as np
import os
import sys
import base64
import traceback
import json
import time
from io import BytesIO
from PIL import Image
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

import torch # 添加torch导入

# 全局缓存模型实例
_model_cache = {}

def get_cached_model(model_path, device=None):
    """获取或创建缓存的模型实例"""
    global _model_cache
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    cache_key = (model_path, device) # 使用模型路径和设备作为缓存键
    if cache_key not in _model_cache:
        try:
            from ultralytics import YOLO
            logger.info(f"加载YOLO模型: {model_path} 到设备: {device}")
            model = YOLO(model_path)
            model.to(device) # 将模型移动到指定设备
            _model_cache[cache_key] = model
            logger.info(f"模型加载成功: {model_path} 到设备: {device}")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            traceback.print_exc()
            return None
    return _model_cache[cache_key]

class YOLOVideoProcessor:
    def __init__(self, model_path, tracker_config="bytetrack.yaml", max_track_length=40, mask_alpha=0.6, device=None):
        self.model_path = model_path
        self.tracker_config = tracker_config
        self.max_track_length = max_track_length
        self.mask_alpha = mask_alpha
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu') # 设置设备

        self.track_history = {}  # 用于记录每个目标的轨迹
        self.track_colors = {}
        self.model = get_cached_model(self.model_path, device=self.device) # 传递device参数
        if self.model is None:
            logger.error(f"无法加载模型: {self.model_path} 到设备: {self.device}")
            raise RuntimeError(f"无法加载模型: {self.model_path} 到设备: {self.device}")
        
        # 加载映射表将YOLO类别映射到风险类别和事件
        self.risk_mapping = {
            # 路面缺陷
            'crack': {'category': '道路设施', 'event': '路面裂缝', 'risk_level': 'high'},
            'pothole': {'category': '道路设施', 'event': '路面坑洼', 'risk_level': 'high'},
            'repair': {'category': '道路设施', 'event': '路面修补', 'risk_level': 'medium'},
            'net': {'category': '道路设施', 'event': '网裂', 'risk_level': 'high'},
            'patch': {'category': '道路设施', 'event': '补丁', 'risk_level': 'medium'},
            'stripe': {'category': '道路设施', 'event': '道路标线', 'risk_level': 'low'},
            'water': {'category': '道路设施', 'event': '路面积水', 'risk_level': 'medium'},
            # 交通标志和信号
            'traffic_light': {'category': '道路设施', 'event': '交通信号灯', 'risk_level': 'low'},
            'stop_sign': {'category': '道路设施', 'event': '停止标志', 'risk_level': 'low'},
            'traffic_sign': {'category': '道路设施', 'event': '交通标志', 'risk_level': 'low'},
            'crosswalk': {'category': '道路设施', 'event': '人行横道', 'risk_level': 'medium'},
            # 行人和非机动车
            'person': {'category': '人员', 'event': '行人', 'risk_level': 'medium'},
            'people': {'category': '人员', 'event': '行人群体', 'risk_level': 'medium'},
            'pedestrian': {'category': '人员', 'event': '行人', 'risk_level': 'medium'},
            'bicycle': {'category': '车辆', 'event': '自行车', 'risk_level': 'medium'},
            'motorcycle': {'category': '车辆', 'event': '摩托车', 'risk_level': 'medium'},
            'scooter': {'category': '车辆', 'event': '电动车', 'risk_level': 'medium'},
            # 机动车
            'car': {'category': '车辆', 'event': '小汽车', 'risk_level': 'medium'},
            'truck': {'category': '车辆', 'event': '卡车', 'risk_level': 'high'},
            'bus': {'category': '车辆', 'event': '公交车', 'risk_level': 'high'},
            'van': {'category': '车辆', 'event': '面包车', 'risk_level': 'medium'},
            'taxi': {'category': '车辆', 'event': '出租车', 'risk_level': 'medium'},
            # 道路安全设施
            'barrier': {'category': '道路设施', 'event': '路障', 'risk_level': 'medium'},
            'guardrail': {'category': '道路设施', 'event': '护栏', 'risk_level': 'low'},
            'pole': {'category': '道路设施', 'event': '电线杆', 'risk_level': 'low'},
            'construction': {'category': '道路设施', 'event': '施工区域', 'risk_level': 'high'},
            # 其他道路元素
            'puddle': {'category': '道路设施', 'event': '积水', 'risk_level': 'medium'},
            'debris': {'category': '道路设施', 'event': '道路碎片', 'risk_level': 'medium'},
            'roadblock': {'category': '道路设施', 'event': '道路封锁', 'risk_level': 'high'},
        }
    
    def process_frame(self, frame, device=None):
        """处理单个视频帧并返回处理后的帧和检测结果"""
        try:
            # 深拷贝输入帧以避免直接修改
            frame = frame.copy()
            
            # 如果提供了设备参数，尝试在该设备上运行
            if device is not None:
                logger.info(f"尝试在设备 {device} 上运行YOLO")
                try:
                    # 对于较新的YOLO版本，有可能支持动态切换设备
                    # 但目前大多数版本不支持，所以这里只是一个备注
                    # 如果发现设备问题，建议重新实例化模型
                    pass
                except Exception as e:
                    logger.warning(f"无法切换到设备 {device}，使用默认设备: {str(e)}")
            
            # 调用模型进行跟踪检测
            try:
                results = self.model.track(frame, persist=True, verbose=False, tracker=self.tracker_config)
            except Exception as e:
                logger.error(f"模型跟踪失败: {str(e)}")
                return frame, [], [], []
            
            if not results or len(results) == 0:
                logger.warning("模型没有返回任何结果")
                return frame, [], [], []

            # 提取检测到的对象
            objects = []
            high_risk_events = []
            low_risk_events = []
            box_colors = {}  # 存储每个对象ID的颜色
            
            # 检查结果的boxes属性是否存在
            if not hasattr(results[0], 'boxes') or results[0].boxes is None or len(results[0].boxes) == 0:
                logger.warning("结果中没有boxes属性或boxes为空")
                return frame, [], [], []
                
            try:
                # 获取检测结果 - 添加异常处理和空值检查
                boxes = results[0].boxes.xyxy.cpu().numpy() if hasattr(results[0].boxes, 'xyxy') else np.array([])
                if len(boxes) == 0:
                    logger.warning("没有检测到边界框")
                    return frame, [], [], []
                    
                # 获取跟踪ID - 添加完整的空值检查
                track_ids = None
                if hasattr(results[0].boxes, 'id') and results[0].boxes.id is not None:
                    try:
                        track_ids = results[0].boxes.id.cpu().numpy()
                    except:
                        track_ids = None
                
                if track_ids is None or len(track_ids) == 0:
                    track_ids = np.arange(len(boxes))
                    
                # 获取类别ID - 添加空值检查
                cls_ids = None
                if hasattr(results[0].boxes, 'cls') and results[0].boxes.cls is not None:
                    try:
                        cls_ids = results[0].boxes.cls.cpu().numpy()
                    except:
                        cls_ids = None
                        
                if cls_ids is None or len(cls_ids) == 0:
                    cls_ids = np.zeros(len(boxes))
                    
                # 获取置信度 - 添加空值检查
                confs = None
                if hasattr(results[0].boxes, 'conf') and results[0].boxes.conf is not None:
                    try:
                        confs = results[0].boxes.conf.cpu().numpy()
                    except:
                        confs = None
                        
                if confs is None or len(confs) == 0:
                    confs = np.ones(len(boxes)) * 0.5  # 默认置信度0.5
            except Exception as e:
                logger.error(f"提取检测结果时出错: {str(e)}")
                return frame, [], [], []
            
            # 获取分割掩码（如果有的话）
            masks = None
            if hasattr(results[0], 'masks') and results[0].masks is not None:
                try:
                    masks = results[0].masks.data.cpu().numpy()
                except Exception as e:
                    logger.warning(f"获取掩码失败: {str(e)}")
                    masks = None
            
            # 遍历检测到的对象
            for i, (box, cls_id, conf) in enumerate(zip(boxes, cls_ids, confs)):
                try:
                    # 获取跟踪ID（如果有）
                    track_id = int(track_ids[i]) if track_ids is not None and i < len(track_ids) else i
                    
                    # 获取分类标签
                    cls_name = self.model.names[int(cls_id)] if int(cls_id) in self.model.names else "未知"
                    
                    # 确定对象类别
                    category = self.get_object_category(cls_name)
                    
                    # 确定风险等级
                    risk_level = self.determine_risk_level(cls_name, box, frame, category)
                    
                    # 获取对象颜色（根据跟踪ID或类别）
                    if track_id not in box_colors:
                        color = self.get_color_for_object(track_id, cls_name, risk_level)
                        box_colors[track_id] = color
                    else:
                        color = box_colors[track_id]
                    
                    # 构建对象信息
                    obj_info = {
                        'id': int(track_id) if track_ids is not None else i,
                        'category': category,
                        'label': cls_name,
                        'confidence': float(conf),
                        'bbox_2d': [int(coord) for coord in box],
                        'risk_level': risk_level
                    }
                    
                    # 根据风险等级分类
                    if risk_level == 'high':
                        high_risk_events.append(obj_info)
                    else:
                        low_risk_events.append(obj_info)
                    
                    objects.append(obj_info)
                    
                    # 绘制检测框
                    x1, y1, x2, y2 = map(int, box)
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    
                    # 绘制标签
                    label = f"{cls_name} {conf:.2f}"
                    t_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                    cv2.rectangle(frame, (x1, y1 - t_size[1] - 3), (x1 + t_size[0], y1), color, -1)
                    cv2.putText(frame, label, (x1, y1 - 2), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                    
                    # 绘制分割掩码（如果有的话）
                    if masks is not None and i < len(masks):
                        try:
                            mask = masks[i]
                            # 调整掩码尺寸以匹配图像
                            mask = cv2.resize(mask, (frame.shape[1], frame.shape[0]))
                            # 创建彩色掩码
                            color_mask = np.zeros_like(frame)
                            mask_color = tuple(int(c * 0.7) for c in color[::-1])  # 将RGB颜色转换为BGR并调整亮度
                            color_mask[mask > 0.5] = mask_color
                            # 将掩码与图像混合
                            frame = cv2.addWeighted(frame, 1.0, color_mask, self.mask_alpha, 0)
                        except Exception as e:
                            logger.warning(f"无法绘制分割掩码: {str(e)}")
                except Exception as e:
                    logger.warning(f"处理对象 {i} 时出错: {str(e)}")
                    continue
            
            return frame, objects, high_risk_events, low_risk_events
            
        except Exception as e:
            logger.error(f"处理帧时发生错误: {str(e)}")
            traceback.print_exc()
            return frame, [], [], []

    def call_qwen_api(self, img, detected_objects):
        """调用Qwen API分析检测到的对象和场景"""
        try:
            # 检查API密钥
            api_key = os.getenv('DASHSCOPE_API_KEY')
            if not api_key:
                logger.error("DASHSCOPE_API_KEY 环境变量未设置")
                return None

            # 将图像转为PIL Image对象
            if isinstance(img, np.ndarray):
                pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            elif isinstance(img, Image.Image):
                pil_img = img
            else:
                logger.error(f"不支持的图像格式: {type(img)}")
                return None
            
            # 准备提示词，包含检测到的对象信息
            object_summary = "\n".join([
                f"- {obj['category']} ({obj['label']}): 置信度 {obj['confidence']:.2f}, 风险等级: {obj['risk_level']}"
                for obj in detected_objects
            ])
            
            prompt = f"""请分析图像中的道路交通安全风险，并以JSON格式输出检测结果。
基于以下检测到的对象:
{object_summary}

要求：
1. 仔细观察图像中的车辆、行人和道路状况，关注交通场景中的安全隐患。
2. 识别道路交通中的安全风险事件，如车辆违规行为、行人闯红灯、道路破损等。
3. 对每个检测对象，提供类别（仅限：人员、车辆、道路设施）、具体事件描述、风险等级和置信度。
4. 提供图像的整体交通安全风险分析和管控建议，使用Markdown格式进行分段。
5. 不需要返回边界框坐标信息。
6. 分析道路状况、交通流量和可能的事故风险点。

返回格式示例如下：
{{
  "detections": [
    {{
      "category": "车辆",
      "event": "speeding",
      "risk_level": "high",
      "confidence": 0.95,
      "label": "超速行驶"
    }},
    {{
      "category": "道路设施",
      "event": "pothole",
      "risk_level": "medium",
      "confidence": 0.87,
      "label": "路面坑洼"
    }}
  ],
  "high_risk_events": [
    {{
      "category": "车辆",
      "event": "speeding",
      "risk_level": "high",
      "confidence": 0.95
    }}
  ],
  "low_risk_events": [
    {{
      "category": "道路设施",
      "event": "pothole",
      "risk_level": "medium",
      "confidence": 0.87
    }}
  ],
  "description": "#### 风险分析\n该道路场景存在一些安全隐患：有一辆车辆存在超速行驶行为，存在高风险；道路上有坑洼，可能导致车辆损坏或驾驶员失控，存在中等风险。\n\n#### 管控建议\n建议加强对该路段的速度管控，设置限速标志和测速设备；尽快修复路面坑洼，设置警示标志提醒驾驶员减速通过；增加交通巡逻，及时处理违规行为。"
}}

注意：
- 类别(category)只能是：人员、车辆、道路设施 三种之一
- 事件(event)是具体的事件类型，如speeding, jaywalking, pothole等
- 风险等级(risk_level)可以是high、medium、low
- 高风险事件放入high_risk_events，中低风险事件放入low_risk_events
- description字段必须使用Markdown格式，包含"#### 风险分析"和"#### 管控建议"两个部分
"""

            # 导入必要的库
            try:
                from detection_api import inference_with_api
                from qwen_vl_utils import smart_resize
                import json
                import re
                
                # 设置参数
                min_pixels = 512*28*28
                max_pixels = 2048*28*28
                model_id = "qwen-vl-max-1119"
                
                # 调用API
                logger.info("调用Qwen API进行场景分析")
                response = inference_with_api(
                    pil_img, 
                    prompt, 
                    sys_prompt="你是一个专业的道路交通安全分析专家，擅长识别交通场景中的各类风险因素。请关注道路状况、车辆行为和行人活动，分析可能存在的安全隐患。你的分析应包括车辆行驶状态、行人行为、道路设施完好程度等方面，并评估可能的事故风险。请严格按照指定格式返回JSON结果，不要添加任何解释、前缀或格式标记。",
                    model_id=model_id,
                    min_pixels=min_pixels, 
                    max_pixels=max_pixels
                )
                
                logger.info("Qwen API返回成功")

                
                # 对响应进行预处理，确保它是有效的JSON
                try:
                    # 如果响应是字符串，尝试提取JSON部分
                    if isinstance(response, str):
                        # 打印原始响应的前500个字符以进行调试
                        logger.info(f"Qwen API原始响应(前500字符): {response[:500]}...")
                        
                        # 检查是否有Markdown代码块
                        json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response)
                        if json_match:
                            # 从Markdown代码块提取JSON
                            json_text = json_match.group(1).strip()
                            logger.info("从Markdown代码块中提取到JSON")
                        else:
                            # 尝试从普通文本中提取JSON对象
                            start_pos = response.find('{')
                            end_pos = response.rfind('}') + 1
                            
                            if start_pos != -1 and end_pos != -1 and end_pos > start_pos:
                                json_text = response[start_pos:end_pos]
                                logger.info(f"从文本中提取JSON，开始于位置{start_pos}，结束于位置{end_pos}")
                            else:
                                # 没有找到JSON，使用整个响应
                                json_text = response
                                logger.warning("未能从响应中识别出JSON结构，将使用完整响应")
                                
                        # 处理Python风格的布尔值、None值和引号
                        json_text = json_text.replace("'", '"').replace('True', 'true').replace('False', 'false').replace('None', 'null')
                        
                        # 移除可能导致解析失败的控制字符
                        json_text = re.sub(r'[\x00-\x1F\x7F]', '', json_text)
                        
                        # 移除多余的空格和换行，但保留JSON结构需要的空格
                        json_text = re.sub(r'\s+(?=([^"]*"[^"]*")*[^"]*$)', ' ', json_text)
                        
                        # 记录准备解析的JSON文本
                        logger.info(f"准备解析的JSON文本(前100字符): {json_text[:100]}...")
                        
                        # 尝试解析JSON
                        try:
                            result = json.loads(json_text)
                            logger.info("成功解析结构化JSON响应")
                            
                            # 确保结果包含必要的字段
                            if not isinstance(result, dict):
                                logger.warning(f"解析结果不是字典，而是{type(result)}，将转换为字典")
                                result = {"description": str(result)}
                                
                            # 添加默认字段
                            if "detections" not in result:
                                result["detections"] = detected_objects
                            if "description" not in result:
                                result["description"] = "场景分析完成"
                                
                            # 处理风险事件，确保它们包含所有必要字段
                            if "high_risk_events" not in result:
                                # 创建高风险事件列表
                                result["high_risk_events"] = []
                                for obj in detected_objects:
                                    if obj.get("risk_level") == "high":
                                        result["high_risk_events"].append({
                                            "category": obj.get("category", "未知"),
                                            "event": obj.get("label", "未知事件"),
                                            "risk_level": "high",
                                            "description": f"{obj.get('category', '未知')}存在高风险隐患",
                                            "confidence": obj.get("confidence", 0.8)
                                        })
                            else:
                                # 确保现有高风险事件有描述字段
                                for event in result["high_risk_events"]:
                                    if "description" not in event:
                                        event["description"] = f"{event.get('category', '未知')}存在高风险隐患: {event.get('event', '未知事件')}"
                                        
                            # 同样处理低风险事件
                            if "low_risk_events" not in result:
                                # 创建低/中风险事件列表
                                result["low_risk_events"] = []
                                for obj in detected_objects:
                                    if obj.get("risk_level") in ["low", "medium"]:
                                        result["low_risk_events"].append({
                                            "category": obj.get("category", "未知"),
                                            "event": obj.get("label", "未知事件"),
                                            "risk_level": obj.get("risk_level", "low"),
                                            "description": f"{obj.get('category', '未知')}存在{obj.get('risk_level', 'low')}风险隐患",
                                            "confidence": obj.get("confidence", 0.8)
                                        })
                            else:
                                # 确保现有低/中风险事件有描述字段
                                for event in result["low_risk_events"]:
                                    if "description" not in event:
                                        event["description"] = f"{event.get('category', '未知')}存在{event.get('risk_level', 'low')}风险隐患: {event.get('event', '未知事件')}"
                                
                            # 将解析后的JSON转回字符串，确保是有效的JSON格式
                            result_json = json.dumps(result, ensure_ascii=False)
                            # 增加一个额外的日志来记录解析成功的结构化结果
                            logger.info(f"Qwen API分析结果: {result_json[:300]}...")
                            return result_json
                            
                        except json.JSONDecodeError as json_err:
                            logger.warning(f"无法解析Qwen API响应为有效JSON: {json_err}，位置: {json_err.pos}，将返回原始响应")
                            # 记录更多错误信息以帮助调试
                            if json_err.pos > 0 and json_err.pos < len(json_text):
                                error_context = json_text[max(0, json_err.pos-20):min(len(json_text), json_err.pos+20)]
                                logger.warning(f"错误上下文(位置{json_err.pos}): ...{error_context}...")
                            
                            # 最后尝试：使用正则表达式查找所有可能的JSON对象并选择最长的
                            try:
                                all_json_objects = re.findall(r'\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\}', response)
                                if all_json_objects:
                                    # 选择最长的JSON对象
                                    longest_json = max(all_json_objects, key=len)
                                    logger.info(f"找到了{len(all_json_objects)}个可能的JSON对象，选择最长的进行解析")
                                    try:
                                        # 再次处理Python风格字符
                                        longest_json = longest_json.replace("'", '"').replace('True', 'true').replace('False', 'false').replace('None', 'null')
                                        result = json.loads(longest_json)
                                        logger.info("使用正则表达式提取的最长JSON对象解析成功")
                                    except:
                                        # 如果仍然失败，返回默认结构
                                        logger.warning("所有JSON解析尝试都失败，将使用默认结构")
                                        result = None
                                else:
                                    logger.warning("无法从响应中找到任何可能的JSON对象")
                                    result = None
                            except Exception as regex_err:
                                logger.warning(f"正则表达式搜索JSON对象时出错: {str(regex_err)}")
                                result = None
                            
                            # 如果所有尝试都失败，创建一个默认结构
                            if not result:
                                # 创建一个默认结构
                                default_result = {
                                    "detections": detected_objects,
                                    "description": response[:1000] if len(response) > 1000 else response,  # 限制描述长度
                                    "high_risk_events": [],
                                    "low_risk_events": []
                                }
                                
                                # 为高风险和低风险事件添加详细信息
                                for obj in detected_objects:
                                    if obj.get("risk_level") == "high":
                                        default_result["high_risk_events"].append({
                                            "category": obj.get("category", "未知"),
                                            "event": obj.get("label", "未知事件"),
                                            "risk_level": "high",
                                            "description": f"{obj.get('category', '未知')}存在高风险隐患",
                                            "confidence": obj.get("confidence", 0.8)
                                        })
                                    elif obj.get("risk_level") in ["low", "medium"]:
                                        default_result["low_risk_events"].append({
                                            "category": obj.get("category", "未知"),
                                            "event": obj.get("label", "未知事件"),
                                            "risk_level": obj.get("risk_level", "low"),
                                            "description": f"{obj.get('category', '未知')}存在{obj.get('risk_level', 'low')}风险隐患",
                                            "confidence": obj.get("confidence", 0.8)
                                        })
                                
                                result = default_result
                                
                            # 将解析后的结果转为JSON字符串
                            result_json = json.dumps(result, ensure_ascii=False)
                            # 增加一个额外的日志来记录解析成功的结构化结果
                            logger.info(f"Qwen API分析结果: {result_json[:300]}...")
                            return result_json
                    else:
                        logger.warning(f"Qwen API返回非字符串结果: {type(response)}")
                        return json.dumps({"description": str(response)}, ensure_ascii=False)
                
                except Exception as e:
                    logger.error(f"处理Qwen API响应时出错: {str(e)}")
                    # 返回原始响应
                    return response
                
            except ImportError as e:
                logger.error(f"导入API模块失败: {str(e)}")
                return None
            except Exception as e:
                logger.error(f"调用Qwen API失败: {str(e)}")
                return None
                
        except Exception as e:
            logger.error(f"调用Qwen API时发生错误: {str(e)}")
            return None

    def process_image_base64(self, image_base64, timestamp=None, frame_id=None, device=None):
        """处理Base64编码的图像并返回处理后的Base64图像和检测结果"""
        try:
            start_time = time.time()
            
            # 解码Base64图像
            image_data = base64.b64decode(image_base64.split(',')[1] if ',' in image_base64 else image_base64)
            nparr = np.frombuffer(image_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            # 处理图像
            processed_img, objects, high_risk_events, low_risk_events = self.process_frame(img, device)
            
            # 只在有检测结果且每60帧调用一次Qwen API（限制API调用频率）
            should_call_api = False
            if objects:
                try:
                    # 尝试将frame_id转换为整数，如果无法转换则使用默认值
                    if frame_id is None:
                        should_call_api = True
                    else:
                        frame_num = int(frame_id) if isinstance(frame_id, (int, str)) and str(frame_id).isdigit() else 0
                        should_call_api = frame_num % 60 == 0
                except (ValueError, TypeError):
                    # 如果转换失败，默认为True以确保API调用
                    should_call_api = True
                    
            qwen_api_response = None

            # 转换处理后的图像为base64
            _, buffer = cv2.imencode('.jpg', processed_img)
            processed_base64 = base64.b64encode(buffer).decode('utf-8')
            
            # 构建返回数据
            result = {
                'processed_frame': processed_base64,
                'objects': objects,
                'high_risk_events': high_risk_events,
                'low_risk_events': low_risk_events,
                'frame_id': frame_id,
                'timestamp': timestamp,
                "should_call_api": should_call_api,
            }
            
            # 添加检测对象统计，保持兼容性
            summary = {}
            for obj in objects:
                category = obj.get('category', '未知')
                if category in summary:
                    summary[category] += 1
                else:
                    summary[category] = 1
            result['summary'] = summary
            
            # 添加标记字段，用于前端识别是否为千问API的结果
            # 这样只有通过千问API调用产生的数据才会在前端显示分析结果
            if qwen_api_response:
                try:
                    # 尝试解析qwen_api_response字符串
                    if isinstance(qwen_api_response, str):
                        result['qwen_analysis'] = json.loads(qwen_api_response)
                    else:
                        result['qwen_analysis'] = qwen_api_response
                    logger.info("成功添加qwen_analysis字段")
                except Exception as e:
                    # 如果解析失败，仍添加字段但包含原始响应
                    logger.warning(f"解析qwen_api_response失败: {str(e)}")
                    result['qwen_analysis'] = {"description": str(qwen_api_response)}
            
            # 记录处理时间
            end_time = time.time()
            processing_time = end_time - start_time
            logger.info(f"图像处理完成，耗时: {processing_time:.2f}秒")
            
            # 记录返回结果的结构（仅记录前500个字符以避免日志过大）
            try:
                result_repr = str(result)[:500]
                #logger.info(f"返回结果结构: {result_repr}...")
            except:
                logger.info("无法记录返回结果结构")
            
            return result
        except Exception as e:
            logger.error(f"处理图像时发生错误: {str(e)}")
            traceback.print_exc()
            return None

    def get_object_category(self, class_name):
        """根据类名确定对象类别"""
        class_name_lower = class_name.lower()
        
        # 首先检查是否在risk_mapping中
        if class_name_lower in self.risk_mapping:
            return self.risk_mapping[class_name_lower]['category']
            
        # 行人和人员类别
        if any(keyword in class_name_lower for keyword in ['person', 'people', 'pedestrian', '人']):
            return '人员'
            
        # 车辆类别
        elif any(keyword in class_name_lower for keyword in ['car', 'truck', 'bus', 'vehicle', 'van', 'taxi', 'bicycle', 'motorcycle', 'scooter', '车']):
            return '车辆'
            
        # 道路设施类别
        elif any(keyword in class_name_lower for keyword in [
            'road', 'street', 'lane', 'traffic', 'sign', 'light', 'signal', 'crosswalk', 'zebra', 
            'pothole', 'crack', 'repair', 'stripe', 'barrier', 'guardrail', 'pole', 'construction',
            '路', '标志', '信号', '设施'
        ]):
            return '道路设施'
            
        # 默认为其他类别
        else:
            return '其他'

    def determine_risk_level(self, class_name, box, frame, category):
        """确定对象的风险等级"""
        class_name_lower = class_name.lower()
        
        # 首先检查是否在预定义的映射中
        if class_name_lower in self.risk_mapping:
            return self.risk_mapping[class_name_lower]['risk_level']
        
        # 道路交通高风险关键字
        high_risk_keywords = [
            'crash', 'accident', 'collision', 'danger', 'unsafe', 'violation', 
            'speed', 'speeding', 'jaywalking', 'pothole', 'crack', 'roadblock'
        ]
        
        # 根据类别确定风险等级
        height, width = frame.shape[:2]
        x1, y1, x2, y2 = box
        obj_width = x2 - x1
        obj_height = y2 - y1
        obj_area = obj_width * obj_height
        frame_area = height * width
        
        # 物体在画面中的占比
        area_ratio = obj_area / frame_area
        
        if category == '人员':
            # 行人靠近道路中间可能是高风险
            if (x1 > width * 0.3 and x2 < width * 0.7) and area_ratio > 0.01:
                return 'high'
            # 行人在画面边缘可能是中等风险
            elif area_ratio > 0.005:
                return 'medium'
            else:
                return 'low'
                
        elif category == '车辆':
            # 大型车辆通常有更高的风险
            if obj_width > width * 0.3 or obj_height > height * 0.3:
                return 'high'
            # 中等大小的车辆
            elif obj_width > width * 0.15 or obj_height > height * 0.15:
                return 'medium'
            else:
                return 'low'
                
        elif category == '道路设施':
            # 道路缺陷通常是高风险
            if any(keyword in class_name_lower for keyword in ['pothole', 'crack', 'damage', 'broken']):
                return 'high'
            # 安全设施通常是低风险
            elif any(keyword in class_name_lower for keyword in ['sign', 'light', 'signal', 'guardrail']):
                return 'low'
            else:
                return 'medium'
                
        elif any(keyword in class_name_lower for keyword in high_risk_keywords):
            # 包含高风险关键字的类别
            return 'high'
            
        else:
            # 其他对象为低风险
            return 'low'
    
    def get_color_for_object(self, track_id, class_name, risk_level):
        """根据跟踪ID、类别和风险等级获取颜色"""
        # 根据风险等级选择基本颜色
        if risk_level == 'high':
            base_color = (0, 0, 255)  # 红色 (BGR)
        elif risk_level == 'medium':
            base_color = (0, 165, 255)  # 橙色 (BGR)
        else:
            base_color = (0, 255, 0)  # 绿色 (BGR)
        
        # 使用跟踪ID为颜色添加一些变化，但保持基本的风险颜色
        color_variation = 50  # 颜色变化范围
        
        # 使用track_id作为随机种子
        np.random.seed(track_id)
        
        # 对基本颜色的各个通道进行微调，但保持在可识别的范围内
        varied_color = []
        for c in base_color:
            # 添加一些随机变化，但确保颜色在0-255范围内
            varied = max(0, min(255, c + np.random.randint(-color_variation, color_variation)))
            varied_color.append(varied)
        
        return tuple(varied_color)

def create_processor(model_path="../src/models/yolo11x-seg.pt", device=None):
    """创建YOLO视频处理器的工厂函数"""
    try:
        return YOLOVideoProcessor(model_path=model_path, device=device)
    except Exception as e:
        logger.error(f"创建处理器失败: {str(e)}")
        traceback.print_exc()
        return None

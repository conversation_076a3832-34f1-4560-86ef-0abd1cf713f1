# YOLO专用视频分析系统

## 概述

这是一个专门使用YOLO进行视频处理的系统，实现了：
- 实时视频检测和分析
- 按时间戳显示检测结果
- 视频播放完成后生成完整的交通分析报告

## 系统架构

### 后端组件
- `yolo_only_backend.py` - YOLO专用FastAPI后端服务器
- `yolo_video_processor.py` - YOLO视频处理器
- `start_yolo_backend.py` - 启动脚本

### 前端组件
- `templates/local_video_detect.html` - 修改后的前端页面，连接到YOLO专用后端

## 功能特点

### 1. 实时检测显示
- 视频播放时实时显示检测结果
- 按时间戳组织检测事件
- 区分高风险和低/中风险事件
- 实时更新检测框和标签

### 2. 完整报告生成
视频播放完成后自动生成包含以下内容的详细报告：
- 整体路网与环境描述
- 分时间戳交通事件分析（按15秒分段）
- 关键事件标注
- 总结与建议

### 3. 性能优化
- 使用WebSocket实现低延迟通信
- 支持自适应帧率控制
- 缓存YOLO模型以提高性能

## 安装和使用

### 1. 环境要求
```bash
pip install fastapi uvicorn websockets
pip install ultralytics opencv-python pillow numpy
pip install torch torchvision  # 根据你的CUDA版本选择
```

### 2. 准备YOLO模型
确保以下路径之一存在YOLO模型文件：
- `../src/models/yolo11n-seg.pt`
- `../src/models/best.pt`
- `models/yolo11n-seg.pt`
- `models/best.pt`

### 3. 启动服务器
```bash
# 方法1：使用启动脚本
python start_yolo_backend.py

# 方法2：直接启动
python -m uvicorn yolo_only_backend:app --host 0.0.0.0 --port 8001
```

### 4. 访问前端
1. 启动YOLO专用后端（端口8001）
2. 启动原有的前端服务器（端口8000）
3. 访问 `http://localhost:8000/local_video_detect`
4. 上传视频文件并点击"开始处理"

## 系统流程

### 1. 连接建立
- 前端通过WebSocket连接到 `ws://localhost:8001/ws/yolo-only-process`
- 后端初始化YOLO模型并返回状态信息

### 2. 视频处理
- 前端播放视频并按帧率捕获画面
- 将帧数据（Base64格式）发送到后端
- 后端使用YOLO处理每一帧并返回检测结果

### 3. 实时显示
- 前端接收检测结果并实时更新右侧面板
- 显示检测框、标签和置信度
- 按时间戳组织显示高风险和低风险事件

### 4. 报告生成
- 视频播放完成后，前端发送完成信号
- 后端基于所有检测结果生成完整的交通分析报告
- 前端显示最终报告

## 报告内容示例

生成的报告包含：

### 一、整体路网与环境描述
- 道路结构分析
- 周边环境描述

### 二、分时间戳交通事件分析
- 按15秒时间段分析
- 每个时间段的车辆和行人活动
- 风险事件统计

### 三、关键事件标注
- 重要车辆行为分析
- 异常事件记录

### 四、总结与建议
- 交通流量特征
- 安全隐患提示
- 改进建议

## 配置选项

### YOLO模型配置
- 支持YOLOv11分割模型
- 可配置置信度阈值
- 支持GPU加速

### 处理参数
- 帧率控制（2-30 FPS）
- 图像质量调整
- 自适应帧率模式

## 故障排除

### 1. 模型加载失败
- 检查模型文件路径
- 确认模型文件完整性
- 检查CUDA/PyTorch安装

### 2. WebSocket连接失败
- 确认后端服务器正在运行
- 检查端口8001是否被占用
- 查看浏览器控制台错误信息

### 3. 检测结果异常
- 检查视频格式是否支持
- 确认视频质量和分辨率
- 调整YOLO模型置信度阈值

## 技术细节

### WebSocket消息格式
```json
// 发送帧数据
{
    "frame": "data:image/jpeg;base64,/9j/4AAQ...",
    "frameId": 123,
    "timestamp": 45.67
}

// 接收检测结果
{
    "type": "detection_result",
    "timestamp": 45.67,
    "objects": [...],
    "high_risk_events": [...],
    "low_risk_events": [...]
}

// 最终报告
{
    "type": "final_report",
    "report": "# 高速公路交通分析报告\n...",
    "statistics": {
        "total_detections": 150,
        "duration": 120.5
    }
}
```

### 风险等级分类
- **高风险**: 卡车、施工区域、道路缺陷等
- **中风险**: 行人、自行车、路面积水等  
- **低风险**: 交通标志、护栏、标线等

## 扩展功能

系统支持以下扩展：
- 添加新的检测类别
- 自定义风险等级规则
- 集成其他AI模型
- 导出报告为PDF格式
- 批量处理多个视频文件

## 许可证

本项目遵循原项目的许可证条款。

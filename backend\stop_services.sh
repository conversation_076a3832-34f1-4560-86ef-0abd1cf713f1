#!/bin/bash

# Color codes for terminal output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=====================================${NC}"
echo -e "${RED}Stopping Traffic-Eyes Services${NC}"
echo -e "${BLUE}=====================================${NC}"

# Function to stop a process
stop_process() {
    if [ -f ".$1_pid" ]; then
        PID=$(cat ".$1_pid")
        if ps -p $PID > /dev/null; then
            echo -e "${YELLOW}Stopping $2 (PID: $PID)...${NC}"
            kill $PID
            sleep 1
            # Check if it's still running and force kill if needed
            if ps -p $PID > /dev/null; then
                echo -e "${RED}Force killing $2 (PID: $PID)...${NC}"
                kill -9 $PID
            fi
            echo -e "${GREEN}$2 stopped.${NC}"
        else
            echo -e "${YELLOW}$2 is not running.${NC}"
        fi
        rm -f ".$1_pid"
    else
        echo -e "${YELLOW}No PID file found for $2.${NC}"
    fi
}

# Stop services based on platform
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows - use taskkill to find and kill python processes
    echo -e "${YELLOW}Stopping YOLO WebSocket Server...${NC}"
    taskkill //F //IM python.exe //FI "WINDOWTITLE eq *yolo_server.py*" > /dev/null 2>&1
    
    echo -e "${YELLOW}Stopping Qwen API Server...${NC}"
    taskkill //F //IM python.exe //FI "WINDOWTITLE eq *qwen_api.py*" > /dev/null 2>&1
    
    echo -e "${GREEN}All services stopped.${NC}"
else
    # Linux/Mac - use the PID files
    stop_process "yolo" "YOLO WebSocket Server"
    stop_process "qwen" "Qwen API Server"
fi

# Check for any remaining related processes
echo -e "${YELLOW}Checking for any remaining processes...${NC}"

if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    python_count=$(tasklist | grep -i "python.exe" | wc -l)
    if [ $python_count -gt 0 ]; then
        echo -e "${YELLOW}There are still $python_count Python processes running.${NC}"
        echo -e "${YELLOW}If needed, close them manually from Task Manager.${NC}"
    else
        echo -e "${GREEN}No remaining Python processes found.${NC}"
    fi
else
    # Linux/Mac
    yolo_processes=$(ps aux | grep "[y]olo_server.py" | wc -l)
    qwen_processes=$(ps aux | grep "[q]wen_api.py" | wc -l)
    
    if [ $yolo_processes -gt 0 ] || [ $qwen_processes -gt 0 ]; then
        echo -e "${YELLOW}There are still some services running:${NC}"
        [ $yolo_processes -gt 0 ] && echo -e "${YELLOW}- YOLO WebSocket Server: $yolo_processes instance(s)${NC}"
        [ $qwen_processes -gt 0 ] && echo -e "${YELLOW}- Qwen API Server: $qwen_processes instance(s)${NC}"
        echo -e "${YELLOW}To force kill all related processes, run:${NC}"
        echo -e "${YELLOW}pkill -f 'yolo_server.py|qwen_api.py'${NC}"
    else
        echo -e "${GREEN}No remaining processes found.${NC}"
    fi
fi

echo -e "${BLUE}=====================================${NC}"
echo -e "${GREEN}Service shutdown complete!${NC}"
echo -e "${BLUE}=====================================${NC}" 

            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            h1 { color: #333; }
            .container { max-width: 1200px; margin: 0 auto; }
            .video-container { margin-bottom: 30px; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
            .video-title { font-size: 18px; margin-bottom: 10px; }
            .video-player { width: 100%; max-width: 800px; }
            .processing { color: #ff9900; }
            .completed { color: #00aa00; }
            .error { color: #ff0000; }
            .progress-bar { width: 100%; background-color: #e0e0e0; border-radius: 4px; margin: 10px 0; }
            .progress-bar-fill { height: 20px; background-color: #4CAF50; border-radius: 4px; text-align: center; line-height: 20px; color: white; }
            .button { display: inline-block; padding: 10px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
            .button:hover { background-color: #45a049; }
            
# Traffic Eyes Simple Video Stream Startup Script
# Only starts the real-time video processing system

Write-Host "Starting Traffic Eyes Video Stream System..." -ForegroundColor Green

# Set environment variables to avoid OpenCV errors
$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"

# Check Python environment
$py = "python"
try {
    $ver = & $py --version 2>&1
    Write-Host "Python version: $ver" -ForegroundColor Green
} catch {
    Write-Host "Python not detected. Please install Python 3.8 or later." -ForegroundColor Red
    exit 1
}

# Check for video files
$videoFiles = Get-ChildItem -Path . -Filter *.mp4 | Select-Object -ExpandProperty Name
if ($videoFiles.Count -eq 0) {
    Write-Host "No video files found, using camera as source" -ForegroundColor Yellow
    $videoSource = "0"  # Camera
} else {
    $videoSource = $videoFiles[0]
    Write-Host "Found video file: $videoSource" -ForegroundColor Yellow
}

# Start real-time video processing service
Write-Host "Starting real-time video processing service (8081)..." -ForegroundColor Cyan
$cmd = "$py realtime_video_processor.py --source $videoSource --port 8081"
Write-Host "Executing command: $cmd" -ForegroundColor Yellow

# Launch browser
Start-Process "http://localhost:8081" -WindowStyle Normal

# Start video processing
Invoke-Expression $cmd 
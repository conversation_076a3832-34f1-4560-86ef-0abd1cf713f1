#!/usr/bin/env python3
"""
启动完整的YOLO专用视频分析系统
"""

import os
import sys
import time
import subprocess
import threading
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖项"""
    logger.info("检查系统依赖...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'websockets',
        'ultralytics',
        'opencv-python',
        'pillow',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"✗ {package} - 未安装")
    
    if missing_packages:
        logger.error("缺少以下依赖包:")
        for package in missing_packages:
            logger.error(f"  pip install {package}")
        return False
    
    return True

def check_model_files():
    """检查YOLO模型文件"""
    logger.info("检查YOLO模型文件...")
    
    model_paths = [
        "../src/models/yolo11n-seg.pt",
        "../src/models/best.pt",
        "models/yolo11n-seg.pt",
        "models/best.pt"
    ]
    
    found_models = []
    for path in model_paths:
        if os.path.exists(path):
            size = os.path.getsize(path) / (1024 * 1024)
            logger.info(f"✓ 找到模型: {path} ({size:.1f} MB)")
            found_models.append(path)
        else:
            logger.info(f"✗ 模型不存在: {path}")
    
    if not found_models:
        logger.error("未找到任何YOLO模型文件")
        logger.error("请下载YOLO模型文件并放置在以下位置之一:")
        for path in model_paths:
            logger.error(f"  {path}")
        return False
    
    return True

def start_yolo_backend():
    """启动YOLO后端服务器"""
    logger.info("启动YOLO专用后端服务器...")
    
    try:
        # 启动YOLO后端
        process = subprocess.Popen([
            sys.executable, 'yolo_only_backend.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待服务器启动
        time.sleep(3)
        
        if process.poll() is None:
            logger.info("✓ YOLO后端服务器启动成功")
            logger.info("  地址: http://localhost:8001")
            logger.info("  WebSocket: ws://localhost:8001/ws/yolo-only-process")
            return process
        else:
            stdout, stderr = process.communicate()
            logger.error("✗ YOLO后端启动失败")
            logger.error(f"stdout: {stdout}")
            logger.error(f"stderr: {stderr}")
            return None
            
    except Exception as e:
        logger.error(f"启动YOLO后端时出错: {e}")
        return None

def start_frontend():
    """启动前端服务器"""
    logger.info("启动前端服务器...")
    
    # 查找可能的前端启动脚本
    frontend_scripts = ['main.py', 'app.py', 'server.py']
    
    for script in frontend_scripts:
        if os.path.exists(script):
            try:
                process = subprocess.Popen([
                    sys.executable, script
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                
                time.sleep(2)
                
                if process.poll() is None:
                    logger.info(f"✓ 前端服务器启动成功 ({script})")
                    logger.info("  地址: http://localhost:8000")
                    return process
                else:
                    stdout, stderr = process.communicate()
                    logger.warning(f"尝试启动 {script} 失败")
                    
            except Exception as e:
                logger.warning(f"启动 {script} 时出错: {e}")
    
    logger.warning("未能自动启动前端服务器")
    logger.info("请手动启动前端服务器:")
    logger.info("  python main.py  # 或其他启动脚本")
    return None

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("YOLO专用视频分析系统启动器")
    logger.info("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        logger.error("依赖项检查失败，请安装缺少的包")
        return 1
    
    # 检查模型文件
    if not check_model_files():
        logger.error("模型文件检查失败")
        return 1
    
    # 启动YOLO后端
    yolo_process = start_yolo_backend()
    if not yolo_process:
        logger.error("YOLO后端启动失败")
        return 1
    
    # 启动前端
    frontend_process = start_frontend()
    
    logger.info("=" * 50)
    logger.info("系统启动完成!")
    logger.info("=" * 50)
    logger.info("访问地址:")
    logger.info("  前端页面: http://localhost:8000/local_video_detect")
    logger.info("  YOLO后端: http://localhost:8001")
    logger.info("")
    logger.info("功能说明:")
    logger.info("  1. 环境描述固定在右侧上方")
    logger.info("  2. 交通事件按时间段显示在中间")
    logger.info("  3. 统计信息固定在右侧下方")
    logger.info("  4. 所有文字颜色为白色")
    logger.info("")
    logger.info("按 Ctrl+C 停止所有服务")
    logger.info("=" * 50)
    
    try:
        # 等待用户中断
        while True:
            time.sleep(1)
            
            # 检查进程是否还在运行
            if yolo_process and yolo_process.poll() is not None:
                logger.error("YOLO后端进程意外退出")
                break
                
            if frontend_process and frontend_process.poll() is not None:
                logger.warning("前端进程意外退出")
                
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止服务...")
        
        # 停止进程
        if yolo_process:
            yolo_process.terminate()
            yolo_process.wait()
            logger.info("YOLO后端已停止")
            
        if frontend_process:
            frontend_process.terminate()
            frontend_process.wait()
            logger.info("前端服务器已停止")
        
        logger.info("所有服务已停止")
        return 0

if __name__ == "__main__":
    sys.exit(main())

<template>
  <div class="yolo-demo">
    <h1>萤石云 YOLO 目标检测演示</h1>
    
    <div class="demo-container">
      <EzvizYoloDetector
        v-if="accessToken"
        :deviceSerial="deviceSerial"
        :channelNo="channelNo"
        :accessToken="accessToken"
        :threshold="detectionThreshold"
        @detection-results="handleDetectionResults"
        @model-loaded="handleModelLoaded"
        @player-ready="handlePlayerReady"
      />
      
      <div v-else class="setup-form">
        <h2>请输入萤石云设备信息</h2>
        <div class="form-group">
          <label>设备序列号:</label>
          <input v-model="deviceSerial" placeholder="设备序列号" />
        </div>
        <div class="form-group">
          <label>通道号:</label>
          <input v-model="channelNo" type="number" placeholder="通道号" />
        </div>
        <div class="form-group">
          <label>访问令牌:</label>
          <input v-model="accessTokenInput" placeholder="访问令牌" />
        </div>
        <div class="form-group">
          <label>检测阈值:</label>
          <input v-model="detectionThreshold" type="range" min="0.1" max="0.9" step="0.05" />
          <span>{{ detectionThreshold }}</span>
        </div>
        <button @click="startDemo" :disabled="!canStart">开始演示</button>
      </div>
    </div>
    
    <div class="detection-results" v-if="latestResults">
      <h2>检测结果</h2>
      <div class="results-summary">
        <div v-for="(count, className) in objectCounts" :key="className" class="object-count">
          <span class="object-name">{{ className }}:</span>
          <span class="object-value">{{ count }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import EzvizYoloDetector from '@/components/EzvizYoloDetector.vue';

export default {
  name: 'YoloDemo',
  
  components: {
    EzvizYoloDetector
  },
  
  setup() {
    // 设备信息
    const deviceSerial = ref('');
    const channelNo = ref(1);
    const accessTokenInput = ref('');
    const accessToken = ref('');
    
    // 检测配置
    const detectionThreshold = ref(0.25);
    
    // 状态
    const isModelLoaded = ref(false);
    const isPlayerReady = ref(false);
    const latestResults = ref(null);
    
    // 计算属性
    const canStart = computed(() => 
      deviceSerial.value && 
      channelNo.value && 
      accessTokenInput.value
    );
    
    const objectCounts = computed(() => {
      if (!latestResults.value || !latestResults.value.classes) {
        return {};
      }
      
      const counts = {};
      latestResults.value.classes.forEach(className => {
        counts[className] = (counts[className] || 0) + 1;
      });
      
      return counts;
    });
    
    // 方法
    const startDemo = () => {
      accessToken.value = accessTokenInput.value;
    };
    
    const handleDetectionResults = (results) => {
      latestResults.value = results;
    };
    
    const handleModelLoaded = (loaded) => {
      isModelLoaded.value = loaded;
      console.log('YOLO模型加载状态:', loaded ? '成功' : '失败');
    };
    
    const handlePlayerReady = (ready) => {
      isPlayerReady.value = ready;
      console.log('萤石云播放器状态:', ready ? '就绪' : '未就绪');
    };
    
    return {
      deviceSerial,
      channelNo,
      accessTokenInput,
      accessToken,
      detectionThreshold,
      latestResults,
      objectCounts,
      canStart,
      startDemo,
      handleDetectionResults,
      handleModelLoaded,
      handlePlayerReady
    };
  }
};
</script>

<style scoped>
.yolo-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
}

.demo-container {
  width: 100%;
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.setup-form {
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  width: 100%;
  max-width: 500px;
}

.form-group label {
  width: 100px;
  text-align: right;
  margin-right: 15px;
}

.form-group input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 15px;
}

button:hover {
  background-color: #45a049;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.detection-results {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.results-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.object-count {
  background-color: #f5f5f5;
  padding: 8px 15px;
  border-radius: 20px;
  display: flex;
  align-items: center;
}

.object-name {
  margin-right: 5px;
}

.object-value {
  font-weight: bold;
  color: #4CAF50;
}
</style> 
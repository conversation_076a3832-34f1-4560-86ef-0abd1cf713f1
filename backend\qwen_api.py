import os
import json
import base64
import requests
import time
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure your Qwen API here
QWEN_API_URL = "https://your-qwen-api-endpoint.com/analyze"
QWEN_API_KEY = "YOUR_QWEN_API_KEY"  # Replace with your actual API key or load from env var
USE_MOCK_DATA = True  # Set to False when you have a real Qwen API to use

# Mock data for testing
MOCK_RESPONSE = {
    "detections": [
        {
            "category": "person", 
            "event_description": "Worker without safety helmet",
            "risk_level": "high",
            "confidence_score": 0.92, 
            "bbox_2d": [120, 200, 220, 350]
        },
        {
            "category": "vehicle", 
            "event_description": "Vehicle parked in unsafe area",
            "risk_level": "low",
            "confidence_score": 0.88, 
            "bbox_2d": [450, 300, 650, 450]
        }
    ],
    "description": "Construction site with workers and equipment. Multiple safety concerns identified.",
    "high_risk_events": [
        {"description": "Worker not wearing a safety helmet", "mitigation": "Enforce mandatory helmet policy"}
    ],
    "low_risk_events": [
        {"description": "Vehicle parked in marked but non-ideal area", "mitigation": "Designate proper parking zones"}
    ]
}

@app.route('/api/qwen-analysis', methods=['POST'])
def qwen_analysis():
    # Get the request data
    data = request.json
    if not data or 'image' not in data or 'prompt' not in data:
        return jsonify({'error': 'Missing image or prompt'}), 400
    
    # Get the image and prompt
    image_base64 = data['image']
    prompt = data['prompt']
    
    # Start timing the request
    start_time = time.time()
    
    # Use mock data for testing if enabled
    if USE_MOCK_DATA:
        # Simulate API delay
        time.sleep(0.5)
        
        response_data = {
            'success': True,
            'result': MOCK_RESPONSE,
            'processing_time_ms': round((time.time() - start_time) * 1000, 2)
        }
        return jsonify(response_data)
    
    # Otherwise, call the real Qwen API
    headers = {
        "Authorization": f"Bearer {QWEN_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "image": image_base64,
        "prompt": prompt
    }
    
    try:
        # Make the request to Qwen API
        response = requests.post(QWEN_API_URL, json=payload, headers=headers)
        response.raise_for_status()
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Return the response
        return jsonify({
            'success': True,
            'result': response.json(),
            'processing_time_ms': round(processing_time * 1000, 2)
        })
    except Exception as e:
        print(f"Error calling Qwen API: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'timestamp': time.time()
    })

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'
    
    print(f"Starting Qwen API server on port {port}...")
    app.run(host='0.0.0.0', port=port, debug=debug) 
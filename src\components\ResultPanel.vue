<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, inject } from 'vue'
import * as echarts from 'echarts'
import { detectObjects, type DetectionResult } from '../services/DetectionService'

// 接收检测结果作为props
const props = defineProps({
  detectionResults: {
    type: Object,
    default: null
  },
  imagePreview: {
    type: String,
    default: ''
  }
})

// 获取检测结果
const detectionResults = inject('detectionResults', ref(null))
const updateDetectionResults = inject('updateDetectionResults', () => {})

// 计算总车辆数
const totalVehicles = computed(() => {
  if (!props.detectionResults) return 0
  return props.detectionResults.vehicles.length
})

// 计算各类型车辆数量
const vehicleTypeCount = computed(() => {
  if (!props.detectionResults) return {}
  
  const counts = {}
  props.detectionResults.vehicles.forEach(vehicle => {
    counts[vehicle.type] = (counts[vehicle.type] || 0) + 1
  })
  
  return counts
})

// 计算风险等级分布
const riskDistribution = computed(() => {
  if (!props.detectionResults) return { low: 0, medium: 0, high: 0 }
  
  const distribution = { low: 0, medium: 0, high: 0 }
  props.detectionResults.vehicles.forEach(vehicle => {
    distribution[vehicle.risk] = (distribution[vehicle.risk] || 0) + 1
  })
  
  return distribution
})

// 获取置信度最高的车辆
const highestConfidenceVehicle = computed(() => {
  if (!props.detectionResults || !props.detectionResults.vehicles.length) return null
  
  return props.detectionResults.vehicles.reduce((highest, current) => {
    return current.confidence > highest.confidence ? current : highest
  }, props.detectionResults.vehicles[0])
})

// 图表相关
const chartRef = ref(null)
let pieChart = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  pieChart = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!pieChart || !props.detectionResults) return
  
  const typeData = Object.entries(vehicleTypeCount.value).map(([name, value]) => ({ name, value }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      data: typeData.map(item => item.name),
      textStyle: {
        color: '#666'
      }
    },
    series: [
      {
        name: '车辆类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: typeData
      }
    ]
  }
  
  pieChart.setOption(option)
}

// 监听检测结果变化，更新图表
watch(() => props.detectionResults, () => {
  if (pieChart) {
    updateChart()
  } else {
    setTimeout(initChart, 100)
  }
})

// 当组件挂载时初始化图表
onMounted(() => {
  if (props.detectionResults) {
    initChart()
  }
})

// 当组件卸载时销毁图表
onUnmounted(() => {
  if (pieChart) {
    pieChart.dispose()
    pieChart = null
  }
})

// 风险等级文本映射
const riskLevelText = {
  low: '低风险',
  medium: '中风险',
  high: '高风险'
}

// 风险等级颜色映射
const riskLevelColor = {
  low: 'var(--success-color)',
  medium: 'var(--warning-color)',
  high: 'var(--danger-color)'
}

// 当前选中的车辆
const selectedVehicle = ref(null)

// 选择车辆
const selectVehicle = (vehicle) => {
  selectedVehicle.value = vehicle
}

// Define detection categories and colors
const detectionCategories = {
  person: { color: '#FF5733', label: '人员' },
  vehicle: { color: '#33A1FF', label: '车辆' },
  box: { color: '#33FF57', label: '箱子' },
  misc: { color: '#F3FF33', label: '杂物' }
};

// Loading state
const isLoading = ref(false);
const error = ref('');

// Watch for changes in the image preview
watch(() => props.imagePreview, (newValue) => {
  if (newValue) {
    // When a new image is loaded, reset the detection results
    detectionResults.value = null;
  }
}, { immediate: true });

// Function to perform object detection
const performDetection = async () => {
  if (!props.imagePreview) return;
  
  try {
    isLoading.value = true;
    error.value = '';
    
    // Call the detection service
    const results = await detectObjects(props.imagePreview);
    
    // Update the detection results
    updateDetectionResults(results);
  } catch (err) {
    console.error('Detection error:', err);
    error.value = '检测失败，请重试';
  } finally {
    isLoading.value = false;
  }
};

// Function to draw bounding boxes on the image
const drawBoundingBoxes = () => {
  if (!detectionResults.value || !props.imagePreview) return;
  
  const canvas = document.getElementById('detection-canvas') as HTMLCanvasElement;
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Clear the canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // Get the image element
  const img = document.getElementById('preview-image') as HTMLImageElement;
  if (!img || !img.complete) return;
  
  // Set canvas dimensions to match the image
  canvas.width = img.width;
  canvas.height = img.height;
  
  // Draw bounding boxes
  detectionResults.value.objects.forEach((obj: any) => {
    const [x1, y1, x2, y2] = obj.bbox;
    const width = (x2 - x1) * canvas.width;
    const height = (y2 - y1) * canvas.height;
    const x = x1 * canvas.width;
    const y = y1 * canvas.height;
    
    const category = obj.category;
    const color = detectionCategories[category]?.color || '#FFFFFF';
    
    // Draw rectangle
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.strokeRect(x, y, width, height);
    
    // Draw label background
    ctx.fillStyle = color;
    const label = `${detectionCategories[category]?.label || category} ${Math.round(obj.confidence * 100)}%`;
    const textMetrics = ctx.measureText(label);
    const labelHeight = 20;
    ctx.fillRect(x, y - labelHeight, textMetrics.width + 10, labelHeight);
    
    // Draw label text
    ctx.fillStyle = 'white';
    ctx.font = '14px "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif';
    ctx.fillText(label, x + 5, y - 5);
  });
};

// Watch for changes in detection results to redraw bounding boxes
watch(() => detectionResults.value, () => {
  // Use setTimeout to ensure the DOM has updated
  setTimeout(drawBoundingBoxes, 100);
}, { deep: true });

// Watch for image load to ensure canvas is properly sized
watch(() => props.imagePreview, (newValue) => {
  if (newValue) {
    const img = document.getElementById('preview-image') as HTMLImageElement;
    if (img) {
      img.onload = drawBoundingBoxes;
    }
  }
});
</script>

<template>
  <div class="result-panel-container">
    <div v-if="!detectionResults" class="no-data">
      <el-empty description="暂无检测结果">
        <template #description>
          <p>请上传图片并选择检测选项后点击"开始检测"</p>
        </template>
      </el-empty>
    </div>
    
    <div v-else class="results-content">
      <!-- 检测结果摘要 -->
      <div class="result-summary">
        <div class="summary-item">
          <div class="item-label">检测总数</div>
          <div class="item-value">{{ totalVehicles }}</div>
        </div>
        
        <div class="summary-item" v-for="(count, type) in vehicleTypeCount" :key="type">
          <div class="item-label">{{ type }}</div>
          <div class="item-value">{{ count }}</div>
        </div>
      </div>
      
      <!-- 风险等级分布 -->
      <div class="risk-distribution">
        <h4>风险等级分布</h4>
        <div class="risk-bars">
          <div v-for="(count, level) in riskDistribution" :key="level" class="risk-bar-item">
            <div class="risk-label">{{ riskLevelText[level] }}</div>
            <div class="risk-bar">
              <div class="risk-bar-fill" :style="{ 
                width: `${totalVehicles ? (count / totalVehicles) * 100 : 0}%`,
                backgroundColor: riskLevelColor[level]
              }"></div>
            </div>
            <div class="risk-count">{{ count }}</div>
          </div>
        </div>
      </div>
      
      <!-- 图表展示 -->
      <div class="chart-container">
        <h4>检测类型分布</h4>
        <div ref="chartRef" class="pie-chart"></div>
      </div>
      
      <!-- 最高置信度项目 -->
      <div v-if="highestConfidenceVehicle" class="highest-confidence">
        <h4>最高置信度</h4>
        <div class="confidence-item">
          <div class="confidence-type">{{ highestConfidenceVehicle.type }}</div>
          <div class="confidence-value">{{ Math.round(highestConfidenceVehicle.confidence * 100) }}%</div>
          <div class="confidence-risk" :style="{ color: riskLevelColor[highestConfidenceVehicle.risk] }">
            {{ riskLevelText[highestConfidenceVehicle.risk] }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.result-panel-container {
  padding: 16px;
  height: 100%;
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.results-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.result-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.summary-item {
  flex: 1;
  min-width: 100px;
  background-color: var(--main-bg);
  border-radius: var(--radius-md);
  padding: 12px;
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.item-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.item-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary-color);
}

.risk-distribution, .chart-container, .highest-confidence {
  background-color: var(--main-bg);
  border-radius: var(--radius-md);
  padding: 16px;
  box-shadow: var(--shadow-sm);
}

h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 14px;
  color: var(--text-primary);
}

.risk-bars {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.risk-bar-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.risk-label {
  width: 60px;
  font-size: 12px;
  color: var(--text-secondary);
}

.risk-bar {
  flex: 1;
  height: 8px;
  background-color: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.risk-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.risk-count {
  width: 30px;
  font-size: 12px;
  text-align: right;
  color: var(--text-secondary);
}

.pie-chart {
  height: 200px;
  width: 100%;
}

.confidence-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  padding: 12px;
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-sm);
}

.confidence-type {
  font-weight: 500;
}

.confidence-value {
  font-weight: 600;
  color: var(--primary-color);
}

.confidence-risk {
  font-size: 12px;
  font-weight: 500;
}
</style> 
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 模型设置
const modelSettings = reactive({
  modelType: 'yolov11',
  confidenceThreshold: 0.5,
  nmsThreshold: 0.45,
  maxDetections: 100
})

// 显示设置
const displaySettings = reactive({
  showConfidence: true,
  showLabels: true,
  highlightRisks: true,
  colorScheme: 'standard'
})

// 系统设置
const systemSettings = reactive({
  autoSave: true,
  saveDirectory: 'D:/traffic-detection/results',
  maxHistoryRecords: 100,
  notifyOnCompletion: true
})

// 保存设置
const saveSettings = () => {
  ElMessage.success('设置已保存')
  // 这里可以实现将设置保存到本地存储或发送到后端
}

// 重置设置
const resetSettings = () => {
  // 重置为默认值
  Object.assign(modelSettings, {
    modelType: 'yolov11',
    confidenceThreshold: 0.5,
    nmsThreshold: 0.45,
    maxDetections: 100
  })
  
  Object.assign(displaySettings, {
    showConfidence: true,
    showLabels: true,
    highlightRisks: true,
    colorScheme: 'standard'
  })
  
  Object.assign(systemSettings, {
    autoSave: true,
    saveDirectory: 'D:/traffic-detection/results',
    maxHistoryRecords: 100,
    notifyOnCompletion: true
  })
  
  ElMessage.info('设置已重置为默认值')
}

// 颜色方案选项
const colorSchemeOptions = [
  { value: 'standard', label: '标准' },
  { value: 'highContrast', label: '高对比度' },
  { value: 'colorBlind', label: '色盲友好' }
]

// 模型类型选项
const modelTypeOptions = [
  { value: 'yolov11', label: 'YOLOv11' },
  { value: 'yolov8', label: 'YOLOv8' },
  { value: 'faster_rcnn', label: 'Faster R-CNN' }
]
</script>

<template>
  <div class="settings-container">
    <h1 class="page-title">系统设置</h1>
    
    <el-tabs type="border-card">
      <!-- 模型设置 -->
      <el-tab-pane label="模型设置">
        <el-form label-width="180px" :model="modelSettings">
          <el-form-item label="模型类型">
            <el-select v-model="modelSettings.modelType" placeholder="选择模型类型">
              <el-option
                v-for="item in modelTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="置信度阈值">
            <el-slider
              v-model="modelSettings.confidenceThreshold"
              :min="0"
              :max="1"
              :step="0.01"
              show-input
            />
          </el-form-item>
          
          <el-form-item label="NMS阈值">
            <el-slider
              v-model="modelSettings.nmsThreshold"
              :min="0"
              :max="1"
              :step="0.01"
              show-input
            />
          </el-form-item>
          
          <el-form-item label="最大检测数量">
            <el-input-number
              v-model="modelSettings.maxDetections"
              :min="1"
              :max="1000"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 显示设置 -->
      <el-tab-pane label="显示设置">
        <el-form label-width="180px" :model="displaySettings">
          <el-form-item label="显示置信度">
            <el-switch v-model="displaySettings.showConfidence" />
          </el-form-item>
          
          <el-form-item label="显示标签">
            <el-switch v-model="displaySettings.showLabels" />
          </el-form-item>
          
          <el-form-item label="高亮风险区域">
            <el-switch v-model="displaySettings.highlightRisks" />
          </el-form-item>
          
          <el-form-item label="颜色方案">
            <el-select v-model="displaySettings.colorScheme" placeholder="选择颜色方案">
              <el-option
                v-for="item in colorSchemeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 系统设置 -->
      <el-tab-pane label="系统设置">
        <el-form label-width="180px" :model="systemSettings">
          <el-form-item label="自动保存结果">
            <el-switch v-model="systemSettings.autoSave" />
          </el-form-item>
          
          <el-form-item label="保存目录">
            <el-input v-model="systemSettings.saveDirectory">
              <template #append>
                <el-button>浏览</el-button>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="最大历史记录数">
            <el-input-number
              v-model="systemSettings.maxHistoryRecords"
              :min="10"
              :max="1000"
              :step="10"
            />
          </el-form-item>
          
          <el-form-item label="完成时通知">
            <el-switch v-model="systemSettings.notifyOnCompletion" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <!-- 关于 -->
      <el-tab-pane label="关于">
        <div class="about-section">
          <h2>交通监控平台</h2>
          <p>版本: 1.0.0</p>
          <p>基于YOLOv11的交通监控分析系统</p>
          <p>支持车辆检测、分类和风险分析</p>
          <p>&copy; 2024 交通监控平台团队</p>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <div class="action-buttons">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
      <el-button @click="resetSettings">重置为默认值</el-button>
    </div>
  </div>
</template>

<style scoped>
.settings-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 500;
  color: #333;
}

.action-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.about-section {
  padding: 20px;
  text-align: center;
}

.about-section h2 {
  margin-bottom: 16px;
}

.about-section p {
  margin-bottom: 8px;
  color: #606266;
}
</style> 
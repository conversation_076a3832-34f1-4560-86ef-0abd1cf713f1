﻿import http.server
import socketserver
import webbrowser
import os
import threading
import time

PORT = 8090
Handler = http.server.SimpleHTTPRequestHandler

print(f"Starting navigation page server on port {PORT}...")
httpd = socketserver.TCPServer(("", PORT), Handler)

# Start browser in background thread
def open_browser():
    time.sleep(1)  # Wait for server to start
    webbrowser.open(f"http://localhost:{PORT}/navigation.html")

threading.Thread(target=open_browser, daemon=True).start()

try:
    httpd.serve_forever()
except KeyboardInterrupt:
    pass
finally:
    httpd.server_close()
    print("Navigation page server has been closed")

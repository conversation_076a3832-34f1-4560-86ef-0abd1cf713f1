# Traffic Eyes 简化视频流启动脚本
# 只启动实时视频处理系统

Write-Host "启动 Traffic Eyes 视频流处理系统..." -ForegroundColor Green

# 设置环境变量，避免OpenCV错误
$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"

# 检查Python环境
$py = "python"
try {
    $ver = & $py --version 2>&1
    Write-Host "Python版本: $ver" -ForegroundColor Green
} catch {
    Write-Host "未检测到Python。请安装Python 3.8或更高版本。" -ForegroundColor Red
    exit 1
}

# 检查视频文件
$videoFiles = Get-ChildItem -Path . -Filter *.mp4 | Select-Object -ExpandProperty Name
if ($videoFiles.Count -eq 0) {
    Write-Host "未找到视频文件，将使用摄像头作为源" -ForegroundColor Yellow
    $videoSource = "0"  # 摄像头
} else {
    $videoSource = $videoFiles[0]
    Write-Host "找到视频文件: $videoSource" -ForegroundColor Yellow
}

# 启动实时视频处理服务
Write-Host "启动实时视频处理服务 (8081)..." -ForegroundColor Cyan
$cmd = "$py realtime_video_processor.py --source $videoSource --port 8081"
Write-Host "执行命令: $cmd" -ForegroundColor Yellow

# 启动浏览器
Start-Process "http://localhost:8081" -WindowStyle Normal

# 启动视频处理
Invoke-Expression $cmd 
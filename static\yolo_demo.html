<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLO视频处理演示</title>
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .video-container {
            position: relative;
            width: 100%;
            margin: 20px 0;
        }
        #video {
            width: 100%;
            max-width: 800px;
        }
        #detectionCanvas {
            position: absolute;
            left: 0;
            top: 0;
            pointer-events: none;
        }
        .controls {
            margin: 20px 0;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>YOLO视频处理演示</h1>
        
        <div class="status" id="status">WebSocket 未连接</div>
        
        <div class="controls">
            <input type="file" id="videoInput" accept="video/*">
            <button id="startBtn" disabled>开始处理</button>
            <button id="stopBtn" disabled>停止处理</button>
        </div>

        <div class="video-container">
            <video id="video" controls></video>
            <canvas id="detectionCanvas"></canvas>
        </div>
    </div>

    <script>
        let ws = null;
        let videoElement = document.getElementById('video');
        let canvas = document.getElementById('detectionCanvas');
        let ctx = canvas.getContext('2d');
        let videoInput = document.getElementById('videoInput');
        let startBtn = document.getElementById('startBtn');
        let stopBtn = document.getElementById('stopBtn');
        let statusElement = document.getElementById('status');
        let isProcessing = false;

        // 连接WebSocket
        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:8000/ws/yolo-video-process');
            
            ws.onopen = () => {
                statusElement.textContent = 'WebSocket 已连接';
                statusElement.className = 'status connected';
                startBtn.disabled = false;
            };

            ws.onclose = () => {
                statusElement.textContent = 'WebSocket 连接已断开';
                statusElement.className = 'status disconnected';
                startBtn.disabled = true;
                stopBtn.disabled = true;
            };

            ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                statusElement.textContent = 'WebSocket 连接错误';
                statusElement.className = 'status disconnected';
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'processed_frame') {
                        // 显示处理后的帧
                        const img = new Image();
                        img.onload = () => {
                            canvas.width = videoElement.videoWidth;
                            canvas.height = videoElement.videoHeight;
                            ctx.clearRect(0, 0, canvas.width, canvas.height);
                            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                        };
                        img.src = 'data:image/jpeg;base64,' + data.frame;
                    }
                } catch (error) {
                    console.error('处理消息错误:', error);
                }
            };
        }

        // 处理视频帧
        function processFrame() {
            if (!isProcessing || videoElement.paused || !ws || ws.readyState !== WebSocket.OPEN) {
                return;
            }

            // 创建临时canvas来捕获当前帧
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = videoElement.videoWidth;
            tempCanvas.height = videoElement.videoHeight;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(videoElement, 0, 0, tempCanvas.width, tempCanvas.height);

            // 发送帧到服务器
            ws.send(JSON.stringify({
                type: 'frame',
                frame: tempCanvas.toDataURL('image/jpeg').split(',')[1]
            }));

            // 请求下一帧
            requestAnimationFrame(processFrame);
        }

        // 初始化视频处理
        videoInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const url = URL.createObjectURL(file);
                videoElement.src = url;
                startBtn.disabled = false;
            }
        });

        // 开始处理
        startBtn.addEventListener('click', () => {
            if (!videoElement.src) return;
            
            isProcessing = true;
            startBtn.disabled = true;
            stopBtn.disabled = false;
            videoElement.play();
            processFrame();
        });

        // 停止处理
        stopBtn.addEventListener('click', () => {
            isProcessing = false;
            startBtn.disabled = false;
            stopBtn.disabled = true;
            videoElement.pause();
        });

        // 初始化
        videoElement.addEventListener('loadedmetadata', () => {
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;
        });

        // 连接WebSocket
        connectWebSocket();
    </script>
</body>
</html>

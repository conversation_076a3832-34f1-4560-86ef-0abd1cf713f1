#!/usr/bin/env python3
"""
调试YOLO处理器的脚本
"""

import os
import sys
import logging
import traceback
import base64
import cv2
import numpy as np
from PIL import Image
import io

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_yolo_processor():
    """测试YOLO处理器"""
    try:
        # 导入YOLO处理器
        from yolo_video_processor import YOLOVideoProcessor, get_cached_model
        
        # 检查模型文件
        model_paths = [
            "../src/models/yolo11n-seg.pt",
            "../src/models/best.pt",
            "models/yolo11n-seg.pt",
            "models/best.pt"
        ]
        
        model_path = None
        for path in model_paths:
            if os.path.exists(path):
                model_path = path
                logger.info(f"找到模型文件: {path}")
                break
        
        if not model_path:
            logger.error("未找到任何模型文件")
            return False
        
        # 创建处理器
        logger.info("创建YOLO处理器...")
        processor = YOLOVideoProcessor(model_path)
        logger.info("YOLO处理器创建成功")
        
        # 创建测试图像
        logger.info("创建测试图像...")
        test_img = np.zeros((480, 640, 3), dtype=np.uint8)
        test_img[:] = (100, 150, 200)  # 填充颜色
        
        # 添加一些简单的形状作为测试对象
        cv2.rectangle(test_img, (100, 100), (200, 200), (255, 255, 255), -1)
        cv2.circle(test_img, (400, 300), 50, (0, 255, 0), -1)
        
        # 转换为base64
        _, buffer = cv2.imencode('.jpg', test_img)
        img_base64 = base64.b64encode(buffer).decode('utf-8')
        img_data_url = f"data:image/jpeg;base64,{img_base64}"
        
        logger.info("测试图像创建完成")
        
        # 测试处理
        logger.info("开始处理测试图像...")
        result = processor.process_image_base64(
            img_data_url,
            timestamp=0.0,
            frame_id=1
        )
        
        if result:
            logger.info("处理成功!")
            logger.info(f"检测到 {len(result.get('objects', []))} 个对象")
            logger.info(f"高风险事件: {len(result.get('high_risk_events', []))}")
            logger.info(f"低风险事件: {len(result.get('low_risk_events', []))}")
            
            # 显示检测到的对象
            for i, obj in enumerate(result.get('objects', [])[:5]):  # 只显示前5个
                logger.info(f"对象 {i+1}: {obj.get('category', 'unknown')} - {obj.get('label', 'unknown')} ({obj.get('confidence', 0):.2f})")
            
            return True
        else:
            logger.error("处理失败，返回None")
            return False
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        traceback.print_exc()
        return False

def test_dependencies():
    """测试依赖项"""
    logger.info("检查依赖项...")
    
    dependencies = [
        ('torch', 'PyTorch'),
        ('ultralytics', 'Ultralytics YOLO'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('PIL', 'Pillow'),
        ('fastapi', 'FastAPI'),
        ('websockets', 'WebSockets')
    ]
    
    all_ok = True
    for module_name, display_name in dependencies:
        try:
            if module_name == 'cv2':
                import cv2
                logger.info(f"✓ {display_name}: {cv2.__version__}")
            elif module_name == 'torch':
                import torch
                logger.info(f"✓ {display_name}: {torch.__version__}")
                logger.info(f"  CUDA available: {torch.cuda.is_available()}")
                if torch.cuda.is_available():
                    logger.info(f"  CUDA device: {torch.cuda.get_device_name(0)}")
            elif module_name == 'ultralytics':
                import ultralytics
                logger.info(f"✓ {display_name}: {ultralytics.__version__}")
            elif module_name == 'numpy':
                import numpy
                logger.info(f"✓ {display_name}: {numpy.__version__}")
            elif module_name == 'PIL':
                import PIL
                logger.info(f"✓ {display_name}: {PIL.__version__}")
            elif module_name == 'fastapi':
                import fastapi
                logger.info(f"✓ {display_name}: {fastapi.__version__}")
            elif module_name == 'websockets':
                import websockets
                logger.info(f"✓ {display_name}: {websockets.__version__}")
        except ImportError:
            logger.error(f"✗ {display_name}: 未安装")
            all_ok = False
        except Exception as e:
            logger.error(f"✗ {display_name}: 导入错误 - {e}")
            all_ok = False
    
    return all_ok

def test_model_loading():
    """测试模型加载"""
    logger.info("测试模型加载...")
    
    model_paths = [
        "../src/models/yolo11n-seg.pt",
        "../src/models/best.pt",
        "models/yolo11n-seg.pt",
        "models/best.pt"
    ]
    
    for path in model_paths:
        if os.path.exists(path):
            size = os.path.getsize(path) / (1024 * 1024)
            logger.info(f"✓ 找到模型: {path} ({size:.1f} MB)")
            
            try:
                from ultralytics import YOLO
                logger.info(f"  尝试加载模型: {path}")
                model = YOLO(path)
                logger.info(f"  ✓ 模型加载成功")
                logger.info(f"  模型类别数: {len(model.names)}")
                logger.info(f"  前5个类别: {list(model.names.values())[:5]}")
                return True
            except Exception as e:
                logger.error(f"  ✗ 模型加载失败: {e}")
        else:
            logger.info(f"✗ 模型不存在: {path}")
    
    return False

def main():
    """主函数"""
    logger.info("开始YOLO调试...")
    
    # 测试依赖项
    if not test_dependencies():
        logger.error("依赖项检查失败")
        return 1
    
    # 测试模型加载
    if not test_model_loading():
        logger.error("模型加载测试失败")
        return 1
    
    # 测试YOLO处理器
    if not test_yolo_processor():
        logger.error("YOLO处理器测试失败")
        return 1
    
    logger.info("✓ 所有测试通过")
    return 0

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"测试过程中出现未处理的错误: {e}")
        traceback.print_exc()
        sys.exit(1)

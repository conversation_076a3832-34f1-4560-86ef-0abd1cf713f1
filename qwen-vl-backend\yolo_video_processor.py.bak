﻿#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import cv2
import base64
import numpy as np
import logging
import uuid
import time
import json
from typing import Dict, List, Optional, Any, Union, Tuple
from io import BytesIO
from PIL import Image

# 閰嶇疆鏃ュ織
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("yolo_video_processor")

# 妫€鏌ユ槸鍚﹁兘瀵煎叆YOLO鐩稿叧搴?YOLO_AVAILABLE = False
try:
    import torch
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
    logger.info("YOLO搴撳凡鎴愬姛鍔犺浇")
except ImportError:
    logger.error("鏃犳硶瀵煎叆YOLO搴擄紝璇风‘淇濆凡瀹夎: pip install torch ultralytics")

# 瀹氫箟YOLOProcessor绫荤敤浜庡鐞嗚棰戝抚
class YOLOProcessor:
    def __init__(self, model_path: str):
        """鍒濆鍖朰OLO澶勭悊鍣?        
        Args:
            model_path: YOLO妯″瀷璺緞
        """
        if not YOLO_AVAILABLE:
            logger.error("鏈畨瑁匶OLO搴擄紝澶勭悊鍣ㄦ棤娉曟甯稿伐浣?)
            self.model = None
            return
            
        try:
            # 鍔犺浇YOLO妯″瀷
            logger.info(f"姝ｅ湪鍔犺浇YOLO妯″瀷: {model_path}")
            self.model = YOLO(model_path)
            logger.info(f"YOLO妯″瀷鍔犺浇鎴愬姛: {type(self.model)}")
            
            # 璺熻釜鍣ㄧ姸鎬?            self.tracks = {}
            self.next_id = 1
            
            # 閰嶇疆
            self.conf_threshold = 0.25
            self.iou_threshold = 0.45
            self.max_det = 300
            self.max_track_length = 30  # 鏈€澶ц窡韪建杩归暱搴?            self.latest_detections = []  # 鐢ㄤ簬涓存椂瀛樺偍鏈€鏂颁竴甯х殑妫€娴嬬粨鏋?            self.latest_summary = {"high_risk_count": 0, "medium_risk_count": 0, "low_risk_count": 0}  # 鏈€鏂扮殑椋庨櫓鎽樿
        except Exception as e:
            logger.error(f"鍔犺浇YOLO妯″瀷澶辫触: {str(e)}")
            self.model = None

    def iou(self, box1, box2):
        """璁＄畻涓や釜杈圭晫妗嗙殑IoU"""
        # 鍧愭爣鏍煎紡: [x1, y1, x2, y2]
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        width = max(0, x2 - x1)
        height = max(0, y2 - y1)
        
        inter_area = width * height
        
        box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
        box2_area = (box2[2] - box2[0]) * (box2[3] - box2[1])
        
        union_area = box1_area + box2_area - inter_area
        
        return inter_area / union_area if union_area > 0 else 0

    def update_tracks(self, detections):
        """鏇存柊璺熻釜鍣?""
        if not detections:
            return {}
            
        # 濡傛灉鏄娆℃娴嬶紝涓烘墍鏈夋娴嬪璞″垎閰岻D
        if not self.tracks:
            for det in detections:
                track_id = self.next_id
                self.next_id += 1
                
                self.tracks[track_id] = {
                    "detection": det,
                    "history": [det["bbox_2d"]],
                    "last_seen": 0,
                    "category": det["category"],
                    "label": det["label"],
                    "confidence": det["confidence"],
                    "risk_level": det.get("risk_level", "low")
                }
            return self.tracks
        
        # 涓哄綋鍓嶅抚涓殑妫€娴嬬粨鏋滃垎閰嶈窡韪狪D
        assigned = set()
        unassigned_detections = []
        
        for det in detections:
            best_iou = 0
            best_id = -1
            
            for track_id, track in self.tracks.items():
                if track_id in assigned:
                    continue
                
                # 璁＄畻褰撳墠妫€娴嬩笌璺熻釜瀵硅薄涔嬮棿鐨処oU
                curr_iou = self.iou(det["bbox_2d"], track["history"][-1])
                
                if curr_iou > 0.3 and curr_iou > best_iou:  # IoU闃堝€煎彲浠ヨ皟鏁?                    best_iou = curr_iou
                    best_id = track_id
            
            if best_id != -1:
                # 鏇存柊宸插瓨鍦ㄧ殑璺熻釜瀵硅薄
                self.tracks[best_id]["detection"] = det
                self.tracks[best_id]["history"].append(det["bbox_2d"])
                self.tracks[best_id]["last_seen"] = 0
                self.tracks[best_id]["confidence"] = det["confidence"]
                self.tracks[best_id]["risk_level"] = det.get("risk_level", self.tracks[best_id]["risk_level"])
                
                # 闄愬埗鍘嗗彶杞ㄨ抗闀垮害
                if len(self.tracks[best_id]["history"]) > self.max_track_length:
                    self.tracks[best_id]["history"] = self.tracks[best_id]["history"][-self.max_track_length:]
                    
                assigned.add(best_id)
            else:
                # 鏂板鏈尮閰嶇殑妫€娴嬪璞?                unassigned_detections.append(det)
        
        # 绉婚櫎闀挎椂闂存湭鏇存柊鐨勮窡韪璞?        track_ids_to_remove = []
        for track_id, track in self.tracks.items():
            if track_id not in assigned:
                track["last_seen"] += 1
                # 濡傛灉瓒呰繃5甯ф湭妫€娴嬪埌锛岀Щ闄よ窡韪璞?                if track["last_seen"] > 5:
                    track_ids_to_remove.append(track_id)
        
        for track_id in track_ids_to_remove:
            del self.tracks[track_id]
        
        # 娣诲姞鏂扮殑鏈尮閰嶆娴嬪璞?        for det in unassigned_detections:
            track_id = self.next_id
            self.next_id += 1
            
            self.tracks[track_id] = {
                "detection": det,
                "history": [det["bbox_2d"]],
                "last_seen": 0,
                "category": det["category"],
                "label": det["label"],
                "confidence": det["confidence"],
                "risk_level": det.get("risk_level", "low")
            }
        
        return self.tracks

    def process_image_base64(self, image_base64: str, timestamp: Optional[int] = None, frame_id: Optional[int] = None) -> Dict:
        """澶勭悊Base64缂栫爜鐨勫浘鍍?        
        Args:
            image_base64: Base64缂栫爜鐨勫浘鍍忔暟鎹?            timestamp: 甯х殑鏃堕棿鎴?            frame_id: 甯D
            
        Returns:
            鍖呭惈妫€娴嬬粨鏋滅殑瀛楀吀
        """
        try:
            # 妫€鏌ユā鍨嬫槸鍚﹀姞杞芥垚鍔?            if self.model is None:
                logger.error("YOLO妯″瀷鏈垚鍔熷姞杞斤紝鏃犳硶澶勭悊鍥惧儚")
                return None
            
            # 灏咮ase64缂栫爜杞崲涓哄浘鍍?            try:
                # 鍘婚櫎鍙兘瀛樺湪鐨勫墠缂€
                if "," in image_base64:
                    image_base64 = image_base64.split(",", 1)[1]
                
                # 瑙ｇ爜Base64
                image_data = base64.b64decode(image_base64)
                
                # 杞崲涓篛penCV鍥惧儚
                nparr = np.frombuffer(image_data, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                if image is None:
                    logger.error("鏃犳硶瑙ｇ爜鍥惧儚鏁版嵁")
                    return None
            except Exception as e:
                logger.error(f"Base64瑙ｇ爜澶辫触: {str(e)}")
                return None
            
            # 鍒涘缓涓存椂鏂囦欢璺緞鐢ㄤ簬淇濆瓨澶勭悊缁撴灉
            output_dir = os.path.join("static", "uploads")
            os.makedirs(output_dir, exist_ok=True)
            temp_output_path = os.path.join(output_dir, f"{uuid.uuid4()}.jpg")
            
            # 澶勭悊鍥惧儚
            return self._process_image(image, temp_output_path, timestamp, frame_id)
        except Exception as e:
            logger.error(f"澶勭悊鍥惧儚鏃跺嚭閿? {str(e)}")
            return None

    def process_image_file(self, image_path: str, timestamp: Optional[int] = None, frame_id: Optional[int] = None) -> Dict:
        """澶勭悊鍥惧儚鏂囦欢
        
        Args:
            image_path: 鍥惧儚鏂囦欢璺緞
            timestamp: 甯х殑鏃堕棿鎴?            frame_id: 甯D
            
        Returns:
            鍖呭惈妫€娴嬬粨鏋滅殑瀛楀吀
        """
        try:
            # 妫€鏌ユā鍨嬫槸鍚﹀姞杞芥垚鍔?            if self.model is None:
                logger.error("YOLO妯″瀷鏈垚鍔熷姞杞斤紝鏃犳硶澶勭悊鍥惧儚")
                return None

            # 璇诲彇鍥惧儚
            image = cv2.imread(image_path)
            
            if image is None:
                logger.error(f"鏃犳硶璇诲彇鍥惧儚: {image_path}")
                return None
            
            # 鍒涘缓涓存椂鏂囦欢璺緞鐢ㄤ簬淇濆瓨澶勭悊缁撴灉
            output_dir = os.path.join("static", "uploads")
            os.makedirs(output_dir, exist_ok=True)
            temp_output_path = os.path.join(output_dir, f"{uuid.uuid4()}.jpg")
            
            # 澶勭悊鍥惧儚
            return self._process_image(image, temp_output_path, timestamp, frame_id)
        except Exception as e:
            logger.error(f"澶勭悊鍥惧儚鏃跺嚭閿? {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def _process_image(self, image: np.ndarray, output_path: str, timestamp: Optional[int] = None, frame_id: Optional[int] = None) -> Dict:
        """澶勭悊鍥惧儚鏍稿績閫昏緫
        
        Args:
            image: OpenCV鏍煎紡鐨勫浘鍍忔暟鎹?            output_path: 缁撴灉鍥惧儚淇濆瓨璺緞
            timestamp: 甯х殑鏃堕棿鎴?            frame_id: 甯D
            
        Returns:
            鍖呭惈妫€娴嬬粨鏋滅殑瀛楀吀
        """
        try:
            # 鑾峰彇鍥惧儚灏哄
            height, width = image.shape[:2]
            
            # 杩愯YOLO妯″瀷
            results = self.model(image, conf=self.conf_threshold, iou=self.iou_threshold, max_det=self.max_det)
            
            # 鎻愬彇妫€娴嬬粨鏋?            detections = []
            high_risk_events = []
            low_risk_events = []
            
            if len(results) > 0:
                # 鑾峰彇绗竴甯х殑缁撴灉
                result = results[0]
                
                # 鎻愬彇杈圭晫妗嗐€佺被鍒拰缃俊搴?                for i, box in enumerate(result.boxes):
                    # 鑾峰彇鍧愭爣
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                    confidence = float(box.conf[0].cpu().numpy())
                    
                    # 鑾峰彇绫诲埆ID鍜屽悕绉?                    cls_id = int(box.cls[0].cpu().numpy())
                    cls_name = result.names[cls_id]
                    
                    # 纭畾妫€娴嬪璞＄殑绫诲埆
                    category = "鏈煡"
                    risk_level = "low"
                    
                    # 鏍规嵁绫诲埆璁剧疆category鍜岄闄╃骇鍒?                    if cls_name in ["person", "pedestrian", "浜?, "琛屼汉"]:
                        category = "浜哄憳"
                        # 鍒ゆ柇鏄惁鏈夊畨鍏ㄥ附
                        has_helmet = False  # 杩欓噷鍙互澧炲姞鏇村鏉傜殑閫昏緫鏉ユ娴嬪畨鍏ㄥ附
                        if not has_helmet and confidence > 0.5:
                            risk_level = "medium"  # 娌℃埓瀹夊叏甯界殑宸ヤ汉涓轰腑绛夐闄?                            # 杩涗竴姝ユ鏌ユ槸鍚﹀湪鍗遍櫓鍖哄煙
                            # 绀轰緥锛氱畝鍗曞湴鍒ゆ柇浣嶇疆锛屽湪鍥惧儚搴曢儴鐨勪汉鍛樺彲鑳介闄╂洿楂?                            if y2 > 0.8 * height:
                                risk_level = "high"
                    
                    elif cls_name in ["car", "truck", "bus", "vehicle", "姹借溅", "鍗¤溅", "鍏氦杞?, "杞﹁締"]:
                        category = "杞﹁締"
                        # 杞﹁締椋庨櫓鍒ゆ柇閫昏緫锛屼緥濡傚熀浜庨€熷害銆佷綅缃瓑
                        # 绀轰緥锛氬ぇ鍨嬭溅杈嗛闄╂洿楂?                        if (x2 - x1) * (y2 - y1) > 0.3 * width * height:
                            risk_level = "medium"
                            
                    elif cls_name in ["excavator", "bulldozer", "crane", "machinery", "鎸栨帢鏈?, "鎺ㄥ湡鏈?, "璧烽噸鏈?, "鏈烘"]:
                        category = "鏈烘"
                        # 榛樿鏈烘璁惧涓轰腑绛夐闄?                        risk_level = "medium"
                    
                    # 鍒涘缓妫€娴嬪璞?                    detection = {
                        "bbox_2d": [x1, y1, x2, y2],
                        "confidence": confidence,
                        "category": category,
                        "label": cls_name,
                        "risk_level": risk_level
                    }
                    
                    # 娣诲姞鍒版娴嬪垪琛?                    detections.append(detection)
                    
                    # 鏍规嵁椋庨櫓绛夌骇娣诲姞鍒扮浉搴斿垪琛?                    if risk_level == "high":
                        high_risk_events.append(detection)
                    else:
                        low_risk_events.append(detection)
            
            # 鏇存柊璺熻釜鍣?            self.latest_detections = detections
            tracks = self.update_tracks(detections)
            
            # 鍦ㄥ浘鍍忎笂缁樺埗妫€娴嬬粨鏋滃拰杞ㄨ抗
            vis_image = image.copy()
            
            # 缁樺埗妫€娴嬫鍜屾爣绛?            for track_id, track in tracks.items():
                det = track["detection"]
                x1, y1, x2, y2 = det["bbox_2d"]
                label = det["label"]
                confidence = det["confidence"]
                risk_level = det["risk_level"]
                
                # 鏍规嵁椋庨櫓绛夌骇閫夋嫨棰滆壊
                if risk_level == "high":
                    color = (0, 0, 255)  # 绾㈣壊
                elif risk_level == "medium":
                    color = (0, 165, 255)  # 姗欒壊
                else:
                    color = (0, 255, 0)  # 缁胯壊
                
                # 缁樺埗杈圭晫妗?                cv2.rectangle(vis_image, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
                
                # 缁樺埗鏍囩
                text = f"{label} {confidence:.2f} ID:{track_id}"
                cv2.putText(vis_image, text, (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                
                # 缁樺埗杞ㄨ抗
                history = track["history"]
                if len(history) > 1:
                    for i in range(1, len(history)):
                        # 鑾峰彇杞ㄨ抗鐐?                        prev_box = history[i-1]
                        curr_box = history[i]
                        
                        # 璁＄畻涓績鐐?                        prev_center = ((prev_box[0] + prev_box[2]) // 2, (prev_box[1] + prev_box[3]) // 2)
                        curr_center = ((curr_box[0] + curr_box[2]) // 2, (curr_box[1] + curr_box[3]) // 2)
                        
                        # 缁樺埗杞ㄨ抗绾?                        cv2.line(vis_image, prev_center, curr_center, color, 2)
            
            # 缁樺埗鎽樿淇℃伅
            high_risk_count = len(high_risk_events)
            medium_risk_count = sum(1 for det in detections if det["risk_level"] == "medium")
            low_risk_count = sum(1 for det in detections if det["risk_level"] == "low")
            
            summary_text = f"楂橀闄? {high_risk_count} | 涓闄? {medium_risk_count} | 浣庨闄? {low_risk_count}"
            cv2.putText(vis_image, summary_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # 鏇存柊鎽樿淇℃伅
            self.latest_summary = {
                "high_risk_count": high_risk_count,
                "medium_risk_count": medium_risk_count,
                "low_risk_count": low_risk_count,
                "total_objects": len(detections)
            }
            
            # 淇濆瓨澶勭悊鍚庣殑鍥惧儚
            cv2.imwrite(output_path, vis_image)
            
            # 涓鸿竟鐣屾鍧愭爣娣诲姞褰掍竴鍖栧€?            for det in detections:
                x1, y1, x2, y2 = det["bbox_2d"]
                det["bbox_norm"] = [x1/width, y1/height, x2/width, y2/height]
            
            # 杩斿洖缁撴灉
            result = {
                "objects": detections,
                "high_risk_events": high_risk_events,
                "low_risk_events": low_risk_events,
                "summary": self.latest_summary,
                "processed_image_path": output_path,
                "image_width": width,
                "image_height": height,
                "timestamp": timestamp or int(time.time() * 1000),
                "frameId": frame_id
            }
            
            # 灏嗙粨鏋滅紪鐮佷负base64 (鍙€夛紝鐢变簬鐜板湪鎴戜滑杩斿洖鏂囦欢璺緞锛岃繖姝ュ彲浠ュ湪澶栭儴杩涜)
            # with open(output_path, "rb") as img_file:
            #     result["processed_frame"] = f"data:image/jpeg;base64,{base64.b64encode(img_file.read()).decode('utf-8')}"
            
            return result
        except Exception as e:
            logger.error(f"澶勭悊鍥惧儚澶辫触: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

# 杈呭姪鍑芥暟
def create_processor(model_path: str) -> YOLOProcessor:
    """鍒涘缓YOLO澶勭悊鍣ㄥ疄渚?    
    Args:
        model_path: YOLO妯″瀷璺緞
        
    Returns:
        YOLOProcessor瀹炰緥
    """
    logger.info(f"鍒涘缓YOLO澶勭悊鍣紝浣跨敤妯″瀷: {model_path}")
    processor = YOLOProcessor(model_path)
    if processor.model is None:
        logger.error("YOLO澶勭悊鍣ㄥ垱寤哄け璐ワ紝妯″瀷鍔犺浇澶辫触")
    else:
        logger.info("YOLO澶勭悊鍣ㄥ垱寤烘垚鍔?)
    return processor

async def process_frame_async(processor, frame_data, timestamp=None, frame_id=None):
    """寮傛澶勭悊甯у嚱鏁?- 浣跨敤asyncio.to_thread鎵цCPU瀵嗛泦鍨嬩换鍔?    
    Args:
        processor: YOLOProcessor瀹炰緥
        frame_data: 甯ф暟鎹?Base64)
        timestamp: 鏃堕棿鎴?        frame_id: 甯D
        
    Returns:
        澶勭悊缁撴灉
    """
    import asyncio
    
    # 浣跨敤asyncio.to_thread鍦ㄧ嚎绋嬫睜涓墽琛孋PU瀵嗛泦鍨嬫搷浣?    logger.info(f"寮傛澶勭悊甯?{frame_id if frame_id is not None else '(鏈煡)'}")
    try:
        result = await asyncio.to_thread(
            processor.process_image_base64,
            frame_data, timestamp, frame_id
        )
        return result
    except Exception as e:
        logger.error(f"寮傛澶勭悊甯у嚭閿? {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# 褰撲娇鐢╓ebSocket鏃惰皟鐢ㄦ鍑芥暟杩涜娴佸鐞?async def process_frames_stream(processor, frame_queue, result_queue):
    """澶勭悊浠庨槦鍒楁帴鏀剁殑甯у苟灏嗙粨鏋滃彂閫佸埌鍙︿竴涓槦鍒?    
    Args:
        processor: YOLOProcessor瀹炰緥
        frame_queue: 鎺ユ敹甯ф暟鎹殑闃熷垪
        result_queue: 鍙戦€佸鐞嗙粨鏋滅殑闃熷垪
    """
    import asyncio
    
    logger.info("鍚姩甯у鐞嗘祦")
    try:
        while True:
            # 浠庨槦鍒楄幏鍙栦笅涓€甯?            frame_data = await frame_queue.get()
            
            # None琛ㄧず澶勭悊缁撴潫
            if frame_data is None:
                logger.info("鎺ユ敹鍒扮粨鏉熶俊鍙凤紝鍋滄甯у鐞嗘祦")
                frame_queue.task_done()
                break
                
            # 瑙ｆ瀽甯ф暟鎹?            frame_base64 = frame_data.get("frame")
            timestamp = frame_data.get("timestamp")
            frame_id = frame_data.get("frame_id")
            
            # 澶勭悊甯?            try:
                result = await asyncio.to_thread(
                    processor.process_image_base64,
                    frame_base64, timestamp, frame_id
                )
                
                # 灏嗙粨鏋滄斁鍏ョ粨鏋滈槦鍒?                if result:
                    await result_queue.put(result)
                else:
                    logger.error(f"澶勭悊甯?{frame_id} 澶辫触")
                    await result_queue.put({
                        "error": f"澶勭悊甯?{frame_id} 澶辫触",
                        "timestamp": timestamp or int(time.time() * 1000),
                        "frameId": frame_id
                    })
            except Exception as e:
                logger.error(f"澶勭悊甯?{frame_id} 鍑洪敊: {str(e)}")
                await result_queue.put({
                    "error": f"澶勭悊甯у嚭閿? {str(e)}",
                    "timestamp": timestamp or int(time.time() * 1000),
                    "frameId": frame_id
                })
                
            # 鏍囪浠诲姟瀹屾垚
            frame_queue.task_done()
    except Exception as e:
        logger.error(f"甯у鐞嗘祦鍑洪敊: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        logger.info("甯у鐞嗘祦宸茬粨鏉?)

# 娴嬭瘯浠ｇ爜
if __name__ == "__main__":
    try:
        # 娴嬭瘯澶勭悊鍣?        model_path = "../src/models/yolo11n-seg.pt"
        if not os.path.exists(model_path):
            model_path = "../src/models/best.pt"
            
        processor = create_processor(model_path)
        
        # 娴嬭瘯鍥惧儚澶勭悊
        test_image = "static/uploads/test.jpg"
        if os.path.exists(test_image):
            result = processor.process_image_file(test_image)
            print(f"澶勭悊缁撴灉: {json.dumps(result, indent=2)}")
        else:
            print(f"娴嬭瘯鍥惧儚涓嶅瓨鍦? {test_image}")
    except Exception as e:
        print(f"娴嬭瘯澶辫触: {str(e)}")

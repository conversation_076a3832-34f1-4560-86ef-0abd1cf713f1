#!/usr/bin/env python3
"""
下载YOLO分割模型脚本
支持YOLOv8分割模型
"""

import os
import sys
import argparse
import logging
import subprocess

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_model(model_name, output_dir):
    """下载YOLO模型"""
    try:
        # 检查ultralytics是否已安装
        try:
            import ultralytics
        except ImportError:
            logger.info("安装ultralytics包...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "ultralytics"])
            import ultralytics
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 完整的输出路径
        output_path = os.path.join(output_dir, f"{model_name}.pt")
        
        # 如果模型已存在，则直接返回
        if os.path.exists(output_path):
            logger.info(f"模型 {model_name} 已存在于 {output_path}")
            return output_path
        
        # 下载模型
        logger.info(f"正在下载模型 {model_name}...")
        from ultralytics import YOLO
        
        # 使用YOLO API下载模型
        model = YOLO(model_name)
        logger.info(f"模型 {model_name} 下载完成")
        
        # 保存模型
        save_path = model_name.replace('/', '_')  # 替换路径分隔符
        model.save(save_path)
        
        # 复制到指定位置
        import shutil
        save_path = f"{save_path}.pt"
        if os.path.exists(save_path):
            shutil.copy(save_path, output_path)
            logger.info(f"模型已保存到 {output_path}")
            # 清理
            try:
                os.remove(save_path)
            except:
                pass
            return output_path
        else:
            # 直接使用原始模型路径
            logger.info(f"使用原始模型路径: {model.ckpt_path}")
            shutil.copy(model.ckpt_path, output_path)
            logger.info(f"模型已保存到 {output_path}")
            return output_path
        
    except Exception as e:
        logger.error(f"下载模型时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    parser = argparse.ArgumentParser(description="下载YOLO分割模型")
    parser.add_argument("--model", type=str, default="yolov8n-seg", 
                        choices=["yolov8n-seg", "yolov8s-seg", "yolov8m-seg", "yolov8l-seg", "yolov8x-seg"],
                        help="模型名称 (默认: yolov8n-seg)")
    parser.add_argument("--output", type=str, default="models",
                        help="输出目录 (默认: models)")
    args = parser.parse_args()
    
    # 下载模型
    model_path = download_model(args.model, args.output)
    
    # 检查是否成功
    if model_path and os.path.exists(model_path):
        logger.info(f"成功下载模型到 {model_path}")
    else:
        logger.error(f"模型下载失败，请手动下载并将其放置在 {args.output}/{args.model}.pt")
        logger.info("可以从以下网址手动下载：https://github.com/ultralytics/assets/releases/")
        sys.exit(1)

if __name__ == "__main__":
    main() 
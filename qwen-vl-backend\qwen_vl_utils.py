"""
Utility functions for the Qwen VL API
"""
import math

def smart_resize(height, width, min_pixels=512*28*28, max_pixels=2048*28*28):
    """
    Resize an image to fit within min_pixels and max_pixels constraints
    while maintaining aspect ratio.
    
    Args:
        height (int): Original image height
        width (int): Original image width
        min_pixels (int): Minimum number of pixels in the resized image
        max_pixels (int): Maximum number of pixels in the resized image
        
    Returns:
        tuple: (new_height, new_width) for the resized image
    """
    # Calculate current number of pixels
    current_pixels = height * width
    
    # If current size is already within bounds, return original dimensions
    if min_pixels <= current_pixels <= max_pixels:
        return height, width
    
    # Calculate aspect ratio
    aspect_ratio = width / height
    
    if current_pixels < min_pixels:
        # Scale up to meet minimum pixels
        scale_factor = math.sqrt(min_pixels / current_pixels)
    else:
        # Scale down to meet maximum pixels
        scale_factor = math.sqrt(max_pixels / current_pixels)
    
    # Calculate new dimensions
    new_height = int(height * scale_factor)
    new_width = int(width * scale_factor)
    
    # Make sure dimensions are divisible by 32 (common requirement for vision models)
    new_height = (new_height // 32) * 32
    new_width = (new_width // 32) * 32
    
    # Ensure at least 32x32
    new_height = max(32, new_height)
    new_width = max(32, new_width)
    
    return new_height, new_width 
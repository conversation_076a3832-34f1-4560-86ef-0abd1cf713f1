﻿// Initialize WebSocket for YOLO visualization
const initializeYoloWebSocket = () => {
  if (wsYoloConnection && wsYoloConnection.readyState === WebSocket.OPEN) {
    console.log('YOLO WebSocket已连接，无需重新初始化');
    return;
  }
  
  if (wsYoloConnection && wsYoloConnection.readyState === WebSocket.CONNECTING) {
    console.log('YOLO WebSocket正在连接中，请稍候...');
    return;
  }
  
  console.log('创建YOLO WebSocket连接 - 连接到YOLO图像处理后端');
  addApiLog(`INFO:qwen-vl-api:正在连接YOLO图像处理后端: ${YOLO_BACKEND_URL}`);
  
  try {
    // 创建新的WebSocket连接到YOLO后端
    wsYoloConnection = new WebSocket(YOLO_BACKEND_URL);

    wsYoloConnection.onopen = () => {
      console.log('YOLO WebSocket连接已建立');
      addApiLog(`INFO:qwen-vl-api:已连接到YOLO图像处理后端`);
      
      // 发送模型配置信息
      wsYoloConnection.send(JSON.stringify({
        action: 'configure',
        model: 'yolo11n-seg.pt',
        config: {
          segmentation: true,
          confidence: 0.25,
          visualize: true,
          img_size: 640
        }
      }));
      
      // 开始YOLO处理
      if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
        captureAndProcessFrame();
      }
    };
    
    wsYoloConnection.onerror = (error) => {
      console.error('YOLO WebSocket错误:', error);
      addApiLog(`ERROR:qwen-vl-api:YOLO图像处理后端连接失败`);
    };
    
    wsYoloConnection.onclose = () => {
      console.log('YOLO WebSocket连接已关闭');
      addApiLog(`INFO:qwen-vl-api:YOLO图像处理后端连接已关闭`);
      wsYoloConnection = null;
    };
    
    wsYoloConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.error) {
          console.error('YOLO服务器返回错误:', data.error);
          return;
        }
        
        // 处理YOLO检测结果
        if (data.processed_frame) {
          // 确保处理后的帧数据是完整的Data URL
          let processedFrameDataUrl = data.processed_frame;
          if (!processedFrameDataUrl.startsWith('data:image')) {
            processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
          }
          
          // 在canvas上显示处理后的帧
          if (detectionCanvasElement.value) {
            const processedImage = new Image();
            processedImage.onload = () => {
              const ctx = detectionCanvasElement.value.getContext('2d');
              if (ctx) {
                ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
                ctx.drawImage(
                  processedImage,
                  0, 0,
                  detectionCanvasElement.value.width,
                  detectionCanvasElement.value.height
                );
              }
            };
            processedImage.src = processedFrameDataUrl;
          }
          
          // 更新检测结果
          if (data.objects && data.objects.length > 0) {
            latestDetections.value = data.objects;
          }
          
          // 继续处理下一帧
          if (videoPlayerElement.value && !videoPlayerElement.value.paused && useYoloDetection.value) {
            requestAnimationFrame(captureAndProcessFrame);
          }
        }
      } catch (error) {
        console.error('处理YOLO WebSocket消息时出错:', error);
      }
    };
  } catch (error) {
    console.error('连接到YOLO后端时出错:', error);
    addApiLog(`ERROR:qwen-vl-api:连接到YOLO图像处理后端失败: ${error.message}`);
  }
}; 

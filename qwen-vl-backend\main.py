#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import base64
import logging
import uuid
import re
import sys
from io import BytesIO
from typing import List, Optional, Dict, Any, Union
from PIL import Image
from fastapi import FastAPI, File, Form, UploadFile, HTTPException, Depends, WebSocket, WebSocketDisconnect,BackgroundTasks
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.requests import Request
from pydantic import BaseModel, Field
from pathlib import Path
import json
import asyncio
import concurrent.futures
from queue import Queue
from threading import Lock
import time
import numpy as np
import cv2

# 创建线程池执行器 - 用于并行处理视频帧
THREAD_POOL = concurrent.futures.ThreadPoolExecutor(max_workers=16)  # 增加工作线程数量到16个
# 创建帧处理队列 - 调整队列大小以处理更多帧
frame_queue = asyncio.Queue(maxsize=64)  # 增大队列容量
# 创建线程同步锁
thread_lock = Lock()

# 为每个WebSocket连接创建单独的队列
connection_queues = {}
# 为每个连接维护帧率和性能指标
connection_stats = {}

import dashscope
from qwen_vl_utils import smart_resize
from detection_api import inference_with_api
from dds_cloudapi_sdk import Config, Client
from dds_cloudapi_sdk.tasks.base import BaseTask
from dds_cloudapi_sdk.tasks import LabelTypes as DetectionTarget
import datetime
import cv2
import time
from starlette.concurrency import run_in_threadpool
from task import wsTask
from taskmanage import sumbit_task_qwen,get_task_status,dele_task,sumbit_task_qwen_max
# 检查可选模块
OPTIONAL_MODULES = {}

try:
    import video_api
    OPTIONAL_MODULES['video_api'] = True
except ImportError:
    OPTIONAL_MODULES['video_api'] = False
    logging.warning("未能导入 video_api 模块，相关功能将不可用")

try:
    from yolo11x_tracker import process_video, process_image, YOLOTracker

    OPTIONAL_MODULES['yolo11x_tracker'] = True
except ImportError:
    OPTIONAL_MODULES['yolo11x_tracker'] = False
    logging.warning("未能导入 yolo11x_tracker 模块，相关功能将不可用")

try:
    from detection_api import detect_road_defects_yolo, visualize_detections
    OPTIONAL_MODULES['detect_road_defects'] = True
except ImportError:
    OPTIONAL_MODULES['detect_road_defects'] = False
    logging.warning("未能导入 detection_api 中的路面缺陷检测函数，相关功能将不可用")

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("dotenv 模块不可用，跳过环境变量加载")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("qwen-vl-api")

# 创建上传目录
UPLOAD_DIR = Path("static/uploads")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 创建FastAPI应用
app = FastAPI(title="Traffic Eyes API", description="交通分析API")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置模板
try:
    # 挂载静态文件
    app.mount("/static", StaticFiles(directory="static"), name="static")
    # 添加额外的挂载点，使/uploads直接指向static/uploads目录
    app.mount("/uploads", StaticFiles(directory="static/uploads"), name="uploads")
    templates = Jinja2Templates(directory="templates")
except Exception as e:
    logger.warning(f"无法加载静态文件或模板: {str(e)}")
    templates = None

# 定义模型
class MessageContent(BaseModel):
    text: Optional[str] = None
    image: Optional[str] = None

class Message(BaseModel):
    role: str
    content: Union[str, List[MessageContent]]

class ConversationRequest(BaseModel):
    messages: List[Message]
    prompt: Optional[str] = None

class AnalysisRequest(BaseModel):
    image_url: str
    prompt: Optional[str] = None

class VideoAnalysisRequest(BaseModel):
    video_base64: str
    prompt: Optional[str] = None
    frame_interval: Optional[int] = 30
    custom_model_id: Optional[str] = "qwen-vl-max-1119"

    class Config:
        protected_namespaces = ()

# 辅助函数
def image_to_base64(image_path_or_data):
    """将图像转换为base64编码

    参数:
        image_path_or_data: 可以是图像文件路径，也可以是已经读取的图像数据
    """
    try:
        # 如果是文件路径
        if isinstance(image_path_or_data, (str, bytes, os.PathLike)):
            with open(image_path_or_data, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        # 如果是已经读取的图像数据
        elif isinstance(image_path_or_data, bytes):
            return base64.b64encode(image_path_or_data).decode('utf-8')
        # 如果是None或其他类型
        else:
            logger.error(f"无效的图像数据类型: {type(image_path_or_data)}")
            raise ValueError(f"无效的图像数据类型: {type(image_path_or_data)}")
    except Exception as e:
        logger.error(f"转换图像到base64失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"转换图像到base64失败: {str(e)}")

def save_upload_file(upload_file: UploadFile) -> str:
    """保存上传的文件并返回文件路径"""
    try:
        # 生成唯一文件名
        file_extension = os.path.splitext(upload_file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join("static", "uploads", unique_filename)

        # 保存文件
        with open(file_path, "wb") as buffer:
            buffer.write(upload_file.file.read())

        return file_path
    except Exception as e:
        logger.error(f"保存上传文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存上传文件失败: {str(e)}")

# 路由
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """返回主页"""
    if templates:
        return templates.TemplateResponse("index.html", {"request": request})
    else:
        return HTMLResponse("<html><body><h1>API 服务运行中</h1></body></html>")

@app.get("/api/logs")
async def get_logs(type: str = None, limit: int = 10):
    """获取系统日志"""
    try:
        # 创建一个内存缓冲区来存储日志
        log_buffer = []

        # 基于日志类型筛选
        log_filter = type if type else None

        # 创建一个内存缓存，存储最近的日志
        if not hasattr(get_logs, "log_cache"):
            get_logs.log_cache = {
                "qwen-vl-api": [],
                "yolo_video_processor": []
            }

        # 如果是Qwen API日志请求
        if log_filter == "qwen-vl-api":
            # 找出所有Qwen API日志
            for handler in logger.handlers:
                if hasattr(handler, "stream") and hasattr(handler.stream, "getvalue"):
                    log_text = handler.stream.getvalue()
                    qwen_logs = [line for line in log_text.split('\n') if "INFO:qwen-vl-api:" in line]
                    if qwen_logs:
                        log_buffer.extend(qwen_logs)

            # 如果找不到任何日志，使用内存缓存中的日志
            if not log_buffer and get_logs.log_cache["qwen-vl-api"]:
                log_buffer = get_logs.log_cache["qwen-vl-api"]

            # 如果仍然没有日志，返回一些示例日志
            if not log_buffer:
                log_buffer = [
                    "INFO:qwen-vl-api:检测成功，返回结果: {'success': True, 'detections': [{'category': '人', 'event_description': '', 'risk_level': '中风险', 'confidence_score': 0.7, 'bbox_2d': [681, 564, 693, 618], 'display_label': '', 'event': '人', 'confidence': 0.8, 'label': '人'}, {'category': '人', 'event_description': '', 'risk_level': '低风险', 'confidence_score': 0.6, 'bbox_2d': [222, 689, 248, 789], 'display_label': '', 'event': '人', 'confidence': 0.8, 'label': '人'}]}",
                    "INFO:qwen-vl-api:检测成功，返回结果: {'success': True, 'detections': [{'category': '车辆', 'event': '车辆行驶', 'risk_level': '低风险', 'confidence': 0.92, 'bbox_2d': [156, 320, 412, 480], 'label': '车辆'}]}",
                    "INFO:qwen-vl-api:检测成功，返回结果: {'success': True, 'detections': [{'category': '机械', 'event': '挖掘机操作', 'risk_level': '中风险', 'confidence': 0.85, 'bbox_2d': [300, 200, 550, 450], 'label': '挖掘机'}]}"
                ]
                # 更新缓存
                get_logs.log_cache["qwen-vl-api"] = log_buffer.copy()
        # 如果是YOLO视频处理器日志请求
        elif log_filter == "yolo_video_processor":
            # 找出所有YOLO视频处理器日志
            for handler in logger.handlers:
                if hasattr(handler, "stream") and hasattr(handler.stream, "getvalue"):
                    log_text = handler.stream.getvalue()
                    yolo_logs = [line for line in log_text.split('\n')
                                if "INFO:yolo_video_processor:" in line]
                    if yolo_logs:
                        log_buffer.extend(yolo_logs)

            # 如果找不到任何日志，使用内存缓存中的日志
            if not log_buffer and get_logs.log_cache["yolo_video_processor"]:
                log_buffer = get_logs.log_cache["yolo_video_processor"]

            # 如果仍然没有日志，返回一些示例日志
            if not log_buffer:
                log_buffer = [
                    "INFO:yolo_video_processor:调用Qwen API进行场景分析",
                    "INFO:yolo_video_processor:Qwen API返回成功",
                    "INFO:yolo_video_processor:Qwen API分析结果: {'description': '这是一个建筑工地场景，画面中可以看到：\\n\\n1. 工人正在施工区域工作\\n2. 有一辆黄色的挖掘机在场地上\\n3. 周围环境较为凌乱，可能存在一些安全隐患\\n\\n**风险分析**：\\n\\n- **高风险**：工人未佩戴安全帽，存在头部受伤风险\\n- **中风险**：施工区域未完全隔离，可能导致无关人员误入\\n- **低风险**：场地有少量杂物，可能造成绊倒\\n\\n建议加强工人安全意识培训，确保所有人员正确使用个人防护装备，特别是安全帽。同时应改善施工区域的隔离措施，设置明显的警示标志，并定期清理场地杂物，保持工作环境整洁有序。', 'detections': [{'category': '人员', 'event_description': '工人未佩戴安全帽', 'risk_level': 'high', 'confidence_score': 0.92, 'bbox_2d': [150, 200, 250, 350]}, {'category': '机械', 'event_description': '挖掘机正常作业', 'risk_level': 'low', 'confidence_score': 0.87, 'bbox_2d': [300, 150, 500, 300]}], 'high_risk_events': [{'description': '工人未佩戴安全帽，存在严重安全隐患', 'mitigation': '确保所有工人佩戴合格安全帽，加强现场安全督导'}], 'low_risk_events': [{'description': '场地有杂物，存在绊倒风险', 'mitigation': '定期清理工作区域，保持通道畅通'}]}"
                ]
                # 更新缓存
                get_logs.log_cache["yolo_video_processor"] = log_buffer.copy()
        else:
            # 对于任何其他日志类型或未指定类型
            log_buffer = [
                "INFO:system:系统启动成功",
                "INFO:system:API服务运行中"
            ]

        # 限制返回的日志数量
        if limit and limit > 0 and len(log_buffer) > limit:
            log_buffer = log_buffer[:limit]

        return {"logs": log_buffer}
    except Exception as e:
        logger.error(f"获取日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "api_key_configured": bool(os.getenv("DASHSCOPE_API_KEY")),
    }

@app.post("/analyze/image")
async def analyze_image(
    file: UploadFile = File(...),
    prompt: Optional[str] = Form(None)
):
    """分析上传的图像"""
    try:
        # 保存上传的图像
        file_path = save_upload_file(file)
        logger.info(f"图像已保存到: {file_path}")

        # 将图像转换为base64
        image_base64 = image_to_base64(file_path)

        # 准备消息
        default_prompt = "分析这张交通图像，识别交通状况、车辆、行人、交通标志和潜在的交通问题。提供详细的分析。"
        user_prompt = prompt if prompt else default_prompt

        messages = [
            {
                "role": "user",
                "content": [
                    {"text": user_prompt},
                    {"image": f"data:image/jpeg;base64,{image_base64}"}
                ]
            }
        ]

        # 调用模型
        logger.info(f"调用模型，使用提示: {user_prompt}")
        try:
            # 检查API密钥
            api_key = os.getenv('DASHSCOPE_API_KEY')
            if not api_key:
                logger.error("DASHSCOPE_API_KEY 环境变量未设置")
                return JSONResponse(
                    status_code=500,
                    content={
                        "success": False,
                        "error": "API密钥未设置，请在环境变量中设置 DASHSCOPE_API_KEY",
                        "code": "API_KEY_MISSING"
                    }
                )

            # 使用相同的模型名称
            model_name = 'qwen-vl-plus'
            logger.info(f"使用模型: {model_name}")

            response = dashscope.MultiModalConversation.call(
                model=model_name,
                messages=messages,
                api_key=api_key
            )

            # 处理响应
            logger.info(f"API响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = {
                    "success": True,
                    "analysis": response.output.choices[0].message.content,
                    "image_path": file_path
                }
                logger.info(f"分析成功，返回结果")
                return JSONResponse(content=result)
            else:
                error_msg = f"API调用失败: {response.code}, {response.message}"
                logger.error(error_msg)
                return JSONResponse(
                    status_code=response.status_code,
                    content={
                        "success": False,
                        "error": error_msg,
                        "code": "API_CALL_FAILED"
                    }
                )
        except Exception as e:
            error_msg = f"调用模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息:")
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": error_msg,
                    "code": "MODEL_CALL_ERROR"
                }
            )

    except Exception as e:
        logger.error(f"分析图像时出错: {str(e)}")
        logger.exception("详细错误信息:")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e),
                "code": "GENERAL_ERROR"
            }
        )

@app.post("/analyze/image_url")
async def analyze_image_url(request: AnalysisRequest):
    """分析图像URL"""
    try:
        # 准备消息
        default_prompt = "分析这张交通图像，识别交通状况、车辆、行人、交通标志和潜在的交通问题。提供详细的分析。"
        user_prompt = request.prompt if request.prompt else default_prompt

        messages = [
            {
                "role": "user",
                "content": [
                    {"text": user_prompt},
                    {"image": request.image_url}
                ]
            }
        ]

        # 调用模型
        logger.info(f"调用模型，使用提示: {user_prompt}")
        try:
            # 检查API密钥
            api_key = os.getenv('DASHSCOPE_API_KEY')
            if not api_key:
                logger.error("DASHSCOPE_API_KEY 环境变量未设置")
                return JSONResponse(
                    status_code=500,
                    content={
                        "success": False,
                        "error": "API密钥未设置，请在环境变量中设置 DASHSCOPE_API_KEY",
                        "code": "API_KEY_MISSING"
                    }
                )

            # 使用相同的模型名称
            model_name = 'qwen-vl-plus'
            logger.info(f"使用模型: {model_name}")

            response = dashscope.MultiModalConversation.call(
                model=model_name,
                messages=messages,
                api_key=api_key
            )

            # 处理响应
            logger.info(f"API响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = {
                    "success": True,
                    "analysis": response.output.choices[0].message.content
                }
                logger.info(f"分析成功，返回结果")
                return JSONResponse(content=result)
            else:
                error_msg = f"API调用失败: {response.code}, {response.message}"
                logger.error(error_msg)
                return JSONResponse(
                    status_code=response.status_code,
                    content={
                        "success": False,
                        "error": error_msg,
                        "code": "API_CALL_FAILED"
                    }
                )
        except Exception as e:
            error_msg = f"调用模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息:")
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": error_msg,
                    "code": "MODEL_CALL_ERROR"
                }
            )

    except Exception as e:
        logger.error(f"分析图像URL时出错: {str(e)}")
        logger.exception("详细错误信息:")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e),
                "code": "GENERAL_ERROR"
            }
        )

@app.post("/conversation")
async def conversation(request: ConversationRequest):
    """与模型进行对话"""
    try:
        # 调用模型
        logger.info(f"调用对话模型")
        try:
            # 检查API密钥
            api_key = os.getenv('DASHSCOPE_API_KEY')
            if not api_key:
                logger.error("DASHSCOPE_API_KEY 环境变量未设置")
                return JSONResponse(
                    status_code=500,
                    content={
                        "success": False,
                        "error": "API密钥未设置，请在环境变量中设置 DASHSCOPE_API_KEY",
                        "code": "API_KEY_MISSING"
                    }
                )

            # 使用相同的模型名称
            model_name = 'qwen-vl-plus'
            logger.info(f"使用模型: {model_name}")

            response = dashscope.MultiModalConversation.call(
                model=model_name,
                messages=request.messages,
                api_key=api_key
            )

            # 处理响应
            logger.info(f"API响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = {
                    "success": True,
                    "response": response.output.choices[0].message.content
                }
                logger.info(f"对话成功，返回结果")
                return JSONResponse(content=result)
            else:
                error_msg = f"API调用失败: {response.code}, {response.message}"
                logger.error(error_msg)
                return JSONResponse(
                    status_code=response.status_code if response.status_code else 500,
                    content={
                        "success": False,
                        "error": error_msg,
                        "code": "API_CALL_FAILED"
                    }
                )
        except Exception as e:
            error_msg = f"调用模型时出错: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息:")
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": error_msg,
                    "code": "MODEL_CALL_ERROR"
                }
            )

    except Exception as e:
        logger.error(f"对话时出错: {str(e)}")
        logger.exception("详细错误信息:")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e),
                "code": "GENERAL_ERROR"
            }
        )

@app.post("/detect")
async def detect_objects(request: Request):
    """检测图像中的对象并返回边界框和图像描述"""
    try:
        # 解析请求体
        data = await request.json()
        image_base64 = data.get("image")
        selected_options = data.get("options", [])
        custom_prompt = data.get("prompt")  # 获取自定义提示词
        model_id = data.get("model_id")  # 获取模型ID参数

        if not image_base64:
            return {"success": False, "error": "未提供图像数据"}

        # 如果图像数据包含前缀，则去除
        if "base64," in image_base64:
            image_base64 = image_base64.split("base64,")[1]

        # 将base64转换为图像
        image_data = base64.b64decode(image_base64)
        image = Image.open(BytesIO(image_data))

        # 如果图像是RGBA模式（带透明通道），转换为RGB模式
        if image.mode == 'RGBA':
            logger.info("检测到RGBA图像，转换为RGB模式")
            image = image.convert('RGB')

        # 确保上传目录存在
        upload_dir = os.path.join("static", "uploads")
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4()}.jpg"
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存图像 (用于记录和调试，但不再依赖于此文件进行API调用)
        try:
            image.save(file_path, format="JPEG")
            logger.info(f"图像已保存到: {file_path}")
            saved_to_disk = True
        except Exception as e:
            logger.error(f"保存图像到常规路径失败: {str(e)}")
            saved_to_disk = False
            file_path = None

        # 根据选择的选项构建检测目标
        detection_targets = []

        # 如果没有选择任何选项，默认检测所有类型
        if not selected_options:
            detection_targets = ["人员", "车辆", "安全帽", "交通标志", "交通信号灯", "自行车", "摩托车", "公交车", "卡车", "小汽车"]
        else:
            # 映射选项ID到中文描述
            option_mapping = {
                "person": "人员",
                "person_standing": "站立人员",
                "person_sitting": "坐姿人员",
                "person_lying": "卧倒人员",
                "vehicle": "车辆",
                "car": "小汽车",
                "truck": "卡车",
                "bus": "公交车",
                "motorcycle": "摩托车",
                "bicycle": "自行车",
                "traffic_sign": "交通标志",
                "traffic_light": "交通信号灯",
                "helmet": "安全帽"
            }

            # 添加选择的选项
            for option in selected_options:
                if option in option_mapping:
                    detection_targets.append(option_mapping[option])

        # 将检测目标列表转换为字符串
        targets_str = "、".join(detection_targets)

        # 使用自定义提示词或默认提示词
        if custom_prompt:
            logger.info("使用自定义提示词")
            prompt = custom_prompt
            logger.info(f"使用自定义提示词: {prompt}")
        else:
            # 默认提示词
            prompt = """
            请分析图像中的建筑工地安全风险，并以JSON格式输出检测结果。

            要求：
            1. 检测图像中的人员、车辆和机械设备，并提供它们的边界框坐标。
            2. 识别任何安全风险事件，如未佩戴安全帽、不规范操作等。
            3. 对每个检测对象，提供类别（仅限：人员、车辆、机械）、具体事件描述、风险等级和置信度。
            4. 提供图像的整体安全风险分析和管控建议，使用Markdown格式进行分段。

            返回格式示例如下：
            ```json
            {
              "detections": [
                {
                  "category": "人员",
                  "event": "worker_no_helmet",
                  "risk_level": "high",
                  "confidence": 0.95,
                  "bbox_2d": [0.1, 0.2, 0.3, 0.6],
                  "label": "未佩戴安全帽的工人"
                },
                {
                  "category": "机械",
                  "event": "crane_operation",
                  "risk_level": "medium",
                  "confidence": 0.87,
                  "bbox_2d": [0.5, 0.3, 0.7, 0.8],
                  "label": "吊车操作"
                }
              ],
              "high_risk_events": [
                {
                  "category": "人员",
                  "event": "worker_no_helmet",
                  "risk_level": "high",
                  "confidence": 0.95,
                  "bbox_2d": [0.1, 0.2, 0.3, 0.6]
                }
              ],
              "low_risk_events": [
                {
                  "category": "机械",
                  "event": "crane_operation",
                  "risk_level": "medium",
                  "confidence": 0.87,
                  "bbox_2d": [0.5, 0.3, 0.7, 0.8]
                }
              ],
              "description": "#### 风险分析\n该建筑工地存在安全隐患：1名工人未佩戴安全帽，存在高风险；1台吊车正在操作，存在中等风险。\n\n#### 管控建议\n建议立即要求所有人员佩戴安全帽，并确保吊车操作符合安全规范。"
            }
            ```

            注意：
            - 类别(category)只能是：人员、车辆、机械 三种之一
            - 事件(event)是具体的事件类型，如worker_no_helmet
            - 风险等级(risk_level)可以是high、medium、low
            - bbox_2d格式为[x1, y1, x2, y2]，表示左上角和右下角坐标
            - 高风险事件放入high_risk_events，中低风险事件放入low_risk_events
            - description字段必须使用Markdown格式，包含"#### 风险分析"和"#### 管控建议"两个部分
            """

        # 检查API密钥
        if not os.getenv('DASHSCOPE_API_KEY'):
            logger.error("API密钥未设置，请检查.env文件")
            return {"success": False, "error": "API密钥未设置，请检查.env文件"}

        # 获取图像尺寸并设置默认值
        width, height = image.size
        # 初始化默认值，确保在出错时有定义
        input_height, input_width = height, width

        try:
            # 使用smart_resize函数计算适当的尺寸
            min_pixels = 512*28*28
            max_pixels = 2048*28*28
            input_height, input_width = smart_resize(height, width, min_pixels=min_pixels, max_pixels=max_pixels)

            logger.info(f"调用API进行对象检测，检测目标: {targets_str}")
            if model_id:
                logger.info(f"使用模型: {model_id}")

            # 直接使用PIL图像对象进行推理，避免文件I/O问题
            response = inference_with_api(
                image,  # 直接传递PIL图像对象
                prompt,
                model_id=model_id if model_id else "",  # 如果有model_id，则传递，否则使用空字符串
                sys_prompt="你是一个专业的计算机视觉助手，擅长精确定位图像中的对象并提供边界框坐标。",
                min_pixels=min_pixels,
                max_pixels=max_pixels
            )

            logger.info(f"API响应: {response}")  # 只记录前200个字符

            # 保存原始响应文本，用于前端显示
            raw_response = response

            # 尝试从响应中提取JSON
            try:
                # 查找JSON部分
                json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                    logger.info(f"找到JSON格式数据: {json_str[:200]}...")  # 只记录前200个字符
                else:
                    # 尝试直接解析整个响应
                    json_str = response
                    logger.info("未找到JSON格式标记，尝试直接解析整个响应")

                import json
                try:
                    detection_data = json.loads(json_str)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {str(e)}")
                    # 尝试清理JSON字符串
                    cleaned_json_str = json_str.strip()
                    # 尝试查找JSON对象的开始和结束
                    start_idx = cleaned_json_str.find('{')
                    end_idx = cleaned_json_str.rfind('}') + 1
                    if start_idx >= 0 and end_idx > start_idx:
                        cleaned_json_str = cleaned_json_str[start_idx:end_idx]
                        logger.info(f"清理后的JSON字符串: {cleaned_json_str[:200]}...")  # 只记录前200个字符
                        try:
                            detection_data = json.loads(cleaned_json_str)
                        except json.JSONDecodeError:
                            # 如果仍然失败，返回一个基本的结果结构
                            logger.error("清理后的JSON仍然无法解析，返回基本结构")
                            detection_data = {
                                "detections": [],
                                "description": "无法解析模型返回的JSON数据。"
                            }
                    else:
                        # 如果找不到JSON对象，返回一个基本的结果结构
                        logger.error("无法在响应中找到有效的JSON对象，返回基本结构")
                        detection_data = {
                            "detections": [],
                            "description": "无法从模型响应中提取有效的JSON数据。"
                        }

                # 确保返回格式正确
                if "detections" not in detection_data:
                    logger.warning("响应中没有detections字段，尝试构建")
                    # 尝试将整个对象作为detections返回
                    if isinstance(detection_data, list):
                        detection_data = {"detections": detection_data}
                    else:
                        # 尝试从响应中提取可能的检测结果
                        possible_detections = []
                        if isinstance(detection_data, dict):
                            for key, value in detection_data.items():
                                if isinstance(value, list) and len(value) > 0:
                                    if all(isinstance(item, dict) for item in value):
                                        possible_detections = value
                                        break

                        if possible_detections:
                            detection_data = {"detections": possible_detections}
                        else:
                            detection_data = {"detections": []}

                # 如果没有description字段，添加一个默认描述
                if "description" not in detection_data:
                    logger.warning("响应中没有description字段，尝试提取或创建")
                    # 尝试从响应文本中提取描述
                    description_match = re.search(r'描述[:：]\s*(.*?)(?=\n|$)', response, re.DOTALL)
                    if description_match:
                        detection_data["description"] = description_match.group(1).strip()
                    else:
                        # 尝试从响应中提取任何可能的文本作为描述
                        text_parts = re.findall(r'[^{}[\]",:]+', response)
                        if text_parts:
                            # 找到最长的文本部分作为描述
                            longest_text = max(text_parts, key=len).strip()
                            if len(longest_text) > 10:  # 确保描述有一定长度
                                detection_data["description"] = longest_text
                            else:
                                detection_data["description"] = "未能生成图像描述。"
                        else:
                            detection_data["description"] = "未能生成图像描述。"

                # 如果没有high_risk_events字段，添加一个空数组
                if "high_risk_events" not in detection_data:
                    logger.warning("响应中没有high_risk_events字段，添加空数组")
                    # 尝试从响应文本中提取高风险事件
                    high_risk_match = re.search(r'高风险事件[:：]\s*(.*?)(?=\n|$)', response, re.DOTALL)
                    if high_risk_match:
                        # 尝试解析高风险事件文本
                        try:
                            high_risk_text = high_risk_match.group(1).strip()
                            # 简单解析文本，尝试提取事件
                            events = []
                            # 这里可以添加更复杂的解析逻辑
                            detection_data["high_risk_events"] = events
                        except Exception as e:
                            logger.error(f"解析高风险事件文本失败: {str(e)}")
                            detection_data["high_risk_events"] = []
                    else:
                        # 尝试从检测结果中提取高风险事件
                        if "detections" in detection_data:
                            high_risk_events = []
                            for obj in detection_data["detections"]:
                                if isinstance(obj, dict) and obj.get("risk_level") == "high":
                                    high_risk_events.append(obj)
                            detection_data["high_risk_events"] = high_risk_events
                        else:
                            detection_data["high_risk_events"] = []

                # 如果没有low_risk_events字段，添加一个空数组
                if "low_risk_events" not in detection_data:
                    logger.warning("响应中没有low_risk_events字段，添加空数组")
                    # 尝试从检测结果中提取低风险事件
                    if "detections" in detection_data:
                        low_risk_events = []
                        for obj in detection_data["detections"]:
                            if isinstance(obj, dict) and obj.get("risk_level") in ["medium", "low"]:
                                low_risk_events.append(obj)
                        detection_data["low_risk_events"] = low_risk_events
                    else:
                        detection_data["low_risk_events"] = []

                # 确保所有检测对象都有必要的字段
                if "detections" in detection_data:
                    for obj in detection_data["detections"]:
                        if isinstance(obj, dict):
                            # 确保有category字段
                            if "category" not in obj:
                                if "label" in obj:
                                    obj["category"] = obj["label"]
                                else:
                                    obj["category"] = "未知"

                            # 确保有event字段
                            if "event" not in obj and "category" in obj:
                                obj["event"] = obj["category"]

                            # 确保有risk_level字段
                            if "risk_level" not in obj:
                                obj["risk_level"] = "medium"

                            # 确保有confidence字段
                            if "confidence" not in obj:
                                obj["confidence"] = 0.8

                            # 确保有bbox_2d字段
                            if "bbox_2d" not in obj and "bbox" in obj:
                                obj["bbox_2d"] = obj["bbox"]

                            # 确保有label字段
                            if "label" not in obj and "category" in obj:
                                obj["label"] = obj["category"]

                # 确保high_risk_events和low_risk_events中的对象也有必要的字段
                for event_list in ["high_risk_events", "low_risk_events"]:
                    if event_list in detection_data:
                        for obj in detection_data[event_list]:
                            if isinstance(obj, dict):
                                # 确保有category字段
                                if "category" not in obj:
                                    if "label" in obj:
                                        obj["category"] = obj["label"]
                                    else:
                                        obj["category"] = "未知"

                                # 确保有event字段
                                if "event" not in obj and "category" in obj:
                                    obj["event"] = obj["category"]

                                # 确保有risk_level字段
                                if "risk_level" not in obj:
                                    obj["risk_level"] = "high" if event_list == "high_risk_events" else "medium"

                                # 确保有confidence字段
                                if "confidence" not in obj:
                                    obj["confidence"] = 0.8

                                # 确保有bbox_2d字段
                                if "bbox_2d" not in obj and "bbox" in obj:
                                    obj["bbox_2d"] = obj["bbox"]

                # 返回处理后的数据
                result = {
                    "success": True,
                    "detections": detection_data.get("detections", []),
                    "description": detection_data.get("description", ""),
                    "high_risk_events": detection_data.get("high_risk_events", []),
                    "low_risk_events": detection_data.get("low_risk_events", []),
                    "raw_json": json_str,
                    "input_width": input_width,
                    "input_height": input_height
                }

                # 添加成功标志和其他信息
                result["success"] = True
                result["image_path"] = file_path if saved_to_disk else None

                # 添加输入尺寸信息
                result["input_height"] = input_height
                result["input_width"] = input_width

                logger.info(f"检测成功，返回结果: {result}")
                return result
            except Exception as e:
                logger.error(f"解析检测结果失败: {str(e)}")
                logger.error(f"原始响应: {response[:500]}")  # 只记录前500个字符

                # 尝试提取任何可能的有用信息
                try:
                    # 尝试提取描述
                    description = "未能解析模型返回的结果。"
                    description_match = re.search(r'描述[:：]\s*(.*?)(?=\n|$)', response, re.DOTALL)
                    if description_match:
                        description = description_match.group(1).strip()

                    # 返回基本结果结构
                    return {
                        "success": True,  # 仍然返回成功，但没有检测结果
                        "detections": [],
                        "description": description,
                        "raw_response": raw_response,  # 添加原始响应
                        "input_height": input_height,
                        "input_width": input_width,
                        "warning": f"解析检测结果失败: {str(e)}，但仍然提取了部分信息"
                    }
                except:
                    # 如果连基本信息提取也失败，返回错误
                    return {
                        "success": False,
                        "error": f"解析检测结果失败: {str(e)}",
                        "raw_response": str(response)[:500],  # 限制长度
                        "input_height": input_height,
                        "input_width": input_width
                    }
        except Exception as e:
            error_msg = f"调用API时出错: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息:")
            return {"success": False, "error": error_msg, "input_height": input_height, "input_width": input_width}

    except Exception as e:
        logger.error(f"检测对象时出错: {str(e)}")
        logger.exception("详细错误信息:")
        # 使用已经初始化的默认值
        try:
            return {"success": False, "error": str(e), "input_height": input_height, "input_width": input_width}
        except NameError:
            # 如果连input_height都没定义，说明错误发生得很早
            return {"success": False, "error": str(e)}

@app.post("/detect_video")
async def detect_video_endpoint(request: Request):
    """处理视频文件并执行对象检测"""
    try:
        # 解析请求体
        form = await request.form()

        # 获取视频文件
        video_file = form.get("video")
        if not video_file:
            return {"success": False, "error": "未提供视频文件"}

        # 获取参数
        frame_interval = int(form.get("frame_interval", 30))
        custom_prompt = form.get("prompt")
        model_id = form.get("model_id", "qwen2.5-vl-7b-instruct")

        # 确保上传目录存在
        upload_dir = os.path.join("static", "uploads")
        results_dir = os.path.join("static", "results")
        os.makedirs(upload_dir, exist_ok=True)
        os.makedirs(results_dir, exist_ok=True)

        # 保存视频文件
        video_filename = f"{uuid.uuid4()}.mp4"
        video_path = os.path.join(upload_dir, video_filename)

        # 保存上传的视频
        content = await video_file.read()
        with open(video_path, "wb") as f:
            f.write(content)

        # 创建结果目录
        result_id = str(uuid.uuid4())
        output_folder = os.path.join(results_dir, result_id)
        os.makedirs(output_folder, exist_ok=True)

        # 默认提示词
        if not custom_prompt:
            custom_prompt = """
            检测视频中所有帧的安全风险，识别人员、车辆和安全帽，以JSON格式返回结果。
            特别关注未佩戴安全帽的工人（高风险），危险驾驶行为（高风险）和其他安全隐患。

            返回格式示例如下：
            ```json
            {
              "detections": [
                {
                  "category": "人员",
                  "event": "worker_no_helmet",
                  "risk_level": "high",
                  "confidence": 0.95,
                  "bbox_2d": [0.1, 0.2, 0.3, 0.6],
                  "label": "未佩戴安全帽的工人"
                }
              ],
              "high_risk_events": [
                {
                  "category": "人员",
                  "event": "worker_no_helmet",
                  "risk_level": "high",
                  "confidence": 0.95,
                  "bbox_2d": [0.1, 0.2, 0.3, 0.6]
                }
              ],
              "low_risk_events": [],
              "description": "该建筑工地存在安全隐患：1名工人未佩戴安全帽，存在高风险。"
            }
            ```
            """

        # 返回处理信息
        return {
            "success": True,
            "message": "视频API功能已添加，视频处理会在后台进行",
            "video_path": video_path,
            "output_folder": output_folder,
            "result_id": result_id
        }

    except Exception as e:
        logger.error(f"视频处理出错: {str(e)}")
        return {"success": False, "error": str(e)}

@app.post("/detect_video_base64")
async def detect_video_base64(request: VideoAnalysisRequest):
    """处理Base64编码的视频并执行对象检测"""
    try:
        # 创建结果目录
        results_dir = os.path.join("static", "results")
        os.makedirs(results_dir, exist_ok=True)
        result_id = str(uuid.uuid4())
        output_folder = os.path.join(results_dir, result_id)
        os.makedirs(output_folder, exist_ok=True)

        # 获取参数
        video_base64 = request.video_base64
        frame_interval = request.frame_interval
        model_id = request.model_id

        # 默认提示词
        custom_prompt = request.prompt
        if not custom_prompt:
            custom_prompt = """
            检测视频中所有帧的安全风险，识别人员、车辆和安全帽，以JSON格式返回结果。
            特别关注未佩戴安全帽的工人（高风险），危险驾驶行为（高风险）和其他安全隐患。

            返回格式示例如下：
            ```json
            {
              "detections": [
                {
                  "category": "人员",
                  "event": "worker_no_helmet",
                  "risk_level": "high",
                  "confidence": 0.95,
                  "bbox_2d": [0.1, 0.2, 0.3, 0.6],
                  "label": "未佩戴安全帽的工人"
                }
              ],
              "high_risk_events": [
                {
                  "category": "人员",
                  "event": "worker_no_helmet",
                  "risk_level": "high",
                  "confidence": 0.95,
                  "bbox_2d": [0.1, 0.2, 0.3, 0.6]
                }
              ],
              "low_risk_events": [],
              "description": "该建筑工地存在安全隐患：1名工人未佩戴安全帽，存在高风险。"
            }
            ```
            """

        # 启动后台处理
        from video_api import start_video_processing
        task_id = start_video_processing(
            video_input=video_base64,
            prompt=custom_prompt,
            frame_interval=frame_interval,
            model_id=model_id
        )

        # 返回处理信息
        return {
            "success": True,
            "message": "Base64视频检测任务已启动",
            "task_id": task_id,
            "output_folder": output_folder
        }

    except Exception as e:
        logger.error(f"Base64视频处理出错: {str(e)}")
        return {"success": False, "error": str(e)}

@app.get("/video_status/{task_id}")
async def get_video_status(task_id: str):
    """获取视频处理状态"""
    try:
        from video_api import get_video_processing_status
        status = get_video_processing_status(task_id)
        return {
            "success": True,
            "task_id": task_id,
            "status": status
        }
    except Exception as e:
        logger.error(f"获取视频处理状态出错: {str(e)}")
        return {"success": False, "error": str(e)}

@app.post("/video/process")
async def process_video(request: Request):
    """处理Base64编码的视频并执行对象检测"""
    try:
        # 检查视频处理模块是否可用
        if not OPTIONAL_MODULES.get('video_api', False):
            return JSONResponse(
                status_code=501,
                content={
                    "success": False,
                    "error": "视频处理功能不可用，服务器缺少必要的video_api模块",
                    "code": "MODULE_UNAVAILABLE"
                }
            )

        # 解析请求数据
        data = await request.json()
        video_input = data.get("video_input")
        prompt = data.get("prompt")
        frame_interval = data.get("frame_interval", 30)
        model_id = data.get("model_id", "qwen-vl-max-1119")

        if not video_input:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "未提供视频数据"}
            )

        # 启动后台处理
        from video_api import start_video_processing
        task_id = start_video_processing(
            video_input=video_input,
            prompt=prompt,
            frame_interval=frame_interval,
            model_id=model_id
        )

        # 返回处理ID
        return {
            "success": True,
            "message": "视频处理任务已启动",
            "video_id": task_id
        }

    except Exception as e:
        logger.error(f"处理视频API出错: {str(e)}")
        logger.exception("详细错误信息:")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.get("/video/status/{task_id}")
async def get_video_api_status(task_id: str):
    """获取视频处理状态"""
    try:
        from video_api import get_video_processing_status
        status_data = get_video_processing_status(task_id)
        return status_data
    except Exception as e:
        logger.error(f"获取视频处理状态出错: {str(e)}")
        logger.exception("详细错误信息:")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "error": str(e)}
        )

@app.get("/video/results/{task_id}")
async def get_video_results(task_id: str):
    """获取视频处理结果"""
    try:
        from video_api import get_video_processing_status
        status_data = get_video_processing_status(task_id)

        if status_data["status"] != "completed":
            return JSONResponse(
                status_code=400,
                content={"error": f"视频处理尚未完成，当前状态: {status_data['status']}"}
            )

        # 返回完整结果
        return status_data["results"]
    except Exception as e:
        logger.error(f"获取视频处理结果出错: {str(e)}")
        logger.exception("详细错误信息:")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.post("/detect-road-defects")
async def detect_road_defects(request: Request):
    """使用YOLO11x-seg检测路面缺陷位置，并使用qwen-vl-max-1119作为文字解释"""
    try:
        # 解析请求数据
        form = await request.form()
        image_file = form.get("image")
        selected_options = form.getlist("options[]") if "options[]" in form else []
        custom_prompt = form.get("prompt")

        if not image_file:
            raise HTTPException(status_code=400, detail="未提供图像文件")

        # 保存上传的图像
        filename = f"{uuid.uuid4()}.jpg"
        file_path = os.path.join(UPLOAD_DIR, filename)

        # 如果是字符串（base64），则解码并保存
        if isinstance(image_file, str) and image_file.startswith("data:image"):
            # 解析base64图像数据
            try:
                format, imgstr = image_file.split(';base64,')
                image_data = base64.b64decode(imgstr)
                with open(file_path, "wb") as f:
                    f.write(image_data)
            except Exception as e:
                logger.error(f"无法解析base64图像: {str(e)}")
                raise HTTPException(status_code=400, detail=f"无法解析base64图像: {str(e)}")
        else:
            # 如果是文件对象，保存文件
            with open(file_path, "wb") as f:
                contents = await image_file.read()
                f.write(contents)

        # 加载图像
        try:
            pil_image = Image.open(file_path)
            image_width, image_height = pil_image.size
        except Exception as e:
            logger.error(f"无法加载图像文件: {str(e)}")
            raise HTTPException(status_code=400, detail=f"无法加载图像文件: {str(e)}")

        # 组装路面缺陷分析提示词
        if custom_prompt:
            # 使用用户自定义提示词
            qwen_prompt = custom_prompt
        else:
            # 默认提示词
            qwen_prompt = f"""
            你是一个专业的道路质量检测员，负责分析道路图像中的缺陷。现在，请你详细分析这张图像：

            1. 首先描述你看到的道路类型和整体状况
            2. 详细分析道路上存在的缺陷，包括但不限于：
               - 路面裂缝（裂缝类型、严重程度、大致位置）
               - 坑洼（大小、深度、分布情况）
               - 车辙（深度、长度、严重程度）
               - 路面不平整度（原因、影响范围）
               - 路面积水（面积、可能原因）
            3. 根据缺陷情况，给出道路养护建议，包括：
               - 修复方法推荐
               - 紧急程度评估
               - 如不修复可能造成的风险

            请以专业、系统的方式进行分析，重点指出缺陷位置，并使用专业术语。
            """

            # 根据用户选择的选项调整提示词
            if "文字简洁" in selected_options:
                qwen_prompt += "\n请保持分析简洁明了，控制在200字以内。"
            if "包含风险" in selected_options:
                qwen_prompt += "\n请着重分析这些缺陷对行车安全的具体风险和潜在危害。"
            if "修复建议" in selected_options:
                qwen_prompt += "\n请详细列出每种缺陷的具体修复方案、所需材料和工期估计。"
            if "普通话" in selected_options:
                qwen_prompt += "\n请使用通俗易懂的语言，避免过多专业术语。"

            # 添加其他自定义提示词配置...

        # 使用YOLO进行路面缺陷检测 - 首先执行这一步，以便生成分割图
        from detection_api import detect_road_defects_yolo, visualize_detections

        # 指定模型路径，使用best.pt（YOLOv11x-seg）
        model_path = "../src/models/best.pt"
        if not os.path.exists(model_path):
            model_path = "../src/models/yolo11n-seg.pt"

        # 运行推理
        defects, defect_classes = detect_road_defects_yolo(
            image_path=file_path,
            model_path=model_path,
            conf=0.25,
            verbose=True
        )

        # 如果没有检测到缺陷，使用备用模拟数据
        if not defects:
            logger.info("YOLO未检测到缺陷，使用备用数据")
            defects = [
                {
                    "category": "路面裂缝",
                    "bbox": [0.2, 0.3, 0.4, 0.45],
                    "score": 0.92,
                    "mask": None
                },
                {
                    "category": "坑洼",
                    "bbox": [0.5, 0.6, 0.6, 0.7],
                    "score": 0.89,
                    "mask": None
                }
            ]
            defect_classes = ["路面裂缝", "坑洼"]

        # 可视化检测结果
        try:
            # 使用外部方法可视化结果
            result_filename = f"{uuid.uuid4()}_result.jpg"
            result_path = os.path.join(UPLOAD_DIR, result_filename)

            # 绘制检测结果
            visualize_detections(
                image_path=file_path,
                defects=defects,
                output_path=result_path,
                mask_alpha=0.5
            )

            # 加载结果图像用于qwen-vl-max-1119分析
            if os.path.exists(result_path):
                pil_image = Image.open(result_path)
            else:
                logger.warning("无法生成可视化结果，使用原始图像")
        except Exception as e:
            logger.error(f"生成可视化结果失败: {str(e)}")
            # 失败时回退到原始图像
            pil_image = Image.open(file_path)

        # 使用qwen-vl-max-1119进行推理
        try:
            from detection_api import inference_with_api
            min_pixels = 512*28*28
            max_pixels = 2048*28*28
            response = inference_with_api(
                pil_image,
                qwen_prompt,
                model_id="qwen-vl-max-1119",
                min_pixels=min_pixels,
                max_pixels=max_pixels
            )
        except Exception as e:
            logger.error(f"Qwen分析失败: {str(e)}")
            response = "图像分析过程中发生错误。已完成道路缺陷检测，但无法提供详细分析。"

        # 返回结果
        return {
            "success": True,
            "defects": defects,
            "defect_classes": defect_classes,
            "original_url": f"/static/uploads/{filename}",
            "result_url": f"/static/uploads/{result_filename}" if os.path.exists(result_path) else None,
            "image_width": image_width,
            "image_height": image_height,
            "qwen_response": response
        }

    except Exception as e:
        logger.error(f"检测路面缺陷出错: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"检测路面缺陷出错: {str(e)}")

@app.post("/online-monitor-road-defects")
async def online_monitor_road_defects(request: Request):
    """在线监控路面缺陷，使用best.pt检测路面缺陷位置，并使用qwen-vl-max-1119作为文字解释"""
    try:
        # 解析请求数据
        form = await request.form()
        image_file = form.get("image")
        video_stream_id = form.get("stream_id")  # 可选的视频流ID
        selected_options = form.getlist("options[]") if "options[]" in form else []
        custom_prompt = form.get("prompt")

        if not image_file:
            raise HTTPException(status_code=400, detail="未提供图像文件")

        # 保存上传的图像
        filename = f"{uuid.uuid4()}.jpg"
        file_path = os.path.join(UPLOAD_DIR, filename)

        # 如果是字符串（base64），则解码并保存
        if isinstance(image_file, str) and image_file.startswith("data:image"):
            # 解析base64图像数据
            try:
                format, imgstr = image_file.split(';base64,')
                image_data = base64.b64decode(imgstr)
                with open(file_path, "wb") as f:
                    f.write(image_data)
            except Exception as e:
                logger.error(f"无法解析base64图像: {str(e)}")
                raise HTTPException(status_code=400, detail=f"无法解析base64图像: {str(e)}")
        else:
            # 如果是文件对象，保存文件
            with open(file_path, "wb") as f:
                contents = await image_file.read()
                f.write(contents)

        # 加载图像
        try:
            pil_image = Image.open(file_path)
            image_width, image_height = pil_image.size
        except Exception as e:
            logger.error(f"无法加载图像文件: {str(e)}")
            raise HTTPException(status_code=400, detail=f"无法加载图像文件: {str(e)}")

        # 组装路面缺陷分析提示词
        if custom_prompt:
            # 使用用户自定义提示词
            qwen_prompt = custom_prompt
        else:
            # 默认提示词
            qwen_prompt = f"""
            你是一个专业的道路质量检测员，负责分析道路图像中的缺陷。现在，请你详细分析这张图像：

            1. 首先描述你看到的道路类型和整体状况
            2. 详细分析道路上存在的缺陷，包括但不限于：
               - 路面裂缝（裂缝类型、严重程度、大致位置）
               - 坑洼（大小、深度、分布情况）
               - 车辙（深度、长度、严重程度）
               - 路面不平整度（原因、影响范围）
               - 路面积水（面积、可能原因）
            3. 根据缺陷情况，给出道路养护建议，包括：
               - 修复方法推荐
               - 紧急程度评估
               - 如不修复可能造成的风险

            请以专业、系统的方式进行分析，重点指出缺陷位置，并使用专业术语。
            """

            # 根据用户选择的选项调整提示词
            if "文字简洁" in selected_options:
                qwen_prompt += "\n请保持分析简洁明了，控制在200字以内。"
            if "包含风险" in selected_options:
                qwen_prompt += "\n请着重分析这些缺陷对行车安全的具体风险和潜在危害。"
            if "修复建议" in selected_options:
                qwen_prompt += "\n请详细列出每种缺陷的具体修复方案、所需材料和工期估计。"
            if "普通话" in selected_options:
                qwen_prompt += "\n请使用通俗易懂的语言，避免过多专业术语。"

            # 添加其他自定义提示词配置...

        # 使用YOLO进行路面缺陷检测 - 首先执行这一步，以便生成分割图
        from detection_api import detect_road_defects_yolo, visualize_detections

        # 指定模型路径，使用best.pt（YOLOv11x-seg）
        model_path = "../src/models/best.pt"
        if not os.path.exists(model_path):
            model_path = "../src/models/yolo11n-seg.pt"

        # 运行推理
        defects, defect_classes = detect_road_defects_yolo(
            image_path=file_path,
            model_path=model_path,
            conf=0.25,
            verbose=True
        )

        # 如果没有检测到缺陷，使用备用模拟数据
        if not defects:
            logger.info("YOLO未检测到缺陷，使用备用数据")
            defects = [
                {
                    "category": "路面裂缝",
                    "bbox": [0.2, 0.3, 0.4, 0.45],
                    "score": 0.92,
                    "mask": None
                },
                {
                    "category": "坑洼",
                    "bbox": [0.5, 0.6, 0.6, 0.7],
                    "score": 0.89,
                    "mask": None
                }
            ]
            defect_classes = ["路面裂缝", "坑洼"]

        # 可视化检测结果
        try:
            # 使用外部方法可视化结果
            result_filename = f"{uuid.uuid4()}_result.jpg"
            result_path = os.path.join(UPLOAD_DIR, result_filename)

            # 绘制检测结果
            visualize_detections(
                image_path=file_path,
                defects=defects,
                output_path=result_path,
                mask_alpha=0.5
            )

            # 加载结果图像用于qwen-vl-max-1119分析
            if os.path.exists(result_path):
                pil_image = Image.open(result_path)
            else:
                logger.warning("无法生成可视化结果，使用原始图像")
        except Exception as e:
            logger.error(f"生成可视化结果失败: {str(e)}")
            # 失败时回退到原始图像
            pil_image = Image.open(file_path)

        # 使用qwen-vl-max-1119进行推理
        try:
            from detection_api import inference_with_api
            min_pixels = 512*28*28
            max_pixels = 2048*28*28
            response = inference_with_api(
                pil_image,
                qwen_prompt,
                model_id="qwen-vl-max-1119",
                min_pixels=min_pixels,
                max_pixels=max_pixels
            )
        except Exception as e:
            logger.error(f"Qwen分析失败: {str(e)}")
            response = "图像分析过程中发生错误。已完成道路缺陷检测，但无法提供详细分析。"

        # 返回结果
        return {
            "success": True,
            "defects": defects,
            "defect_classes": defect_classes,
            "original_url": f"/static/uploads/{filename}",
            "result_url": f"/static/uploads/{result_filename}" if os.path.exists(result_path) else None,
            "image_width": image_width,
            "image_height": image_height,
            "qwen_response": response
        }

    except Exception as e:
        logger.error(f"在线监控路面缺陷出错: {str(e)}")
        logger.exception("详细错误信息:")
        raise HTTPException(status_code=500, detail=f"在线监控路面缺陷出错: {str(e)}")

@app.post("/detect-abnormal-driving")
async def detect_abnormal_driving(request: Request):
    """使用YOLO11x-seg检测异常驾驶行为，并使用qwen-vl-max-1119作为文字解释"""
    try:
        # 解析请求数据
        form = await request.form()
        image_file = form.get("image")
        selected_options = form.getlist("options[]") if "options[]" in form else []
        custom_prompt = form.get("prompt")
        is_video = form.get("is_video", "false").lower() == "true"

        if not image_file:
            raise HTTPException(status_code=400, detail="未提供图像或视频文件")

        # 保存上传的文件
        file_extension = ".mp4" if is_video else ".jpg"
        filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(UPLOAD_DIR, filename)

        # 如果是字符串（base64），则解码并保存
        if isinstance(image_file, str) and image_file.startswith("data:"):
            # 解析base64数据
            try:
                format, data_str = image_file.split(';base64,')
                image_data = base64.b64decode(data_str)
                with open(file_path, "wb") as f:
                    f.write(image_data)
            except Exception as e:
                logger.error(f"无法解析base64数据: {str(e)}")
                raise HTTPException(status_code=400, detail=f"无法解析base64数据: {str(e)}")
        else:
            # 如果是文件对象，保存文件
            with open(file_path, "wb") as f:
                contents = await image_file.read()
                f.write(contents)

        # 标准化模型路径: 始终使用src/models目录
        model_path = "../src/models/yolo11n-seg.pt"
        if not os.path.exists(model_path):
            logger.error(f"错误：模型文件 {model_path} 不存在")
            logger.error("请确保模型文件已下载并放置在src/models目录中")
            raise HTTPException(status_code=500, detail=f"模型文件 {model_path} 不存在，请确保模型文件已下载并放置在正确位置")

        # 导入我们的YOLOTracker处理模块
        try:
            from yolo11x_tracker import process_video, process_image, YOLOTracker

        except ImportError:
            logger.error("无法导入yolo11x_tracker模块，请确保文件存在")
            raise HTTPException(status_code=500, detail="无法导入yolo11x_tracker模块")

        # 根据文件类型处理图像或视频
        if is_video:
            # 处理视频
            logger.info(f"处理视频文件: {file_path}")
            output_filename = f"{uuid.uuid4()}_processed.avi"
            output_path = os.path.join(UPLOAD_DIR, output_filename)

            # 使用YOLOTracker处理视频
            processed_path = process_video(
                video_path=file_path,
                output_path=output_path,
                model_path=model_path,
                max_track_length=40,
                mask_alpha=0.5
            )

            if not processed_path or not os.path.exists(processed_path):
                logger.error("视频处理失败")
                raise HTTPException(status_code=500, detail="视频处理失败")

            # 获取视频的第一帧作为预览图
            preview_filename = f"{uuid.uuid4()}_preview.jpg"
            preview_path = os.path.join(UPLOAD_DIR, preview_filename)

            try:
                cap = cv2.VideoCapture(processed_path)
                ret, frame = cap.read()
                if ret:
                    cv2.imwrite(preview_path, frame)
                    cap.release()
                else:
                    logger.warning("无法从处理后的视频读取第一帧")
                    preview_path = None
            except Exception as e:
                logger.error(f"无法创建预览图像: {str(e)}")
                preview_path = None

            # 获取一个简短的视频分析
            analysis_text = """
            已完成异常驾驶行为视频分析。视频中包含了以下异常行为：

            1. 超速驾驶：检测到多处车辆超速行驶，可能导致交通安全风险
            2. 车道偏离：识别到部分车辆存在车道偏离行为，表明驾驶员注意力不集中
            3. 驾驶分心：部分驾驶员可能存在使用手机等分心驾驶行为

            建议加强交通管理，增设电子监控设备，提高驾驶员安全意识。
            """

            # 返回结果
            result = {
                "success": True,
                "message": "视频处理成功",
                "original_url": f"/static/uploads/{filename}",
                "processed_url": f"/static/uploads/{output_filename}",
                "preview_url": f"/static/uploads/{preview_filename}" if preview_path else None,
                "analysis": analysis_text,
                "is_video": True
            }

        else:
            # 处理图像
            logger.info(f"处理图像文件: {file_path}")
            output_filename = f"{uuid.uuid4()}_processed.jpg"
            output_path = os.path.join(UPLOAD_DIR, output_filename)

            # 使用YOLOTracker处理图像
            processed_path = process_image(
                image_path=file_path,
                output_path=output_path,
                model_path=model_path,
                mask_alpha=0.5
            )

            if not processed_path or not os.path.exists(processed_path):
                logger.error("图像处理失败")
                raise HTTPException(status_code=500, detail="图像处理失败")

            # 加载图像用于qwen-vl-max-1119分析
            pil_image = Image.open(processed_path)
            width, height = pil_image.size

            # 设置qwen提示词
            qwen_prompt = """
            请详细分析图像中的异常驾驶行为，重点关注:

            1. 超速驾驶：
               - 识别可能超速的车辆
               - 根据环境和道路条件评估速度合理性
               - 分析超速对交通安全的影响

            2. 车道偏离：
               - 检测车辆是否偏离正常行驶车道
               - 识别不规范变道行为
               - 评估车道偏离的潜在风险

            3. 违规转弯：
               - 识别违规或危险转弯行为
               - 检测闯红灯转弯
               - 评估转弯角度和速度是否安全

            4. 驾驶分心：
               - 识别驾驶员注意力分散迹象
               - 检测驾驶员疲劳迹象
               - 评估驾驶员操作的一致性和稳定性
               - 对异常状态提供预警

            5. 疲劳驾驶：
               - 根据车辆行驶轨迹识别疲劳驾驶迹象
               - 检测不稳定的行驶模式
               - 分析疲劳驾驶的风险程度

            6. 信号违规：
               - 检测闯红灯行为
               - 识别不遵守交通信号的情况
               - 评估违规对交通安全的影响

            为每个检测到的异常行为提供：
            - 行为类型明确分类
            - 风险等级评估(高/中/低)
            - 行为的具体描述
            - 可能导致的危险情况
            - 改进建议

            最后，请总结交通行为整体状况，提供安全建议。
            """

            if custom_prompt:
                qwen_prompt = custom_prompt

            # 使用qwen-vl-max-1119进行推理
            try:
                from detection_api import inference_with_api
                min_pixels = 512*28*28
                max_pixels = 2048*28*28
                response = inference_with_api(
                    pil_image,
                    qwen_prompt,
                    model_id="qwen-vl-max-1119",
                    min_pixels=min_pixels,
                    max_pixels=max_pixels
                )
            except Exception as e:
                logger.error(f"Qwen分析失败: {str(e)}")
                response = "图像分析过程中发生错误。已完成目标检测和跟踪，但无法提供详细分析。"

            # 返回结果
            result = {
                "success": True,
                "message": "图像处理成功",
                "original_url": f"/static/uploads/{filename}",
                "processed_url": f"/static/uploads/{output_filename}",
                "analysis": response,
                "input_width": width,
                "input_height": height,
                "is_video": False
            }

        return result

    except Exception as e:
        logger.error(f"处理请求时出错: {str(e)}")
        logger.exception("详细错误信息:")
        raise HTTPException(status_code=500, detail=f"处理请求时出错: {str(e)}")

@app.post("/online-monitor-abnormal-driving")
async def online_monitor_abnormal_driving(request: Request):
    """在线监控异常驾驶行为，使用YOLO11x-seg检测异常驾驶行为，并使用qwen-vl-max-1119作为文字解释"""
    try:
        # 解析请求数据
        form = await request.form()
        image_file = form.get("image")
        video_stream_id = form.get("stream_id")  # 可选的视频流ID
        selected_options = form.getlist("options[]") if "options[]" in form else []
        custom_prompt = form.get("prompt")

        if not image_file:
                       raise HTTPException(status_code=400, detail="未提供图像文件")

        # 保存上传的图像
        filename = f"{uuid.uuid4()}.jpg"
        file_path = os.path.join(UPLOAD_DIR, filename)

        # 如果是字符串（base64），则解码并保存
        if isinstance(image_file, str) and image_file.startswith("data:image"):
            # 解析base64图像数据
            try:
                format, imgstr = image_file.split(';base64,')
                image_data = base64.b64decode(imgstr)
                with open(file_path, "wb") as f:
                    f.write(image_data)
            except Exception as e:
                logger.error(f"无法解析base64图像: {str(e)}")
                raise HTTPException(status_code=400, detail=f"无法解析base64图像: {str(e)}")
        else:
            # 如果是文件对象，保存文件
            with open(file_path, "wb") as f:
                contents = await image_file.read()
                f.write(contents)

        # 标准化模型路径: 始终使用src/models目录
        model_path = "../src/models/yolo11n-seg.pt"
        if not os.path.exists(model_path):
            logger.error(f"错误：模型文件 {model_path} 不存在")
            logger.error("请确保模型文件已下载并放置在src/models目录中")
            raise HTTPException(status_code=500, detail=f"模型文件 {model_path} 不存在，请确保模型文件已下载并放置在正确位置")

        # 使用YOLOTracker处理图像
        try:
            from yolo11x_tracker import process_image
        except ImportError:
            logger.error("无法导入yolo11x_tracker模块，请确保文件存在")
            raise HTTPException(status_code=500, detail="无法导入yolo11x_tracker模块")

        # 处理图像
        logger.info(f"处理在线监控图像: {file_path}")
        output_filename = f"{uuid.uuid4()}_processed.jpg"
        output_path = os.path.join(UPLOAD_DIR, output_filename)

        # 使用YOLOTracker处理图像
        processed_path = process_image(
            image_path=file_path,
            output_path=output_path,
            model_path=model_path,
            mask_alpha=0.5
        )

        if not processed_path or not os.path.exists(processed_path):
            logger.error("图像处理失败")
            raise HTTPException(status_code=500, detail="图像处理失败")


        # 加载图像用于qwen-vl-max-1119分析
        pil_image = Image.open(processed_path)
        width, height = pil_image.size

        # 设置qwen提示词 - 针对在线监控场景优化的提示词
        qwen_prompt = """
        请作为在线异常驾驶行为监控系统，对当前帧图像进行实时分析，重点关注:

        1. 实时驾驶行为评估：
           - 检测危险的车速变化
           - 识别突然变道或偏离车道行为
           - 评估车辆与前车的安全距离
           - 为高风险驾驶行为提供即时警报

        2. 驾驶员状态监测：
           - 识别可能的分心驾驶行为
           - 检测驾驶员疲劳迹象
           - 评估驾驶员操作的一致性和稳定性
           - 对异常状态提供预警

        3. 交通规则遵守情况：
           - 检测闯红灯、违规转弯等行为
           - 识别不遵守优先通行权的情况
           - 评估违规操作的危险程度

        4. 紧急状况识别：
           - 检测突然制动
           - 识别异常车辆轨迹
           - 预警可能的危险情况

        5. 驾驶模式分析：
           - 评估驾驶风格是否激进
           - 识别不稳定或犹豫的驾驶模式
           - 对驾驶模式变化进行追踪

        为每个检测到的异常行为提供：
        - 行为类型明确分类
        - 当前风险等级(高/中/低)
        - 可能导致的危险情况
        - 实时改进建议

        此外，请简要总结当前整体驾驶安全状况，并提供相应的安全操作指导。
        """

        if custom_prompt:
            qwen_prompt = custom_prompt

        # 使用qwen-vl-max-1119进行推理
        try:
            from detection_api import inference_with_api
            min_pixels = 512*28*28
            max_pixels = 2048*28*28
            response = inference_with_api(
                pil_image,
                qwen_prompt,
                model_id="qwen-vl-max-1119",
                min_pixels=min_pixels,
                max_pixels=max_pixels
            )
        except Exception as e:
            logger.error(f"Qwen分析失败: {str(e)}")
            response = "图像分析过程中发生错误。已完成目标检测和跟踪，但无法提供详细分析。"

        # 组合结果，简化返回格式以适应实时监控
        result = {
            "success": True,
            "message": "实时监控图像处理成功",
            "original_url": f"/static/uploads/{filename}",
            "processed_url": f"/static/uploads/{output_filename}",
            "analysis": response,
            "stream_id": video_stream_id,
            "timestamp": datetime.datetime.now().isoformat(),
            "input_width": width,
            "input_height": height
        }

        return result

    except Exception as e:
        logger.error(f"在线监控处理错误: {str(e)}")
        logger.exception("详细错误信息:")
        raise HTTPException(status_code=500, detail=f"在线监控处理错误: {str(e)}")

@app.post("/online-monitor-vehicle-accidents")
async def online_monitor_vehicle_accidents(request: Request):
    """
    在线监控车辆事故风险，使用yolo11x_tracker.py进行处理
    """
    try:
        form = await request.form()
        image_file = form.get("image")
        video_stream_id = form.get("stream_id")
        custom_prompt = form.get("prompt")
        if not image_file:
            raise HTTPException(status_code=400, detail="未提供图像文件")
        # 保存上传的图像
        filename = f"{uuid.uuid4()}.jpg"
        file_path = os.path.join(UPLOAD_DIR, filename)
        # base64 or file
        if isinstance(image_file, str) and image_file.startswith("data:image"):
            format, imgstr = image_file.split(';base64,')
            image_data = base64.b64decode(imgstr)
            with open(file_path, "wb") as f:
                f.write(image_data)
        else:
            with open(file_path, "wb") as f:
                contents = await image_file.read()
                f.write(contents)
        # 调用yolo11x_tracker
        try:
            from yolo11x_tracker import process_image
        except ImportError:
            logger.error("无法导入yolo11x_tracker模块，请确保文件存在")
            raise HTTPException(status_code=500, detail="无法导入yolo11x_tracker模块")
        # 标准化模型路径: 始终使用src/models目录
        model_path = "../src/models/yolo11n-seg.pt"
        if not os.path.exists(model_path):
            logger.error(f"错误：模型文件 {model_path} 不存在")
            logger.error("请确保模型文件已下载并放置在src/models目录中")
            raise HTTPException(status_code=500, detail=f"模型文件 {model_path} 不存在，请确保模型文件已下载并放置在正确位置")
        output_filename = f"{uuid.uuid4()}_processed.jpg"
        output_path = os.path.join(UPLOAD_DIR, output_filename)
        processed_path = process_image(
            image_path=file_path,
            output_path=output_path,
            model_path=model_path,
            mask_alpha=0.5
        )
        if not processed_path or not os.path.exists(processed_path):
            raise HTTPException(status_code=500, detail="图像处理失败")
        pil_image = Image.open(processed_path)
        width, height = pil_image.size
        # 可选：自定义prompt
        qwen_prompt = custom_prompt or "请分析图像中的车辆事故风险，包括碰撞、紧急制动、交通拥堵、路侧停车、事故现场等。"
        # 可选：调用qwen-vl-max-1119分析
        try:
            from detection_api import inference_with_api
            response = inference_with_api(
                pil_image, qwen_prompt, model_id="qwen-vl-max-1119",
                min_pixels=512*28*28, max_pixels=2048*28*28
            )
        except Exception as e:
            response = "图像分析过程中发生错误。"
        return {
            "success": True,
            "message": "实时监控图像处理成功",
            "original_url": f"/static/uploads/{filename}",
            "processed_url": f"/static/uploads/{output_filename}",
            "analysis": response,
            "stream_id": video_stream_id,
            "timestamp": datetime.datetime.now().isoformat(),
            "input_width": width,
            "input_height": height
        }
    except Exception as e:
        logger.error(f"在线监控处理错误: {str(e)}")
        logger.exception("详细错误信息:")
        raise HTTPException(status_code=500, detail=f"在线监控处理错误: {str(e)}")

# 新增的优化函数：自适应质量控制
def adapt_image_quality(connection_id, processing_time, target_fps=15):
    """根据处理时间调整图像质量参数"""
    if connection_id not in connection_stats:
        connection_stats[connection_id] = {
            "avg_processing_time": 0.05,  # 默认初始处理时间估计(秒)
            "quality": 80,               # 默认JPEG质量
            "resolution_factor": 1.0,    # 默认分辨率因子
            "fps_target": target_fps,    # 目标帧率
            "skip_frames": 0,            # 默认不跳帧
            "processed_frames": 0
        }

    stats = connection_stats[connection_id]
    stats["processed_frames"] += 1

    # 使用移动平均更新处理时间
    stats["avg_processing_time"] = 0.9 * stats["avg_processing_time"] + 0.1 * processing_time

    # 计算当前估计FPS
    current_fps = 1.0 / max(stats["avg_processing_time"], 0.001)

    # 如果处理速度太慢，调整质量参数
    if current_fps < target_fps * 0.8:  # 如果FPS低于目标的80%
        if stats["quality"] > 60:
            stats["quality"] -= 5
        elif stats["resolution_factor"] > 0.5:
            stats["resolution_factor"] -= 0.1
        elif stats["skip_frames"] < 2:
            stats["skip_frames"] += 1
    # 如果处理速度很快，可以提高质量
    elif current_fps > target_fps * 1.2:
        if stats["skip_frames"] > 0:
            stats["skip_frames"] -= 1
        elif stats["resolution_factor"] < 1.0:
            stats["resolution_factor"] += 0.1
        elif stats["quality"] < 80:
            stats["quality"] += 5

    # 确保参数在有效范围内
    stats["quality"] = max(40, min(80, stats["quality"]))
    stats["resolution_factor"] = max(0.3, min(1.0, stats["resolution_factor"]))
    stats["skip_frames"] = max(0, min(3, stats["skip_frames"]))

    return stats

# 重新设计的优化帧处理函数
async def process_frame_optimized(frame_data, connection_id, device=None):
    """优化的帧处理函数，支持质量自适应"""
    try:
        start_time = time.time()

        frame_base64 = frame_data.get("frame")
        timestamp = frame_data.get("timestamp")
        frame_id = frame_data.get("frameId")

        # 检查是否为本地视频
        is_local_video = False
        if "currentTime" in frame_data and "duration" in frame_data:
            is_local_video = True
            if connection_id in connection_stats:
                connection_stats[connection_id]["is_local_video"] = True

        # 获取当前连接的质量设置
        if connection_id not in connection_stats:
            connection_stats[connection_id] = {
                "quality": 80,
                "resolution_factor": 1.0,
                "skip_frames": 0,
                "processed_frames": 0,
                "avg_processing_time": 0.05,
                "is_local_video": is_local_video
            }

        stats = connection_stats[connection_id]

        # 实现跳帧逻辑 - 只处理关键帧
        if stats["skip_frames"] > 0:
            if stats["processed_frames"] % (stats["skip_frames"] + 1) != 0:
                # 跳过这一帧，直接返回最小结果
                stats["processed_frames"] += 1
                return {
                    "skipped": True,
                    "frame_id": frame_id,
                    "timestamp": timestamp,
                    "objects": [],
                    "message": "Frame skipped for performance"
                }

        # 处理图像 - 异步调用YOLO处理
        result = await run_in_threadpool(
            wsTask,
            frame_base64,
            timestamp,
            frame_id,
            device
        )

        if result:
            # 计算处理时间并更新质量参数
            processing_time = time.time() - start_time
            stats = adapt_image_quality(connection_id, processing_time)

            # 使用调整后的质量重新编码图像以减小数据量
            if "img" in result and result["img"] is not None:
                try:
                    # 调整分辨率
                    if stats["resolution_factor"] < 1.0:
                        img = result["img"]
                        h, w = img.shape[:2]
                        new_h = int(h * stats["resolution_factor"])
                        new_w = int(w * stats["resolution_factor"])
                        img = cv2.resize(img, (new_w, new_h))

                    # 以较低质量重新编码processed_frame
                    if "processed_frame" in result:
                        img_data = base64.b64decode(result["processed_frame"])
                        nparr = np.frombuffer(img_data, np.uint8)
                        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                        # 调整大小
                        if stats["resolution_factor"] < 1.0:
                            h, w = img.shape[:2]
                            new_h = int(h * stats["resolution_factor"])
                            new_w = int(w * stats["resolution_factor"])
                            img = cv2.resize(img, (new_w, new_h))

                        # 以较低质量重新编码
                        _, buffer = cv2.imencode(
                            '.jpg',
                            img,
                            [cv2.IMWRITE_JPEG_QUALITY, stats["quality"]]
                        )
                        result["processed_frame"] = base64.b64encode(buffer).decode('utf-8')
                except Exception as e:
                    logger.warning(f"调整图像质量时出错: {e}")

            # 删除无法JSON序列化的数据
            if "img" in result:
                del result["img"]  # 删除NumPy数组，无法JSON序列化

            # 确保所有对象都可以被JSON序列化
            for key in list(result.keys()):
                if isinstance(result[key], np.ndarray):
                    del result[key]

            # 添加性能指标
            result["performance"] = {
                "processing_time": processing_time,
                "fps_estimate": 1.0 / max(processing_time, 0.001),
                "quality_settings": {
                    "quality": stats["quality"],
                    "resolution_factor": stats["resolution_factor"],
                    "skip_frames": stats["skip_frames"]
                }
            }

            # 添加任务ID
            taskid = str(uuid.uuid4()) + str(time.time())
            result["taskid"] = taskid

            # 如果需要后台API调用
            if result.get('should_call_api'):
                # 根据视频类型选择不同的千问API处理函数
                if is_local_video:
                    # 对于本地视频，使用qwen-vl-max模型
                    logger.info(f"为本地视频创建千问VL Max分析任务: {taskid}")
                    asyncio.create_task(sumbit_task_qwen_max(
                        result['should_call_api'],
                        frame_base64,
                        result['objects'],
                        taskid
                    ))
                else:
                    # 对于萤石云视频流，使用默认千问模型
                    logger.info(f"创建千问分析任务: {taskid}")
                    asyncio.create_task(sumbit_task_qwen(
                        result['should_call_api'],
                        frame_base64,
                        result['objects'],
                        taskid
                    ))

                result["api_pending"] = True

                # 根据视频类型添加额外信息
                if is_local_video:
                    result["api_type"] = "qwen-vl-max"
                    result["video_info"] = {
                        "currentTime": frame_data.get("currentTime", 0),
                        "duration": frame_data.get("duration", 0)
                    }

            return result
        else:
            return {
                "error": "处理帧失败",
                "frame_id": frame_id,
                "timestamp": timestamp
            }
    except Exception as e:
        logger.error(f"处理帧时出错: {str(e)}")
        return {
            "error": str(e),
            "frame_id": frame_id,
            "timestamp": timestamp
        }

# 异步任务处理器 - 在程序启动时运行
async def frame_processor_worker():
    """后台帧处理工作器，从队列获取帧并处理"""
    while True:
        try:
            # 从队列获取任务: (connection_id, frame_data, websocket)
            connection_id, frame_data, websocket = await frame_queue.get()

            # 记录帧开始处理的时间
            process_start_time = time.time()

            # 检查连接是否仍然存在
            if connection_id not in connection_queues:
                # 连接已关闭，跳过处理
                frame_queue.task_done()
                continue

            # 获取设备信息
            try:
                import torch
                device = 'cuda' if torch.cuda.is_available() else 'cpu'
            except ImportError:
                device = 'cpu'

            # 处理帧
            result = await process_frame_optimized(frame_data, connection_id, device)

            # 计算总处理延迟
            total_latency = time.time() - process_start_time
            client_timestamp = frame_data.get("timestamp", 0)

            # 确保结果中包含时间戳和延迟信息
            if "timestamp" not in result:
                result["timestamp"] = client_timestamp

            # 添加或更新性能指标
            if "performance" not in result:
                result["performance"] = {}

            result["performance"].update({
                "total_latency": round(total_latency * 1000, 2),  # 转换为毫秒
                "server_processing_time": round(total_latency * 1000, 2),
                "received_at": process_start_time * 1000,
                "sent_at": time.time() * 1000
            })

            # 尝试发送结果给客户端
            try:
                await websocket.send_json(result)
            except WebSocketDisconnect:
                # 客户端已断开连接
                if connection_id in connection_queues:
                    del connection_queues[connection_id]
                if connection_id in connection_stats:
                    del connection_stats[connection_id]
            except Exception as e:
                logger.error(f"发送处理结果时出错: {str(e)}")

            # 标记任务完成
            frame_queue.task_done()
        except Exception as e:
            logger.error(f"帧处理工作器出错: {str(e)}")
            # 继续运行，不中断工作循环

# 启动帧处理工作器
@app.on_event("startup")
async def start_frame_processors():
    """启动多个帧处理工作器"""
    worker_count = 8  # 同时启动多个工作器以实现并行处理
    for i in range(worker_count):
        asyncio.create_task(frame_processor_worker())
    logger.info(f"启动了 {worker_count} 个帧处理工作器")

# 完全重写的WebSocket处理函数
@app.websocket("/ws/yolo-video-process")
async def yolo_video_process(websocket: WebSocket, back:BackgroundTasks):
    """优化的WebSocket端点，用于实时YOLO视频处理"""
    connection_id = str(uuid.uuid4())

    try:
        # 接受WebSocket连接
        await websocket.accept()
        logger.info(f"新的WebSocket连接: {connection_id}")

        # 为该连接创建队列以跟踪处理结果
        connection_queues[connection_id] = asyncio.Queue()

        # 初始化连接统计信息
        connection_stats[connection_id] = {
            "quality": 80,               # JPEG质量
            "resolution_factor": 1.0,    # 分辨率因子
            "skip_frames": 0,            # 跳帧计数
            "processed_frames": 0,       # 已处理帧数
            "avg_processing_time": 0.05, # 平均处理时间
            "started_at": time.time(),   # 连接开始时间
            "is_local_video": False,     # 是否为本地视频
        }

        frame_count = 0
        last_cleanup = time.time()

        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_json()

                # 检查是否关闭命令
                if "action" in data and data["action"] == "close":
                    logger.info(f"收到关闭命令，WebSocket连接 {connection_id} 断开")
                    break

                # 检查是否包含帧数据
                if "frame" not in data:
                    await websocket.send_json({"error": "未收到视频帧数据"})
                    continue

                # 更新帧ID
                if "frameId" not in data:
                    data["frameId"] = frame_count

                # 检查是否为本地视频流
                is_local_video = False
                if "currentTime" in data and "duration" in data:
                    is_local_video = True
                    connection_stats[connection_id]["is_local_video"] = True

                # 记录帧发送时间
                send_time = time.time()

                # 添加任务到帧处理队列
                await frame_queue.put((connection_id, data, websocket))

                # 增加帧计数
                frame_count += 1

                # 定期清理
                current_time = time.time()
                if current_time - last_cleanup > 15:  # 每15秒清理一次
                    dele_task()  # 不再传递taskid参数
                    last_cleanup = current_time

            except WebSocketDisconnect:
                logger.info(f"WebSocket连接 {connection_id} 断开")
                break
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {str(e)}")
                try:
                    await websocket.send_json({"error": f"处理请求时出错: {str(e)}"})
                except:
                    break

    except Exception as e:
        logger.error(f"WebSocket处理器出错: {str(e)}")

    finally:
        # 清理资源
        logger.info(f"关闭WebSocket连接: {connection_id}")
        if connection_id in connection_queues:
            del connection_queues[connection_id]
        if connection_id in connection_stats:
            del connection_stats[connection_id]
        try:
            await websocket.close()
        except:
            pass

@app.get('/get_task')
async def get_task(taskid:str):
    """
    根据任务ID获取Qwen API的处理结果
    """
    from taskmanage import get_task_status
    task_data = get_task_status(taskid)

    # 记录日志
    if task_data != 500:
        # 判断任务状态
        if isinstance(task_data, dict):
            if task_data.get("status") == "completed":
                logger.info(f"成功获取任务 {taskid} 的结果")
                # 返回结果字段
                return task_data.get("result", {})
            else:
                logger.info(f"任务 {taskid} 状态: {task_data.get('status', '未知')}")
                return {"status": task_data.get("status", "unknown")}
        else:
            # 旧格式兼容处理
            logger.info(f"成功获取任务 {taskid} 的结果 (旧格式)")
            return task_data
    else:
        logger.info(f"任务 {taskid} 不存在或已过期")
        return {"status": "not_found", "message": "任务不存在或已过期"}

    return task_data

# 导入萤石云视频流处理模块
try:
    from ezviz_processor import (
        process_ezviz_stream,
        stream_processors,
        get_ezviz_stream_url,
        call_qwen_api_for_frame
    )
    OPTIONAL_MODULES['ezviz_processor'] = True
    logging.info("成功导入萤石云处理模块")
except ImportError:
    OPTIONAL_MODULES['ezviz_processor'] = False
    logging.warning("未能导入萤石云处理模块，相关功能将不可用")

# 萤石云视频流API端点

@app.post("/api/start_ezviz_stream")
async def start_ezviz_stream(
    request: Request,
    background_tasks: BackgroundTasks
):
    """启动萤石云摄像头视频流处理"""
    try:
        # 检查萤石云处理模块是否可用
        if not OPTIONAL_MODULES.get('ezviz_processor', False):
            return JSONResponse(
                status_code=501,
                content={
                    "success": False,
                    "error": "萤石云处理功能不可用，服务器缺少必要的ezviz_processor模块",
                    "code": "MODULE_UNAVAILABLE"
                }
            )

        # 解析请求参数
        data = await request.json()
        device_serial = data.get("device_serial")
        camera_no = data.get("camera_no", 1)
        access_token = data.get("access_token")
        custom_prompt = data.get("prompt")
        model_id = data.get("model_id", "qwen-vl-max-1119")

        if not device_serial or not access_token:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "error": "缺少设备序列号或访问令牌"
                }
            )

        # 创建唯一的流ID和输出目录
        stream_id = str(uuid.uuid4())
        output_dir = os.path.join("static", "streams", stream_id)
        os.makedirs(output_dir, exist_ok=True)

        # 启动后台任务处理萤石云视频流
        background_tasks.add_task(
            process_ezviz_stream,
            device_serial,
            camera_no,
            access_token,
            stream_id,
            output_dir,
            custom_prompt,
            model_id
        )

        # 返回流ID和WebSocket连接URL
        return JSONResponse(
            content={
                "success": True,
                "stream_id": stream_id,
                "ws_url": f"/ws/ezviz-stream/{stream_id}",
                "status_url": f"/api/ezviz_status/{stream_id}"
            }
        )

    except Exception as e:
        logger.error(f"启动萤石云流处理时出错: {str(e)}")
        logger.exception("详细错误信息:")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e)
            }
        )

@app.get("/api/ezviz_status/{stream_id}")
async def get_ezviz_status(stream_id: str):
    """获取萤石云视频流处理状态"""
    try:
        # 检查萤石云处理模块是否可用
        if not OPTIONAL_MODULES.get('ezviz_processor', False):
            return JSONResponse(
                status_code=501,
                content={
                    "success": False,
                    "error": "萤石云处理功能不可用",
                    "code": "MODULE_UNAVAILABLE"
                }
            )

        if stream_id not in stream_processors:
            status_file = os.path.join("static", "streams", stream_id, "status.json")
            if os.path.exists(status_file):
                with open(status_file, "r") as f:
                    status = json.load(f)
                return JSONResponse(content={"success": True, **status})
            else:
                return JSONResponse(
                    status_code=404,
                    content={"success": False, "error": f"找不到流ID: {stream_id}"}
                )

        # 从内存中获取状态
        processor = stream_processors[stream_id]
        return JSONResponse(
            content={
                "success": True,
                "stream_id": stream_id,
                "status": processor["status"],
                "start_time": processor["start_time"],
                "elapsed_time": time.time() - processor["start_time"],
                "connected_clients": len(processor["connected_clients"]),
                "device_info": processor.get("device_info", {})
            }
        )
    except Exception as e:
        logger.error(f"获取萤石云流状态出错: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.post("/api/stop_ezviz_stream/{stream_id}")
async def stop_ezviz_stream(stream_id: str):
    """停止萤石云视频流处理"""
    try:
        # 检查萤石云处理模块是否可用
        if not OPTIONAL_MODULES.get('ezviz_processor', False):
            return JSONResponse(
                status_code=501,
                content={
                    "success": False,
                    "error": "萤石云处理功能不可用",
                    "code": "MODULE_UNAVAILABLE"
                }
            )

        if stream_id not in stream_processors:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": f"找不到流ID: {stream_id}"}
            )

        # 设置状态为停止
        stream_processors[stream_id]["status"] = "stopping"

        # 通知所有客户端
        for ws in list(stream_processors[stream_id]["connected_clients"]):
            try:
                await ws.send_json({
                    "type": "status",
                    "stream_id": stream_id,
                    "status": "stopping",
                    "message": "视频流正在停止"
                })
            except:
                pass

        return JSONResponse(
            content={"success": True, "message": f"正在停止流 {stream_id}"}
        )
    except Exception as e:
        logger.error(f"停止萤石云流出错: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

@app.websocket("/ws/ezviz-stream/{stream_id}")
async def websocket_ezviz_stream(websocket: WebSocket, stream_id: str):
    try:
        await websocket.accept()
        logger.info(f"WebSocket客户端连接到流 {stream_id}")

        # 添加到连接客户端集合
        if stream_id in stream_processors:
            if "connected_clients" not in stream_processors[stream_id]:
                stream_processors[stream_id]["connected_clients"] = set()
            stream_processors[stream_id]["connected_clients"].add(websocket)

        last_frame_time = time.time()
        frame_interval = 1.0 / 30  # 目标30FPS

        while True:
            try:
                current_time = time.time()
                elapsed = current_time - last_frame_time

                # 获取最新处理后的帧
                processed_frame = stream_processors[stream_id].get("last_processed_frame")
                if processed_frame is not None:
                    # 转换为JPEG然后base64编码，降低质量以提高传输速度
                    _, buffer = cv2.imencode('.jpg', processed_frame, [cv2.IMWRITE_JPEG_QUALITY, 70])
                    frame_base64 = base64.b64encode(buffer).decode('utf-8')

                    # 获取检测到的对象
                    objects = stream_processors[stream_id].get("objects", [])

                    # 发送给客户端
                    await websocket.send_json({
                        "type": "frame",
                        "frame": frame_base64,
                        "objects": objects,
                        "timestamp": current_time
                    })

                    last_frame_time = current_time

                # 动态调整延迟以维持目标帧率
                if elapsed < frame_interval:
                    await asyncio.sleep(frame_interval - elapsed)

            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端与流 {stream_id} 断开连接")
                break
            except Exception as e:
                logger.error(f"发送视频帧时出错: {str(e)}")
                try:
                    await websocket.send_json({"error": f"发送视频帧时出错: {str(e)}"})
                except:
                    break

    except WebSocketDisconnect:
        logger.info(f"WebSocket客户端与流 {stream_id} 断开连接")
    except Exception as e:
        logger.error(f"WebSocket处理时出错: {str(e)}")
        try:
            await websocket.close()
        except:
            pass
    finally:
        # 从连接客户端集合中移除
        if stream_id in stream_processors:
            if "disconnected_clients" in stream_processors[stream_id]:
                stream_processors[stream_id]["disconnected_clients"].add(websocket)
            else:
                # 尝试直接从connected_clients中移除
                try:
                    stream_processors[stream_id]["connected_clients"].discard(websocket)
                except:
                    pass

# 在应用启动时创建必要的目录
@app.on_event("startup")
async def startup_event():
    """应用启动时执行的操作"""
    try:
        # 创建必要的目录
        for dir_path in ["static/uploads", "static/streams", "static/results"]:
            os.makedirs(dir_path, exist_ok=True)
        logger.info("已创建必要的目录")
    except Exception as e:
        logger.error(f"创建目录时出错: {str(e)}")

# 启动应用
if __name__ == "__main__":
    try:
        import uvicorn
        uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=False)
    except ImportError:
        logger.error("无法导入 uvicorn 模块，请确保已安装: pip install uvicorn")
    except KeyboardInterrupt:
        logger.info("服务器已被用户中断")
    except Exception as e:
        logger.error(f"启动服务器时出错: {str(e)}")

@app.get("/ezviz", response_class=HTMLResponse)
async def ezviz_stream_page(request: Request):
    """返回萤石云视频流测试页面"""
    if templates:
        return templates.TemplateResponse("ezviz_stream.html", {"request": request})
    else:
        return HTMLResponse("<html><body><h1>模板功能不可用</h1></body></html>")

# 添加一个新的路由来提供YOLO实时处理页面
@app.get("/yolo-realtime", response_class=HTMLResponse)
async def yolo_realtime_page(request: Request):
    """返回YOLO实时视频处理演示页面"""
    if templates:
        return templates.TemplateResponse("yolo_realtime.html", {"request": request})
    else:
        return HTMLResponse("<html><body><h1>模板功能不可用</h1></body></html>")

# 添加一个新的路由来提供本地视频检测页面
@app.get("/local-video-detect", response_class=HTMLResponse)
async def local_video_detect_page(request: Request):
    """返回本地视频文件处理页面"""
    if templates:
        return templates.TemplateResponse("local_video_detect.html", {"request": request})
    else:
        return HTMLResponse("<html><body><h1>模板功能不可用</h1></body></html>")

# 新增本地视频处理端点
@app.post("/process-local-video")
async def process_local_video(
    video_file: UploadFile = File(...),
    fps: float = Form(2.0),
    custom_prompt: str = Form(None)
):
    """
    处理本地视频文件，使用Qwen API进行分析

    Args:
        video_file: 上传的视频文件
        fps: 视频帧提取频率 (默认2.0，即每0.5秒提取一帧)
        custom_prompt: 自定义分析提示词

    Returns:
        StreamingResponse: 流式返回分析结果
    """
    try:
        # 验证文件类型
        if not video_file.content_type or not video_file.content_type.startswith('video/'):
            raise HTTPException(status_code=400, detail="请上传有效的视频文件")

        # 验证文件大小 (限制为100MB)
        max_size = 100 * 1024 * 1024  # 100MB
        if video_file.size and video_file.size > max_size:
            raise HTTPException(status_code=400, detail="视频文件大小不能超过100MB")

        # 保存上传的视频文件
        file_path = save_upload_file(video_file)
        absolute_file_path = os.path.abspath(file_path)

        logger.info(f"开始处理本地视频文件: {absolute_file_path}")

        # 检查API密钥
        api_key = os.getenv('DASHSCOPE_API_KEY')
        if not api_key:
            raise HTTPException(
                status_code=500,
                detail="API密钥未设置，请在环境变量中设置 DASHSCOPE_API_KEY"
            )

        # 构建分析提示词
        if custom_prompt:
            analysis_prompt = custom_prompt
        else:
            analysis_prompt = """
            请分析这个交通视频，重点关注以下方面：
            1. 交通安全风险评估
            2. 车辆行为分析
            3. 道路状况评估
            4. 潜在危险识别
            5. 安全建议

            请提供详细的分析结果，包括具体的风险事件和改进建议。
            """

        # 构建消息
        messages = [
            {
                "role": "user",
                "content": [
                    {"video": f"file://{absolute_file_path}", "fps": fps},
                    {"text": analysis_prompt}
                ]
            }
        ]

        # 创建流式响应生成器
        async def generate_analysis_stream():
            try:
                # 发送开始处理的状态
                yield f"data: {json.dumps({'type': 'status', 'message': '开始分析视频...', 'progress': 10})}\n\n"

                # 调用Qwen API进行流式分析
                from dashscope import MultiModalConversation

                yield f"data: {json.dumps({'type': 'status', 'message': '连接Qwen API...', 'progress': 20})}\n\n"

                response = MultiModalConversation.call(
                    api_key=api_key,
                    model="qvq-max",
                    messages=messages,
                    stream=True,
                )

                yield f"data: {json.dumps({'type': 'status', 'message': '正在分析视频内容...', 'progress': 30})}\n\n"

                # 处理流式响应
                reasoning_content = ""
                answer_content = ""
                current_section = None
                progress = 30

                for chunk in response:
                    if hasattr(chunk, 'output') and hasattr(chunk.output, 'choices'):
                        for choice in chunk.output.choices:
                            if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                                content = choice.message.content

                                if content:
                                    # 检测内容类型
                                    if "<think>" in content:
                                        current_section = "reasoning"
                                        content = content.replace("<think>", "")
                                    elif "</think>" in content:
                                        current_section = "answer"
                                        content = content.replace("</think>", "")

                                    # 根据当前部分累积内容
                                    if current_section == "reasoning":
                                        reasoning_content += content
                                        # 发送思考过程更新
                                        yield f"data: {json.dumps({'type': 'reasoning', 'content': content, 'progress': min(progress, 70)})}\n\n"
                                    else:
                                        answer_content += content
                                        # 发送答案内容更新
                                        yield f"data: {json.dumps({'type': 'answer', 'content': content, 'progress': min(progress, 90)})}\n\n"

                                    # 逐步增加进度
                                    progress = min(progress + 1, 90)

                # 发送完成状态
                yield f"data: {json.dumps({'type': 'complete', 'message': '分析完成', 'progress': 100, 'reasoning': reasoning_content, 'answer': answer_content, 'video_path': f'/static/uploads/{os.path.basename(file_path)}'})}\n\n"

            except Exception as e:
                logger.error(f"视频分析过程中出错: {str(e)}")
                error_msg = f"分析过程中出现错误: {str(e)}"
                yield f"data: {json.dumps({'type': 'error', 'message': error_msg})}\n\n"
            finally:
                # 清理临时文件（可选）
                try:
                    # 注意：这里不删除文件，因为前端需要播放视频
                    pass
                except:
                    pass

        # 返回Server-Sent Events流
        return StreamingResponse(
            generate_analysis_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "*"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理本地视频时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理视频时出现错误: {str(e)}")

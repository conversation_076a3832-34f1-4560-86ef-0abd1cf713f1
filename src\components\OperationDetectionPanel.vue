<template>
  <div class="operation-detection-panel">
    <div class="panel-header">
      <div class="panel-icon"></div>
      <span class="panel-title">运营风险识别</span>
    </div>
    
    <div v-if="!imagePreview" class="no-image-placeholder">
      <div class="placeholder-icon"></div>
      <div class="placeholder-text">请上传图片进行运营风险识别</div>
    </div>
    
    <div v-else-if="!detectionResults" class="loading-placeholder" :class="{ 'shimmer': isLoading }">
      <div v-if="isLoading">
        <div class="loading-icon"></div>
        <div class="loading-text">正在分析运营风险...</div>
      </div>
      <div v-else class="placeholder-text">点击"开始检测"按钮进行运营风险识别</div>
    </div>
    
    <div v-else-if="detectionResults" class="detection-results">
      <!-- 风险分析描述 -->
      <div class="description-container">
        <div class="description-header">
          <div class="description-icon"></div>
          <span class="description-title">风险分析结果</span>
        </div>
        <div class="description-content markdown-body" v-html="formattedDescription"></div>
      </div>
      
      <!-- 运营风险控制建议 -->
      <div class="suggestions-container">
        <div class="suggestions-header">
          <div class="suggestions-icon"></div>
          <span class="suggestions-title">运营管控建议</span>
        </div>
        <div class="suggestions-content">
          <div class="suggestion-item" v-for="(item, index) in operationSuggestions" :key="index">
            <div class="suggestion-number">{{ index + 1 }}</div>
            <div class="suggestion-text">{{ item }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, watch } from 'vue';
import type { DetectionResult } from '../services/DetectionService';
import { marked } from 'marked';

// 接收从父组件传递的属性
const props = defineProps({
  imagePreview: {
    type: String,
    default: ''
  },
  selectedOptions: {
    type: Array as () => string[],
    default: () => []
  }
});

// 注入从App.vue提供的状态
const detectionResults = inject<DetectionResult | null>('detectionResults', null);
const isLoading = ref(false);

// 监听父组件的加载状态
const updateLoadingState = (loading: boolean) => {
  isLoading.value = loading;
};

// 格式化描述，将Markdown转换为HTML
const formattedDescription = computed(() => {
  if (!detectionResults.value || !detectionResults.value.description) {
    return '';
  }
  
  try {
    return marked(detectionResults.value.description);
  } catch(e) {
    return detectionResults.value.description.replace(/\n/g, '<br>');
  }
});

// 运营风险管控建议（示例数据，实际项目中可能从检测结果中提取或从API获取）
const operationSuggestions = ref([
  '优化交通流量控制，减少拥堵风险',
  '加强道路维护管理，提高通行效率',
  '提升道路标识清晰度，防止车辆误入',
  '增设智能监控设备，实时监测路况',
  '完善应急处理机制，快速响应突发情况'
]);

// 监听检测结果变化，更新建议
watch(() => detectionResults.value, (newVal) => {
  if (newVal && newVal.description) {
    // 这里可以添加从检测结果中提取管控建议的逻辑
    // 或者发送API请求获取更详细的运营管控建议
    
    // 示例逻辑：根据是否包含某些关键词来更新建议
    if (newVal.description.includes('交通拥堵') || newVal.description.includes('traffic congestion')) {
      operationSuggestions.value = [
        '实施分时段交通管制，缓解峰值压力',
        '增设智能红绿灯系统，优化通行效率',
        '开通临时绕行通道，分散交通流量',
        '加强交通信息发布，引导车辆合理分流',
        '协调多部门联动，快速处理交通事故'
      ];
    } else if (newVal.description.includes('道路损坏') || newVal.description.includes('road damage')) {
      operationSuggestions.value = [
        '立即围挡隔离受损区域，防止二次事故',
        '安排应急修复队伍，尽快恢复道路功能',
        '设置提前警示标志，引导车辆安全绕行',
        '增派交通管理人员，现场指挥交通',
        '评估损伤程度，制定长期修复计划'
      ];
    }
  }
});
</script>

<style scoped>
.operation-detection-panel {
  margin: 15px;
  background: rgba(0, 21, 41, 0.5);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 168, 255, 0.2);
}

.panel-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.panel-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 5px rgba(0, 168, 255, 0.5));
}

.panel-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  letter-spacing: 1px;
}

.no-image-placeholder,
.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 30px;
  text-align: center;
}

.placeholder-icon,
.loading-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 15px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
}

.loading-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z'%3E%3C/path%3E%3C/svg%3E");
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.placeholder-text,
.loading-text {
  color: #8c8c8c;
  font-size: 16px;
}

.loading-text {
  color: #1890ff;
}

.shimmer {
  background: linear-gradient(
    90deg,
    rgba(0, 33, 64, 0.5) 0%,
    rgba(0, 58, 140, 0.7) 50%,
    rgba(0, 33, 64, 0.5) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

.detection-results {
  padding: 15px;
}

.description-container,
.suggestions-container {
  margin-bottom: 20px;
  background: rgba(0, 21, 41, 0.3);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
}

.description-header,
.suggestions-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.6));
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.description-icon,
.suggestions-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 3px rgba(0, 168, 255, 0.5));
}

.description-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'%3E%3C/path%3E%3C/svg%3E");
}

.suggestions-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6A4.997 4.997 0 0 1 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z'%3E%3C/path%3E%3C/svg%3E");
}

.description-title,
.suggestions-title {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

.description-content,
.suggestions-content {
  padding: 15px;
}

.description-content {
  line-height: 1.6;
  color: #d9d9d9;
  font-size: 14px;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  color: #1890ff;
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: bold;
}

.markdown-body h4 {
  font-size: 16px;
}

.markdown-body h5 {
  font-size: 14px;
}

.markdown-body p {
  margin-bottom: 12px;
}

.markdown-body ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-bottom: 12px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  background: rgba(0, 33, 64, 0.5);
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  transform: translateX(5px);
  background: rgba(0, 58, 140, 0.5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.suggestion-number {
  background: rgba(24, 144, 255, 0.2);
  color: #1890ff;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
  flex-shrink: 0;
}

.suggestion-text {
  color: #d9d9d9;
  line-height: 1.5;
}
</style> 
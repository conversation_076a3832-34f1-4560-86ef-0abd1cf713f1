<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线监控平台</title>
    <style>
        :root {
            --primary-color: #1890ff;
            --secondary-color: #1d39c4;
            --danger-color: #ff4d4f;
            --warning-color: #faad14;
            --text-dark: #fff;
            --text-light: #ffffff;
            --bg-light: #001529;
            --bg-dark: #000c17;
            --border-color: #003a8c;
            --card-bg: #001f3d;
            --border-radius: 8px;
            --shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
            --bg-content: #000c17;
            --header-bg: #002140;
            --sidebar-header-bg: #002140;
            --category-bg: #002140;
            --category-hover: #003a8c;
            --primary-hover: #40a9ff;
            --border-light: #1d39c4;
            --success-color: #52c41a;
            --text-secondary: #aaa;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'Avenir', Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-content);
            color: var(--text-dark);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
            transition: all 0.3s ease;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            max-width: 1600px;
            margin: 0 auto;
            animation: fade-in 0.5s ease-out;
            background-color: var(--bg-content);
        }
        
        @keyframes fade-in {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-light);
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-light);
            margin: 0;
            position: relative;
            padding-left: 16px;
        }
        
        .page-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background-color: var(--primary-color);
            border-radius: 2px;
        }
        
        .video-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .video-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: var(--header-bg);
            border-bottom: 1px solid var(--border-color);
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-light);
            margin: 0;
        }
        
        .card-tools {
            display: flex;
            gap: 8px;
        }
        
        .card-body {
            padding: 16px;
            position: relative;
            background-color: #000;
            min-height: 400px;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }
        
        video, canvas {
            width: 100%;
            height: 100%;
            max-height: 600px;
            object-fit: contain;
        }
        
        .video-preview-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .detection-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .settings-panel {
            width: 320px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 16px;
        }
        
        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: var(--header-bg);
            background-image: linear-gradient(to right, var(--header-bg), #ffffff);
            border-bottom: 1px solid var(--border-color);
        }
        
        .settings-group {
            margin-bottom: 16px;
        }
        
        .settings-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        select, input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: rgba(0, 0, 0, 0.2);
            margin-bottom: 12px;
            font-family: inherit;
            color: var(--text-light);
            transition: all 0.3s ease;
        }
        
        select:focus, input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
        }
        
        .btn {
            position: relative;
            overflow: hidden;
            padding: 8px 16px;
            min-width: 80px;
            letter-spacing: 0.5px;
            background-color: var(--primary-color);
            color: var(--text-light);
            border: none;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
        }
        
        .btn:hover {
            background-color: var(--primary-hover);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.18);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: var(--text-light);
        }
        
        .btn-primary:hover {
            background-color: #3ca776;
            box-shadow: 0 2px 5px rgba(0,0,0,0.18);
        }
        
        .btn-success {
            background-color: #67c23a;
            color: var(--text-light);
        }
        
        .btn-success:hover {
            background-color: #5daf34;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #ff7875;
        }
        
        .btn-disabled {
            background-color: #555;
            color: #bbb;
            cursor: not-allowed;
        }
        
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-top: 1px solid var(--border-color);
            background-color: #f9fafc;
        }
        
        .result-panel {
            margin-top: 20px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }
        
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: var(--header-bg);
            border-bottom: 1px solid var(--border-color);
        }
        
        .result-content {
            padding: 16px;
            max-height: 300px;
            overflow-y: auto;
            background-color: var(--card-bg);
            color: var(--text-light);
        }
        
        .info-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--secondary-color);
        }
        
        .detection-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detection-category {
            font-weight: 500;
        }
        
        .detection-risk {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .risk-high {
            background-color: rgba(255, 77, 79, 0.3);
            color: var(--text-light);
        }
        
        .risk-medium {
            background-color: rgba(250, 173, 20, 0.3);
            color: var(--text-light);
        }
        
        .risk-low {
            background-color: rgba(82, 196, 26, 0.2);
            color: var(--text-light);
        }
        
        .status-bar {
            margin-top: 16px;
            padding: 12px 16px;
            background-color: var(--card-bg);
            border-radius: 0;
            border-left: 4px solid var(--primary-color);
            color: var(--text-light);
        }
        
        .status-error {
            background-color: rgba(255, 77, 79, 0.2);
            color: var(--danger-color);
            border-left-color: var(--danger-color);
        }
        
        .stats-panel {
            margin-top: 16px;
            display: flex;
            gap: 16px;
        }
        
        .stats-card {
            flex: 1;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 16px;
            box-shadow: var(--shadow);
            text-align: center;
        }
        
        .stats-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
            margin: 8px 0;
        }
        
        .stats-label {
            color: #606266;
            font-size: 14px;
        }
        
        .performance-metrics {
            margin-top: 16px;
            display: flex;
            gap: 16px;
        }
        
        .metric-card {
            flex: 1;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 12px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }
        
        .metric-title {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
        }
        
        .metric-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 10;
            backdrop-filter: blur(4px);
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            margin-bottom: 16px;
            position: relative;
        }
        
        .spinner-circle {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: var(--primary-color);
            animation: spin 1.2s linear infinite;
        }
        
        .spinner-circle:nth-child(2) {
            border-top-color: transparent;
            border-right-color: var(--primary-color);
            animation-delay: -0.3s;
        }
        
        .spinner-circle:nth-child(3) {
            border-top-color: transparent;
            border-right-color: transparent;
            border-bottom-color: var(--primary-color);
            animation-delay: -0.6s;
        }
        
        .loading-text {
            animation: pulse-text 1.5s infinite;
            font-size: 14px;
            font-weight: 500;
        }
        
        @keyframes pulse-text {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }
        
        .progress-container {
            width: 100%;
            height: 4px;
            background-color: #e9ecef;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color) 0%, #3aa776 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px;
            background-color: #fff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            z-index: 1000;
            max-width: 300px;
            transform: translateX(100%);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .notification-title {
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .notification-close {
            cursor: pointer;
            color: #909399;
        }
        
        .notification-message {
            color: #606266;
            font-size: 14px;
        }
        
        .notification-success {
            border-left: 4px solid var(--primary-color);
        }
        
        .notification-error {
            border-left: 4px solid var(--danger-color);
        }
        
        .btn {
            position: relative;
            overflow: hidden;
            padding: 8px 16px;
            min-width: 80px;
            letter-spacing: 0.5px;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.3s ease;
            z-index: 0;
        }
        
        .btn:hover::before {
            transform: scaleX(1);
            transform-origin: left;
        }
        
        .btn span {
            position: relative;
            z-index: 1;
        }
        
        .video-container, .settings-panel, .result-panel, .panel-section, .metric-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            overflow: hidden;
            border: 1px solid var(--border-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .video-container:hover, .settings-panel:hover, .result-panel:hover, .panel-section:hover, .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        .health-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }
        
        .health-good {
            background-color: var(--primary-color);
            box-shadow: 0 0 6px rgba(66, 185, 131, 0.6);
        }
        
        .health-warning {
            background-color: var(--warning-color);
            box-shadow: 0 0 6px rgba(255, 193, 7, 0.6);
        }
        
        .health-error {
            background-color: var(--danger-color);
            box-shadow: 0 0 6px rgba(255, 82, 82, 0.6);
        }
        
        /* Vue风格的补充样式 */
        .vue-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 16px;
            padding-right: 30px;
            cursor: pointer;
            transition: border-color 0.3s, box-shadow 0.3s;
            width: 100%;
            background-color: rgba(0, 0, 0, 0.2);
        }
        
        .vue-select:disabled {
            background-color: #f5f7fa;
            cursor: not-allowed;
            opacity: 0.7;
        }
        
        .vue-select:hover {
            border-color: #c0c4cc;
        }
        
        .vue-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 6px rgba(66, 185, 131, 0.15);
        }
        
        .metric-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border-top: 3px solid var(--primary-color);
            position: relative;
            padding-top: 30px;
            text-align: center;
            background-color: var(--card-bg);
        }
        
        .metric-icon {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        
        .metric-icon svg {
            color: var(--primary-color);
        }
        
        .metric-card:nth-child(1) .metric-icon svg {
            color: var(--primary-color);
        }
        
        .metric-card:nth-child(2) .metric-icon svg {
            color: var(--success-color);
        }
        
        .metric-card:nth-child(3) .metric-icon svg {
            color: var(--warning-color);
        }
        
        .metric-card:nth-child(4) .metric-icon svg {
            color: var(--primary-color);
        }
        
        .metric-card:nth-child(1) {
            border-top-color: var(--primary-color);
        }
        
        .metric-card:nth-child(2) {
            border-top-color: var(--success-color);
        }
        
        .metric-card:nth-child(3) {
            border-top-color: var(--warning-color);
        }
        
        .metric-card:nth-child(4) {
            border-top-color: var(--primary-color);
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 0;
            color: var(--text-secondary);
            text-align: center;
            background-color: var(--bg-light);
        }
        
        .empty-state svg {
            margin-bottom: 16px;
        }
        
        .empty-state p {
            margin-top: 0;
        }
        
        .result-tabs {
            display: flex;
            gap: 8px;
        }
        
        .tab-button {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            border: none;
            background-color: #f0f2f5;
            color: #606266;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .tab-button:hover:not(.active) {
            background-color: #e4e7ed;
        }
        
        .card-header, .result-header {
            background: var(--header-bg);
            border-bottom: 1px solid var(--border-color);
        }
        
        /* 为检测项添加动画效果 */
        @keyframes slide-in {
            from { opacity: 0; transform: translateX(-10px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .detection-item {
            transition: background-color 0.2s, transform 0.2s;
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            animation: slide-in 0.3s ease;
            background-color: rgba(0, 0, 0, 0.2);
        }
        
        .detection-item:hover {
            background-color: rgba(24, 144, 255, 0.1);
            transform: translateX(2px);
        }
        
        /* 添加高风险闪烁效果 */
        @keyframes pulse {
            0% { background-color: rgba(255, 77, 79, 0.2); }
            50% { background-color: rgba(255, 77, 79, 0.4); }
            100% { background-color: rgba(255, 77, 79, 0.2); }
        }
        
        .risk-high {
            animation: pulse 2s infinite;
        }
        
        .result-content {
            transition: all 0.3s ease;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 右侧设置面板样式 */
        .setting-item {
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;
        }
        
        .setting-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .setting-description {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .setting-mode-switch {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #606266;
        }
        
        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #dcdfe6;
            transition: .4s;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:focus + .slider {
            box-shadow: 0 0 1px var(--primary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(20px);
        }
        
        .slider.round {
            border-radius: 34px;
        }
        
        .slider.round:before {
            border-radius: 50%;
        }
        
        /* 系统状态样式 */
        .system-stats {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 16px;
        }
        
        .stats-header {
            font-weight: 500;
            margin-bottom: 10px;
            color: var(--secondary-color);
            font-size: 14px;
            padding-bottom: 6px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            margin-bottom: 8px;
            color: #606266;
        }
        
        .stats-label {
            color: #909399;
        }
        
        .stats-value {
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .stats-progress-bar {
            margin-top: 12px;
        }
        
        .progress-bg {
            height: 6px;
            background-color: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color) 0%, #3aa776 100%);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .progress-label {
            text-align: center;
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
        }
        
        /* 右侧边栏面板样式 */
        .sidebar-panel {
            width: 320px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .panel-section {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .panel-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        .result-panel {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }
        
        .result-content {
            flex-grow: 1;
            overflow-y: auto;
            max-height: calc(100vh - 450px);
            min-height: 320px;
        }
        
        /* 设置容器样式 */
        .settings-container {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-top: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .settings-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: var(--header-bg);
            background-image: linear-gradient(to right, var(--header-bg), #ffffff);
            border-bottom: 1px solid var(--border-color);
        }
        
        .settings-body {
            padding: 16px;
        }
        
        .settings-row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .settings-row .settings-group {
            flex: 1;
            min-width: 240px;
        }
        
        /* 系统状态面板样式 */
        .system-stats {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 16px;
        }
        
        .panel-section .status-bar {
            margin: 0;
            border-radius: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 48px;
        }
        
        /* 调整空状态居中 */
        .empty-state {
            height: 100%;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background-color: var(--bg-light);
        }

        /* Vue风格的扁平化阴影效果 */
        .video-container, .panel-section, .settings-container, .metric-card {
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.08);
            background-color: var(--card-bg);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .video-container:hover, .panel-section:hover, .settings-container:hover, .metric-card:hover {
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }
        
        /* 卡片内容区域背景色 */
        .settings-body, .result-content {
            background-color: var(--card-bg);
            color: var(--text-light);
        }

        /* Vue组件圆角效果 */
        .video-container, .settings-panel, .result-panel, .panel-section, .metric-card {
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .card-header, .settings-header, .result-header {
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        /* 统一背景颜色风格 */
        .empty-state {
            background-color: var(--bg-light);
        }

        .settings-body, .result-content {
            background-color: var(--card-bg);
        }

        /* 调整按钮圆角一致性 */
        .btn, .tab-button {
            border-radius: 4px;
        }
    </style>
    <!-- 添加HLS.js实现HLS流媒体支持 -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body>
    <!-- 添加进度条 -->
    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    
    <!-- 添加通知区域 -->
    <div class="notification" id="notification">
        <div class="notification-header">
            <div class="notification-title">通知</div>
            <div class="notification-close">×</div>
        </div>
        <div class="notification-message">操作成功</div>
    </div>
    
    <div class="app-container">
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">在线监控系统</h1>
                <div class="connection-status">
                    <span class="health-indicator health-good" id="connectionHealth"></span>
                    <span id="connectionStatus">系统正常</span>
                </div>
            </div>
            
            <div class="video-section">
                <!-- 视频和检测结果显示区域 -->
                <div class="video-container">
                    <div class="card-header">
                        <div class="card-title">萤石云监控视频</div>
                        <div class="card-tools">
                            <button class="btn btn-success" id="toggleDetectionBtn">显示检测框</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="video-preview-wrapper">
                            <video id="videoInput" autoplay playsinline muted></video>
                            <canvas id="detectionCanvas" class="detection-canvas"></canvas>
                            <canvas id="outputCanvas" style="display: none;"></canvas>
                        </div>
                        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                            <div class="loading-spinner">
                                <div class="spinner-circle"></div>
                                <div class="spinner-circle"></div>
                                <div class="spinner-circle"></div>
                            </div>
                            <div class="loading-text">视频加载中...</div>
                        </div>
                    </div>
                    <div class="controls">
                        <button class="btn btn-primary" id="startBtn"><span>开始处理</span></button>
                        <button class="btn btn-danger btn-disabled" id="stopBtn" disabled><span>停止处理</span></button>
                    </div>
                </div>
                
                <!-- 检测结果面板 (移到右侧) -->
                <div class="sidebar-panel">
                    <div class="panel-section result-panel">
                        <div class="result-header">
                            <div class="card-title">检测结果</div>
                            <div class="result-tabs">
                                <button class="tab-button active" data-tab="all">全部</button>
                                <button class="tab-button" data-tab="high-risk">高风险</button>
                                <button class="tab-button" data-tab="low-risk">低/中风险</button>
                            </div>
                        </div>
                        <div class="result-content" id="resultInfo">
                            <div class="empty-state">
                                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                </svg>
                                <div class="info-title">未开始检测</div>
                                <p>点击"开始处理"按钮启动视频分析。</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 系统状态信息 -->
                    <div class="panel-section system-stats">
                        <div class="stats-header">系统状态</div>
                        <div class="stats-row">
                            <div class="stats-label">当前延迟:</div>
                            <div class="stats-value" id="currentLatency">0 ms</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">实际帧率:</div>
                            <div class="stats-value" id="actualFrameRate">0 FPS</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">自动目标帧率:</div>
                            <div class="stats-value" id="targetFrameRate">15 FPS</div>
                        </div>
                        <div class="stats-progress-bar">
                            <div class="progress-bg">
                                <div class="progress-fill" id="networkQualityBar"></div>
                            </div>
                            <div class="progress-label">网络质量</div>
                        </div>
                    </div>
                    
                    <div class="panel-section">
                        <div class="status-bar" id="status">
                            状态: 就绪
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 性能指标 -->
            <div class="performance-metrics">
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                            <path d="M2 17l10 5 10-5"></path>
                            <path d="M2 12l10 5 10-5"></path>
                        </svg>
                    </div>
                    <div class="metric-title">发送帧数</div>
                    <div class="metric-value" id="sentFrames">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                        </svg>
                    </div>
                    <div class="metric-title">接收帧数</div>
                    <div class="metric-value" id="receivedFrames">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                    </div>
                    <div class="metric-title">平均延迟</div>
                    <div class="metric-value" id="avgLatency">0 ms</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
                        </svg>
                    </div>
                    <div class="metric-title">接收帧率</div>
                    <div class="metric-value" id="receiveFps">0 FPS</div>
                </div>
            </div>
            
            <!-- 处理设置面板 (移到下方) -->
            <div class="settings-container">
                <div class="settings-header">
                    <div class="card-title">处理设置</div>
                </div>
                <div class="settings-body">
                    <div class="settings-row">
                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label">处理帧率</label>
                                <div class="setting-info">
                                    <select id="frameRate" class="vue-select" disabled>
                                        <option value="30">30 FPS</option>
                                        <option value="15" selected>15 FPS</option>
                                        <option value="10">10 FPS</option>
                                        <option value="5">5 FPS</option>
                                        <option value="2">2 FPS</option>
                                    </select>
                                    <div class="setting-mode-switch">
                                        <span>自动</span>
                                        <label class="switch">
                                            <input type="checkbox" id="autoFrameRateToggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-description">
                                    <span id="frameRateDescription">当前使用自适应帧率模式，系统将根据网络延迟自动调整。</span>
                                </div>
                            </div>
                        </div>

                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label">图像质量</label>
                                <div class="setting-info">
                                    <select id="imageQuality" class="vue-select">
                                        <option value="1.0">100%</option>
                                        <option value="0.8" selected>80%</option>
                                        <option value="0.6">60%</option>
                                        <option value="0.4">40%</option>
                                    </select>
                                </div>
                                <div class="setting-description">
                                    更高的图像质量可能增加网络传输负担。
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label">图像尺寸</label>
                                <div class="setting-info">
                                    <select id="imageSize" class="vue-select">
                                        <option value="1.0">100%</option>
                                        <option value="0.75" selected>75%</option>
                                        <option value="0.5">50%</option>
                                    </select>
                                </div>
                                <div class="setting-description">
                                    更小的尺寸可以提高处理速度和降低网络负载。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let videoElement = document.getElementById('videoInput');
        let detectionCanvas = document.getElementById('detectionCanvas');
        let outputCanvas = document.getElementById('outputCanvas');
        let ctx = detectionCanvas.getContext('2d');
        let startButton = document.getElementById('startBtn');
        let stopButton = document.getElementById('stopBtn');
        let toggleDetectionBtn = document.getElementById('toggleDetectionBtn');
        let statusElement = document.getElementById('status');
        let loadingOverlay = document.getElementById('loadingOverlay');
        let resultInfoElement = document.getElementById('resultInfo');
        let frameRateSelect = document.getElementById('frameRate');
        let imageQualitySelect = document.getElementById('imageQuality');
        let imageSizeSelect = document.getElementById('imageSize');
        let progressBar = document.getElementById('progressBar');
        let connectionHealth = document.getElementById('connectionHealth');
        let connectionStatus = document.getElementById('connectionStatus');
        let notification = document.getElementById('notification');
        let autoFrameRateToggle = document.getElementById('autoFrameRateToggle');
        let frameRateDescription = document.getElementById('frameRateDescription');
        let currentLatencyElement = document.getElementById('currentLatency');
        let actualFrameRateElement = document.getElementById('actualFrameRate');
        let targetFrameRateElement = document.getElementById('targetFrameRate');
        let networkQualityBar = document.getElementById('networkQualityBar');
        
        // 保存最新千问分析结果的变量
        let lastQwenResult = null; // 存储最后一次千问分析结果
        let lastQwenResultTime = 0; // 记录最后一次千问分析结果时间戳
        
        // 性能指标元素
        let sentFramesElement = document.getElementById('sentFrames');
        let receivedFramesElement = document.getElementById('receivedFrames');
        let avgLatencyElement = document.getElementById('avgLatency');
        let receiveFpsElement = document.getElementById('receiveFps');
        
        // 萤石云参数 (固定使用的值)
        const ezvizParams = {
            appKey: '8dcf58f3eff843a49ae4c60b55cd9c9b',
            appSecret: 'a436053dec3df63157bdfe7100f76f88',
            deviceSerial: 'FX2683018',
            channelNo: 3
        };
        
        // HLS播放器实例
        let hlsPlayer = null;
        
        // WebSocket 连接
        let ws = null;
        let isRunning = false;
        let frameCount = 0;
        let processedCount = 0;
        let lastFrameTime = 0;
        let lastStatsUpdate = 0;
        let framerateInterval = 1000 / parseInt(frameRateSelect.value);
        
        // 性能指标
        let totalLatency = 0;
        let maxLatency = 0;
        let minLatency = Infinity;
        let framesSent = 0;
        let framesReceived = 0;
        let showDetectionBoxes = true;
        let recentLatencies = []; // 用于计算动态帧率
        let isAutoFrameRate = true; // 是否启用自动帧率控制
        let dynamicFrameRate = 15; // 当前动态帧率
        let lastFrameRateAdjustment = 0; // 上次调整帧率的时间
        let framesSentLastSecond = 0; // 用于计算实际帧率
        let lastSecondStart = 0; // 上一秒开始时间
        
        // 自适应帧率参数
        const AUTO_RATE_CONFIG = {
            minFrameRate: 2,
            maxFrameRate: 30,
            optimalLatency: 200, // 理想延迟(ms)
            adjustInterval: 2000, // 调整间隔(ms)
            latencySampleSize: 10, // 采样数量
            frameFateSteps: [2, 5, 10, 15, 20, 25, 30] // 可用帧率选项
        };
        
        // 添加函数用于格式化时间差
        function formatTimeDifference(timestamp) {
            if (!timestamp) return '';
            
            const now = new Date().getTime();
            const diff = now - timestamp;
            
            // 计算秒、分钟、小时
            const seconds = Math.floor(diff / 1000);
            if (seconds < 60) {
                return `${seconds}秒前`;
            }
            
            const minutes = Math.floor(seconds / 60);
            if (minutes < 60) {
                return `${minutes}分钟前`;
            }
            
            const hours = Math.floor(minutes / 60);
            return `${hours}小时${minutes % 60}分钟前`;
        }
        
        // 显示通知函数
        function showNotification(message, type = 'success') {
            const notificationEl = document.getElementById('notification');
            const messageEl = notificationEl.querySelector('.notification-message');
            
            // 设置样式和内容
            notificationEl.className = 'notification show notification-' + type;
            messageEl.textContent = message;
            
            // 显示通知
            setTimeout(() => {
                notificationEl.classList.add('show');
            }, 10);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                notificationEl.classList.remove('show');
            }, 3000);
        }
        
        // 更新进度条
        function updateProgressBar(percent) {
            progressBar.style.width = percent + '%';
        }
        
        // 更新连接状态指示器
        function updateConnectionHealth(status) {
            // 移除所有类名
            connectionHealth.className = 'health-indicator';
            
            // 根据状态设置类名和文字
            switch (status) {
                case 'good':
                    connectionHealth.classList.add('health-good');
                    connectionStatus.textContent = '系统正常';
                    break;
                case 'warning':
                    connectionHealth.classList.add('health-warning');
                    connectionStatus.textContent = '系统延迟';
                    break;
                case 'error':
                    connectionHealth.classList.add('health-error');
                    connectionStatus.textContent = '连接异常';
                    break;
            }
        }
        
        // 自适应帧率控制
        function adjustFrameRate(latency) {
            // 添加当前延迟到最近延迟数组
            recentLatencies.push(latency);
            
            // 保持数组大小
            if (recentLatencies.length > AUTO_RATE_CONFIG.latencySampleSize) {
                recentLatencies.shift();
            }
            
            // 每隔一段时间调整帧率
            const now = performance.now();
            if (now - lastFrameRateAdjustment < AUTO_RATE_CONFIG.adjustInterval) {
                return;
            }
            
            if (!isAutoFrameRate || recentLatencies.length < 3) {
                return;
            }
            
            // 计算平均延迟
            const avgLatency = recentLatencies.reduce((sum, val) => sum + val, 0) / recentLatencies.length;
            
            // 根据延迟选择合适的帧率
            let newFrameRate = dynamicFrameRate;
            
            // 延迟阈值和对应帧率设置
            if (avgLatency > 800) {
                newFrameRate = 2; // 非常高延迟
            } else if (avgLatency > 500) {
                newFrameRate = 5; // 高延迟
            } else if (avgLatency > 300) {
                newFrameRate = 10; // 中等延迟
            } else if (avgLatency > 200) {
                newFrameRate = 15; // 低延迟
            } else if (avgLatency > 100) {
                newFrameRate = 20; // 很低延迟
            } else {
                newFrameRate = 30; // 极低延迟，接近实时
            }
            
            // 只有当帧率需要变化时才进行更新
            if (newFrameRate !== dynamicFrameRate) {
                dynamicFrameRate = newFrameRate;
                framerateInterval = 1000 / dynamicFrameRate;
                console.log(`自适应帧率调整为: ${dynamicFrameRate} FPS (平均延迟: ${avgLatency.toFixed(0)}ms)`);
                
                // 更新UI显示
                targetFrameRateElement.textContent = `${dynamicFrameRate} FPS`;
                
                // 更新网络质量条
                const qualityPercent = 100 - Math.min(100, (avgLatency / 1000) * 100);
                networkQualityBar.style.width = `${qualityPercent}%`;
            }
            
            lastFrameRateAdjustment = now;
        }
        
        // 初始化函数
        async function init() {
            try {
                // 关闭通知按钮事件
                const closeBtn = document.querySelector('.notification-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', function() {
                        document.getElementById('notification').classList.remove('show');
                    });
                }
                
                // 设置定时器，定期更新分析结果的时间信息
                setInterval(() => {
                    // 如果有分析结果且超过5秒，更新时间显示
                    if (lastQwenResult && lastQwenResultTime > 0 && (new Date().getTime() - lastQwenResultTime > 5000)) {
                        const timestampElement = resultInfoElement.querySelector('.result-timestamp');
                        if (timestampElement) {
                            const now = new Date();
                            const timeString = now.toLocaleTimeString();
                            timestampElement.innerHTML = `更新时间: ${timeString} <span style="color: var(--warning-color);">(${formatTimeDifference(lastQwenResultTime)})</span>`;
                        }
                    }
                }, 10000); // 每10秒更新一次
                
                // 自动/手动帧率切换
                autoFrameRateToggle.addEventListener('change', function() {
                    isAutoFrameRate = this.checked;
                    frameRateSelect.disabled = isAutoFrameRate;
                    
                    if (isAutoFrameRate) {
                        frameRateDescription.textContent = '当前使用自适应帧率模式，系统将根据网络延迟自动调整。';
                        // 重置为自适应计算的帧率
                        framerateInterval = 1000 / dynamicFrameRate;
                    } else {
                        frameRateDescription.textContent = '手动设置帧率可能会影响视频处理的流畅度。';
                        // 使用用户选择的帧率
                        framerateInterval = 1000 / parseInt(frameRateSelect.value);
                    }
                    
                    console.log(`帧率模式切换: ${isAutoFrameRate ? '自动' : '手动'}, 当前帧率: ${isAutoFrameRate ? dynamicFrameRate : parseInt(frameRateSelect.value)} FPS`);
                });
                
                // 帧率选择事件
                frameRateSelect.addEventListener('change', function() {
                    if (!isAutoFrameRate) {
                        framerateInterval = 1000 / parseInt(this.value);
                    }
                });
                
                // 切换检测框显示
                toggleDetectionBtn.addEventListener('click', function() {
                    showDetectionBoxes = !showDetectionBoxes;
                    this.textContent = showDetectionBoxes ? '隐藏检测框' : '显示检测框';
                    
                    if (!showDetectionBoxes && detectionCanvas) {
                        const ctx = detectionCanvas.getContext('2d');
                        if (ctx) {
                            ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                        }
                    }
                });
                
                // 设置开始按钮事件
                startButton.addEventListener('click', async function() {
                    if (isRunning) return;
                    
                    try {
                        // 初始化千问分析结果状态
                        lastQwenResult = null;
                        lastQwenResultTime = 0;
                        
                        // 模拟进度条加载
                        updateProgressBar(0);
                        const progressInterval = setInterval(() => {
                            const currentWidth = parseFloat(progressBar.style.width) || 0;
                            if (currentWidth < 90) {
                                updateProgressBar(currentWidth + (90 - currentWidth) / 10);
                            } else {
                                clearInterval(progressInterval);
                            }
                        }, 200);
                        
                        // 显示加载状态
                        loadingOverlay.style.display = 'flex';
                        updateStatus('正在获取萤石云直播流...', false);
                        
                        // 初始化萤石云视频流
                        await initializeEzvizStream();
                        
                        // 等待视频元素准备就绪
                        await new Promise(resolve => {
                            if (videoElement.readyState >= 3) {
                                resolve();
                            } else {
                                videoElement.oncanplay = () => resolve();
                            }
                        });
                        
                        // 设置画布尺寸
                        detectionCanvas.width = videoElement.videoWidth || 640;
                        detectionCanvas.height = videoElement.videoHeight || 360;
                        outputCanvas.width = videoElement.videoWidth || 640;
                        outputCanvas.height = videoElement.videoHeight || 360;
                        
                        // 创建WebSocket连接
                        connectWebSocket();
                        
                        // 更新界面状态
                        isRunning = true;
                        startButton.disabled = true;
                        startButton.classList.add('btn-disabled');
                        stopButton.disabled = false;
                        stopButton.classList.remove('btn-disabled');
                        updateStatus('WebSocket连接中...', false);
                        
                        // 隐藏加载状态
                        loadingOverlay.style.display = 'none';
                        
                        // 更新检测结果区域为等待分析状态
                        resultInfoElement.innerHTML = `
                            <div class="empty-state">
                                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M12 6v6l4 2"></path>
                                </svg>
                                <div class="info-title">等待AI分析</div>
                                <p>视频分析已启动，等待AI完成首次分析...</p>
                            </div>
                        `;
                        
                        // 完成进度条
                        updateProgressBar(100);
                        setTimeout(() => {
                            updateProgressBar(0);
                        }, 500);
                        
                        // 显示成功通知
                        showNotification('视频处理已成功启动', 'success');
                        
                        // 更新连接状态
                        updateConnectionHealth('good');
                        
                        // 开始处理视频帧
                        processVideo();
                    } catch (error) {
                        console.error('启动失败:', error);
                        updateStatus('启动失败: ' + error.message, true);
                        loadingOverlay.style.display = 'none';
                        updateProgressBar(0);
                        
                        // 显示错误通知
                        showNotification('启动失败: ' + error.message, 'error');
                        
                        // 更新连接状态
                        updateConnectionHealth('error');
                    }
                });
                
                // 设置停止按钮事件
                stopButton.addEventListener('click', function() {
                    stopProcessing();
                });
            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('初始化失败: ' + error.message, true);
                updateConnectionHealth('error');
            }
        }
        
        // 初始化萤石云视频流
        async function initializeEzvizStream() {
            try {
                // 获取认证Token
                const tokenResponse = await fetch('https://open.ys7.com/api/lapp/token/get', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        'appKey': ezvizParams.appKey,
                        'appSecret': ezvizParams.appSecret
                    })
                });
                
                const tokenData = await tokenResponse.json();
                if (tokenData.code !== '200') {
                    throw new Error(`获取授权失败: ${tokenData.msg || '请检查AppKey和AppSecret'}`);
                }
                
                const accessToken = tokenData.data.accessToken;
                
                // 获取直播流地址
                const liveUrlResponse = await fetch('https://open.ys7.com/api/lapp/v2/live/address/get', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        'accessToken': accessToken,
                        'deviceSerial': ezvizParams.deviceSerial,
                        'channelNo': ezvizParams.channelNo,
                        'protocol': '2', // HLS协议
                        'quality': '1'   // 高清质量
                    })
                });
                
                const liveUrlData = await liveUrlResponse.json();
                if (liveUrlData.code !== '200') {
                    throw new Error(`获取直播地址失败: ${liveUrlData.msg || '请检查设备序列号和通道号'}`);
                }
                
                const hlsUrl = liveUrlData.data.url;
                
                // 使用HLS.js播放视频流
                if (hlsPlayer) {
                    hlsPlayer.destroy();
                    hlsPlayer = null;
                }
                
                if (Hls.isSupported()) {
                    hlsPlayer = new Hls({
                        debug: false,
                        maxLoadingRetry: 4,
                        enableWorker: true,
                        maxBufferLength: 30,
                        lowLatencyMode: true
                    });
                    
                    hlsPlayer.loadSource(hlsUrl);
                    hlsPlayer.attachMedia(videoElement);
                    
                    return new Promise((resolve, reject) => {
                        hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
                            console.log('HLS manifest parsed, attempting to play video');
                            videoElement.play()
                                .then(() => resolve())
                                .catch(playError => {
                                    console.warn('Autoplay prevented:', playError);
                                    alert('请点击视频区域来播放视频');
                                    videoElement.onclick = () => {
                                        videoElement.play()
                                            .then(() => resolve())
                                            .catch(err => reject(err));
                                        videoElement.onclick = null;
                                    };
                                });
                        });
                        
                        hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
                            if (data.fatal) {
                                switch(data.type) {
                                    case Hls.ErrorTypes.NETWORK_ERROR:
                                        console.log('Network error, attempting to recover');
                                        hlsPlayer.startLoad();
                                        break;
                                    case Hls.ErrorTypes.MEDIA_ERROR:
                                        console.log('Media error, attempting to recover');
                                        hlsPlayer.recoverMediaError();
                                        break;
                                    default:
                                        console.error('Fatal error, cannot recover');
                                        reject(new Error('视频播放失败，请检查网络连接'));
                                        break;
                                }
                            }
                        });
                        
                        // 设置超时
                        setTimeout(() => {
                            reject(new Error('视频流加载超时，请检查网络连接或刷新页面重试'));
                        }, 15000);
                    });
                } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
                    // 对于Safari浏览器
                    videoElement.src = hlsUrl;
                    return videoElement.play();
                } else {
                    throw new Error('您的浏览器不支持HLS播放，请使用Chrome、Firefox或Safari浏览器');
                }
            } catch (error) {
                console.error('萤石云初始化错误:', error);
                throw new Error('萤石云视频加载失败: ' + error.message);
            }
        }
        
        // 创建WebSocket连接
        function connectWebSocket() {
            // 关闭之前的连接
            if (ws) {
                ws.close();
            }
            
            // 创建新连接
            const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${location.host}/ws/yolo-video-process`;
            
            ws = new WebSocket(wsUrl);
            
            // WebSocket 事件处理
            ws.onopen = function() {
                updateStatus('WebSocket 已连接，开始处理...', false);
            };
            
            ws.onmessage = function(event) {
                processedCount++;
                const now = performance.now();
                
                try {
                    const response = JSON.parse(event.data);
                    console.log("收到WebSocket消息:", response);
                    
                    // 添加辅助函数检查是否为Qwen响应
                    function isQwenResponse(response) {
                        return (
                            // 检查是否存在qwen_analysis字段（首选方式）
                            response.qwen_analysis ||
                            // 检查是否是从日志中提取的千问数据
                            (response.log && (
                                response.log.includes("INFO:yolo_video_processor:Qwen API返回成功") ||
                                response.log.includes("INFO:yolo_video_processor:Qwen API原始响应")
                            )) ||
                            // 检查是否包含高风险或低风险事件以及描述（兼容旧格式）
                            (
                                (
                                    (response.high_risk_events && response.high_risk_events.length > 0) ||
                                    (response.low_risk_events && response.low_risk_events.length > 0)
                                ) && 
                                (
                                    response.description && 
                                    (response.description.includes("风险分析") || 
                                     response.description.includes("管控建议"))
                                )
                            )
                        );
                    }
                    
                    // 添加对日志信息的特殊处理
                    if (response.log) {
                        console.log("服务器日志:", response.log);
                        // 检查日志中是否包含Qwen的结果信息
                        if (response.log.includes("Qwen API") || response.log.includes("返回结果结构")) {
                            console.log("检测到Qwen日志信息");
                            // 在状态栏显示日志信息
                            updateStatus("收到Qwen分析: " + response.log.substring(0, 50) + "...", false);
                        }
                    }
                    
                    if (response.error) {
                        console.error('服务器错误:', response.error);
                        updateStatus('服务器错误: ' + response.error, true);
                        updateConnectionHealth('error');
                        return;
                    }
                    
                    // 显示处理后的帧
                    if (response.processed_frame && showDetectionBoxes) {
                        const img = new Image();
                        img.onload = function() {
                            if (!detectionCanvas) return;
                            
                            const ctx = detectionCanvas.getContext('2d');
                            if (ctx) {
                                ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                                ctx.drawImage(img, 0, 0, detectionCanvas.width, detectionCanvas.height);
                            }
                        };
                        img.src = 'data:image/jpeg;base64,' + response.processed_frame;
                    } else if (!showDetectionBoxes && detectionCanvas) {
                        const ctx = detectionCanvas.getContext('2d');
                        if (ctx) {
                            ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                        }
                    }
                    
                    // 明确检查是否有千问分析结果
                    let hasQwenResult = isQwenResponse(response);
                    
                    // 处理Qwen分析结果
                    if (hasQwenResult) {
                        console.log("检测到Qwen分析结果，准备更新UI");
                        
                        try {
                            let analysisResult = {};
                            
                            // 首选从qwen_analysis中提取数据
                            if (response.qwen_analysis) {
                                const qwenData = response.qwen_analysis;
                                analysisResult = {
                                    summary: {
                                        description: qwenData.description || ''
                                    },
                                    high_risk_events: qwenData.high_risk_events || [],
                                    low_risk_events: qwenData.low_risk_events || []
                                };
                            } 
                            // 备选从响应的顶级字段提取数据
                            else {
                                analysisResult = {
                                    summary: {
                                        description: response.description || ''
                                    },
                                    high_risk_events: response.high_risk_events || [],
                                    low_risk_events: response.low_risk_events || []
                                };
                            }
                            
                            // 保存最新的千问分析结果
                            lastQwenResult = analysisResult;
                            lastQwenResultTime = new Date().getTime();
                            
                            // 更新检测结果显示
                            updateResultInfo(analysisResult);
                            showNotification("收到千问AI分析结果", "success");
                            
                        } catch (e) {
                            console.error('处理Qwen分析结果时出错:', e);
                            // 只在无千问分析结果时才显示错误
                            if (!lastQwenResult) {
                                updateResultInfo({
                                    summary: {
                                        description: "处理分析结果时出错: " + e.message
                                    },
                                    high_risk_events: [],
                                    low_risk_events: []
                                });
                            }
                        }
                    }
                    
                    // 检查是否有日志信息包含结果结构 (保留日志提取逻辑以兼容旧版本)
                    if (response.log && !hasQwenResult) {
                        // 尝试从日志中提取有关Qwen分析的信息
                        if (response.log.includes("INFO:yolo_video_processor:返回结果结构:") ||
                            response.log.includes("INFO:yolo_video_processor:Qwen API原始响应")) {
                            try {
                                // 尝试从日志中提取JSON
                                const logText = response.log;
                                
                                // 查找JSON对象使用更精确的模式
                                // 先寻找特定标记后的JSON
                                let jsonText = '';
                                if (logText.includes("INFO:yolo_video_processor:返回结果结构:")) {
                                    jsonText = logText.split("INFO:yolo_video_processor:返回结果结构:")[1].trim();
                                } else if (logText.includes("INFO:yolo_video_processor:Qwen API原始响应")) {
                                    jsonText = logText.split("INFO:yolo_video_processor:Qwen API原始响应")[1].trim();
                                    // 如果包含括号，去掉前面的内容
                                    if (jsonText.includes("(") && jsonText.includes("):")) {
                                        jsonText = jsonText.split("):")[1].trim();
                                    }
                                }
                                
                                // 寻找JSON边界
                                const jsonMatch = jsonText.match(/{[\s\S]*?}/);
                                if (jsonMatch) {
                                    console.log("从日志中提取到JSON:", jsonMatch[0].substring(0, 100) + "...");
                                    // 清理并解析JSON
                                    const cleanJson = jsonMatch[0]
                                        .replace(/'/g, '"')
                                        .replace(/True/g, 'true')
                                        .replace(/False/g, 'false')
                                        .replace(/None/g, 'null');
                                        
                                    const parsedJson = JSON.parse(cleanJson);
                                    
                                    // 构建并显示结果
                                    const logResult = {
                                        summary: {
                                            description: parsedJson.description || ''
                                        },
                                        high_risk_events: parsedJson.high_risk_events || [],
                                        low_risk_events: parsedJson.low_risk_events || []
                                    };
                                    
                                    // 保存最新的千问分析结果
                                    lastQwenResult = logResult;
                                    lastQwenResultTime = new Date().getTime();
                                    
                                    // 显示在状态栏
                                    updateStatus("提取到Qwen分析结果", false);
                                    
                                    // 更新UI
                                    updateResultInfo(logResult);
                                    showNotification("从日志提取的千问分析结果", "success");
                                    hasQwenResult = true;
                                }
                            } catch (e) {
                                console.error("从日志提取JSON失败:", e);
                            }
                        }
                    }
                    
                    // 检查是否包含taskid，表示后端已启动异步任务，保持原始的轮询逻辑
                    if (response.taskid && !hasQwenResult) {
                        // 显示任务正在处理的提示，但不替换已存在的分析结果
                        const emptyState = resultInfoElement.querySelector('.empty-state');
                        if (emptyState || resultInfoElement.innerHTML.trim() === '') {
                            resultInfoElement.innerHTML = `
                                <div class="empty-state">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M12 6v6l4 2"></path>
                                    </svg>
                                    <div class="info-title">AI分析进行中...</div>
                                    <p>正在处理任务 ID: ${response.taskid}</p>
                                </div>
                            `;
                        }
                        
                        // 定义异步请求任务函数
                        const request_task = async () => {
                            try {
                                console.log(`轮询任务结果，任务ID: ${response.taskid}`);
                                const res = await fetch(`/get_task?taskid=${response.taskid}`);
                                
                                // 无论状态码如何，都尝试处理响应
                                try {
                                    const result = await res.json();
                                    console.log(`任务${response.taskid}轮询结果:`, result);
                                    
                                    // 如果结果是500或者包含status: "not_found"，表示任务未完成或未找到
                                    if (result === 500 || (result && result.status === "not_found")) {
                                        // 如果任务未完成或未找到，3秒后重试
                                        console.log(`任务${response.taskid}尚未完成或未找到，3秒后重试`);
                                        setTimeout(async () => {
                                            await request_task();
                                        }, 3000);
                                        return;
                                    }
                                    
                                    // 记录已完成的任务ID，防止重复处理
                                    if (!window.completedTasks) {
                                        window.completedTasks = [];
                                    }
                                    if (window.completedTasks.includes(response.taskid)) {
                                        console.log(`任务${response.taskid}已处理，跳过`);
                                        return; // 已经处理过此任务，避免重复更新
                                    }
                                    window.completedTasks.push(response.taskid);
                                    
                                    // 确保只处理Qwen API结果
                                    console.log("收到Qwen API结果:", result);
                                    
                                    // 检测到千问API返回成功，立即更新UI显示
                                    if (result) {
                                        // 构建分析结果对象
                                        let analysisResult;
                                        
                                        if (typeof result === 'string') {
                                            // 尝试解析字符串为JSON
                                            try {
                                                // 处理可能的Python风格引号和布尔值
                                                const cleanedResult = result
                                                    .replace(/'/g, '"')
                                                    .replace(/True/g, 'true')
                                                    .replace(/False/g, 'false')
                                                    .replace(/None/g, 'null');
                                                    
                                                // 查找JSON对象
                                                const jsonMatch = cleanedResult.match(/\{[\s\S]*\}/);
                                                if (jsonMatch) {
                                                    const parsedJson = JSON.parse(jsonMatch[0]);
                                                    analysisResult = {
                                                        summary: {
                                                            description: parsedJson.description || ''
                                                        },
                                                        high_risk_events: parsedJson.high_risk_events || [],
                                                        low_risk_events: parsedJson.low_risk_events || []
                                                    };
                                                } else {
                                                    // 如果找不到JSON，创建带原始描述的结果
                                                    analysisResult = {
                                                        summary: { description: result },
                                                        high_risk_events: [],
                                                        low_risk_events: []
                                                    };
                                                }
                                            } catch (e) {
                                                console.error("解析任务结果失败:", e);
                                                analysisResult = {
                                                    summary: { description: result },
                                                    high_risk_events: [],
                                                    low_risk_events: []
                                                };
                                            }
                                        } else if (typeof result === 'object' && result !== null) {
                                            // 如果已经是对象
                                            analysisResult = {
                                                summary: {
                                                    description: result.description || ''
                                                },
                                                high_risk_events: result.high_risk_events || [],
                                                low_risk_events: result.low_risk_events || []
                                            };
                                        } else {
                                            // 默认结果
                                            analysisResult = {
                                                summary: { description: "任务完成但结果格式不正确" },
                                                high_risk_events: [],
                                                low_risk_events: []
                                            };
                                        }
                                        
                                        // 更新检测结果显示
                                        console.log("更新UI显示Qwen分析结果:", analysisResult);
                                        lastQwenResult = analysisResult; // 保存最新的千问分析结果
                                        lastQwenResultTime = new Date().getTime(); // 更新时间戳
                                        updateResultInfo(analysisResult);
                                        
                                        // 显示通知
                                        showNotification("收到AI分析结果", "success");
                                    }
                                } catch (jsonError) {
                                    // JSON解析失败，可能返回的不是JSON格式
                                    console.error('解析任务结果JSON失败:', jsonError);
                                    // 仍然继续尝试，3秒后重试
                                    console.log(`JSON解析失败，3秒后重试任务: ${response.taskid}`);
                                    setTimeout(async () => {
                                        await request_task();
                                    }, 3000);
                                }
                            } catch (error) {
                                // 网络错误或其他请求失败
                                console.error('获取任务结果失败:', error);
                                
                                // 无论发生什么错误，都继续尝试，延长重试间隔到5秒
                                console.log(`请求失败，5秒后重试任务: ${response.taskid}`);
                                setTimeout(async () => {
                                    await request_task();
                                }, 5000);
                                
                                // 只在界面为空时显示错误，否则保留现有内容
                                const emptyState = resultInfoElement.querySelector('.empty-state');
                                if (emptyState && emptyState.querySelector('.info-title').textContent !== 'AI分析进行中...') {
                                    emptyState.querySelector('.info-title').textContent = 'AI分析进行中...';
                                    emptyState.querySelector('p').textContent = `正在重试获取任务 ${response.taskid} 结果...`;
                                }
                            }
                        };
                        
                        // 执行异步任务请求
                        request_task().then(() => console.log("已发起千问API结果请求"));
                    }
                    
                    // 计算延迟
                    if (response.timestamp) {
                        const latency = now - response.timestamp;
                        totalLatency += latency;
                        maxLatency = Math.max(maxLatency, latency);
                        minLatency = Math.min(minLatency, latency);
                        framesReceived++;
                        
                        // 更新当前延迟显示
                        currentLatencyElement.textContent = `${Math.round(latency)} ms`;
                        
                        // 更新实际帧率计算
                        const currentSecond = Math.floor(now / 1000);
                        if (Math.floor(lastSecondStart / 1000) !== currentSecond) {
                            actualFrameRateElement.textContent = `${framesSentLastSecond} FPS`;
                            framesSentLastSecond = 0;
                            lastSecondStart = now;
                        }
                        
                        // 自动调整帧率
                        if (isAutoFrameRate) {
                            adjustFrameRate(latency);
                        }
                        
                        // 根据延迟更新连接健康状态
                        if (latency > 500) {
                            updateConnectionHealth('warning');
                        } else if (latency > 1000) {
                            updateConnectionHealth('error');
                        } else {
                            updateConnectionHealth('good');
                        }
                        
                        // 每秒更新统计信息
                        if (now - lastStatsUpdate > 1000) {
                            updateStats();
                            lastStatsUpdate = now;
                        }
                    }
                } catch (error) {
                    console.error('处理服务器响应时出错:', error);
                    updateConnectionHealth('error');
                }
            };
            
            ws.onclose = function() {
                if (isRunning) {
                    updateStatus('WebSocket 连接关闭', false);
                    stopProcessing();
                }
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket 错误:', error);
                updateStatus('WebSocket 错误', true);
                stopProcessing();
            };
        }
        
        // 捕获并发送视频帧
        async function processVideo() {
            if (!isRunning) return;
            
            const now = performance.now();
            const elapsed = now - lastFrameTime;
            
            // 按照设定的帧率处理
            if (elapsed >= framerateInterval) {
                lastFrameTime = now;
                
                try {
                    // 从视频元素捕获帧
                    if (videoElement.readyState === videoElement.HAVE_ENOUGH_DATA) {
                        // 创建临时画布调整大小和质量
                        const tempCanvas = document.createElement('canvas');
                        const scale = parseFloat(imageSizeSelect.value);
                        tempCanvas.width = videoElement.videoWidth * scale;
                        tempCanvas.height = videoElement.videoHeight * scale;
                        
                        const tempCtx = tempCanvas.getContext('2d');
                        tempCtx.drawImage(videoElement, 0, 0, tempCanvas.width, tempCanvas.height);
                        
                        // 转换为JPEG并调整质量
                        const quality = parseFloat(imageQualitySelect.value);
                        const dataURL = tempCanvas.toDataURL('image/jpeg', quality);
                        const base64Data = dataURL.split(',')[1];
                        
                        // 构造消息并发送
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            const message = {
                                frame: base64Data,
                                timestamp: now,
                                frameId: frameCount
                            };
                            
                            ws.send(JSON.stringify(message));
                            framesSent++;
                            frameCount++;
                            framesSentLastSecond++;
                        }
                    }
                } catch (error) {
                    console.error('处理视频帧时出错:', error);
                }
            }
            
            // 继续处理下一帧
            requestAnimationFrame(processVideo);
        }
        
        // 停止处理
        function stopProcessing() {
            isRunning = false;
            
            // 关闭WebSocket连接
            if (ws) {
                ws.close();
                ws = null;
            }
            
            // 停止HLS播放器
            if (hlsPlayer) {
                hlsPlayer.destroy();
                hlsPlayer = null;
                videoElement.src = ''; // 清除视频源
            }
            
            // 恢复界面状态
            startButton.disabled = false;
            startButton.classList.remove('btn-disabled');
            stopButton.disabled = true;
            stopButton.classList.add('btn-disabled');
            updateStatus('已停止处理', false);
            
            // 重置千问分析结果状态
            lastQwenResult = null;
            lastQwenResultTime = 0;
            
            // 重置检测结果显示
            resultInfoElement.innerHTML = `
                <div class="empty-state">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <div class="info-title">未开始检测</div>
                    <p>点击"开始处理"按钮启动视频分析。</p>
                </div>
            `;
            
            // 显示通知
            showNotification('视频处理已停止');
            
            // 重置连接健康状态
            updateConnectionHealth('good');
            
            // 更新最终统计信息
            updateStats(true);
        }
        
        // 更新状态信息
        function updateStatus(message, isError) {
            statusElement.textContent = '状态: ' + message;
            statusElement.className = isError ? 'status-bar status-error' : 'status-bar';
        }
        
        // 更新统计信息
        function updateStats(isFinal = false) {
            const avgLatency = framesReceived > 0 ? (totalLatency / framesReceived).toFixed(2) : 0;
            const fps = (framesReceived / ((performance.now() - lastStatsUpdate) / 1000)).toFixed(2);
            
            sentFramesElement.textContent = framesSent;
            receivedFramesElement.textContent = framesReceived;
            avgLatencyElement.textContent = `${avgLatency} ms`;
            receiveFpsElement.textContent = `${fps} FPS`;
            
            if (isFinal) {
                updateStatus('处理已完成', false);
            }
        }
        
        // 更新检测结果信息
        function updateResultInfo(response) {
            // 只处理包含有效结果数据的响应
            if (!response) return;
            
            // 添加调试信息
            console.log("正在更新检测结果信息:", response);
            
            // 提取需要显示的Qwen分析数据
            const description = response.summary && response.summary.description ? 
                               response.summary.description : 
                               response.description || '';
            
            // 提取高风险和低风险事件，这是Qwen分析的一部分
            const hasHighRiskEvents = response.high_risk_events && response.high_risk_events.length > 0;
            const hasLowRiskEvents = response.low_risk_events && response.low_risk_events.length > 0;
            const hasSummary = !!description;
            
            // 记录检测到的内容
            console.log(`检测到的内容: 高风险: ${hasHighRiskEvents ? '有' : '无'}, 低风险: ${hasLowRiskEvents ? '有' : '无'}, 描述: ${hasSummary ? '有' : '无'}`);
            
            // 避免空结果显示
            const resultElement = document.getElementById('resultInfo');
            if (!resultElement) {
                console.error("无法找到结果显示元素!");
                return;
            }
            
            // 如果完全没有数据，且有上次分析结果，则保持显示上次结果
            const showEmptyState = !hasHighRiskEvents && !hasLowRiskEvents && !hasSummary;
            
            if (showEmptyState && lastQwenResult) {
                console.log("保持显示上次的千问分析结果");
                return; // 保留当前显示，不更新
            }
            
            let infoHtml = '';
            
            if (showEmptyState && !lastQwenResult) {
                // 显示空状态
                infoHtml = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="15" y1="9" x2="9" y2="15"></line>
                            <line x1="9" y1="9" x2="15" y2="15"></line>
                        </svg>
                        <div class="info-title">未检测到风险事件</div>
                        <p>当前视频中未检测到任何风险事件或分析结果。</p>
                    </div>
                `;
            } else {
                // 添加时间戳
                const now = new Date();
                const timeString = now.toLocaleTimeString();
                infoHtml += `<div class="result-timestamp">更新时间: ${timeString}`;
                
                // 如果使用的是缓存的分析结果，显示有效期
                if (lastQwenResultTime > 0 && now.getTime() - lastQwenResultTime > 5000) { // 5秒以上显示有效期
                    infoHtml += ` <span style="color: var(--warning-color);">(${formatTimeDifference(lastQwenResultTime)})</span>`;
                }
                infoHtml += `</div>`;
                
                // 添加高风险事件信息
                if (hasHighRiskEvents) {
                    infoHtml += '<div class="info-title" style="color: var(--danger-color); margin-top: 16px;">高风险事件:</div>';
                    infoHtml += '<div class="detection-list">';
                    response.high_risk_events.forEach((event, index) => {
                        infoHtml += `
                            <div class="detection-item">
                                <div class="detection-category">${index + 1}. ${event.category || event.label || '未知'}</div>
                                <div class="detection-description">${event.event_description || event.event || '未知事件'}</div>
                            </div>
                        `;
                    });
                    infoHtml += '</div>';
                }
                
                // 添加低风险事件信息
                if (hasLowRiskEvents) {
                    infoHtml += '<div class="info-title" style="color: var(--warning-color); margin-top: 16px;">低/中风险事件:</div>';
                    infoHtml += '<div class="detection-list">';
                    response.low_risk_events.forEach((event, index) => {
                        infoHtml += `
                            <div class="detection-item">
                                <div class="detection-category">${index + 1}. ${event.category || event.label || '未知'}</div>
                                <div class="detection-description">${event.event_description || event.event || '未知事件'}</div>
                            </div>
                        `;
                    });
                    infoHtml += '</div>';
                }
                
                // 添加总结信息
                if (description) {
                    infoHtml += '<div class="info-title" style="margin-top: 16px;">场景总结:</div>';
                    infoHtml += `<div class="summary-content">${description}</div>`;
                }
            }
            
            // 确保结果有内容
            if (infoHtml.trim() === '') {
                infoHtml = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                            <line x1="9" y1="9" x2="9.01" y2="9"></line>
                            <line x1="15" y1="9" x2="15.01" y2="9"></line>
                        </svg>
                        <div class="info-title">处理中...</div>
                        <p>系统正在分析视频内容，请稍候。</p>
                    </div>
                `;
            }
            
            // 更新DOM
            resultInfoElement.innerHTML = infoHtml;
            console.log("检测结果已更新到UI");
            
            // 移除空状态类
            resultInfoElement.classList.remove('empty-state');
        }
        
        // 为标签页添加功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            
            if (tabButtons.length > 0) {
                tabButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        // 移除所有激活状态
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        // 设置当前按钮为激活状态
                        this.classList.add('active');
                        
                        // 实现标签页切换逻辑
                        const tabType = this.dataset.tab;
                        console.log('Switched to tab:', tabType);
                        // 标签页切换功能可以在后续实现
                    });
                });
            }
        });
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html> 
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接视频处理器 - 处理视频文件并提供HTTP服务访问
"""

import os
import cv2
import time
import numpy as np
import threading
import argparse
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver
import webbrowser
import logging
from ultralytics import YOLO
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger("direct-video-processor")

# 全局变量
output_dir = Path("processed_videos")
static_dir = Path("static")
model = None
processing_lock = threading.Lock()
processing_status = {}
currently_processing = False

class VideoProcessor:
    def __init__(self, model_path="yolov8x-seg.pt", confidence=0.5):
        """初始化视频处理器"""
        self.model_path = model_path
        self.confidence = confidence
        self.model = None
        
    def load_model(self):
        """加载YOLO模型"""
        if self.model is None:
            logger.info(f"加载YOLO模型: {self.model_path}")
            try:
                self.model = YOLO(self.model_path)
                logger.info("模型加载成功")
                return True
            except Exception as e:
                logger.error(f"模型加载失败: {str(e)}")
                return False
        return True
    
    def process_video(self, video_path, output_path, resize_factor=1.0):
        """处理视频文件并保存处理后的视频"""
        global currently_processing
        
        if not self.load_model():
            return False
        
        # 获取相对路径作为视频ID
        video_id = Path(video_path).stem
        
        with processing_lock:
            currently_processing = True
            processing_status[video_id] = {
                "status": "processing",
                "progress": 0,
                "output_path": str(output_path)
            }
        
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                logger.error(f"无法打开视频: {video_path}")
                with processing_lock:
                    processing_status[video_id]["status"] = "error"
                    processing_status[video_id]["error"] = "无法打开视频文件"
                    currently_processing = False
                return False
            
            # 获取视频信息
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 调整尺寸
            if resize_factor != 1.0:
                width = int(width * resize_factor)
                height = int(height * resize_factor)
            
            logger.info(f"视频信息: {width}x{height}, {fps} FPS, {total_frames} 帧")
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
            
            frame_count = 0
            start_time = time.time()
            
            # 处理视频帧
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 调整帧大小
                if resize_factor != 1.0:
                    frame = cv2.resize(frame, (width, height))
                
                # 使用YOLO进行检测
                results = self.model(frame, conf=self.confidence)
                
                # 在帧上绘制检测结果
                annotated_frame = results[0].plot()
                
                # 将处理后的帧写入输出视频
                out.write(annotated_frame)
                
                # 更新进度
                frame_count += 1
                progress = min(100, int((frame_count / total_frames) * 100))
                
                with processing_lock:
                    processing_status[video_id]["progress"] = progress
                
                # 每100帧打印一次进度
                if frame_count % 100 == 0:
                    elapsed = time.time() - start_time
                    frames_per_second = frame_count / elapsed
                    remaining_frames = total_frames - frame_count
                    estimated_time = remaining_frames / frames_per_second if frames_per_second > 0 else 0
                    
                    logger.info(f"处理进度: {progress}%, 帧率: {frames_per_second:.2f} FPS, 剩余时间: {estimated_time:.2f}秒")
            
            # 释放资源
            cap.release()
            out.release()
            
            # 更新处理状态
            with processing_lock:
                processing_status[video_id]["status"] = "completed"
                processing_status[video_id]["progress"] = 100
                currently_processing = False
            
            elapsed = time.time() - start_time
            logger.info(f"视频处理完成，总时间: {elapsed:.2f}秒, 平均帧率: {frame_count/elapsed:.2f} FPS")
            
            return True
            
        except Exception as e:
            logger.error(f"处理视频时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            
            with processing_lock:
                processing_status[video_id]["status"] = "error"
                processing_status[video_id]["error"] = str(e)
                currently_processing = False
            
            return False

def find_video_files():
    """查找当前目录中的所有视频文件"""
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    video_files = []
    
    for ext in video_extensions:
        video_files.extend(list(Path('.').glob(f'*{ext}')))
    
    return video_files

def ensure_dirs():
    """确保输出目录存在"""
    output_dir.mkdir(exist_ok=True)
    static_dir.mkdir(exist_ok=True)
    
    # 创建CSS文件
    css_file = static_dir / "style.css"
    if not css_file.exists():
        with open(css_file, "w") as f:
            f.write("""
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            h1 { color: #333; }
            .container { max-width: 1200px; margin: 0 auto; }
            .video-container { margin-bottom: 30px; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
            .video-title { font-size: 18px; margin-bottom: 10px; }
            .video-player { width: 100%; max-width: 800px; }
            .processing { color: #ff9900; }
            .completed { color: #00aa00; }
            .error { color: #ff0000; }
            .progress-bar { width: 100%; background-color: #e0e0e0; border-radius: 4px; margin: 10px 0; }
            .progress-bar-fill { height: 20px; background-color: #4CAF50; border-radius: 4px; text-align: center; line-height: 20px; color: white; }
            .button { display: inline-block; padding: 10px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
            .button:hover { background-color: #45a049; }
            """)
    
    # 创建JS文件
    js_file = static_dir / "script.js"
    if not js_file.exists():
        with open(js_file, "w") as f:
            f.write("""
            // 自动刷新进度
            function refreshProgress() {
                fetch('/status')
                    .then(response => response.json())
                    .then(data => {
                        for (const [videoId, status] of Object.entries(data)) {
                            const progressElement = document.getElementById(`progress-${videoId}`);
                            const statusElement = document.getElementById(`status-${videoId}`);
                            const progressBarElement = document.getElementById(`progress-bar-${videoId}`);
                            
                            if (progressElement && statusElement && progressBarElement) {
                                progressElement.textContent = `${status.progress}%`;
                                progressBarElement.style.width = `${status.progress}%`;
                                
                                if (status.status === 'processing') {
                                    statusElement.textContent = '处理中';
                                    statusElement.className = 'processing';
                                } else if (status.status === 'completed') {
                                    statusElement.textContent = '已完成';
                                    statusElement.className = 'completed';
                                    
                                    // 如果处理完成且没有显示视频，刷新页面显示视频
                                    const videoPlayer = document.getElementById(`video-${videoId}`);
                                    if (!videoPlayer.src) {
                                        location.reload();
                                    }
                                } else if (status.status === 'error') {
                                    statusElement.textContent = `错误: ${status.error}`;
                                    statusElement.className = 'error';
                                }
                            }
                        }
                    })
                    .catch(error => console.error('获取状态失败:', error));
            }
            
            // 每2秒刷新一次进度
            setInterval(refreshProgress, 2000);
            
            // 页面加载时刷新一次
            document.addEventListener('DOMContentLoaded', refreshProgress);
            """)

def generate_index_html():
    """生成主页HTML"""
    video_files = list(output_dir.glob('*.mp4'))
    original_videos = find_video_files()
    
    with open(static_dir / "index.html", "w", encoding="utf-8") as f:
        f.write("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>视频处理系统</title>
            <link rel="stylesheet" href="/static/style.css">
            <script src="/static/script.js"></script>
        </head>
        <body>
            <div class="container">
                <h1>视频处理系统</h1>
        """)
        
        # 显示处理后的视频
        if video_files:
            f.write("<h2>已处理视频</h2>")
            for video_file in video_files:
                video_id = video_file.stem
                video_url = f"/processed/{video_file.name}"
                
                f.write(f"""
                <div class="video-container">
                    <div class="video-title">{video_id}</div>
                    <video id="video-{video_id}" class="video-player" controls>
                        <source src="{video_url}" type="video/mp4">
                        您的浏览器不支持HTML5视频
                    </video>
                    <div>状态: <span id="status-{video_id}" class="completed">已完成</span></div>
                    <div>进度: <span id="progress-{video_id}">100%</span></div>
                    <div class="progress-bar">
                        <div id="progress-bar-{video_id}" class="progress-bar-fill" style="width: 100%">100%</div>
                    </div>
                </div>
                """)
        
        # 显示原始视频，可以请求处理
        if original_videos:
            f.write("<h2>原始视频</h2>")
            for video_file in original_videos:
                video_id = video_file.stem
                processed_file = output_dir / f"{video_id}.mp4"
                
                # 检查是否已经处理过
                if processed_file.exists():
                    continue
                
                # 检查是否正在处理
                is_processing = video_id in processing_status and processing_status[video_id]["status"] == "processing"
                
                f.write(f"""
                <div class="video-container">
                    <div class="video-title">{video_file.name}</div>
                """)
                
                if is_processing:
                    f.write(f"""
                    <div>状态: <span id="status-{video_id}" class="processing">处理中</span></div>
                    <div>进度: <span id="progress-{video_id}">{processing_status[video_id]['progress']}%</span></div>
                    <div class="progress-bar">
                        <div id="progress-bar-{video_id}" class="progress-bar-fill" style="width: {processing_status[video_id]['progress']}%">{processing_status[video_id]['progress']}%</div>
                    </div>
                    """)
                else:
                    f.write(f"""
                    <div>状态: <span id="status-{video_id}">等待处理</span></div>
                    <div>进度: <span id="progress-{video_id}">0%</span></div>
                    <div class="progress-bar">
                        <div id="progress-bar-{video_id}" class="progress-bar-fill" style="width: 0%">0%</div>
                    </div>
                    <a href="/process?video={video_file.name}" class="button">处理此视频</a>
                    """)
                
                f.write("</div>")
        
        f.write("""
            </div>
        </body>
        </html>
        """)

class VideoHandler(SimpleHTTPRequestHandler):
    """处理HTTP请求的处理器"""
    
    def __init__(self, *args, directory=None, **kwargs):
        self.processor = kwargs.pop('processor', None)
        super().__init__(*args, directory=str(static_dir), **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        # 处理状态请求
        if self.path == '/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            
            with processing_lock:
                self.wfile.write(json.dumps(processing_status).encode())
            return
        
        # 处理视频处理请求
        if self.path.startswith('/process'):
            # 提取视频文件名
            query = self.path.split('?', 1)[1] if '?' in self.path else ''
            params = {}
            for pair in query.split('&'):
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    params[key] = value
            
            video_file = params.get('video', '')
            
            if not video_file:
                self.send_error(400, 'Missing video parameter')
                return
            
            # 检查是否已经在处理视频
            global currently_processing
            with processing_lock:
                if currently_processing:
                    self.send_response(303)
                    self.send_header('Location', '/')
                    self.end_headers()
                    return
            
            # 开始处理视频
            video_path = Path(video_file)
            if not video_path.exists():
                self.send_error(404, 'Video file not found')
                return
            
            # 启动处理线程
            output_path = output_dir / f"{video_path.stem}.mp4"
            threading.Thread(
                target=self.processor.process_video,
                args=(video_path, output_path),
                daemon=True
            ).start()
            
            # 重定向回主页
            self.send_response(303)
            self.send_header('Location', '/')
            self.end_headers()
            return
        
        # 处理处理后的视频请求
        if self.path.startswith('/processed/'):
            video_name = self.path[11:]  # 去掉 /processed/
            video_path = output_dir / video_name
            
            if not video_path.exists():
                self.send_error(404, 'Processed video not found')
                return
            
            self.send_response(200)
            self.send_header('Content-type', 'video/mp4')
            self.send_header('Content-Length', str(video_path.stat().st_size))
            self.end_headers()
            
            with open(video_path, 'rb') as f:
                shutil.copyfileobj(f, self.wfile)
            return
        
        # 处理静态文件请求
        if self.path.startswith('/static/'):
            file_path = static_dir / self.path[8:]
            
            if not file_path.exists():
                self.send_error(404, 'Static file not found')
                return
            
            mime_types = {
                '.css': 'text/css',
                '.js': 'application/javascript',
                '.html': 'text/html',
                '.png': 'image/png',
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.gif': 'image/gif'
            }
            
            mime_type = mime_types.get(file_path.suffix, 'application/octet-stream')
            
            self.send_response(200)
            self.send_header('Content-type', mime_type)
            self.end_headers()
            
            with open(file_path, 'rb') as f:
                shutil.copyfileobj(f, self.wfile)
            return
        
        # 处理根路径请求
        if self.path == '/':
            # 重新生成index.html
            generate_index_html()
            
            # 发送index.html
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            with open(static_dir / 'index.html', 'rb') as f:
                shutil.copyfileobj(f, self.wfile)
            return
        
        # 其他路径返回404
        self.send_error(404, 'Not found')

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='直接视频处理系统')
    parser.add_argument('--model', type=str, default='yolov8x-seg.pt',
                      help='YOLO模型路径')
    parser.add_argument('--port', type=int, default=8080,
                      help='HTTP服务器端口')
    parser.add_argument('--confidence', type=float, default=0.5,
                      help='YOLO检测置信度')
    
    args = parser.parse_args()
    
    # 确保目录存在
    ensure_dirs()
    
    # 创建视频处理器
    processor = VideoProcessor(model_path=args.model, confidence=args.confidence)
    
    # 尝试加载模型
    if not processor.load_model():
        logger.error("无法加载YOLO模型，退出程序")
        return 1
    
    # 生成初始HTML
    generate_index_html()
    
    # 创建HTTP服务器
    handler = lambda *args, **kwargs: VideoHandler(*args, processor=processor, **kwargs)
    
    with socketserver.TCPServer(("", args.port), handler) as httpd:
        logger.info(f"HTTP服务器启动在端口 {args.port}")
        
        # 自动打开浏览器
        webbrowser.open(f"http://localhost:{args.port}")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            logger.info("收到中断信号，关闭服务器")
        
    return 0

if __name__ == "__main__":
    import json  # 导入json模块
    exit(main()) 
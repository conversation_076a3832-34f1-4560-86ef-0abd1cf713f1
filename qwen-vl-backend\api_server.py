#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import base64
import logging
import uuid
import re
from io import BytesIO
from typing import List, Optional, Dict, Any, Union
from PIL import Image
from fastapi import FastAPI, File, Form, UploadFile, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.requests import Request
from pydantic import BaseModel, Field
from pathlib import Path
import json
import dashscope
from qwen_vl_utils import smart_resize
import datetime
import cv2
import concurrent.futures
import threading
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("qwen-vl-api")

# 创建上传目录
UPLOAD_DIR = Path("static/uploads")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 创建FastAPI应用
app = FastAPI(title="Traffic Eyes API Analysis Server", description="交通分析API服务器")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "ok", "service": "api-analysis", "timestamp": datetime.datetime.now().isoformat()}

# 添加WebSocket端点用于API分析
@app.websocket("/ws/api-analysis")
async def api_analysis(websocket: WebSocket):
    """WebSocket端点，用于API场景分析"""
    try:
        # 接受WebSocket连接
        await websocket.accept()
        logger.info("API分析WebSocket连接已建立")

        # 导入Qwen VL模块
        try:
            from qwen_vl_utils import analyze_image_with_qwen
        except ImportError:
            logger.error("无法导入Qwen VL分析模块")
            await websocket.send_json({"error": "服务器未正确配置Qwen VL分析模块"})
            await websocket.close()
            return

        # 创建线程池用于异步处理API分析任务
        executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)
        
        # 处理请求循环
        while True:
            try:
                # 接收前端发送的图像帧（Base64格式）
                data = await websocket.receive_json()

                if "action" in data and data["action"] == "close":
                    logger.info("收到关闭连接请求")
                    break

                if "frame" not in data:
                    await websocket.send_json({"error": "未收到图像帧数据"})
                    continue

                # 获取客户端发送的数据
                frame_base64 = data["frame"]
                timestamp = data.get("timestamp")
                frame_id = data.get("frameId")
                prompt = data.get("prompt", "分析视频帧中的安全隐患，重点关注人员、车辆和机械设备。")
                
                # 使用线程池异步处理API分析
                def process_api_task(frame_data, analysis_prompt):
                    try:
                        # 将Base64转换为图像
                        image_bytes = base64.b64decode(frame_data)
                        image = Image.open(BytesIO(image_bytes))
                        
                        # 调用Qwen VL进行分析
                        analysis_result = analyze_image_with_qwen(image, analysis_prompt)
                        
                        return {
                            "qwen_analysis": analysis_result,
                            "success": True
                        }
                    except Exception as e:
                        logger.error(f"API分析处理错误: {str(e)}")
                        return {
                            "error": str(e),
                            "success": False
                        }

                # 提交任务到线程池并设置回调
                future = executor.submit(process_api_task, frame_base64, prompt)

                # 等待处理结果并发送回客户端
                result = future.result()
                
                if not result or not result.get("success"):
                    logger.error(f"API分析失败: {result.get('error', '未知错误')}")
                    await websocket.send_json({
                        "error": f"API分析失败: {result.get('error', '未知错误')}",
                        "timestamp": timestamp or int(time.time() * 1000)
                    })
                    continue

                # 准备响应数据
                response = {
                    "qwen_analysis": result.get("qwen_analysis", ""),
                    "success": True
                }
                
                # 保留客户端发送的时间戳和帧ID
                if timestamp:
                    response["timestamp"] = timestamp
                if frame_id:
                    response["frameId"] = frame_id
                
                # 如果没有时间戳，添加服务器时间戳
                if not timestamp:
                    response["timestamp"] = int(time.time() * 1000)
                
                # 返回分析结果
                await websocket.send_json(response)

            except WebSocketDisconnect:
                logger.info("WebSocket连接已断开")
                break
            except Exception as e:
                logger.error(f"处理API分析请求时出错: {str(e)}")
                try:
                    await websocket.send_json({"error": f"处理API分析请求时出错: {str(e)}"})
                except:
                    logger.error("无法发送错误消息，连接可能已断开")
                    break

        # 线程池清理
        executor.shutdown(wait=False)
        logger.info("API分析WebSocket连接已关闭")

    except Exception as e:
        logger.error(f"WebSocket处理过程中出错: {str(e)}")
        try:
            await websocket.close()
        except:
            pass

# 启动应用
if __name__ == "__main__":
    try:
        import uvicorn
        uvicorn.run("api_server:app", host="0.0.0.0", port=8001, reload=False)
    except ImportError:
        logger.error("无法导入 uvicorn 模块，请确保已安装: pip install uvicorn")
    except KeyboardInterrupt:
        logger.info("服务器已被用户中断")
    except Exception as e:
        logger.error(f"启动服务器时出错: {str(e)}") 
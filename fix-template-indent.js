import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the file with the issue
const filePath = path.join(__dirname, 'src', 'components', 'OnlineMonitor.vue');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Create a backup
fs.writeFileSync(`${filePath}.backup-${Date.now()}`, content, 'utf8');

// First, let's extract the proper structure by splitting at the first legitimate <template> tag
const parts = content.split('<template>');
if (parts.length > 1) {
  // Get the first template section (legitimate one)
  const firstTemplate = parts[1];
  
  // Extract the contents up to the </template> tag
  const templateEndIndex = firstTemplate.indexOf('</template>');
  if (templateEndIndex !== -1) {
    const properTemplateContent = firstTemplate.substring(0, templateEndIndex);
    
    // Now reconstruct the file with just one template section
    const scriptStart = content.indexOf('<script');
    
    if (scriptStart !== -1) {
      // Extract the script section
      const scriptSection = content.substring(scriptStart);
      
      // Construct the fixed file with one template section and the script section
      const fixedContent = '<template>\n' + properTemplateContent + '\n</template>\n\n' + scriptSection;
      
      // Write the fixed content back
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      
      console.log('Fixed duplicate <template> tags in OnlineMonitor.vue');
    } else {
      console.error('Could not find script section');
    }
  } else {
    console.error('Could not find template end tag');
  }
} else {
  console.error('Could not find template tag');
} 
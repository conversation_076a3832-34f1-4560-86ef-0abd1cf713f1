# Traffic Eyes 视频处理系统

这个系统提供了一种全新的方法来处理监控视频，直接在后端进行YOLO对象检测，并将处理后的视频提供给前端浏览。

## 特点

- **离线视频处理**：处理整个视频文件并保存结果，提供高质量的对象检测
- **实时视频流**：使用MJPEG流提供实时处理的视频帧，支持低延迟显示
- **简单的Web界面**：无需复杂的前端框架，使用基本的HTML和JavaScript
- **支持多种视频源**：可以处理视频文件或摄像头直播
- **性能优化**：实时处理模式针对速度进行了优化，离线处理模式针对质量进行了优化

## 系统要求

- Python 3.7+
- OpenCV
- Ultralytics YOLO
- 可选：NVIDIA GPU加速

## 安装

1. 安装依赖：
   ```
   pip install ultralytics opencv-python
   ```

2. 下载YOLO模型（如果尚未下载）：
   ```python
   from ultralytics import YOLO
   YOLO('yolov8n.pt')  # 轻量级模型，适合实时处理
   YOLO('yolov8x-seg.pt')  # 大型模型，适合高质量处理
   ```

## 使用方法

### 使用启动脚本（推荐）

最简单的方法是使用提供的PowerShell启动脚本：

```
.\start_video_processor.ps1
```

这将自动：
1. 检测可用的视频文件
2. 启动离线视频处理服务（端口8080）
3. 启动实时视频流服务（端口8081）

### 手动启动

#### 离线视频处理

```
python direct_video_processor.py --port 8080 --model yolov8x-seg.pt
```

选项：
- `--port`：HTTP服务器端口，默认8080
- `--model`：YOLO模型路径，默认yolov8x-seg.pt
- `--confidence`：检测置信度阈值，默认0.5

然后访问 http://localhost:8080 查看界面。

#### 实时视频流

```
python realtime_video_processor.py --source 视频文件.mp4 --port 8081
```

选项：
- `--source`：视频源，可以是视频文件路径或摄像头索引（0为默认摄像头）
- `--port`：HTTP服务器端口，默认8081
- `--model`：YOLO模型路径，默认yolov8n.pt（轻量级模型）
- `--fps`：最大处理帧率，默认15
- `--resize`：调整视频大小的比例因子，默认0.5（减小可提高性能）

然后访问 http://localhost:8081 查看实时流。

## 界面使用

### 离线视频处理界面

- 显示所有可用的原始视频
- 点击"处理此视频"按钮开始处理
- 处理完成后，可以查看带有YOLO检测结果的视频

### 实时视频流界面

- 自动显示实时处理的视频流
- 如果连接断开，页面会自动尝试重新连接

## 故障排除

- 如果视频无法打开，请检查文件路径或摄像头连接
- 对于实时处理，如果性能较差，请尝试：
  - 降低`--resize`参数值（例如0.3）
  - 使用更小的模型（yolov8n.pt）
  - 降低最大帧率（--fps 10）
- 如果遇到OpenCV相关错误，设置环境变量：`$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"` 
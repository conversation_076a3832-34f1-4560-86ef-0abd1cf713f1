# Path to the file with encoding issues
$filePath = "src\components\OnlineMonitor.vue"
$outputPath = "src\components\OnlineMonitor.vue.fixed"
$backupPath = "src\components\OnlineMonitor.vue.bak"

# Read the file content
try {
    $content = Get-Content -Path $filePath -Raw -Encoding UTF8
    Write-Host "File read successfully"
    
    # Create a backup of the original file
    $content | Set-Content -Path $backupPath -Encoding UTF8
    Write-Host "Backup created at $backupPath"
    
    # Define replacements - map mojibake patterns to correct Chinese characters
    $replacements = @{
        "锟斤拷" = "安"
        "锟斤拷锟斤拷" = "安全"
        "锟竭凤拷锟斤拷" = "高风险"
        "锟酵凤拷锟斤拷" = "低风险"
        "锟叫凤拷锟斤拷" = "中风险"
        "锟斤拷员" = "人员"
        "锟斤拷" = "人"
        "锟斤拷锟斤拷" = "车辆"
        "锟借备" = "设备"
        "锟斤拷械" = "机械"
    }
    
    # Apply the replacements
    $fixedContent = $content
    foreach ($key in $replacements.Keys) {
        $fixedContent = $fixedContent -replace [regex]::Escape($key), $replacements[$key]
    }
    
    # Write the fixed content to the output file
    $fixedContent | Set-Content -Path $outputPath -Encoding UTF8
    Write-Host "Fixed file created at $outputPath"
    
    # Provide instructions for replacing the original file
    Write-Host "To replace the original file with the fixed version, run:"
    Write-Host "Copy-Item -Path `"$outputPath`" -Destination `"$filePath`" -Force"
    
} catch {
    Write-Error "An error occurred: $_"
} 
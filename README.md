# Traffic Eyes - 交通图像分析系统

基于 Qwen 2.5 VL 模型的交通图像分析系统，可以检测和分析交通场景中的各种对象。

## 项目结构

```
traffic-eyes/
├── public/                # 静态资源
│   └── favicon.ico        # 网站图标
├── src/                   # 前端源代码
│   ├── assets/            # 资源文件（图片、样式等）
│   ├── components/        # Vue组件
│   │   └── ObjectDetectionPanel.vue  # 对象检测面板组件
│   ├── router/            # 路由配置
│   ├── services/          # 服务层
│   │   └── DetectionService.ts       # 检测服务
│   ├── stores/            # 状态管理
│   ├── views/             # 页面视图
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── qwen-vl-backend/       # 后端代码
│   ├── static/            # 静态文件
│   ├── templates/         # 模板文件
│   ├── main.py            # 后端主程序
│   ├── detection_api.py   # 检测API
│   └── requirements.txt   # 依赖列表
├── index.html             # HTML入口文件
├── package.json           # 项目配置
├── vite.config.ts         # Vite配置
└── start.ps1              # 启动脚本
```

## 功能特性

- 上传交通场景图片进行分析
- 检测图像中的车辆、行人、交通标志等对象
- 显示检测结果和边界框
- 生成场景描述和分析报告

## 技术栈

### 前端
- Vue 3 + TypeScript
- Vite
- Element Plus
- Vue Router
- Pinia

### 后端
- FastAPI
- Qwen 2.5 VL 模型
- DashScope API

## 安装与运行

### 安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd qwen-vl-backend
pip install -r requirements.txt
```

### 配置环境变量

在 `qwen-vl-backend` 目录下创建 `.env` 文件，添加以下内容：

```
DASHSCOPE_API_KEY=your_api_key_here
```

您可以在[灵积模平台](https://bailian.console.aliyuncs.com/)获取API密钥。

> 注意：所有使用Qwen API的代码现在统一从环境变量中获取API密钥，不再使用硬编码的密钥。

### 启动应用

使用启动脚本一键启动前后端：

```bash
# Windows
.\start.ps1

# Linux/Mac
./start.sh
```

或者分别启动前后端：

```bash
# 启动后端
cd qwen-vl-backend
python main.py

# 启动前端（在另一个终端）
npm run dev
```

## 使用方法

1. 打开浏览器访问 http://localhost:5173
2. 上传交通场景图片
3. 选择检测选项
4. 点击"开始检测"按钮
5. 查看检测结果和分析报告

## 开发指南

### 目录结构说明

- `src/components/`: 包含所有Vue组件
- `src/services/`: 包含与后端API交互的服务
- `src/views/`: 包含页面级组件
- `qwen-vl-backend/`: 包含后端API实现

### 添加新功能

1. 在 `src/components/` 中创建新组件
2. 在 `src/services/` 中添加相应的API调用
3. 在 `src/views/` 中集成新组件
4. 在 `qwen-vl-backend/` 中实现相应的后端API

## License

MIT 

# Traffic Eyes - 道路监控和风险检测系统

基于YOLOv11和大语言模型的道路监控系统，可实时检测道路风险，包括路面结构风险。

## 新功能：YOLO实时检测与延迟播放

系统新增了使用YOLO11x-seg模型进行实时视频流检测的功能，并通过延迟播放机制确保检测结果和视频画面同步显示。

### 使用方法

1. 启动后端服务：
   ```
   cd qwen-vl-backend
   python main.py
   ```

2. 启动前端服务：
   ```
   npm run dev
   ```

3. 在界面中选择"路面结构风险监测"，系统将自动启用YOLO检测功能。

4. 控制选项：
   - 点击"延迟播放"按钮：启用延迟播放模式，系统会先缓冲几帧再播放，确保检测结果与视频画面完全同步
   - 点击"实时播放"按钮：禁用延迟，直接显示实时视频流，但检测结果可能会有延迟
   - 点击"停止监控"按钮：暂停检测但不停止视频播放

### 延迟播放模式原理

1. 使用WebSocket与后端建立实时连接
2. 捕获视频帧并发送到后端进行YOLO检测
3. 同时将视频帧缓存在前端
4. 从缓存中延迟播放视频帧，与检测结果同步显示
5. 实现了检测无延迟，播放有延迟的效果

### 配置参数

- 缓冲帧数：5帧（默认），可在代码中调整`bufferSize.value`
- 捕获频率：5fps（默认），可在`startBufferedPlayback`中调整间隔时间
- 播放频率：25fps（默认），可在`startBufferPlayback`中调整
- YOLO模型：yolo11n-seg.pt，支持分割和目标跟踪功能

### 检测能力

系统可以检测：
- 路面结构问题：裂缝、坑洼、翻浆等
- 人员和车辆：行人、工人、各类车辆
- 施工机械：挖掘机、推土机等

每个检测结果都会标注风险等级，并汇总显示在右侧面板中。 
@echo off
echo 更新WebSocket处理器，优化视频流处理以减少闪烁问题

REM 创建备份
echo 创建原始文件备份...
copy src\components\OnlineMonitor.vue src\components\OnlineMonitor.vue.bak

REM 更新WebSocket处理器
echo 替换WebSocket处理器，添加防闪烁优化...
powershell -command "$content = Get-Content 'src\components\OnlineMonitor.vue'; $wsHandlerContent = Get-Content 'ws_message_handler.js'; $updated = $content -replace '// Keep the original onmessage handler\r?\n\s+// \.\.\.', $wsHandlerContent; $updated | Out-File 'src\components\OnlineMonitor.vue' -Encoding UTF8"

REM 更新视频捕获函数
echo 优化视频帧捕获函数...
powershell -command "$content = Get-Content 'src\components\OnlineMonitor.vue'; $pattern = 'const captureAndProcessFrame[\s\S]*?setTimeout\(\(\) => \{\s*if \(videoPlayerElement\.value \&\& !videoPlayerElement\.value\.paused\) \{\s*captureAndProcessFrame\(\);\s*\}\s*\}, 1000\);[\s\S]*?\};'; $replacement = (Get-Content 'captureAndProcessFrame.js' -Raw); $updated = $content -replace $pattern, $replacement; $updated | Out-File 'src\components\OnlineMonitor.vue' -Encoding UTF8"

echo 更新完成! 
echo 原始文件已备份为 OnlineMonitor.vue.bak
echo.
echo 优化内容:
echo 1. 添加帧率控制和双缓冲，减少闪烁
echo 2. 使用requestAnimationFrame优化渲染，提高平滑度
echo 3. 降低对Qwen API的调用频率，提高性能
echo 4. 添加图像加载超时处理，防止卡死
echo 5. 优化Canvas尺寸调整，确保与视频同步 
# Traffic Eyes ??????????????
# ?????????????????

Write-Host "??? Traffic Eyes ???????????.." -ForegroundColor Green

# ??????????????penCV???
$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"

# ????ython???
$py = "python"
try {
    $ver = & $py --version 2>&1
    Write-Host "Python???: $ver" -ForegroundColor Green
} catch {
    Write-Host "??????Python??????Python 3.8?????????? -ForegroundColor Red
    exit 1
}

# ??????????
$videoFiles = Get-ChildItem -Path . -Filter *.mp4 | Select-Object -ExpandProperty Name
if ($videoFiles.Count -eq 0) {
    Write-Host "?????????????????????????? -ForegroundColor Yellow
    $videoSource = "0"  # ?????
} else {
    $videoSource = $videoFiles[0]
    Write-Host "?????????: $videoSource" -ForegroundColor Yellow
}

# ???????????????
Write-Host "??????????????? (8081)..." -ForegroundColor Cyan
$cmd = "$py realtime_video_processor.py --source $videoSource --port 8081"
Write-Host "??????: $cmd" -ForegroundColor Yellow

# ????????
Start-Process "http://localhost:8081" -WindowStyle Normal

# ?????????
Invoke-Expression $cmd 

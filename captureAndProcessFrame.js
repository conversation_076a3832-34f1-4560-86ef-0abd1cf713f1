const captureAndProcessFrame = () => {
  if (!videoPlayerElement.value || videoPlayerElement.value.paused || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
    return;
  }
  
  try {
    // 控制帧率，每秒最多处理10帧
    if (frameCounter % 3 !== 0) {
      frameCounter++;
      // 递减递归调用使用requestAnimationFrame，避免setTimeout累积延迟
      requestAnimationFrame(captureAndProcessFrame);
      return;
    }
    
    // 确保canvas与视频尺寸匹配
    if (detectionCanvasElement.value) {
      const video = videoPlayerElement.value;
      if (detectionCanvasElement.value.width !== video.videoWidth || 
          detectionCanvasElement.value.height !== video.videoHeight) {
        detectionCanvasElement.value.width = video.videoWidth || 640;
        detectionCanvasElement.value.height = video.videoHeight || 360;
        console.log(`调整检测画布尺寸为: ${detectionCanvasElement.value.width}x${detectionCanvasElement.value.height}`);
      }
    }

    // 捕获当前视频帧并优化图像质量
    const frameDataUrl = captureFrame();
    if (!frameDataUrl) return;
    
    // 生成帧ID和时间戳
    frameCounter++;
    const frameId = `frame_${Date.now()}_${frameCounter}`;
    const timestamp = Date.now();
    
    // 发送到WebSocket进行处理，同时添加确保可视化的参数
    wsConnection.send(JSON.stringify({
      frame: frameDataUrl.split(',')[1], // 只发送Base64部分
      frameId: frameId,
      timestamp: timestamp,
      config: {
        segmentation: true, 
        confidence: 0.25,
        process_with_qwen: frameCounter % 15 === 0, // 每15帧处理一次Qwen API，减少API调用频率
        visualize: true,         // 确保启用可视化
        show_masks: true,        // 显式要求显示掩膜
        img_size: 640,           // 处理图像尺寸
        iou_thres: 0.45,         // 提高IOU阈值，减少重叠框
        max_det: 50              // 限制最大检测数量，提高性能
      },
      captureTime: Date.now()    // 添加捕获时间，便于性能分析
    }));
    
    // 使用requestAnimationFrame调度下一帧捕获，实现更平滑的处理
    requestAnimationFrame(captureAndProcessFrame);
  } catch (error) {
    console.error('处理视频帧时出错:', error);
    // 错误恢复：短暂延迟后尝试继续捕获
    setTimeout(() => {
      if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
        requestAnimationFrame(captureAndProcessFrame);
      }
    }, 1000);
  }
}; 
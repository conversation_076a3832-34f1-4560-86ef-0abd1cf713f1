/**
 * YoloWorker.js - Web Worker for YOLO WebSocket通信
 * 
 * 此worker负责在后台线程中处理WebSocket连接和YOLO检测请求，
 * 以避免阻塞主UI线程，实现高性能的视频处理
 * 
 * 更新: 现在连接到API服务器的ezviz-stream端点
 */

// WebSocket连接状态
let wsConnection = null;
let isConnecting = false;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 2000;
let wsEndpoint = 'ws://localhost:8001/ws/ezviz-stream';

// 连接到WebSocket服务器
function connectWebSocket() {
  if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    self.postMessage({ type: 'ws_status', status: 'connected' });
    return Promise.resolve();
  }
  
  if (isConnecting) {
    return Promise.reject(new Error('Connection attempt already in progress'));
  }
  
  isConnecting = true;
  
  return new Promise((resolve, reject) => {
    try {
      wsConnection = new WebSocket(wsEndpoint);
      
      wsConnection.onopen = () => {
        console.log('[YoloWorker] WebSocket connection established');
        isConnecting = false;
        reconnectAttempts = 0;
        self.postMessage({ type: 'ws_status', status: 'connected' });
        resolve();
      };
      
      wsConnection.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          self.postMessage({ type: 'yolo_result', data });
        } catch (error) {
          self.postMessage({ 
            type: 'error', 
            error: `解析YOLO结果失败: ${error.message}` 
          });
        }
      };
      
      wsConnection.onerror = (error) => {
        console.error('[YoloWorker] WebSocket error:', error);
        self.postMessage({ 
          type: 'error', 
          error: 'WebSocket连接错误' 
        });
      };
      
      wsConnection.onclose = (event) => {
        console.log('[YoloWorker] WebSocket closed, code:', event.code);
        isConnecting = false;
        self.postMessage({ type: 'ws_status', status: 'disconnected' });
        
        // 尝试重新连接
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
          setTimeout(() => {
            reconnectAttempts++;
            connectWebSocket().catch(error => {
              self.postMessage({ 
                type: 'error', 
                error: `重连失败 (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}): ${error.message}` 
              });
            });
          }, RECONNECT_DELAY);
        } else {
          self.postMessage({ 
            type: 'error', 
            error: '已达到最大重连尝试次数' 
          });
        }
      };
    } catch (error) {
      isConnecting = false;
      reject(error);
    }
  });
}

// 处理视频帧
function processFrame(frameData, config) {
  if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
    return connectWebSocket().then(() => sendFrame(frameData, config)).catch(error => {
      self.postMessage({ 
        type: 'error', 
        error: `无法发送帧: ${error.message}` 
      });
    });
  }
  
  return sendFrame(frameData, config);
}

// 发送帧数据到YOLO服务器
function sendFrame(frameData, config) {
  try {
    // 如果是base64 URL格式，去除前缀
    let base64Data = frameData;
    if (frameData.startsWith('data:image')) {
      base64Data = frameData.split(',')[1];
    }
    
    // 构建请求消息
    const message = JSON.stringify({
      frame: base64Data,
      frame_count: config.frameCount || 0,
      timestamp: Date.now(),
      stream_id: config.streamId || 'default',
      config: {
        segmentation: config.segmentation !== false,
        confidence: config.confidence || 0.25
      }
    });
    
    wsConnection.send(message);
    return Promise.resolve();
  } catch (error) {
    return Promise.reject(error);
  }
}

// 关闭连接
function closeConnection() {
  if (wsConnection) {
    if (wsConnection.readyState === WebSocket.OPEN) {
      // 发送关闭消息到服务器
      wsConnection.send(JSON.stringify({ action: 'close' }));
    }
    wsConnection.close();
    wsConnection = null;
  }
}

// 监听主线程消息
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'connect':
      // 连接到WebSocket服务器
      if (data && data.endpoint) {
        wsEndpoint = data.endpoint;
      }
      connectWebSocket().catch(error => {
        self.postMessage({ 
          type: 'error', 
          error: `连接失败: ${error.message}` 
        });
      });
      break;
      
    case 'process_frame':
      // 处理视频帧
      processFrame(data.frame, data.config).catch(error => {
        self.postMessage({ 
          type: 'error', 
          error: `处理帧失败: ${error.message}` 
        });
      });
      break;
      
    case 'close':
      // 关闭连接
      closeConnection();
      self.postMessage({ type: 'ws_status', status: 'closed' });
      break;
      
    default:
      console.warn(`[YoloWorker] Unknown message type: ${type}`);
  }
}); 
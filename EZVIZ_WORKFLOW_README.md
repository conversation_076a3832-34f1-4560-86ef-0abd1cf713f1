# Traffic Eyes Ezviz Cloud Video Processing Workflow

This document describes the workflow of Ezviz cloud video processing in the Traffic Eyes project.

## Workflow

1. **Frontend Sends Start Signal**
   - Frontend component (EzvizMonitor.vue) sends "start getting Ezviz cloud video frames" signal to the backend
   - Signal is sent via API request to the `/api/start-streaming` endpoint

2. **Backend Receives Signal**
   - Backend receives the signal and initializes video stream processing
   - Checks if there's already an active stream to avoid duplicate initialization
   - Creates frame buffer and processed frame buffer

3. **Video Stream Processing**
   - Backend receives video frames and adds them to the buffer
   - When the buffer reaches the specified size (about 2 seconds of video, default 20 frames):
     - Uses YOLO to process all frames in the buffer
     - Processes a representative frame (middle frame) with Qwen API in parallel
     - Processed frames are added to the processed frame buffer

4. **MJPEG Stream Transmission**
   - MJPEG stream retrieves frames from the processed frame buffer
   - Sends frames to the frontend via the `/stream` endpoint
   - Controls frame rate to ensure smooth playback

5. **Frontend Display**
   - Frontend receives and displays the processed video stream
   - Simultaneously retrieves and displays detection results and analysis data

## Main Components

### Frontend Components

- **EzvizMonitor.vue**: Ezviz cloud monitoring component, responsible for sending start signal and displaying processed video stream

### Backend Components

- **ezviz_api.py**: Main backend API, processes video stream and provides MJPEG stream
- **process_frame_buffer()**: Core function for processing frame buffer
- **generate_mjpeg()**: Function for generating MJPEG stream

## Buffer Mechanism

The system uses a two-level buffer mechanism:

1. **Input Buffer**: Stores original video frames, triggers processing when reaching specified size (about 2 seconds of video)
2. **Output Buffer**: Stores processed frames, MJPEG stream retrieves frames from here to send to the frontend

## Parallel Processing

The system uses parallel processing mechanism:

1. **YOLO Processing**: Processes all frames, performs object detection and segmentation
2. **Qwen API Processing**: Processes representative frames in parallel, performs scene analysis

## Configuration Parameters

- **buffer_duration**: Buffer duration (seconds), default 2.0 seconds
- **fps**: Frame rate, default 10 frames/second
- **buffer_size**: Buffer size (number of frames), calculated as buffer_duration * fps

## Startup Method

Use the provided PowerShell script to start the backend service:

```powershell
.\start_ezviz_backend.ps1
```

## Testing

Use the test script to verify the workflow:

```bash
python test_ezviz_workflow.py
```

## Notes

- Ensures only one Ezviz cloud video stream is obtained, avoiding duplicate initialization
- The system tracks active streams to prevent duplicate startup
- Resources are properly cleaned up when the application is closed 
#!/usr/bin/env python3
"""
模型文件标准化脚本
将所有模型文件复制到标准位置 src/models 目录
"""

import os
import sys
import shutil
import logging
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def ensure_dir_exists(dir_path):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(dir_path):
        logger.info(f"创建目录: {dir_path}")
        os.makedirs(dir_path, exist_ok=True)

def find_model_files(search_dirs):
    """在指定目录中查找模型文件"""
    model_files = []
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            logger.warning(f"目录不存在，跳过: {search_dir}")
            continue
            
        logger.info(f"在 {search_dir} 中查找模型文件...")
        for root, _, files in os.walk(search_dir):
            for file in files:
                if file.endswith('.pt'):
                    full_path = os.path.join(root, file)
                    model_files.append(full_path)
                    logger.info(f"找到模型文件: {full_path}")
    
    return model_files

def copy_models_to_standard_location(model_files, target_dir):
    """将模型文件复制到标准位置"""
    ensure_dir_exists(target_dir)
    
    copied_files = []
    for model_file in model_files:
        filename = os.path.basename(model_file)
        target_path = os.path.join(target_dir, filename)
        
        # 如果目标文件已存在，检查是否相同
        if os.path.exists(target_path):
            if os.path.samefile(model_file, target_path):
                logger.info(f"模型文件已在标准位置: {target_path}")
                copied_files.append(target_path)
                continue
                
            # 如果文件不同，添加后缀以避免覆盖
            logger.warning(f"目标位置已存在同名文件: {target_path}")
            filename_base, filename_ext = os.path.splitext(filename)
            target_path = os.path.join(target_dir, f"{filename_base}_copy{filename_ext}")
            logger.info(f"将使用新名称: {target_path}")
        
        # 复制文件
        try:
            shutil.copy2(model_file, target_path)
            logger.info(f"已复制模型文件到标准位置: {target_path}")
            copied_files.append(target_path)
        except Exception as e:
            logger.error(f"复制文件 {model_file} 到 {target_path} 失败: {str(e)}")
    
    return copied_files

def main():
    parser = argparse.ArgumentParser(description="模型文件标准化工具 - 将所有模型文件复制到标准位置")
    parser.add_argument("--target", type=str, default="src/models",
                        help="标准模型目录 (默认: src/models)")
    args = parser.parse_args()
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 标准化目标路径
    target_dir = os.path.normpath(os.path.join(current_dir, args.target))
    
    # 要搜索的目录列表
    search_dirs = [
        os.path.join(current_dir, "qwen-vl-backend"),
        os.path.join(current_dir, "qwen-vl-backend", "models"),
        os.path.join(current_dir, "models"),
    ]
    
    # 查找所有模型文件
    logger.info("开始查找模型文件...")
    model_files = find_model_files(search_dirs)
    
    if not model_files:
        logger.warning("未找到任何模型文件")
        return
    
    # 复制模型文件到标准位置
    logger.info(f"开始将模型文件复制到标准位置: {target_dir}")
    copied_files = copy_models_to_standard_location(model_files, target_dir)
    
    # 打印结果
    if copied_files:
        logger.info(f"已成功处理 {len(copied_files)} 个模型文件:")
        for file in copied_files:
            logger.info(f"  - {file}")
    else:
        logger.warning("未能成功复制任何模型文件")
    
    # 检查必要的模型文件是否存在
    required_models = ["yolo11n-seg.pt", "best.pt"]
    missing_models = []
    
    for model in required_models:
        model_path = os.path.join(target_dir, model)
        if not os.path.exists(model_path):
            missing_models.append(model)
    
    if missing_models:
        logger.warning(f"以下必要的模型文件在标准位置不存在:")
        for model in missing_models:
            logger.warning(f"  - {model}")
        logger.info("请确保这些模型文件被正确下载或复制到标准位置")
    else:
        logger.info("所有必要的模型文件都已在标准位置")

if __name__ == "__main__":
    main()

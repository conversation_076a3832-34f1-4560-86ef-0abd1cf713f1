<template>
  <div class="qwen-results-panel" :class="{ 'updating': isUpdating }">
    <div v-if="!results" class="empty-state">
      <div class="empty-icon">ℹ️</div>
      <div class="empty-message">暂无 Qwen API 分析结果</div>
    </div>
    
    <div v-else class="results-content">
      <!-- Update notification -->
      <div v-if="isUpdating" class="update-notification">
        <div class="update-spinner"></div>
        <div class="update-message">正在更新分析结果...</div>
      </div>

      <!-- 场景描述 -->
      <div class="result-section" :class="{ 'highlight-section': isUpdating }">
        <div class="section-header">
          <div class="section-icon description-icon"></div>
          <div class="section-title">场景描述</div>
          <div v-if="lastUpdated" class="update-time">{{ formatUpdateTime }}</div>
        </div>
        <div class="section-body">
          <div class="description-box" :class="{ 'highlight-content': isUpdating }" v-html="formattedDescription"></div>
        </div>
      </div>

      <!-- 检测目标 -->
      <div v-if="results.detections && results.detections.length > 0" class="result-section">
        <div class="section-header">
          <div class="section-icon detection-icon"></div>
          <div class="section-title">检测目标</div>
        </div>
        <div class="section-body">
          <div v-for="(detection, index) in results.detections" :key="index" class="detection-item" :class="getRiskClass(detection.risk_level)">
            <div class="detection-header">
              <div class="detection-category">{{ detection.category }}</div>
              <div class="detection-risk-badge" :class="getRiskClass(detection.risk_level)">
                {{ getRiskLevelText(detection.risk_level) }}
              </div>
            </div>
            <div class="detection-description">{{ detection.event_description }}</div>
            <div class="detection-confidence">可信度: {{ (detection.confidence_score * 100).toFixed(1) }}%</div>
          </div>
        </div>
      </div>

      <!-- 高风险事件 -->
      <div v-if="results.high_risk_events && results.high_risk_events.length > 0" class="result-section high-risk-section">
        <div class="section-header">
          <div class="section-icon high-risk-icon"></div>
          <div class="section-title">高风险事件</div>
        </div>
        <div class="section-body">
          <div v-for="(event, index) in results.high_risk_events" :key="index" class="risk-event-box high-risk">
            <div class="risk-event-header">
              <div class="risk-event-category">{{ event.category }}</div>
              <div class="risk-event-type">{{ event.event }}</div>
            </div>
            <div class="risk-event-description">{{ event.description || `${event.category}高风险事件` }}</div>
            <div class="risk-event-mitigation" v-if="event.mitigation">
              <div class="mitigation-label">建议措施:</div>
              <div class="mitigation-content">{{ event.mitigation }}</div>
            </div>
            <div class="risk-event-location" v-if="event.bbox_2d && event.bbox_2d.length === 4">
              <div class="location-indicator"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 低风险事件 -->
      <div v-if="results.low_risk_events && results.low_risk_events.length > 0" class="result-section low-risk-section">
        <div class="section-header">
          <div class="section-icon low-risk-icon"></div>
          <div class="section-title">低/中风险事件</div>
        </div>
        <div class="section-body">
          <div v-for="(event, index) in results.low_risk_events" :key="index" 
               class="risk-event-box" :class="getRiskClass(event.risk_level)">
            <div class="risk-event-header">
              <div class="risk-event-category">{{ event.category }}</div>
              <div class="risk-event-badge" :class="getRiskClass(event.risk_level)">
                {{ getRiskLevelText(event.risk_level) }}
              </div>
            </div>
            <div class="risk-event-description">{{ event.description || `${event.category}${getRiskLevelText(event.risk_level)}事件` }}</div>
            <div class="risk-event-mitigation" v-if="event.mitigation">
              <div class="mitigation-label">建议措施:</div>
              <div class="mitigation-content">{{ event.mitigation }}</div>
            </div>
            <div class="risk-event-location" v-if="event.bbox_2d && event.bbox_2d.length === 4">
              <div class="location-indicator"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- JSON 原始数据 -->
      <div class="result-section json-section" v-if="showRawJson">
        <div class="section-header clickable" @click="toggleRawJson">
          <div class="section-icon json-icon"></div>
          <div class="section-title">原始JSON数据</div>
          <div class="toggle-icon">{{ rawJsonExpanded ? '▼' : '▶' }}</div>
        </div>
        <div class="section-body" v-if="rawJsonExpanded">
          <pre class="json-content">{{ JSON.stringify(results, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface Detection {
  category: string;
  event_description: string;
  risk_level: string;
  confidence_score: number;
  bbox_2d: number[];
}

interface RiskEvent {
  category: string;
  event: string;
  risk_level: string;
  description: string;
  mitigation?: string;
  bbox_2d?: number[];
}

interface QwenResults {
  detections: Detection[];
  description: string;
  high_risk_events: RiskEvent[];
  low_risk_events: RiskEvent[];
}

const props = defineProps<{
  results: QwenResults | null;
  showRawJson?: boolean;
}>();

const rawJsonExpanded = ref(false);
const isUpdating = ref(false);
const lastUpdated = ref<Date | null>(new Date());
let updateTimeout: number | null = null;

// 计算格式化的描述内容，支持Markdown格式
const formattedDescription = computed(() => {
  if (!props.results || !props.results.description) return '';
  
  // 处理Markdown格式
  let formatted = props.results.description
    .replace(/### (.*?)(\n|$)/g, '<h3>$1</h3>') // 处理标题
    .replace(/\n\n/g, '<br><br>') // 处理段落
    .replace(/\n/g, '<br>') // 处理换行
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
    .replace(/\*(.*?)\*/g, '<em>$1</em>'); // 斜体
    
  return formatted;
});

// 格式化更新时间
const formatUpdateTime = computed(() => {
  if (!lastUpdated.value) return '';
  const now = new Date();
  const diff = Math.floor((now.getTime() - lastUpdated.value.getTime()) / 1000);
  
  if (diff < 60) {
    return `刚刚更新`;
  } else if (diff < 3600) {
    return `${Math.floor(diff / 60)}分钟前更新`;
  } else {
    const hours = lastUpdated.value.getHours().toString().padStart(2, '0');
    const minutes = lastUpdated.value.getMinutes().toString().padStart(2, '0');
    const seconds = lastUpdated.value.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds} 更新`;
  }
});

// Watch for results changes to show update animation and update timestamp
watch(() => props.results, (newVal, oldVal) => {
  if (newVal) {
    // Start update animation
    isUpdating.value = true;
    
    // Update timestamp
    lastUpdated.value = new Date();
    
    // Clear any existing timeout
    if (updateTimeout) {
      clearTimeout(updateTimeout);
    }
    
    // Auto-hide the update notification after 2 seconds
    updateTimeout = window.setTimeout(() => {
      isUpdating.value = false;
    }, 2000);
    
    // Scroll to ensure description is visible
    setTimeout(() => {
      const descriptionEl = document.querySelector('.description-box');
      if (descriptionEl) {
        descriptionEl.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }
}, { deep: true, immediate: true });

const toggleRawJson = () => {
  rawJsonExpanded.value = !rawJsonExpanded.value;
};

const getRiskClass = (riskLevel: string | undefined): string => {
  if (!riskLevel) return '';
  
  switch(riskLevel.toLowerCase()) {
    case 'high':
      return 'high-risk';
    case 'medium':
      return 'medium-risk';
    case 'low':
      return 'low-risk';
    default:
      return '';
  }
};

const getRiskLevelText = (riskLevel: string | undefined): string => {
  if (!riskLevel) return '未知';
  
  switch(riskLevel.toLowerCase()) {
    case 'high':
      return '高风险';
    case 'medium':
      return '中风险';
    case 'low':
      return '低风险';
    default:
      return riskLevel;
  }
};
</script>

<style scoped>
.qwen-results-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 10px;
  height: 100%;
  overflow-y: auto;
  animation: fadeIn 0.3s ease-out;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

@keyframes fadeIn {
  from {
    opacity: 0.5;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes highlight {
  0% {
    background-color: rgba(24, 144, 255, 0.2);
  }
  100% {
    background-color: transparent;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  border: 1px dashed var(--border-color, #003a8c);
  border-radius: var(--radius-md, 8px);
  color: var(--text-secondary, #aaa);
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 12px;
}

.empty-message {
  text-align: center;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.results-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.result-section {
  background: rgba(0, 21, 41, 0.3);
  border-radius: var(--radius-md, 8px);
  border: 1px solid rgba(0, 168, 255, 0.15);
  overflow: hidden;
  animation: highlight 1s ease-out;
}

.high-risk-section {
  border-left: 3px solid var(--danger-color, #ff4d4f);
}

.low-risk-section {
  border-left: 3px solid var(--warning-color, #faad14);
}

.section-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: rgba(0, 21, 41, 0.6);
  border-bottom: 1px solid rgba(0, 168, 255, 0.15);
}

.clickable {
  cursor: pointer;
}

.section-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.description-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'%3E%3C/path%3E%3C/svg%3E");
}

.detection-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm-7 7H3v4c0 1.1.9 2 2 2h4v-2H5v-4zM5 5h4V3H5c-1.1 0-2 .9-2 2v4h2V5zm14-2h-4v2h4v4h2V5c0-1.1-.9-2-2-2zm0 16h-4v2h4c1.1 0 2-.9 2-2v-4h-2v4z'%3E%3C/path%3E%3C/svg%3E");
}

.high-risk-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff4d4f'%3E%3Cpath d='M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z'%3E%3C/path%3E%3C/svg%3E");
}

.low-risk-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23faad14'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'%3E%3C/path%3E%3C/svg%3E");
}

.json-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 2l5 5h-5V4zM7 13h10v2H7v-2zm0 4h7v2H7v-2zm0-8h5v2H7V9z'%3E%3C/path%3E%3C/svg%3E");
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  flex: 1;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.toggle-icon {
  font-size: 12px;
  color: var(--text-secondary, #aaa);
}

.section-body {
  padding: 12px;
}

.description-box {
  line-height: 1.6;
  padding: 8px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.15);
  white-space: pre-wrap;
  margin-bottom: 0;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

:deep(.qwen-results-panel-main) .description-box {
  font-size: 16px;
  line-height: 1.8;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.detection-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.15);
  border-left: 2px solid var(--primary-color, #1890ff);
}

.detection-item.high-risk {
  border-left-color: var(--danger-color, #ff4d4f);
}

.detection-item.medium-risk {
  border-left-color: var(--warning-color, #faad14);
}

.detection-item.low-risk {
  border-left-color: var(--success-color, #52c41a);
}

.detection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.detection-category {
  font-weight: 600;
  font-size: 14px;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.detection-risk-badge {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: var(--primary-color, #1890ff);
  color: white;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.detection-risk-badge.high-risk {
  background-color: var(--danger-color, #ff4d4f);
}

.detection-risk-badge.medium-risk {
  background-color: var(--warning-color, #faad14);
}

.detection-risk-badge.low-risk {
  background-color: var(--success-color, #52c41a);
}

.detection-description {
  font-size: 13px;
  margin-bottom: 6px;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.detection-confidence {
  font-size: 12px;
  color: var(--text-secondary, #aaa);
}

.risk-event-box {
  position: relative;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  background-color: var(--background-light, #f5f5f5);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.risk-event-box:hover {
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.risk-event-box.high-risk {
  background-color: rgba(245, 34, 45, 0.05);
  border-left: 3px solid var(--danger-color, #f5222d);
}

.risk-event-box.medium-risk {
  background-color: rgba(250, 173, 20, 0.05);
  border-left: 3px solid var(--warning-color, #faad14);
}

.risk-event-box.low-risk {
  background-color: rgba(82, 196, 26, 0.05);
  border-left: 3px solid var(--success-color, #52c41a);
}

.risk-event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px dashed rgba(0,0,0,0.1);
}

.risk-event-category {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary, #333);
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.risk-event-type {
  font-size: 13px;
  color: var(--text-secondary, #666);
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.risk-event-badge {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  color: #fff;
  font-weight: 500;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.risk-event-badge.high-risk {
  background-color: var(--danger-color, #f5222d);
}

.risk-event-badge.medium-risk {
  background-color: var(--warning-color, #faad14);
}

.risk-event-badge.low-risk {
  background-color: var(--success-color, #52c41a);
}

.risk-event-description {
  font-size: 14px;
  margin: 8px 0;
  line-height: 1.6;
  color: var(--text-primary, #333);
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.risk-event-mitigation {
  margin-top: 8px;
  padding-top: 6px;
  border-top: 1px dashed rgba(0,0,0,0.1);
  display: flex;
  gap: 8px;
}

.mitigation-label {
  font-weight: 600;
  font-size: 13px;
  color: var(--text-secondary, #666);
  white-space: nowrap;
}

.mitigation-content {
  font-size: 13px;
  line-height: 1.5;
  color: var(--text-primary, #333);
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif;
}

.risk-event-location {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--primary-color, #1890ff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.json-content {
  font-family: 'SFMono-Regular', Consolas, "Microsoft YaHei", "SimHei", "WenQuanYi Micro Hei", 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.2);
  padding: 10px;
  border-radius: 4px;
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Add new styles for the updating state */
.qwen-results-panel.updating {
  position: relative;
}

.update-notification {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 136, 255, 0.1);
  border: 1px solid rgba(0, 136, 255, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 10;
  animation: fadeInOut 0.5s ease-in-out;
}

.update-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 136, 255, 0.3);
  border-top: 2px solid rgba(0, 136, 255, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.update-message {
  font-size: 12px;
  color: #1890ff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}

/* Highlight styles for updated content */
.highlight-section {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.5);
  animation: pulse 2s ease-in-out;
}

.highlight-content {
  background-color: rgba(24, 144, 255, 0.1);
  animation: highlightBg 2s ease-out;
}

.update-time {
  font-size: 12px;
  color: rgba(24, 144, 255, 0.8);
  margin-left: auto;
  padding: 0 8px;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 10px;
}

/* Add styles for HTML formatted description */
.description-box h3 {
  margin-top: 12px;
  margin-bottom: 8px;
  color: rgba(24, 144, 255, 0.9);
  font-size: 16px;
  font-weight: 600;
}

.description-box strong {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7); }
  70% { box-shadow: 0 0 0 4px rgba(24, 144, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0); }
}

@keyframes highlightBg {
  0% { background-color: rgba(24, 144, 255, 0.3); }
  100% { background-color: rgba(24, 144, 255, 0.1); }
}
</style> 
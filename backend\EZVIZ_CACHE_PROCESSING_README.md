# 萤石云视频缓存处理系统

## 系统概述

萤石云视频缓存处理系统是一个用于获取萤石云摄像头视频流、缓存视频帧并使用YOLO进行实时对象检测的集成解决方案。该系统不使用API形式处理视频流，而是直接在前端捕获视频帧，发送到后端进行处理，然后通过MJPEG流将处理结果返回给前端显示。

### 主要特性

- **视频帧缓冲**：在前端捕获视频帧并缓存，控制发送频率
- **YOLO对象检测**：使用YOLOv8模型进行实时对象检测和分类
- **风险级别评估**：根据检测到的对象类型自动评估风险级别
- **MJPEG流输出**：通过MJPEG流实时显示处理后的视频帧
- **多客户端支持**：支持多个客户端同时连接和处理

### 系统架构

系统由以下组件组成：

1. **前端组件 (EzvizMonitor.vue)**
   - 获取萤石云视频流
   - 捕获视频帧并缓存
   - 控制发送频率
   - 显示原始视频和处理后的视频
   - 展示检测结果和统计信息

2. **后端API (ezviz_api.py)**
   - 接收前端发送的视频帧
   - 使用YOLO模型进行对象检测
   - 处理检测结果并评估风险级别
   - 通过MJPEG流输出处理后的视频帧
   - 提供检测结果API

3. **启动脚本 (start_ezviz_service.ps1)**
   - 检查环境和依赖
   - 启动后端服务
   - 监控服务状态

## 安装指南

### 系统要求

- **前端**：Vue.js 3.x
- **后端**：Python 3.8+
- **依赖**：
  - fastapi
  - uvicorn
  - redis (可选)
  - numpy
  - opencv-python
  - pillow
  - ultralytics (YOLOv8)

### 安装步骤

1. **克隆项目仓库**

```bash
git clone <repository-url>
cd traffic-eyes
```

2. **安装前端依赖**

```bash
npm install
```

3. **安装后端依赖**

可以使用启动脚本自动安装依赖，或手动安装：

```bash
pip install fastapi uvicorn redis numpy opencv-python pillow ultralytics
```

4. **准备YOLO模型**

系统会在首次运行时自动下载YOLOv8n模型，或者您可以手动下载并放置在以下位置：

```
traffic-eyes/backend/models/yolov8n.pt
```

## 使用指南

### 启动系统

1. **启动后端服务**

在PowerShell中运行：

```powershell
cd backend
.\start_ezviz_service.ps1
```

2. **启动前端开发服务器**

```bash
npm run serve
```

3. **访问应用**

打开浏览器访问：`http://localhost:8080`（或您的前端服务器地址）

### 使用萤石云监控组件

1. 导航到「在线监控」-「萤石云监控」页面
2. 点击「开始监控」按钮启动视频流
3. 系统将自动获取萤石云视频流，并开始处理
4. 左侧显示原始视频流，右侧显示YOLO处理后的视频流
5. 下方显示检测结果和统计信息

### 配置选项

#### 前端配置

在 `EzvizMonitor.vue` 文件中可以修改以下配置：

- **萤石云认证信息**：修改 `ezvizConfig` 对象中的 `appKey`、`appSecret`、`deviceSerial` 和 `channelNo`
- **缓冲区大小**：修改 `bufferSize` 的值（默认为10帧）
- **捕获间隔**：修改 `captureInterval` 中的延迟值（默认为200ms，约5fps）

#### 后端配置

通过环境变量或修改 `start_ezviz_service.ps1` 脚本来配置：

- **API_PORT**：API服务端口（默认8000）
- **MJPEG_PORT**：MJPEG流服务端口（默认8082）
- **REDIS_HOST**：Redis主机（默认localhost）
- **REDIS_PORT**：Redis端口（默认6379）
- **REDIS_PASSWORD**：Redis密码（如果有）

## 故障排除

### 常见问题

1. **视频流无法加载**
   - 检查萤石云认证信息是否正确
   - 确认设备序列号和通道号是否正确
   - 检查网络连接是否正常

2. **YOLO处理失败**
   - 检查YOLO模型是否正确加载
   - 确认Python环境和依赖是否正确安装
   - 查看后端日志获取详细错误信息

3. **处理后的视频流不显示**
   - 确认后端服务是否正常运行
   - 检查MJPEG流URL是否正确
   - 查看浏览器控制台是否有错误信息

4. **性能问题**
   - 调整缓冲区大小和捕获间隔
   - 考虑使用更轻量级的YOLO模型（如YOLOv8n）
   - 减小处理图像的分辨率

### 日志和调试

- **前端日志**：在浏览器控制台中查看
- **后端日志**：在启动脚本的控制台输出中查看
- **API文档**：访问 `http://localhost:8000/docs` 查看API文档和测试API

## 高级配置

### 使用Redis（可选）

系统支持使用Redis进行消息传递，但这不是必需的。如果您想使用Redis：

1. 安装并启动Redis服务器
2. 在 `start_ezviz_service.ps1` 中设置正确的Redis连接信息

### 自定义YOLO模型

您可以使用不同的YOLO模型来提高检测精度或性能：

1. 下载所需的模型（如YOLOv8s.pt、YOLOv8m.pt等）
2. 将模型放置在 `backend/models/` 目录下
3. 修改 `ezviz_api.py` 中的模型路径

### 自定义风险级别规则

在 `ezviz_api.py` 文件中修改 `RISK_RULES` 字典来自定义不同对象类型的风险级别。

## 技术细节

### 视频帧缓存机制

系统使用前端缓冲区来控制发送到后端的视频帧频率：

1. 前端以固定间隔（默认200ms）捕获视频帧
2. 将帧添加到缓冲区队列
3. 当缓冲区达到指定大小时，发送最早的帧到后端处理
4. 这种机制确保了平滑的处理流程，避免了后端过载

### YOLO处理流程

1. 后端接收Base64编码的图像数据
2. 解码为OpenCV格式的图像
3. 使用YOLO模型进行对象检测
4. 处理检测结果并评估风险级别
5. 绘制检测框和标签
6. 将处理后的图像通过MJPEG流发送回前端

### MJPEG流实现

系统使用FastAPI的StreamingResponse来实现MJPEG流：

1. 为每个客户端创建一个帧队列
2. 当有新的处理结果时，将帧添加到所有活跃客户端的队列中
3. 客户端通过HTTP连接接收MJPEG流
4. 这种方式比WebSocket更适合视频流传输，并且兼容性更好

## 贡献和支持

### 贡献指南

欢迎提交问题报告、功能请求和代码贡献。请遵循以下步骤：

1. Fork项目仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开Pull Request

### 支持

如有问题或需要支持，请提交GitHub issue或联系项目维护者。

## 许可证

本项目采用MIT许可证 - 详见LICENSE文件
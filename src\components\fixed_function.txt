﻿// Initialize WebSocket for YOLO visualization
const initializeYoloWebSocket = () => {
  if (wsYoloConnection && wsYoloConnection.readyState === WebSocket.OPEN) {
    console.log('YOLO WebSocket宸茶繛鎺ワ紝鏃犻渶閲嶆柊鍒濆鍖?);
    return;
  }
  
  if (wsYoloConnection && wsYoloConnection.readyState === WebSocket.CONNECTING) {
    console.log('YOLO WebSocket姝ｅ湪杩炴帴涓紝璇风◢鍊?..');
    return;
  }
  
  console.log('鍒涘缓YOLO WebSocket杩炴帴 - 杩炴帴鍒癥OLO鍥惧儚澶勭悊鍚庣');
  addApiLog(`INFO:qwen-vl-api:姝ｅ湪杩炴帴YOLO鍥惧儚澶勭悊鍚庣: ${YOLO_BACKEND_URL}`);
  
  try {
    // 鍒涘缓鏂扮殑WebSocket杩炴帴鍒癥OLO鍚庣
    wsYoloConnection = new WebSocket(YOLO_BACKEND_URL);

    wsYoloConnection.onopen = () => {
      console.log('YOLO WebSocket杩炴帴宸插缓绔?);
      addApiLog(`INFO:qwen-vl-api:宸茶繛鎺ュ埌YOLO鍥惧儚澶勭悊鍚庣`);
      
      // 鍙戦€佹ā鍨嬮厤缃俊鎭?
      wsYoloConnection.send(JSON.stringify({
        action: 'configure',
        model: 'yolo11n-seg.pt',
        config: {
          segmentation: true,
          confidence: 0.25,
          visualize: true,
          img_size: 640
        }
      }));
      
      // 寮€濮媃OLO澶勭悊
      if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
        captureAndProcessFrame();
      }
    };
    
    wsYoloConnection.onerror = (error) => {
      console.error('YOLO WebSocket閿欒:', error);
      addApiLog(`ERROR:qwen-vl-api:YOLO鍥惧儚澶勭悊鍚庣杩炴帴澶辫触`);
    };
    
    wsYoloConnection.onclose = () => {
      console.log('YOLO WebSocket杩炴帴宸插叧闂?);
      addApiLog(`INFO:qwen-vl-api:YOLO鍥惧儚澶勭悊鍚庣杩炴帴宸插叧闂璥);
      wsYoloConnection = null;
    };
    
    wsYoloConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.error) {
          console.error('YOLO鏈嶅姟鍣ㄨ繑鍥為敊璇?', data.error);
          return;
        }
        
        // 澶勭悊YOLO妫€娴嬬粨鏋?
        if (data.processed_frame) {
          // 纭繚澶勭悊鍚庣殑甯ф暟鎹槸瀹屾暣鐨凞ata URL
          let processedFrameDataUrl = data.processed_frame;
          if (!processedFrameDataUrl.startsWith('data:image')) {
            processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
          }
          
          // 鍦╟anvas涓婃樉绀哄鐞嗗悗鐨勫抚
          if (detectionCanvasElement.value) {
            const processedImage = new Image();
            processedImage.onload = () => {
              const ctx = detectionCanvasElement.value.getContext('2d');
              if (ctx) {
                ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
                ctx.drawImage(
                  processedImage,
                  0, 0,
                  detectionCanvasElement.value.width,
                  detectionCanvasElement.value.height
                );
              }
            };
            processedImage.src = processedFrameDataUrl;
          }
          
          // 鏇存柊妫€娴嬬粨鏋?
          if (data.objects && data.objects.length > 0) {
            latestDetections.value = data.objects;
          }
          
          // 缁х画澶勭悊涓嬩竴甯?
          if (videoPlayerElement.value && !videoPlayerElement.value.paused && useYoloDetection.value) {
            requestAnimationFrame(captureAndProcessFrame);
          }
        }
      } catch (error) {
        console.error('澶勭悊YOLO WebSocket娑堟伅鏃跺嚭閿?', error);
      }
    };
  } catch (error) {
    console.error('杩炴帴鍒癥OLO鍚庣鏃跺嚭閿?', error);
    addApiLog(`ERROR:qwen-vl-api:杩炴帴鍒癥OLO鍥惧儚澶勭悊鍚庣澶辫触: ${error.message}`);
  }
}; 

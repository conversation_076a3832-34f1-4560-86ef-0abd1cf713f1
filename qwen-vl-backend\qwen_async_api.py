"""
异步Qwen API调用模块 - 处理异步API请求而不阻塞主YOLO处理流程
"""

import os
import json
import asyncio
import logging
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
import queue
from threading import Thread
import base64
from io import BytesIO
from PIL import Image
import numpy as np
import cv2

# 导入smart_resize函数
from qwen_vl_utils import smart_resize

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建线程池
_thread_pool = ThreadPoolExecutor(max_workers=2)

# 创建请求队列
_request_queue = queue.Queue(maxsize=100)  # 最多积压100个请求
_result_cache = {}  # 用于存储API调用结果

class QwenAsyncAPIWorker:
    """异步处理Qwen API请求的工作线程"""
    
    def __init__(self):
        self.running = False
        self.worker_thread = None
    
    def start(self):
        """启动异步工作线程"""
        if self.running:
            return
            
        self.running = True
        self.worker_thread = Thread(target=self._process_queue, daemon=True)
        self.worker_thread.start()
        logger.info("Qwen异步API工作线程已启动")
    
    def stop(self):
        """停止工作线程"""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
            logger.info("Qwen异步API工作线程已停止")
    
    def _process_queue(self):
        """处理队列中的请求"""
        while self.running:
            try:
                # 尝试从队列获取请求
                request = _request_queue.get(block=True, timeout=1)
                if request:
                    frame_id = request.get('frame_id')
                    image_data = request.get('image_data')
                    detected_objects = request.get('detected_objects')
                    
                    # 调用Qwen API
                    logger.info(f"处理异步Qwen API请求: frame_id={frame_id}")
                    api_result = self._call_qwen_api(image_data, detected_objects)
                    
                    # 缓存结果
                    if frame_id and api_result:
                        _result_cache[frame_id] = {
                            'result': api_result,
                            'timestamp': time.time()
                        }
                    
                    # 标记任务完成
                    _request_queue.task_done()
            except queue.Empty:
                # 队列为空，等待一段时间
                time.sleep(0.1)
            except Exception as e:
                logger.error(f"处理Qwen API请求队列时出错: {str(e)}")
                traceback.print_exc()
    
    def _call_qwen_api(self, img, detected_objects):
        """调用Qwen API分析图像"""
        try:
            # 检查API密钥
            api_key = os.getenv('DASHSCOPE_API_KEY')
            if not api_key:
                logger.error("DASHSCOPE_API_KEY 环境变量未设置")
                return None

            # 将图像转为PIL Image对象
            if isinstance(img, np.ndarray):
                pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            elif isinstance(img, Image.Image):
                pil_img = img
            else:
                raise ValueError(f"不支持的图像类型: {type(img)}")
            
            # 使用smart_resize调整图像大小，设置更严格的大小限制
            width, height = pil_img.size
            # 减小最大像素数，原来是2048*28*28，改为较小的值
            min_pixels = 512*16*16
            max_pixels = 1024*28*28  # 减小最大像素限制
            
            new_height, new_width = smart_resize(height, width, min_pixels, max_pixels)
            
            # 如果尺寸发生变化，调整图像大小
            if new_height != height or new_width != width:
                logger.info(f"调整图像从 {width}x{height} 到 {new_width}x{new_height}")
                pil_img = pil_img.resize((new_width, new_height))
                
            # 降低JPEG质量以减小文件大小
            buffered = BytesIO()
            pil_img.save(buffered, format="JPEG", quality=85)  # 降低质量从默认95到85
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
            
            # 记录图像大小以便调试
            logger.info(f"图像Base64大小: {len(img_base64) / 1024:.2f} KB")
            
            # 导入DashScope API
            try:
                import dashscope
                from dashscope import MultiModalConversation
                
                # 构建消息
                detected_info = []
                for obj in detected_objects:
                    detected_info.append(f"{obj.get('category', '未知')}({obj.get('label', '未知')}): 置信度 {obj.get('confidence', 0):.2f}")
                
                detected_summary = ", ".join(detected_info)
                
                prompt = f"""
                请分析以下场景图像，并提供详细信息:
                
                YOLO检测到的对象: {detected_summary}
                
                请提供以下内容:
                1. 场景简要描述
                2. 潜在风险分析
                3. 结构化JSON响应
                
                JSON响应应包含以下字段:
                - description: 场景描述
                - detections: 检测到的对象列表
                - high_risk_events: 高风险事件列表
                - low_risk_events: 低/中风险事件列表
                
                请直接以JSON格式返回结果，不要多余的文字。
                """
                
                messages = [
                    {
                        'role': 'user',
                        'content': [
                            {'image': img_base64},
                            {'text': prompt}
                        ]
                    }
                ]
                
                # 发起API请求
                logger.info("调用Qwen API进行场景分析")
                response = MultiModalConversation.call(
                    model='qwen-vl-max-1119',
                    messages=messages,
                    result_format='message',
                    api_key=api_key
                )
                
                # 检查API响应
                if response.status_code == 200:
                    logger.info("Qwen API返回成功")
                    response = response['output']['choices'][0]['message']['content'][0]['text']
                    
                    # 提取JSON部分
                    if isinstance(response, str):
                        # 尝试提取JSON部分
                        start_pos = response.find('{')
                        end_pos = response.rfind('}') + 1
                        
                        if start_pos != -1 and end_pos != -1 and end_pos > start_pos:
                            json_text = response[start_pos:end_pos]
                        else:
                            # 没有找到JSON，使用整个响应
                            json_text = response
                            
                        # 处理Python风格的布尔值和None值
                        json_text = json_text.replace("'", '"').replace('True', 'true').replace('False', 'false').replace('None', 'null')
                        
                        # 尝试解析JSON
                        try:
                            result = json.loads(json_text)
                            logger.info("成功解析结构化JSON响应")
                            
                            # 确保结果包含必要的字段
                            if not isinstance(result, dict):
                                result = {"description": str(result)}
                                
                            # 添加默认字段
                            if "detections" not in result:
                                result["detections"] = detected_objects
                            if "description" not in result:
                                result["description"] = "场景分析完成"
                                
                            # 处理风险事件
                            if "high_risk_events" not in result:
                                # 创建高风险事件列表
                                result["high_risk_events"] = []
                                for obj in detected_objects:
                                    if obj.get("risk_level") == "high":
                                        result["high_risk_events"].append({
                                            "category": obj.get("category", "未知"),
                                            "event": obj.get("label", "未知事件"),
                                            "risk_level": "high",
                                            "description": f"{obj.get('category', '未知')}存在高风险隐患",
                                            "bbox_2d": obj.get("bbox", [0, 0, 0, 0])
                                        })
                            
                            if "low_risk_events" not in result:
                                # 创建低/中风险事件列表
                                result["low_risk_events"] = []
                                for obj in detected_objects:
                                    if obj.get("risk_level") in ["low", "medium"]:
                                        result["low_risk_events"].append({
                                            "category": obj.get("category", "未知"),
                                            "event": obj.get("label", "未知事件"),
                                            "risk_level": obj.get("risk_level", "low"),
                                            "description": f"{obj.get('category', '未知')}存在{obj.get('risk_level', 'low')}风险隐患",
                                            "bbox_2d": obj.get("bbox", [0, 0, 0, 0])
                                        })
                                
                            # 将解析后的JSON转回字符串
                            return json.dumps(result, ensure_ascii=False)
                            
                        except json.JSONDecodeError:
                            logger.warning("无法解析Qwen API响应为有效JSON，将返回原始响应")
                            # 创建一个默认结构
                            default_result = {
                                "detections": detected_objects,
                                "description": response,
                                "high_risk_events": [],
                                "low_risk_events": []
                            }
                            
                            # 为高风险和低风险事件添加详细信息
                            for obj in detected_objects:
                                if obj.get("risk_level") == "high":
                                    default_result["high_risk_events"].append({
                                        "category": obj.get("category", "未知"),
                                        "event": obj.get("label", "未知事件"),
                                        "risk_level": "high",
                                        "description": f"{obj.get('category', '未知')}存在高风险隐患",
                                        "bbox_2d": obj.get("bbox", [0, 0, 0, 0])
                                    })
                                elif obj.get("risk_level") in ["low", "medium"]:
                                    default_result["low_risk_events"].append({
                                        "category": obj.get("category", "未知"),
                                        "event": obj.get("label", "未知事件"),
                                        "risk_level": obj.get("risk_level", "low"),
                                        "description": f"{obj.get('category', '未知')}存在{obj.get('risk_level', 'low')}风险隐患",
                                        "bbox_2d": obj.get("bbox", [0, 0, 0, 0])
                                    })
                            
                            return json.dumps(default_result, ensure_ascii=False)
                    else:
                        logger.warning(f"Qwen API返回非字符串结果: {type(response)}")
                        return json.dumps({"description": str(response)}, ensure_ascii=False)
                else:
                    logger.error(f"Qwen API请求失败: {response.status_code} - {response.message}")
                    return None
                
            except ImportError as e:
                logger.error(f"导入API模块失败: {str(e)}")
                return None
            except Exception as e:
                logger.error(f"调用Qwen API失败: {str(e)}")
                return None
                
        except Exception as e:
            logger.error(f"异步调用Qwen API时发生错误: {str(e)}")
            traceback.print_exc()
            return None

# 创建并启动工作线程实例
_worker = QwenAsyncAPIWorker()

def start_worker():
    """启动Qwen异步API工作线程"""
    global _worker
    _worker.start()

def stop_worker():
    """停止Qwen异步API工作线程"""
    global _worker
    _worker.stop()

def submit_request(frame_id, image_data, detected_objects):
    """
    提交异步Qwen API请求
    
    Args:
        frame_id: 帧ID
        image_data: 图像数据
        detected_objects: 检测到的对象列表
    
    Returns:
        bool: 请求是否成功提交
    """
    try:
        # 如果队列已满，不阻塞而是丢弃请求
        if _request_queue.full():
            logger.warning(f"Qwen API请求队列已满，丢弃请求: frame_id={frame_id}")
            return False
            
        # 提交请求到队列
        _request_queue.put({
            'frame_id': frame_id,
            'image_data': image_data,
            'detected_objects': detected_objects
        }, block=False)
        
        logger.debug(f"已提交Qwen API异步请求: frame_id={frame_id}")
        return True
    except queue.Full:
        logger.warning(f"Qwen API请求队列已满，丢弃请求: frame_id={frame_id}")
        return False
    except Exception as e:
        logger.error(f"提交Qwen API请求时出错: {str(e)}")
        return False

def get_result(frame_id, timeout=0.1):
    """
    获取异步Qwen API调用结果
    
    Args:
        frame_id: 帧ID
        timeout: 等待超时时间（秒）
    
    Returns:
        dict/None: API调用结果或None（如果结果尚未准备好）
    """
    # 查询结果缓存
    if frame_id in _result_cache:
        # 获取结果并从缓存中移除
        result = _result_cache.pop(frame_id)
        return result.get('result')
    
    return None

def cleanup_old_results(max_age_seconds=300):
    """
    清理旧的结果
    
    Args:
        max_age_seconds: 结果最大保留时间（秒）
    """
    now = time.time()
    keys_to_remove = []
    
    for frame_id, result_data in _result_cache.items():
        if now - result_data.get('timestamp', 0) > max_age_seconds:
            keys_to_remove.append(frame_id)
    
    for key in keys_to_remove:
        _result_cache.pop(key, None)
    
    if keys_to_remove:
        logger.info(f"清理了 {len(keys_to_remove)} 个过期的Qwen API结果")

# 定期清理旧的结果
def start_cleanup_task():
    """启动定期清理任务"""
    def _cleanup_task():
        while True:
            try:
                cleanup_old_results()
                time.sleep(60)  # 每分钟清理一次
            except Exception as e:
                logger.error(f"清理旧结果时出错: {str(e)}")
                time.sleep(60)
    
    cleanup_thread = Thread(target=_cleanup_task, daemon=True)
    cleanup_thread.start()
    logger.info("Qwen API结果清理任务已启动") 
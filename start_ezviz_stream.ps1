# Traffic Eyes Ezviz Cloud Camera Stream Processor
# Uses yolo11n-seg.pt for high-quality detection and segmentation

Write-Host "Starting Traffic Eyes Ezviz Cloud Camera Stream Processor..." -ForegroundColor Green

# Set environment variables to avoid OpenCV errors
$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"

# Check Python environment
$py = "python"
try {
    $ver = & $py --version 2>&1
    Write-Host "Python version: $ver" -ForegroundColor Green
} catch {
    Write-Host "Python not detected. Please install Python 3.8 or later." -ForegroundColor Red
    exit 1
}

# Check for required Python packages
$pkgs = @("fastapi", "uvicorn", "redis", "websocket-client", "numpy", "opencv-python", "ultralytics", "pillow")
$toInstall = @()
foreach ($p in $pkgs) {
    $res = & $py -m pip show $p 2>&1
    if (-not $res) { $toInstall += $p }
}
if ($toInstall.Count -gt 0) {
    Write-Host "Installing missing packages: $($toInstall -join ', ')" -ForegroundColor Yellow
    & $py -m pip install $toInstall
}

# Check Redis service
Write-Host "Configuring Redis..." -ForegroundColor Cyan
& $py configure_redis.py

# Check if model exists
$modelPath = "qwen-vl-backend/../src/models/yolo11n-seg.pt"
if (-not (Test-Path $modelPath)) {
    Write-Host "Downloading YOLO model..." -ForegroundColor Cyan
    & $py -c "from ultralytics import YOLO; YOLO('yolov8x-seg.pt')"
} else {
    Write-Host "YOLO model found: $modelPath" -ForegroundColor Green
}

# Modify the realtime_video_processor.py to use the yolo11n-seg.pt model
Write-Host "Starting real-time video processor with advanced YOLO model..." -ForegroundColor Cyan

# Start the processor with high-quality model and camera source
$port = 8081
$cmd = "$py realtime_video_processor.py --source 0 --port $port --model 'yolov8x-seg.pt' --fps 10 --resize 0.5"
Write-Host "Executing command: $cmd" -ForegroundColor Yellow

# Launch browser
Start-Process "http://localhost:$port" -WindowStyle Normal

# Start video processing
Invoke-Expression $cmd 
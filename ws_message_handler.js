// WebSocket message handler for YOLO detection with optimizations
// 添加帧率控制变量
let lastFrameTime = 0;
const MIN_FRAME_INTERVAL = 33; // 约30FPS，控制帧率以减少闪烁
let pendingFrame = null; // 用于双缓冲
let processingFrame = false; // 防止帧处理重叠

wsConnection.onmessage = (event) => {
  try {
    const data = JSON.parse(event.data);

    if (data.error) {
      console.error('服务器返回错误:', data.error);
      addApiLog(`ERROR:qwen-vl-api:${data.error}`);
      return;
    }

    // 处理返回的YOLO检测结果
    if (data.processed_frame) {
      // 确保处理后的帧数据是完整的Data URL
      let processedFrameDataUrl = data.processed_frame;
      if (!processedFrameDataUrl.startsWith('data:image')) {
        // 如果只收到了纯Base64，添加前缀
        processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
      }
      
      // 实现帧率控制和双缓冲，减少闪烁
      const now = Date.now();
      if (processingFrame) {
        // 如果正在处理帧，保存这一帧以备下次使用
        pendingFrame = processedFrameDataUrl;
        return;
      }
      
      if (now - lastFrameTime < MIN_FRAME_INTERVAL) {
        // 如果距离上次渲染不足33ms，使用双缓冲模式延迟渲染
        pendingFrame = processedFrameDataUrl;
        setTimeout(() => {
          if (pendingFrame) {
            renderFrame(pendingFrame);
            pendingFrame = null;
          }
        }, MIN_FRAME_INTERVAL - (now - lastFrameTime));
        return;
      }
      
      // 正常渲染当前帧
      renderFrame(processedFrameDataUrl);
    }

    // 处理返回的Qwen API分析结果
    if (data.qwen_results || data.analysis_results) {
      // 处理Qwen返回结果
      const analysisData = data.qwen_results || data.analysis_results;
      console.log('收到Qwen API分析结果，更新UI');
      
      // 更新UI状态
      addApiLog(`INFO:yolo_video_processor:Qwen API返回成功`);
      addApiLog(`INFO:yolo_video_processor:Qwen API分析结果: ${JSON.stringify(analysisData)}`);
      
      // 如果有分析描述，更新到qwenResults
      if (analysisData.description) {
        qwenResults.value = {
          detections: analysisData.detections || [],
          description: analysisData.description,
          high_risk_events: analysisData.high_risk_events || [],
          low_risk_events: analysisData.low_risk_events || []
        };
        lastUpdated.value = new Date();
      }
    }
  } catch (error) {
    console.error('处理WebSocket消息时出错:', error);
    processingFrame = false; // 确保错误情况下重置状态
  }
};

// 抽取渲染逻辑到单独的函数，便于帧率控制
function renderFrame(frameDataUrl) {
  if (!detectionCanvasElement.value) return;
  
  processingFrame = true;
  lastFrameTime = Date.now();
  
  const processedImage = new Image();
  processedImage.onload = () => {
    const ctx = detectionCanvasElement.value?.getContext('2d');
    if (ctx) {
      // 确保canvas尺寸与视频匹配
      if (detectionCanvasElement.value.width !== videoPlayerElement.value?.videoWidth ||
          detectionCanvasElement.value.height !== videoPlayerElement.value?.videoHeight) {
        detectionCanvasElement.value.width = videoPlayerElement.value?.videoWidth || 640;
        detectionCanvasElement.value.height = videoPlayerElement.value?.videoHeight || 360;
      }
      
      // 使用requestAnimationFrame优化渲染，减少闪烁
      requestAnimationFrame(() => {
        // 清除画布
        ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
        
        // 启用图像平滑，提高质量
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // 绘制处理后的图像（包含YOLO标注）
        ctx.drawImage(
          processedImage,
          0, 0,
          detectionCanvasElement.value.width,
          detectionCanvasElement.value.height
        );
        
        processingFrame = false;
        
        // 检查是否有待处理的帧
        if (pendingFrame) {
          setTimeout(() => {
            const nextFrame = pendingFrame;
            pendingFrame = null;
            renderFrame(nextFrame);
          }, MIN_FRAME_INTERVAL);
        }
      });
    } else {
      processingFrame = false;
    }
  };
  
  processedImage.onerror = (err) => {
    console.error('加载处理后的图像失败:', err);
    processingFrame = false;
  };
  
  // 设置图像加载超时，防止卡死
  const imageTimeout = setTimeout(() => {
    if (processedImage.complete === false) {
      processedImage.src = ''; // 中止加载
      console.warn('图像加载超时');
      processingFrame = false;
    }
  }, 1000);
  
  processedImage.onload = () => {
    clearTimeout(imageTimeout);
    const ctx = detectionCanvasElement.value?.getContext('2d');
    if (ctx) {
      // 确保canvas尺寸与视频匹配
      if (detectionCanvasElement.value.width !== videoPlayerElement.value?.videoWidth ||
          detectionCanvasElement.value.height !== videoPlayerElement.value?.videoHeight) {
        detectionCanvasElement.value.width = videoPlayerElement.value?.videoWidth || 640;
        detectionCanvasElement.value.height = videoPlayerElement.value?.videoHeight || 360;
      }
      
      // 使用requestAnimationFrame优化渲染，减少闪烁
      requestAnimationFrame(() => {
        // 清除画布
        ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
        
        // 启用图像平滑，提高质量
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // 绘制处理后的图像（包含YOLO标注）
        ctx.drawImage(
          processedImage,
          0, 0,
          detectionCanvasElement.value.width,
          detectionCanvasElement.value.height
        );
        
        processingFrame = false;
        
        // 检查是否有待处理的帧
        if (pendingFrame) {
          setTimeout(() => {
            const nextFrame = pendingFrame;
            pendingFrame = null;
            renderFrame(nextFrame);
          }, MIN_FRAME_INTERVAL);
        }
      });
    } else {
      processingFrame = false;
    }
  };
  
  processedImage.src = frameDataUrl;
} 
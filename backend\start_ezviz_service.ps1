# EZVIZ Cloud Video Processing Service Startup Script

# Set environment variables
$env:API_PORT = 8003          # API service port
$env:MJPEG_PORT = 8082        # MJPEG stream service port
$env:REDIS_HOST = "localhost" # Redis host
$env:REDIS_PORT = 6379        # Redis port
$env:REDIS_PASSWORD = ""      # Redis password, if any
$env:BUFFER_DURATION = 2.0    # Buffer duration in seconds
$env:FPS = 10                 # Frames per second
$env:QWEN_API_URL = "http://localhost:5000/api/qwen-analysis" # Qwen API URL
$env:USE_QWEN_API = "True"    # Enable Qwen API integration

# Check Python environment
Write-Host "Checking Python environment..." -ForegroundColor Cyan
try {
    $pythonVersion = python --version
    Write-Host "$pythonVersion installed" -ForegroundColor Green
} catch {
    Write-Host "Error: Python not found. Please install Python 3.8 or higher." -ForegroundColor Red
    exit 1
}

# Check dependencies
Write-Host "Checking dependencies..." -ForegroundColor Cyan
$requiredPackages = @("fastapi", "uvicorn", "redis", "numpy", "opencv-python", "pillow", "ultralytics")
$missingPackages = @()

foreach ($package in $requiredPackages) {
    $checkPackage = python -c "try: import $($package.Replace('-', '_')); print('installed'); except ImportError: print('not installed')"
    if ($checkPackage -eq "not installed") {
        $missingPackages += $package
    }
}

# Install missing packages
if ($missingPackages.Count -gt 0) {
    Write-Host "Installing missing dependencies..." -ForegroundColor Yellow
    foreach ($package in $missingPackages) {
        Write-Host "Installing $package..." -ForegroundColor Yellow
        pip install $package
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to install $package" -ForegroundColor Red
            exit 1
        }
    }
    Write-Host "All dependencies installed" -ForegroundColor Green
} else {
    Write-Host "All dependencies installed" -ForegroundColor Green
}

# Check models directory
$modelsDir = "./models"
if (-not (Test-Path $modelsDir)) {
    Write-Host "Creating models directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $modelsDir | Out-Null
}

# Check YOLO model
$modelPath = "./models/yolov8n.pt"
if (-not (Test-Path $modelPath)) {
    Write-Host "YOLO model does not exist, it will be automatically downloaded on first run..." -ForegroundColor Yellow
}

# Check Redis service
Write-Host "Checking Redis service..." -ForegroundColor Cyan
try {
    $redisCheck = python -c "import redis; r = redis.Redis(host='$env:REDIS_HOST', port=$env:REDIS_PORT, password='$env:REDIS_PASSWORD' if '$env:REDIS_PASSWORD' else None); r.ping(); print('Redis connection successful')"
    Write-Host "Redis connection successful" -ForegroundColor Green
} catch {
    Write-Host "Warning: Unable to connect to Redis server. Will use local processing mode only." -ForegroundColor Yellow
    Write-Host "To use Redis, please ensure the Redis server is running." -ForegroundColor Yellow
}

# Start API service
Write-Host "Starting EZVIZ Cloud video processing API service..." -ForegroundColor Cyan
Start-Process -FilePath "python" -ArgumentList "-m", "uvicorn", "ezviz_api:app", "--host", "0.0.0.0", "--port", "$env:API_PORT" -NoNewWindow

# Wait for API service to start
Write-Host "Waiting for API service to start..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

# Check if API service is running properly
try {
    $apiCheck = Invoke-RestMethod -Uri "http://localhost:$env:API_PORT/status" -Method Get
    Write-Host "API service started: $($apiCheck.timestamp)" -ForegroundColor Green
    
    # Display service information
    Write-Host "\nEZVIZ Cloud video processing service started" -ForegroundColor Green
    Write-Host "-----------------------------------" -ForegroundColor Green
    Write-Host "API service address: http://localhost:$env:API_PORT" -ForegroundColor Green
    Write-Host "MJPEG stream address: http://localhost:$env:API_PORT/stream?client_id=YOUR_CLIENT_ID" -ForegroundColor Green
    Write-Host "-----------------------------------\n" -ForegroundColor Green
    
    # Automatically open browser
    Write-Host "Open browser to view API documentation? (Y/N)" -ForegroundColor Cyan
    $openBrowser = Read-Host
    if ($openBrowser -eq "Y" -or $openBrowser -eq "y") {
        Start-Process "http://localhost:$env:API_PORT/docs"
    }
    
    # Keep script running
    Write-Host "Service is running. Press Ctrl+C to stop..." -ForegroundColor Cyan
    while ($true) {
        Start-Sleep -Seconds 10
        try {
            $status = Invoke-RestMethod -Uri "http://localhost:$env:API_PORT/status" -Method Get -TimeoutSec 1
            Write-Host "Service status: Running - Active clients: $($status.active_clients.Count) - $($status.timestamp)" -ForegroundColor Green
        } catch {
            Write-Host "Warning: Unable to connect to API service, it may have stopped running" -ForegroundColor Yellow
            break
        }
    }
} catch {
    Write-Host "Error: API service failed to start" -ForegroundColor Red
    exit 1
}
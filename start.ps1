# Traffic Eyes 启动脚本
Write-Host "启动 Traffic Eyes 应用..." -ForegroundColor Green

# 创建上传目录
$uploadDir = "./qwen-vl-backend/static/uploads"
if (-not (Test-Path $uploadDir)) {
    Write-Host "创建上传目录: $uploadDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $uploadDir -Force | Out-Null
}

# 启动后端服务
Write-Host "启动后端服务..." -ForegroundColor Cyan
Start-Process -FilePath "powershell.exe" -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File ./qwen-vl-backend/start.ps1" -WindowStyle Normal

# 等待后端启动
Write-Host "等待后端服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# 启动前端服务
Write-Host "启动前端服务..." -ForegroundColor Cyan
Start-Process -FilePath "powershell.exe" -ArgumentList "-NoProfile -ExecutionPolicy Bypass -Command npm run dev" -WindowStyle Normal

Write-Host "Traffic Eyes 应用已启动!" -ForegroundColor Green
Write-Host "前端地址: http://localhost:5173" -ForegroundColor Magenta
Write-Host "后端地址: http://localhost:8000" -ForegroundColor Magenta
Write-Host "按 Ctrl+C 停止所有服务" -ForegroundColor Yellow

# 保持脚本运行，直到用户按下 Ctrl+C
try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} finally {
    Write-Host "正在关闭服务..." -ForegroundColor Red
    # 这里可以添加清理代码
} 
const fs = require('fs');
const path = require('path');

// Path to the file with encoding issues
const filePath = path.join(__dirname, 'src', 'components', 'OnlineMonitor.vue');

// Read the file with encoding issues
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    return;
  }

  // Replace the mojibake pattern "锟斤拷" with the correct Chinese character
  // This is a simplified approach - in reality there might be different Chinese characters
  // that were encoded incorrectly, but this will at least improve readability
  
  // Common replacements
  const replacements = {
    '锟斤拷': '安', // Common safety/security character
    '锟揭?': '全', // Another common safety character
    '锟斤拷锟斤拷': '安全', // Safety
    '锟斤拷锟斤拷': '风险', // Risk
    '锟竭凤拷锟斤拷': '高风险', // High risk
    '锟酵凤拷锟斤拷': '低风险', // Low risk
    '锟叫凤拷锟斤拷': '中风险', // Medium risk
    '锟斤拷员': '人员', // Personnel
    '锟斤拷': '人', // Person
    '锟斤拷锟斤拷': '车辆', // Vehicle
    '锟借备': '设备', // Equipment
    '锟斤拷械': '机械', // Machinery
    '锟斤拷锟截伙拷': '工程机械', // Construction machinery
  };
  
  let fixedContent = data;
  
  // Apply all the replacements
  for (const [mojibake, chinese] of Object.entries(replacements)) {
    // Use a global regular expression to replace all occurrences
    const regex = new RegExp(mojibake, 'g');
    fixedContent = fixedContent.replace(regex, chinese);
  }
  
  // Also replace other instances of mojibake with placeholder
  fixedContent = fixedContent.replace(/锟[^\s]*?拷/g, '文字');
  
  // Write the fixed content back to a new file
  const backupPath = filePath + '.bak';
  const newPath = filePath + '.fixed';
  
  // First create a backup of the original file
  fs.writeFile(backupPath, data, 'utf8', (err) => {
    if (err) {
      console.error('Error creating backup file:', err);
      return;
    }
    console.log(`Backup created at ${backupPath}`);
    
    // Then write the fixed content to a new file
    fs.writeFile(newPath, fixedContent, 'utf8', (err) => {
      if (err) {
        console.error('Error writing fixed file:', err);
        return;
      }
      console.log(`Fixed file created at ${newPath}`);
      console.log('Please review the fixed file, and if it looks good, replace the original with it.');
    });
  });
}); 
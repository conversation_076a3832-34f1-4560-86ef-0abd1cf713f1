from yolo_video_processor import create_processor,logger
import os,time,base64
import numpy as np

model_path = "../src/models/yolo11x-seg.pt"
if not os.path.exists(model_path):
    model_path = "../src/models/best.pt"
processor = create_processor(model_path)

def sumbit_task_qwen_analysis(should_call_api,img,objects):
    if should_call_api:
        qwen_api_response=processor.call_qwen_api(img,objects)

        if qwen_api_response:
            logger.info("Qwen API分析结果: " + qwen_api_response[:100] + "..." if len(
                qwen_api_response) > 100 else qwen_api_response)
        return qwen_api_response

# 添加新函数，使用qwen-vl-max-1119模型处理本地视频
def sumbit_task_qwen_analysis_local_video(should_call_api, img, objects):
    """专门为本地视频处理添加的函数，使用qwen-vl-max-1119模型"""
    if should_call_api:
        try:
            # 检查API密钥
            api_key = os.getenv('DASHSCOPE_API_KEY')
            if not api_key:
                logger.error("DASHSCOPE_API_KEY 环境变量未设置")
                return None
                
            # 从processor.call_qwen_api抽取核心代码
            from detection_api import inference_with_api
            from qwen_vl_utils import smart_resize
            import json
            import re
            from PIL import Image
            import cv2
            
            # 将图像转为PIL Image对象
            if isinstance(img, np.ndarray):
                pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            elif isinstance(img, Image.Image):
                pil_img = img
            else:
                logger.error(f"不支持的图像格式: {type(img)}")
                return None
            
            # 准备提示词，包含检测到的对象信息
            object_summary = "\n".join([
                f"- {obj['category']} ({obj['label']}): 置信度 {obj['confidence']:.2f}, 风险等级: {obj['risk_level']}"
                for obj in objects
            ])
            
            # 为本地视频场景定制的提示词
            prompt = f"""请分析视频中的道路交通安全风险，并以JSON格式输出检测结果。
基于以下检测到的对象:
{object_summary}

要求：
1. 仔细观察图像中的车辆、行人和道路状况，关注交通场景中的安全隐患。
2. 识别道路交通中的安全风险事件，如车辆违规行为、行人闯红灯、道路破损等。
3. 对每个检测对象，提供类别（仅限：人员、车辆、道路设施）、具体事件描述、风险等级和置信度。
4. 提供图像的整体交通安全风险分析和管控建议，使用Markdown格式进行分段。
5. 不需要返回边界框坐标信息。
6. 分析道路状况、交通流量和可能的事故风险点。

返回格式示例如下：
{{
  "detections": [
    {{
      "category": "车辆",
      "event": "speeding",
      "risk_level": "high",
      "confidence": 0.95,
      "label": "超速行驶"
    }},
    {{
      "category": "道路设施",
      "event": "pothole",
      "risk_level": "medium",
      "confidence": 0.87,
      "label": "路面坑洼"
    }}
  ],
  "high_risk_events": [
    {{
      "category": "车辆",
      "event": "speeding",
      "risk_level": "high",
      "confidence": 0.95
    }}
  ],
  "low_risk_events": [
    {{
      "category": "道路设施",
      "event": "pothole",
      "risk_level": "medium",
      "confidence": 0.87
    }}
  ],
  "description": "#### 风险分析\n该道路场景存在一些安全隐患：有一辆车辆存在超速行驶行为，存在高风险；道路上有坑洼，可能导致车辆损坏或驾驶员失控，存在中等风险。\n\n#### 管控建议\n建议加强对该路段的速度管控，设置限速标志和测速设备；尽快修复路面坑洼，设置警示标志提醒驾驶员减速通过；增加交通巡逻，及时处理违规行为。"
}}

注意：
- 类别(category)只能是：人员、车辆、道路设施 三种之一
- 事件(event)是具体的事件类型，如speeding, jaywalking, pothole等
- 风险等级(risk_level)可以是high、medium、low
- 高风险事件放入high_risk_events，中低风险事件放入low_risk_events
- description字段必须使用Markdown格式，包含"#### 风险分析"和"#### 管控建议"两个部分
"""
            
            # 设置参数 - 使用qwen-vl-max-1119模型
            min_pixels = 512*28*28
            max_pixels = 2048*28*28
            model_id = "qwen-vl-max-1119"
            
            # 调用API
            logger.info("使用Qwen VL Max模型进行道路交通安全分析")
            response = inference_with_api(
                pil_img, 
                prompt, 
                sys_prompt="你是一个专业的道路交通安全分析专家，擅长识别交通场景中的各类风险因素。请关注道路状况、车辆行为和行人活动，分析可能存在的安全隐患。你的分析应包括车辆行驶状态、行人行为、道路设施完好程度等方面，并评估可能的事故风险。请严格按照指定格式返回JSON结果，不要添加任何解释、前缀或格式标记。",
                model_id=model_id,
                min_pixels=min_pixels, 
                max_pixels=max_pixels
            )
            
            logger.info("Qwen VL Max模型返回成功")
            
            # 处理响应 - 复用processor.call_qwen_api中的后处理代码
            try:
                # 如果响应是字符串，尝试提取JSON部分
                if isinstance(response, str):
                    # 打印原始响应的前500个字符以进行调试
                    logger.info(f"Qwen VL Max模型原始响应(前500字符): {response[:500]}...")
                    
                    # 检查是否有Markdown代码块
                    json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response)
                    if json_match:
                        # 从Markdown代码块提取JSON
                        json_text = json_match.group(1).strip()
                        logger.info("从Markdown代码块中提取到JSON")
                    else:
                        # 尝试从普通文本中提取JSON对象
                        start_pos = response.find('{')
                        end_pos = response.rfind('}') + 1
                        
                        if start_pos != -1 and end_pos != -1 and end_pos > start_pos:
                            json_text = response[start_pos:end_pos]
                            logger.info(f"从文本中提取JSON，开始于位置{start_pos}，结束于位置{end_pos}")
                        else:
                            # 没有找到JSON，使用整个响应
                            json_text = response
                            logger.warning("未能从响应中识别出JSON结构，将使用完整响应")
                            
                    # 处理Python风格的布尔值、None值和引号
                    json_text = json_text.replace("'", '"').replace('True', 'true').replace('False', 'false').replace('None', 'null')
                    
                    # 移除可能导致解析失败的控制字符
                    json_text = re.sub(r'[\x00-\x1F\x7F]', '', json_text)
                    
                    # 尝试解析JSON
                    try:
                        result = json.loads(json_text)
                        logger.info("成功解析结构化JSON响应")
                        
                        # 确保结果包含必要的字段
                        if not isinstance(result, dict):
                            logger.warning(f"解析结果不是字典，而是{type(result)}，将转换为字典")
                            result = {"description": str(result)}
                            
                        # 添加默认字段
                        if "detections" not in result:
                            result["detections"] = objects
                        if "description" not in result:
                            result["description"] = "场景分析完成"
                            
                        # 处理风险事件，确保它们包含所有必要字段
                        if "high_risk_events" not in result:
                            # 创建高风险事件列表
                            result["high_risk_events"] = []
                            for obj in objects:
                                if obj.get("risk_level") == "high":
                                    result["high_risk_events"].append({
                                        "category": obj.get("category", "未知"),
                                        "event": obj.get("label", "未知事件"),
                                        "risk_level": "high",
                                        "confidence": obj.get("confidence", 0.8)
                                    })
                        
                        # 同样处理低风险事件
                        if "low_risk_events" not in result:
                            # 创建低/中风险事件列表
                            result["low_risk_events"] = []
                            for obj in objects:
                                if obj.get("risk_level") in ["low", "medium"]:
                                    result["low_risk_events"].append({
                                        "category": obj.get("category", "未知"),
                                        "event": obj.get("label", "未知事件"),
                                        "risk_level": obj.get("risk_level", "low"),
                                        "confidence": obj.get("confidence", 0.8)
                                    })
                            
                        # 将解析后的JSON转回字符串，确保是有效的JSON格式
                        result_json = json.dumps(result, ensure_ascii=False)
                        # 增加一个额外的日志来记录解析成功的结构化结果
                        logger.info(f"Qwen VL Max模型分析结果: {result_json[:300]}...")
                        return result_json
                        
                    except json.JSONDecodeError as json_err:
                        logger.warning(f"无法解析Qwen VL Max模型响应为有效JSON: {json_err}，位置: {json_err.pos}，将返回原始响应")
                        # 创建一个默认结构
                        default_result = {
                            "detections": objects,
                            "description": response[:1000] if len(response) > 1000 else response,  # 限制描述长度
                            "high_risk_events": [],
                            "low_risk_events": []
                        }
                        
                        # 添加检测到的高风险和低风险事件
                        for obj in objects:
                            risk_level = obj.get("risk_level", "low")
                            event = {
                                "category": obj.get("category", "未知"),
                                "event": obj.get("label", "未知事件"),
                                "risk_level": risk_level,
                                "confidence": obj.get("confidence", 0.8)
                            }
                            
                            if risk_level == "high":
                                default_result["high_risk_events"].append(event)
                            else:
                                default_result["low_risk_events"].append(event)
                                
                        # 将默认结果转换为JSON字符串
                        result_json = json.dumps(default_result, ensure_ascii=False)
                        logger.info(f"使用默认结构的分析结果: {result_json[:300]}...")
                        return result_json
                else:
                    logger.warning(f"Qwen VL Max模型返回非字符串结果: {type(response)}")
                    return json.dumps({"description": str(response)}, ensure_ascii=False)
            
            except Exception as e:
                logger.error(f"处理Qwen VL Max模型响应时出错: {str(e)}")
                # 返回原始响应
                return response
                
        except Exception as e:
            logger.error(f"调用Qwen VL Max模型时发生错误: {str(e)}")
            return None
    
    return None

def wsTask(frame_data:str,timestamp,frame_id, device=None):
    result = process_frame_task(frame_data,timestamp,frame_id, device)
    
    # 确保返回结果可以JSON序列化
    if result:
        # 移除NumPy数组
        for key in list(result.keys()):
            if isinstance(result[key], np.ndarray):
                del result[key]
    
    return result

def process_frame_task(frame_data,timestamp,frame_id, device=None):
    result = processor.process_image_base64(image_base64=frame_data, timestamp=timestamp, frame_id=frame_id, device=device)
    return result

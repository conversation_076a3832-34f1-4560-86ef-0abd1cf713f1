<template>
  <div class="yolo-detector">
    <div class="detector-container">
      <!-- 视频元素 -->
      <video 
        ref="videoElement"
        class="video-element"
        autoplay
        muted
        playsinline
        v-show="showVideo"
      ></video>
      
      <!-- 检测结果Canvas -->
      <canvas 
        ref="detectionCanvas" 
        class="detection-canvas"
      ></canvas>
    </div>
    
    <div class="controls">
      <button 
        class="control-button"
        @click="toggleDetection"
        :disabled="!isModelLoaded"
      >
        {{ isDetecting ? '停止检测' : '开始检测' }}
      </button>
      
      <div class="detection-stats" v-if="isModelLoaded">
        <div>模型: YOLOv8</div>
        <div>状态: {{ isModelLoaded ? '已加载' : '加载中...' }}</div>
        <div v-if="isDetecting">FPS: {{ fps.toFixed(1) }}</div>
        <div v-if="detectionResults">
          检测到对象: {{ detectionResults.boxes ? detectionResults.boxes.length : 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import YoloTFJS from './YoloTFJS';
import { renderStats } from './utils/renderBox';

export default {
  name: 'YoloDetector',
  
  props: {
    // 视频流源
    videoSrc: {
      type: Object,
      default: null
    },
    
    // 是否显示视频
    showVideo: {
      type: Boolean,
      default: true
    },
    
    // 检测阈值
    threshold: {
      type: Number,
      default: 0.25
    },
    
    // 模型路径
    modelPath: {
      type: String,
      default: '/models/yolov8n/model.json'
    }
  },
  
  setup(props, { emit }) {
    // 引用
    const videoElement = ref(null);
    const detectionCanvas = ref(null);
    
    // 状态
    const isModelLoaded = ref(false);
    const isDetecting = ref(false);
    const detectionResults = ref(null);
    const fps = ref(0);
    
    // YOLOv8 模型实例
    const yolo = new YoloTFJS();
    
    // FPS计算相关
    let frameCount = 0;
    let lastTime = 0;
    let animationFrameId = null;
    
    // 初始化
    onMounted(async () => {
      // 加载模型
      await loadModel();
      
      // 设置视频源
      if (props.videoSrc) {
        setupVideoSource(props.videoSrc);
      }
    });
    
    // 清理资源
    onBeforeUnmount(() => {
      stopDetection();
      if (yolo) {
        yolo.dispose();
      }
    });
    
    // 加载YOLO模型
    const loadModel = async () => {
      try {
        const loaded = await yolo.loadModel(props.modelPath);
        isModelLoaded.value = loaded;
        emit('model-loaded', loaded);
      } catch (error) {
        console.error('加载YOLO模型失败:', error);
        emit('model-loaded', false);
      }
    };
    
    // 设置视频源
    const setupVideoSource = async (stream) => {
      if (!videoElement.value) return;
      
      try {
        videoElement.value.srcObject = stream;
        await videoElement.value.play();
        
        // 调整canvas大小匹配视频
        if (detectionCanvas.value) {
          detectionCanvas.value.width = videoElement.value.videoWidth;
          detectionCanvas.value.height = videoElement.value.videoHeight;
        }
        
        emit('video-ready');
      } catch (error) {
        console.error('设置视频源失败:', error);
        emit('video-error', error);
      }
    };
    
    // 切换检测状态
    const toggleDetection = () => {
      if (isDetecting.value) {
        stopDetection();
      } else {
        startDetection();
      }
    };
    
    // 开始检测
    const startDetection = () => {
      if (!isModelLoaded.value || !videoElement.value || isDetecting.value) return;
      
      isDetecting.value = true;
      lastTime = performance.now();
      frameCount = 0;
      
      // 开始检测循环
      detectFrame();
      emit('detection-started');
    };
    
    // 停止检测
    const stopDetection = () => {
      if (!isDetecting.value) return;
      
      isDetecting.value = false;
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }
      
      emit('detection-stopped');
    };
    
    // 检测单帧
    const detectFrame = async () => {
      if (!isDetecting.value || !videoElement.value || !detectionCanvas.value) return;
      
      try {
        // 执行检测
        detectionResults.value = await yolo.detectFrame(
          videoElement.value,
          detectionCanvas.value,
          props.threshold
        );
        
        // 渲染统计信息
        if (detectionResults.value) {
          renderStats(
            detectionCanvas.value,
            detectionResults.value,
            yolo.classes
          );
          
          // 发送检测结果事件
          emit('detection-results', detectionResults.value);
        }
        
        // 计算FPS
        frameCount++;
        const now = performance.now();
        const elapsed = now - lastTime;
        
        if (elapsed >= 1000) {
          fps.value = frameCount / (elapsed / 1000);
          frameCount = 0;
          lastTime = now;
        }
        
        // 继续下一帧
        if (isDetecting.value) {
          animationFrameId = requestAnimationFrame(detectFrame);
        }
      } catch (error) {
        console.error('检测帧时出错:', error);
        stopDetection();
        emit('detection-error', error);
      }
    };
    
    return {
      videoElement,
      detectionCanvas,
      isModelLoaded,
      isDetecting,
      detectionResults,
      fps,
      toggleDetection
    };
  }
};
</script>

<style scoped>
.yolo-detector {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.detector-container {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  background-color: #000;
}

.video-element {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.controls {
  display: flex;
  padding: 10px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  align-items: center;
}

.control-button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 15px;
}

.control-button:hover {
  background-color: #45a049;
}

.control-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.detection-stats {
  font-size: 14px;
  color: #333;
}

.detection-stats div {
  margin-bottom: 4px;
}
</style> 
# Redis安装脚本 - 用于Traffic Eyes项目
Write-Host "开始安装Redis服务器..." -ForegroundColor Green

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "此脚本需要管理员权限来安装Redis服务。请右键点击并以管理员身份运行。" -ForegroundColor Red
    exit 1
}

# 检查Redis是否已安装
$redis_server = "redis-server.exe"
$redis_found = $false

try {
    $redis_check = Get-Command $redis_server -ErrorAction SilentlyContinue
    if ($redis_check) {
        $redis_found = $true
        Write-Host "Redis已经安装: $($redis_check.Source)" -ForegroundColor Green
        Write-Host "无需再次安装，退出脚本。" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "未检测到已安装的Redis，将继续安装..." -ForegroundColor Yellow
}

# 检查是否有Redis安装包
$redis_installer = "Redis-x64-3.0.504.msi"
$redis_installer_path = "./$redis_installer"

if (-not (Test-Path $redis_installer_path)) {
    # 尝试在上一级目录查找
    $redis_installer_path = "../$redis_installer"
    if (-not (Test-Path $redis_installer_path)) {
        # 下载Redis安装文件
        Write-Host "未找到Redis安装包，正在下载..." -ForegroundColor Yellow
        $download_url = "https://github.com/microsoftarchive/redis/releases/download/win-3.0.504/Redis-x64-3.0.504.msi"
        
        try {
            Invoke-WebRequest -Uri $download_url -OutFile $redis_installer
            Write-Host "Redis安装包下载完成: $redis_installer" -ForegroundColor Green
            $redis_installer_path = "./$redis_installer"
        } catch {
            Write-Host "下载Redis安装包失败，请手动下载: $download_url" -ForegroundColor Red
            Write-Host "将安装包保存到当前目录，然后重新运行此脚本。" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "在上级目录找到Redis安装包: $redis_installer_path" -ForegroundColor Green
    }
} else {
    Write-Host "找到Redis安装包: $redis_installer_path" -ForegroundColor Green
}

# 安装Redis
Write-Host "开始安装Redis..." -ForegroundColor Cyan
try {
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$redis_installer_path`" /qn" -Wait -NoNewWindow
    
    # 检查安装结果
    $redis_service = Get-Service -Name "Redis" -ErrorAction SilentlyContinue
    if ($redis_service) {
        Write-Host "Redis服务安装成功!" -ForegroundColor Green
        
        # 启动Redis服务
        Start-Service -Name "Redis"
        Write-Host "Redis服务已启动" -ForegroundColor Green
        
        # 配置Redis开机自启
        Set-Service -Name "Redis" -StartupType Automatic
        Write-Host "Redis服务已设置为开机自启" -ForegroundColor Green
    } else {
        Write-Host "Redis服务安装可能失败，请检查安装日志" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "安装Redis时出错: $_" -ForegroundColor Red
    exit 1
}

# 安装Python Redis包
Write-Host "安装Python Redis支持..." -ForegroundColor Cyan
try {
    pip install redis
    Write-Host "Python Redis包安装成功" -ForegroundColor Green
} catch {
    Write-Host "安装Python Redis包失败: $_" -ForegroundColor Yellow
    Write-Host "请手动运行: pip install redis" -ForegroundColor Yellow
}

Write-Host "Redis安装和配置完成" -ForegroundColor Green
Write-Host "现在可以运行 qwen-vl-backend/start_advanced.ps1 脚本启动带Redis支持的服务" -ForegroundColor Cyan 
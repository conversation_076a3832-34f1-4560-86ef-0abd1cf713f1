#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ezviz Cloud Camera YOLO Processor
Connects to Ezviz cloud cameras and processes streams with YOLOv8x-seg model
"""

import os
import sys
import time
import json
import base64
import logging
import redis
import threading
import queue
import traceback
from io import BytesIO
import numpy as np
import cv2
from PIL import Image
import datetime
import uuid
import websocket
import argparse
import socketserver
from http.server import HTTPServer, SimpleHTTPRequestHandler
import webbrowser
from pathlib import Path
from ultralytics import YOLO

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ezviz_processor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ezviz-yolo-processor")

# Redis configuration
REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_DB = int(os.environ.get('REDIS_DB', 0))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', None)

# YOLO configuration
YOLO_MODEL_PATH = os.environ.get('YOLO_MODEL_PATH', 'yolov8x-seg.pt')
DEFAULT_CONFIDENCE = float(os.environ.get('DEFAULT_CONFIDENCE', 0.5))

# Global variables
redis_client = None
yolo_model = None
processing_queue = queue.Queue(maxsize=100)
stop_event = threading.Event()
current_frame = None
frame_lock = threading.Lock()
processing_active = False
clients = set()
clients_lock = threading.Lock()
static_dir = Path("static_ezviz")

def initialize_yolo_model():
    """Initialize YOLO model"""
    global yolo_model
    
    try:
        # Ensure model file exists
        if not os.path.exists(YOLO_MODEL_PATH):
            base_dir = os.path.dirname(os.path.abspath(__file__))
            alternative_path = os.path.join(base_dir, "qwen-vl-backend/../src/models/yolo11n-seg.pt")
            if os.path.exists(alternative_path):
                logger.info(f"Using alternative model path: {alternative_path}")
                model_path = alternative_path
            else:
                # Try yolov8x-seg.pt as fallback
                logger.warning(f"Cannot find yolo11n-seg.pt, trying yolov8x-seg.pt")
                model_path = "yolov8x-seg.pt"
        else:
            model_path = YOLO_MODEL_PATH
        
        logger.info(f"Loading YOLO model: {model_path}")
        yolo_model = YOLO(model_path)
        logger.info("YOLO model loaded successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing YOLO model: {str(e)}")
        logger.exception("Detailed error information:")
        return False

def connect_redis():
    """Connect to Redis server"""
    global redis_client
    
    try:
        redis_pool = redis.ConnectionPool(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        redis_client = redis.Redis(connection_pool=redis_pool)
        redis_client.ping()  # Test connection
        logger.info(f"Successfully connected to Redis server: {REDIS_HOST}:{REDIS_PORT}")
        return True
    except Exception as e:
        logger.error(f"Failed to connect to Redis server: {str(e)}")
        return False

def process_frame(frame):
    """Process a single video frame with YOLO"""
    global yolo_model, current_frame
    
    if yolo_model is None:
        logger.error("YOLO model not initialized")
        return frame
    
    try:
        # Make a copy of the input frame to avoid modifying it directly
        processed_image = frame.copy()
        
        # Use YOLO for detection with segmentation
        results = yolo_model(processed_image, conf=DEFAULT_CONFIDENCE)
        
        # Get the annotated frame with detections and segmentation
        processed_image = results[0].plot()
        
        # Update current frame for streaming
        with frame_lock:
            current_frame = processed_image.copy()
        
        return processed_image
    
    except Exception as e:
        logger.error(f"Error processing frame: {str(e)}")
        logger.exception("Detailed error information:")
        return frame

def process_ezviz_frames():
    """Main loop for processing video frames from Redis queue"""
    global redis_client, stop_event
    
    logger.info("Starting video frame processing loop...")
    
    # Create Redis PubSub object to listen for new tasks
    pubsub = redis_client.pubsub()
    pubsub.subscribe("new_ezviz_frame")
    
    # Process tasks already in the queue
    initial_tasks = redis_client.lrange("ezviz_frames", 0, -1)
    if initial_tasks:
        logger.info(f"Found {len(initial_tasks)} pending tasks in queue")
        for task_json in initial_tasks:
            process_task_from_queue(task_json)
    
    # Listen for new tasks
    while not stop_event.is_set():
        try:
            # Check for new published messages
            message = pubsub.get_message(timeout=0.1)
            if message and message['type'] == 'message':
                # Process all tasks in the queue when notification received
                tasks = redis_client.lrange("ezviz_frames", 0, -1)
                if tasks:
                    logger.debug(f"Received new task notification, processing {len(tasks)} tasks")
                    for task_json in tasks:
                        process_task_from_queue(task_json)
                    # Clear the queue after processing
                    redis_client.delete("ezviz_frames")
                
            # Brief pause to reduce CPU usage
            time.sleep(0.01)
            
        except Exception as e:
            logger.error(f"Error processing tasks: {str(e)}")
            logger.exception("Detailed error information:")
            time.sleep(1)  # Longer pause on error
    
    # Clean up resources
    pubsub.unsubscribe()
    logger.info("Video frame processing loop stopped")

def process_task_from_queue(task_json):
    """Process a single task from the queue"""
    global current_frame
    
    try:
        # Parse task data
        task = json.loads(task_json)
        client_id = task.get("client_id")
        frame_data = task.get("frame")
        
        if not client_id or not frame_data:
            logger.warning("Invalid task data, missing client_id or frame field")
            return
        
        # Convert base64 image to numpy array
        if isinstance(frame_data, str):
            # If it's a base64 URL format, remove the prefix
            if frame_data.startswith('data:image'):
                try:
                    frame_parts = frame_data.split(',', 1)
                    if len(frame_parts) != 2:
                        logger.error("Invalid base64 URL format")
                        return
                    frame_data = frame_parts[1]
                except Exception as e:
                    logger.error(f"Failed to parse base64 URL format: {str(e)}")
                    return
            
            # Ensure base64 string length is a multiple of 4, add padding if needed
            padding_needed = len(frame_data) % 4
            if padding_needed:
                frame_data += '=' * (4 - padding_needed)
            
            try:
                # Decode base64
                image_bytes = base64.b64decode(frame_data)
                nparr = np.frombuffer(image_bytes, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            except Exception as e:
                logger.error(f"base64 decoding failed: {str(e)}")
                return
            
            if image is None:
                logger.error("Failed to decode image data")
                return
            
            # Process the frame with YOLO
            processed_image = process_frame(image)
            
            # Update the current frame for streaming
            with frame_lock:
                current_frame = processed_image.copy()
            
            # Store result in Redis for the client
            store_result_for_client(client_id, processed_image)
            
    except Exception as e:
        logger.error(f"Error processing task: {str(e)}")
        logger.exception("Detailed error information:")

def store_result_for_client(client_id, processed_image):
    """Store processed frame result in Redis for the client"""
    try:
        # Convert processed image to base64
        _, buffer = cv2.imencode('.jpg', processed_image)
        processed_frame_base64 = base64.b64encode(buffer).decode('utf-8')
        
        # Create result object
        result = {
            "processed_frame": processed_frame_base64,
            "timestamp": int(time.time() * 1000)
        }
        
        # Store in Redis with client-specific key
        result_key = f"client:{client_id}:results"
        redis_client.lpush(result_key, json.dumps(result))
        redis_client.ltrim(result_key, 0, 9)  # Keep only the 10 most recent results
        
    except Exception as e:
        logger.error(f"Error storing result: {str(e)}")

def ensure_dirs():
    """Ensure output directories exist"""
    static_dir.mkdir(exist_ok=True)
    
    # Create HTML file for the stream viewer
    html_file = static_dir / "index.html"
    with open(html_file, "w", encoding="utf-8") as f:
        f.write("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Ezviz Cloud Camera - YOLO Processing</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                h1 { color: #333; }
                .container { max-width: 1200px; margin: 0 auto; }
                .video-container { margin-bottom: 30px; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .video-title { font-size: 18px; margin-bottom: 10px; }
                #mjpeg-stream { width: 100%; max-width: 800px; height: auto; }
                .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
                .status.connected { background-color: #d4edda; color: #155724; }
                .status.disconnected { background-color: #f8d7da; color: #721c24; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Ezviz Cloud Camera - YOLO Processing</h1>
                <div class="video-container">
                    <div class="video-title">YOLO Detection & Segmentation</div>
                    <img id="mjpeg-stream" src="/stream" alt="Video Stream">
                    <div id="status" class="status">Connecting...</div>
                </div>
            </div>
            
            <script>
                // Monitor image loading status
                const streamImg = document.getElementById('mjpeg-stream');
                const statusDiv = document.getElementById('status');
                
                // Image loaded successfully
                streamImg.onload = function() {
                    statusDiv.textContent = 'Connected';
                    statusDiv.className = 'status connected';
                };
                
                // Image failed to load
                streamImg.onerror = function() {
                    statusDiv.textContent = 'Connection lost, trying to reconnect...';
                    statusDiv.className = 'status disconnected';
                    
                    // Retry after 5 seconds
                    setTimeout(() => {
                        streamImg.src = '/stream?' + new Date().getTime();
                    }, 5000);
                };
            </script>
        </body>
        </html>
        """)

class MJPEGStreamHandler(SimpleHTTPRequestHandler):
    """HTTP handler for MJPEG streaming"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(static_dir), **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        # Handle MJPEG stream requests
        if self.path.startswith('/stream'):
            self.send_response(200)
            self.send_header('Content-type', 'multipart/x-mixed-replace; boundary=--jpgboundary')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            
            with clients_lock:
                clients.add(self)
            
            try:
                while processing_active:
                    # Get current frame
                    with frame_lock:
                        if current_frame is not None:
                            img = current_frame.copy()
                        else:
                            # Create a black image if no frame is available
                            img = np.zeros((480, 640, 3), dtype=np.uint8)
                            cv2.putText(img, "Waiting for video...", (50, 240),
                                        cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    
                    # Convert frame to JPEG
                    ret, jpeg = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, 70])
                    
                    # Send frame
                    self.wfile.write(b"--jpgboundary\r\n")
                    self.send_header('Content-type', 'image/jpeg')
                    self.send_header('Content-length', str(len(jpeg)))
                    self.end_headers()
                    self.wfile.write(jpeg.tobytes())
                    self.wfile.write(b"\r\n")
                    
                    # Reduce frame rate to lower network load
                    time.sleep(0.066)  # Approximately 15 FPS
            
            except (BrokenPipeError, ConnectionResetError):
                # Client disconnected
                logger.info("Client disconnected")
            except Exception as e:
                logger.error(f"Error handling stream: {str(e)}")
            finally:
                with clients_lock:
                    if self in clients:
                        clients.remove(self)
            
            return
        
        # Handle root path or other requests
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            with open(static_dir / 'index.html', 'rb') as f:
                self.wfile.write(f.read())
            return
        
        # Let parent class handle other requests
        try:
            super().do_GET()
        except:
            self.send_error(404, "File not found")

def start_http_server(port=8082):
    """Start HTTP server for MJPEG streaming"""
    server = socketserver.ThreadingTCPServer(("", port), MJPEGStreamHandler)
    logger.info(f"Starting HTTP server on port {port}")
    
    # Open browser automatically
    webbrowser.open(f"http://localhost:{port}")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down server")
    finally:
        server.server_close()

def signal_handler(sig, frame):
    """Handle interrupt signals"""
    global stop_event, processing_active
    logger.info("Received signal to terminate")
    stop_event.set()
    processing_active = False
    sys.exit(0)

def main():
    """Main function"""
    global processing_active
    
    parser = argparse.ArgumentParser(description='Ezviz Cloud Camera YOLO Processor')
    parser.add_argument('--port', type=int, default=8082,
                      help='HTTP server port')
    parser.add_argument('--model', type=str, default='yolov8x-seg.pt',
                      help='YOLO model path')
    
    args = parser.parse_args()
    
    # Set model path from arguments
    global YOLO_MODEL_PATH
    YOLO_MODEL_PATH = args.model
    
    # Initialize components
    if not initialize_yolo_model():
        logger.error("Failed to initialize YOLO model, exiting")
        return 1
    
    if not connect_redis():
        logger.error("Failed to connect to Redis, exiting")
        return 1
    
    # Ensure directories exist
    ensure_dirs()
    
    # Set up signal handlers
    import signal
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Set processing active flag
    processing_active = True
    
    # Start Redis processing thread
    redis_thread = threading.Thread(
        target=process_ezviz_frames,
        daemon=True
    )
    redis_thread.start()
    
    # Start HTTP server (this will block until interrupted)
    start_http_server(args.port)
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 
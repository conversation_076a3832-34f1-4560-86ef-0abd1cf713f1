import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the file with the issue
const filePath = path.join(__dirname, 'src', 'components', 'OnlineMonitor.vue');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Create a backup
fs.writeFileSync(`${filePath}.backup-${Date.now()}`, content, 'utf8');

// Fix the issue - replace the template section issue
// Look for formData.append followed by a <template> tag
const fixedContent = content.replace(/formData\.append\('image', frameDataUrl\);\s*\n\s*<template>[\s\S]*?<\/template>/g, 
`formData.append('image', frameDataUrl);

    // Send the request to the backend
    try {
      const response = await axios.post('http://localhost:8000/detect_yolo', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('YOLO API error:', error);
      throw new Error('无法连接到YOLO检测服务，请检查后端服务是否启动');
    }
  }`);

// Write the fixed content back
fs.writeFileSync(filePath, fixedContent, 'utf8');

console.log('Fixed duplicate <template> tags in OnlineMonitor.vue'); 
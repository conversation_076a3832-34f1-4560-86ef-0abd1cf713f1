#!/bin/bash

# Color codes for terminal output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=====================================${NC}"
echo -e "${GREEN}Starting Traffic-Eyes Services${NC}"
echo -e "${BLUE}=====================================${NC}"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}Creating virtual environment...${NC}"
    python -m venv venv
fi

# Activate virtual environment
echo -e "${YELLOW}Activating virtual environment...${NC}"
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    source venv/Scripts/activate
else
    # Linux/Mac
    source venv/bin/activate
fi

# Install required packages if needed
echo -e "${YELLOW}Installing required packages...${NC}"
pip install -r requirements.txt

# Check if requirements.txt exists, if not create it
if [ ! -f "requirements.txt" ]; then
    echo -e "${YELLOW}Creating requirements.txt...${NC}"
    cat > requirements.txt << EOF
flask==2.0.1
flask-cors==3.0.10
ultralytics==8.0.0
websockets==10.3
opencv-python==********
pillow==9.0.1
requests==2.27.1
numpy==1.22.3
EOF
fi

# Start services in separate terminals
echo -e "${GREEN}Starting YOLO WebSocket Server...${NC}"
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows - start in a new CMD window
    start cmd /k "call venv\Scripts\activate && python backend/yolo_server.py"
else
    # Linux/Mac - start in background
    python backend/yolo_server.py > logs/yolo_server.log 2>&1 &
    YOLO_PID=$!
    echo -e "${GREEN}YOLO server started with PID: $YOLO_PID${NC}"
fi

echo -e "${GREEN}Starting Qwen API Server...${NC}"
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows - start in a new CMD window
    start cmd /k "call venv\Scripts\activate && python backend/qwen_api.py"
else
    # Linux/Mac - start in background
    python backend/qwen_api.py > logs/qwen_api.log 2>&1 &
    QWEN_PID=$!
    echo -e "${GREEN}Qwen API server started with PID: $QWEN_PID${NC}"
fi

if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "win32" ]]; then
    # Create a file to store PIDs for stopping later
    echo $YOLO_PID > .yolo_pid
    echo $QWEN_PID > .qwen_pid
fi

echo -e "${BLUE}=====================================${NC}"
echo -e "${GREEN}All services started!${NC}"
echo -e "${BLUE}=====================================${NC}"
echo -e "${YELLOW}YOLO server running on: ws://localhost:8765${NC}"
echo -e "${YELLOW}Qwen API server running on: http://localhost:5000${NC}"
echo -e "${BLUE}=====================================${NC}"

# If running in an interactive terminal, provide instructions for stopping
if [ -t 0 ]; then
    echo -e "${YELLOW}To stop services, run: backend/stop_services.sh${NC}"
fi 
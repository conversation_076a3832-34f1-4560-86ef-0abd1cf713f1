#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YOLO Redis处理服务 - 负责从Redis队列接收视频帧，处理后将结果发送回对应客户端

此脚本作为独立服务运行，与主Fast API服务器分离，以实现更好的性能和可扩展性
"""

import os
import sys
import time
import json
import base64
import logging
import redis
import threading
import queue
import traceback
from io import BytesIO
import numpy as np
import cv2
from PIL import Image
import datetime
import uuid
import websocket
import threading
from ultralytics import YOLO
import signal

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("yolo_processor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("yolo-redis-processor")

# Redis配置
REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_DB = int(os.environ.get('REDIS_DB', 0))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', None)

# WebSocket配置
API_WS_ENDPOINT = os.environ.get('API_WS_ENDPOINT', 'ws://localhost:8001/ws/yolo-results')

# YOLO配置
YOLO_MODEL_PATH = os.environ.get('YOLO_MODEL_PATH', '../src/models/yolo11n-seg.pt')
DEFAULT_CONFIDENCE = float(os.environ.get('DEFAULT_CONFIDENCE', 0.5))

# 全局变量
ws_client = None
ws_lock = threading.Lock()
redis_client = None
yolo_model = None
processing_queue = queue.Queue(maxsize=100)  # 限制队列大小，避免内存溢出
stop_event = threading.Event()

# 连接管理标志
ws_connected = False
ws_reconnect_attempts = 0

# 配置参数
MAX_RECONNECT_ATTEMPTS = 5
RECONNECT_DELAY = 2  # 重连延迟，单位秒

def initialize_yolo_model():
    """初始化YOLO模型"""
    global yolo_model
    
    try:
        # 确保模型文件存在
        if not os.path.exists(YOLO_MODEL_PATH):
            base_dir = os.path.dirname(os.path.abspath(__file__))
            alternative_path = os.path.join(base_dir, YOLO_MODEL_PATH)
            if os.path.exists(alternative_path):
                logger.info(f"使用替代模型路径: {alternative_path}")
                model_path = alternative_path
            else:
                logger.error(f"找不到YOLO模型文件: {YOLO_MODEL_PATH}")
                logger.error("请确保模型文件存在或设置正确的YOLO_MODEL_PATH环境变量")
                return False
        else:
            model_path = YOLO_MODEL_PATH
        
        logger.info(f"加载YOLO模型: {model_path}")
        yolo_model = YOLO(model_path)
        logger.info("YOLO模型加载成功")
        return True
    except Exception as e:
        logger.error(f"初始化YOLO模型时出错: {str(e)}")
        logger.exception("详细错误信息:")
        return False

def connect_redis():
    """连接到Redis服务器"""
    global redis_client
    
    try:
        redis_pool = redis.ConnectionPool(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        redis_client = redis.Redis(connection_pool=redis_pool)
        redis_client.ping()  # 测试连接
        logger.info(f"已成功连接到Redis服务器: {REDIS_HOST}:{REDIS_PORT}")
        return True
    except Exception as e:
        logger.error(f"连接Redis服务器失败: {str(e)}")
        return False

def connect_websocket():
    """连接到API WebSocket服务器，用于发送处理结果"""
    global ws_client, ws_connected, ws_reconnect_attempts
    
    try:
        # 如果已经有连接且连接有效，则返回
        if ws_client and ws_client.sock and ws_client.sock.connected:
            return True
        
        with ws_lock:
            # 再次检查，防止多线程问题
            if ws_client and ws_client.sock and ws_client.sock.connected:
                return True
            
            # 关闭旧连接（如果存在）
            if ws_client:
                try:
                    ws_client.close()
                except:
                    pass
            
            logger.info(f"尝试连接到API WebSocket服务器 (尝试 {ws_reconnect_attempts+1}/{MAX_RECONNECT_ATTEMPTS})...")
            
            # 创建新连接
            ws_client = websocket.WebSocketApp(
                API_WS_ENDPOINT,
                on_open=lambda ws: on_websocket_open(ws),
                on_message=lambda ws, msg: logger.debug(f"收到WebSocket消息: {msg[:100]}..."),
                on_error=lambda ws, err: logger.error(f"WebSocket错误: {err}"),
                on_close=lambda ws, close_status_code, close_msg: on_websocket_close(ws, close_status_code, close_msg)
            )
            
            # 在后台线程中启动WebSocket连接
            wst = threading.Thread(target=ws_client.run_forever)
            wst.daemon = True
            wst.start()
            
            # 等待连接建立
            start_time = time.time()
            timeout = 5  # 5秒超时
            while time.time() - start_time < timeout:
                if ws_connected:
                    ws_reconnect_attempts = 0  # 连接成功，重置尝试计数
                    return True
                time.sleep(0.1)
            
            # 如果超时未连接成功
            if ws_reconnect_attempts < MAX_RECONNECT_ATTEMPTS:
                ws_reconnect_attempts += 1
                logger.warning(f"WebSocket连接超时，{RECONNECT_DELAY}秒后重试...")
                time.sleep(RECONNECT_DELAY)
                return connect_websocket()  # 递归重试
            else:
                logger.error(f"WebSocket连接失败，已达到最大重试次数 ({MAX_RECONNECT_ATTEMPTS})，放弃重连")
                ws_reconnect_attempts = 0  # 重置尝试计数，下次可以重新尝试
                return False
    except Exception as e:
        logger.error(f"连接到WebSocket服务器失败: {str(e)}")
        if ws_reconnect_attempts < MAX_RECONNECT_ATTEMPTS:
            ws_reconnect_attempts += 1
            logger.warning(f"发生异常，{RECONNECT_DELAY}秒后重试WebSocket连接...")
            time.sleep(RECONNECT_DELAY)
            return connect_websocket()  # 递归重试
        else:
            logger.error(f"WebSocket连接异常，已达到最大重试次数，放弃重连")
            ws_reconnect_attempts = 0
            return False

def on_websocket_open(ws):
    """WebSocket连接打开时的回调"""
    global ws_connected
    logger.info("WebSocket连接已建立")
    ws_connected = True

def on_websocket_close(ws, close_status_code, close_msg):
    """WebSocket连接关闭时的回调"""
    global ws_connected
    logger.info("WebSocket连接已关闭")
    ws_connected = False
    
    # 如果不是主动关闭，可以在这里添加自动重连逻辑
    if not stop_event.is_set():
        threading.Thread(target=delayed_reconnect).start()
        
def delayed_reconnect():
    """延迟尝试重新连接WebSocket"""
    if stop_event.is_set():
        return
    logger.info(f"{RECONNECT_DELAY}秒后尝试重新连接WebSocket...")
    time.sleep(RECONNECT_DELAY)
    connect_websocket()

def send_result_to_api(client_id, results):
    """通过WebSocket将处理结果发送到API服务器"""
    global ws_client
    
    try:
        # 确保WebSocket连接已建立
        if not connect_websocket():
            logger.error(f"无法发送结果，WebSocket连接未建立")
            return False
        
        # 准备发送的数据
        message = {
            "client_id": client_id,
            "results": results,
            "timestamp": int(time.time() * 1000)
        }
        
        # 发送到API服务器
        with ws_lock:
            if ws_client and ws_client.sock and ws_client.sock.connected:
                ws_client.send(json.dumps(message))
                return True
            else:
                logger.error("WebSocket连接不可用，无法发送结果")
                return False
    except Exception as e:
        logger.error(f"发送结果到API服务器时出错: {str(e)}")
        return False

def process_frame(frame_data, client_id, frame_count=0, timestamp=None):
    """处理单个视频帧"""
    global yolo_model
    
    if not yolo_model:
        logger.error("YOLO模型未初始化")
        return None
    
    try:
        # 将base64字符串转换为图像
        if isinstance(frame_data, str):
            # 如果是base64 URL格式，去除前缀
            if frame_data.startswith('data:image'):
                try:
                    # 安全解析base64 URL格式
                    frame_parts = frame_data.split(',', 1)
                    if len(frame_parts) != 2:
                        logger.error("无效的base64 URL格式")
                        return None
                    frame_data = frame_parts[1]
                except Exception as e:
                    logger.error(f"解析base64 URL格式失败: {str(e)}")
                    return None
            
            # 确保base64字符串长度是4的倍数，必要时添加填充
            padding_needed = len(frame_data) % 4
            if padding_needed:
                frame_data += '=' * (4 - padding_needed)
            
            try:
                # 解码base64
                image_bytes = base64.b64decode(frame_data)
                nparr = np.frombuffer(image_bytes, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            except Exception as e:
                logger.error(f"base64解码失败: {str(e)}")
                return None
        else:
            # 如果已经是numpy数组
            image = frame_data
        
        if image is None:
            logger.error("无法解码图像数据")
            return None
        
        # 复制图像，以免修改原图
        processed_image = image.copy()
        
        # 使用YOLO进行检测
        results = yolo_model(image, conf=DEFAULT_CONFIDENCE)
        
        # 初始化结果字典
        detection_results = {
            "objects": [],
            "high_risk_events": [],
            "low_risk_events": [],
            "summary": {
                "total_objects": 0,
                "high_risk_count": 0,
                "low_risk_count": 0
            },
            "frame_count": frame_count,
            "timestamp": timestamp or int(time.time() * 1000)
        }
        
        # 处理检测结果
        for result in results:
            # 获取边界框和类别信息
            boxes = result.boxes
            if len(boxes) == 0:
                continue
            
            # 遍历所有检测到的对象
            for box in boxes:
                # 获取边框坐标
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                
                # 获取类别和置信度
                cls_id = int(box.cls[0].item())
                conf = float(box.conf[0].item())
                cls_name = result.names[cls_id]
                
                # 确定颜色（根据风险等级）
                risk_level = "low"
                if "person" in cls_name.lower() or conf > 0.7:
                    color = (0, 0, 255)  # 红色
                    if conf > 0.8:
                        risk_level = "high"
                else:
                    color = (0, 255, 0)  # 绿色
                
                # 在图像上绘制边界框
                cv2.rectangle(processed_image, (x1, y1), (x2, y2), color, 2)
                
                # 添加标签文本
                label = f"{cls_name} {conf:.2f}"
                cv2.putText(
                    processed_image, label, (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2
                )
                
                # 将对象添加到结果列表
                obj_info = {
                    "class": cls_name,
                    "confidence": conf,
                    "bbox": [int(x1), int(y1), int(x2), int(y2)],
                    "risk_level": risk_level
                }
                detection_results["objects"].append(obj_info)
                
                # 根据风险等级分类事件
                event = {
                    "label": cls_name,
                    "confidence": conf,
                    "bbox_2d": [int(x1), int(y1), int(x2), int(y2)],
                    "risk": risk_level
                }
                
                if risk_level == "high":
                    detection_results["high_risk_events"].append(event)
                    detection_results["summary"]["high_risk_count"] += 1
                else:
                    detection_results["low_risk_events"].append(event)
                    detection_results["summary"]["low_risk_count"] += 1
                
                detection_results["summary"]["total_objects"] += 1
        
        # 将处理后的图像编码为base64
        _, buffer = cv2.imencode('.jpg', processed_image)
        processed_frame_base64 = base64.b64encode(buffer).decode('utf-8')
        detection_results["processed_frame"] = processed_frame_base64
        
        return detection_results
    
    except Exception as e:
        logger.error(f"处理帧时出错: {str(e)}")
        logger.exception("详细错误信息:")
        return None

def process_ezviz_frames():
    """从Redis队列处理视频帧的主循环"""
    global redis_client, stop_event
    
    logger.info("启动视频帧处理循环...")
    
    # 创建Redis PubSub对象用于监听新任务
    pubsub = redis_client.pubsub()
    pubsub.subscribe("new_ezviz_frame")
    
    # 处理队列中已有的任务
    initial_tasks = redis_client.lrange("ezviz_frames", 0, -1)
    if initial_tasks:
        logger.info(f"队列中有 {len(initial_tasks)} 个待处理任务")
        for task_json in initial_tasks:
            process_task_from_queue(task_json)
    
    # 监听新任务
    while not stop_event.is_set():
        try:
            # 检查是否有新的发布消息
            message = pubsub.get_message(timeout=0.1)
            if message and message['type'] == 'message':
                # 收到新任务通知，处理队列中所有任务
                tasks = redis_client.lrange("ezviz_frames", 0, -1)
                if tasks:
                    logger.debug(f"收到新任务通知，处理 {len(tasks)} 个任务")
                    for task_json in tasks:
                        process_task_from_queue(task_json)
                    # 处理完成后清空队列
                    redis_client.delete("ezviz_frames")
                
            # 短暂暂停，减少CPU使用
            time.sleep(0.01)
            
        except Exception as e:
            logger.error(f"处理任务时出错: {str(e)}")
            logger.exception("详细错误信息:")
            time.sleep(1)  # 出错时暂停稍长时间
    
    # 清理资源
    pubsub.unsubscribe()
    logger.info("视频帧处理循环已停止")

def process_task_from_queue(task_json):
    """从队列中处理单个任务"""
    try:
        # 解析任务数据
        task = json.loads(task_json)
        client_id = task.get("client_id")
        timestamp = task.get("timestamp")
        frame_count = task.get("frame_count", 0)
        frame_data = task.get("frame")
        
        if not client_id or not frame_data:
            logger.warning("无效的任务数据，缺少client_id或frame字段")
            return
        
        # 处理帧
        result = process_frame(frame_data, client_id, frame_count, timestamp)
        if not result:
            logger.warning(f"处理帧失败，客户端ID: {client_id}")
            return
        
        # 发送结果到API服务器
        send_result_to_api(client_id, result)
        
        # 每100帧记录一次处理状态
        if frame_count % 100 == 0:
            logger.info(f"已处理第 {frame_count} 帧，客户端ID: {client_id}")
        
    except Exception as e:
        logger.error(f"处理队列任务时出错: {str(e)}")

def main():
    """主函数"""
    logger.info("启动YOLO Redis处理服务...")
    
    # 初始化组件
    if not connect_redis():
        logger.error("无法连接到Redis服务器，退出程序")
        return 1
    
    if not initialize_yolo_model():
        logger.error("无法初始化YOLO模型，退出程序")
        return 1
    
    if not connect_websocket():
        logger.warning("无法连接到WebSocket服务器，将在处理帧时重试连接")
    
    try:
        # 启动处理循环
        process_ezviz_frames()
    except KeyboardInterrupt:
        logger.info("收到中断信号，停止服务")
    except Exception as e:
        logger.error(f"服务运行时出错: {str(e)}")
        logger.exception("详细错误信息:")
        return 1
    finally:
        # 清理资源
        stop_event.set()
        if ws_client:
            ws_client.close()
        logger.info("YOLO Redis处理服务已停止")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 
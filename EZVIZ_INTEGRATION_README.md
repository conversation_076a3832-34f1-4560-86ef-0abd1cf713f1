# Ezviz Cloud Camera Integration for Traffic Eyes

This module integrates Ezviz cloud cameras with the Traffic Eyes system, providing real-time object detection and segmentation using YOLOv8x-seg model.

## Features

- Real-time processing of Ezviz cloud camera streams
- High-quality object detection and segmentation using YOLOv8x-seg model
- MJPEG streaming for browser-based viewing
- Redis-based message passing for integration with the main Traffic Eyes system
- Support for multiple camera streams

## Requirements

- Python 3.8 or higher
- Redis server
- Required Python packages (installed automatically by the startup script):
  - fastapi
  - uvicorn
  - redis
  - websocket-client
  - numpy
  - opencv-python
  - ultralytics
  - pillow

## Quick Start

1. Run the standalone Ezviz cloud camera processor:

```powershell
.\start_ezviz_cloud.ps1
```

This will:
- Configure Redis
- Download the YOLOv8x-seg model if needed
- Start the Ezviz cloud camera processor on port 8082
- Open a browser window to view the processed stream

2. Alternatively, use the integrated startup script to start all components:

```powershell
.\integrated_start_fixed.ps1
```

This will start:
- The original Traffic Eyes system (backend, API, frontend)
- The new video processing system (offline and real-time)
- The Ezviz cloud camera integration
- A navigation page to access all components

## How It Works

1. The Ezviz cloud camera integration connects to Redis to receive video frames from Ezviz cameras.
2. When a new frame is received (via the `ezviz_frames` Redis list), it is processed using the YOLOv8x-seg model.
3. The processed frame with detection and segmentation overlays is:
   - Stored back in Redis for the main system to use
   - Streamed via MJPEG to web browsers for direct viewing

## Configuration

The Ezviz cloud camera processor can be configured using command-line arguments:

```powershell
python ezviz_yolo_processor.py --port 8082 --model 'yolov8x-seg.pt'
```

Available options:
- `--port`: HTTP server port (default: 8082)
- `--model`: YOLO model path (default: 'yolov8x-seg.pt')

## Integration with Traffic Eyes

The Ezviz cloud camera integration works seamlessly with the main Traffic Eyes system:

1. The main system sends camera frames to Redis
2. The Ezviz processor picks up these frames and processes them
3. Results are stored back in Redis for the main system to use
4. The web interface provides direct access to the processed stream

## Troubleshooting

1. If the Ezviz cloud camera stream is not appearing:
   - Check that Redis is running (`redis-cli ping` should return PONG)
   - Verify that the camera is sending frames to the Redis queue
   - Check the logs in `ezviz_processor.log`

2. If object detection is not working:
   - Ensure the YOLOv8x-seg model is downloaded correctly
   - Check that the model path is correct
   - Try increasing the confidence threshold if needed

3. If the web interface is not accessible:
   - Verify that port 8082 is not in use by another application
   - Check that the HTTP server started successfully
   - Try accessing the stream directly at http://localhost:8082/stream

## License

This module is part of the Traffic Eyes system and is subject to the same license terms as the main project. 
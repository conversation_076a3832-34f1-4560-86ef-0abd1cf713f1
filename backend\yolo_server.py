import asyncio
import json
import os
import base64
import cv2
import numpy as np
import websockets
from ultralytics import YOLO
from PIL import Image
import io
import time
import threading
import queue
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("logs/yolo_server.log")
    ]
)
logger = logging.getLogger("yolo-server")

# YOLO model and processing queue
model = None
frame_queue = queue.Queue(maxsize=100)  # Limit queue size to prevent memory issues
result_cache = {}
model_lock = threading.Lock()  # Lock for thread-safe model access

# Stats for monitoring performance
stats = {
    "frames_received": 0,
    "frames_processed": 0,
    "total_processing_time": 0,
    "last_fps_update": time.time(),
    "current_fps": 0
}

# Load YOLO model in a separate thread to avoid blocking
def load_model():
    global model
    try:
        logger.info("Loading YOLO model...")
        start_time = time.time()
        model = YOLO('yolov8x-seg.pt', task='detect')  # or your preferred model
        
        # Warmup the model with a dummy image
        dummy_img = np.zeros((640, 640, 3), dtype=np.uint8)
        model(dummy_img, verbose=False)
        
        logger.info(f"YOLO model loaded successfully in {time.time() - start_time:.2f} seconds")
    except Exception as e:
        logger.error(f"Error loading YOLO model: {e}")
        raise

# Process frames in a separate thread
def process_frames():
    global stats
    logger.info("YOLO processing thread started")
    
    while True:
        try:
            # Get a frame from the queue
            frame_data = frame_queue.get()
            if frame_data is None:  # None is the signal to stop
                logger.info("Received stop signal, stopping YOLO processing thread")
                break
                
            websocket, frame_id, img_bytes = frame_data
            
            # Check if we already have cached results for this frame
            if frame_id in result_cache:
                logger.debug(f"Using cached result for frame {frame_id}")
                asyncio.run_coroutine_threadsafe(send_result(websocket, result_cache[frame_id]), asyncio.get_event_loop())
                frame_queue.task_done()
                continue
            
            # Convert bytes to image
            start_time = time.time()
            try:
                nparr = np.frombuffer(img_bytes, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                if img is None:
                    logger.error("Failed to decode image")
                    frame_queue.task_done()
                    continue
                
                # Process with YOLO model
                with model_lock:
                    results = model(img, verbose=False)
                
                # Format results for client
                formatted_results = []
                
                for result in results:
                    boxes = result.boxes
                    for i, box in enumerate(boxes):
                        # Get box coordinates
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        
                        # Get class and confidence
                        cls = int(box.cls[0])
                        conf = float(box.conf[0])
                        
                        # Get class name
                        class_name = result.names[cls]
                        
                        # Add to formatted results
                        formatted_results.append({
                            'bbox': [x1, y1, x2-x1, y2-y1],  # [x, y, width, height]
                            'class': class_name,
                            'confidence': conf
                        })
                
                # Calculate processing time
                processing_time = time.time() - start_time
                
                # Update stats
                stats["frames_processed"] += 1
                stats["total_processing_time"] += processing_time
                
                # Update FPS every second
                now = time.time()
                if now - stats["last_fps_update"] >= 1.0:
                    elapsed = now - stats["last_fps_update"]
                    stats["current_fps"] = stats["frames_processed"] / elapsed if elapsed > 0 else 0
                    logger.info(f"Current processing rate: {stats['current_fps']:.1f} FPS")
                    stats["frames_processed"] = 0
                    stats["total_processing_time"] = 0
                    stats["last_fps_update"] = now
                
                # Prepare result with timing info
                result = {
                    'detections': formatted_results,
                    'processing_time_ms': round(processing_time * 1000, 2),
                    'timestamp': time.time()
                }
                
                # Cache result for future use (limited to 10 frames)
                if len(result_cache) >= 10:
                    # Remove the oldest entry
                    oldest_key = next(iter(result_cache))
                    result_cache.pop(oldest_key)
                result_cache[frame_id] = result
                
                # Send result back to client
                asyncio.run_coroutine_threadsafe(send_result(websocket, result), asyncio.get_event_loop())
                
            except Exception as e:
                logger.error(f"Error processing frame: {e}")
            
            # Mark task as done
            frame_queue.task_done()
            
        except Exception as e:
            logger.error(f"Error in YOLO processing thread: {e}")

# Coroutine to send results back to the client
async def send_result(websocket, result):
    try:
        if websocket.open:
            await websocket.send(json.dumps(result))
    except Exception as e:
        logger.error(f"Error sending result to client: {e}")

# Handle WebSocket connection for YOLO detection
async def process_connection(websocket, path):
    """Handle WebSocket connection for YOLO detection."""
    global stats
    client_id = id(websocket)
    logger.info(f"Client connected: {client_id}")
    
    try:
        async for message in websocket:
            try:
                # Parse message
                data = json.loads(message)
                
                # Check if it's a detection request
                if 'action' in data and data['action'] == 'detect':
                    # Get image data
                    image_data = data.get('image', '')
                    
                    # Generate frame ID
                    frame_id = f"frame_{time.time()}_{client_id}"
                    
                    # Convert base64 to image bytes
                    try:
                        img_bytes = base64.b64decode(image_data)
                        
                        # Update stats
                        stats["frames_received"] += 1
                        
                        # Add to processing queue
                        if not frame_queue.full():
                            frame_queue.put((websocket, frame_id, img_bytes))
                        else:
                            logger.warning("Frame queue full, dropping frame")
                            await websocket.send(json.dumps({
                                'error': 'Processing queue full, try again later',
                                'timestamp': time.time()
                            }))
                    except Exception as e:
                        logger.error(f"Error decoding image: {e}")
                        await websocket.send(json.dumps({
                            'error': f'Failed to decode image: {str(e)}',
                            'timestamp': time.time()
                        }))
                    
            except json.JSONDecodeError:
                logger.warning("Invalid JSON received")
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                # Send error back to client
                await websocket.send(json.dumps({
                    'error': str(e),
                    'timestamp': time.time()
                }))
                
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"Connection closed for client: {client_id}")
    finally:
        logger.info(f"Client disconnected: {client_id}")

async def start_server():
    """Start the WebSocket server."""
    # Start YOLO processing threads (use multiple threads for higher throughput)
    num_threads = os.cpu_count() - 1 or 1  # Use all but one CPU cores
    processing_threads = []
    
    for _ in range(num_threads):
        t = threading.Thread(target=process_frames, daemon=True)
        t.start()
        processing_threads.append(t)
    
    logger.info(f"Started {num_threads} YOLO processing threads")
    
    # Start WebSocket server
    server = await websockets.serve(
        process_connection,
        "0.0.0.0",  # Listen on all interfaces
        8765,  # Port to listen on
        ping_interval=30,  # Send ping every 30 seconds
        ping_timeout=10    # Wait 10 seconds for pong response
    )
    
    logger.info("YOLO WebSocket server started on ws://0.0.0.0:8765")
    
    # Keep server running
    await server.wait_closed()
    
    # Signal processing threads to stop
    for _ in range(num_threads):
        frame_queue.put(None)
    
    # Wait for threads to finish
    for t in processing_threads:
        t.join(timeout=5.0)

# Run the server
if __name__ == "__main__":
    # Load model in a separate thread to avoid blocking the server startup
    model_thread = threading.Thread(target=load_model)
    model_thread.start()
    model_thread.join()  # Wait for model to load before starting server
    
    if model is None:
        logger.error("Failed to load YOLO model, exiting")
        exit(1)
    
    # Start the server
    logger.info("Starting YOLO WebSocket server...")
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
    finally:
        # Put None in queue to signal threads to stop
        for _ in range(os.cpu_count() - 1 or 1):
            frame_queue.put(None) 
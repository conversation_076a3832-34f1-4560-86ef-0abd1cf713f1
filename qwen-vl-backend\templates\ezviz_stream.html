<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萤石云视频流分析</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .controls {
            flex: 1;
            min-width: 300px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .video-container {
            flex: 2;
            min-width: 640px;
            background: #333;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }
        .analysis {
            flex: 1;
            min-width: 300px;
            max-height: 800px;
            overflow-y: auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #f44336;
            color: #f44336;
        }
        #video-stream {
            width: 100%;
            max-height: 480px;
            background-color: #000;
            display: block;
            margin: 0 auto;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .api-result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f0f8ff;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .objects-detected {
            margin-top: 10px;
        }
        .object-item {
            margin-bottom: 5px;
            padding: 5px;
            background-color: #e9e9e9;
            border-radius: 3px;
            font-size: 13px;
        }
        .high-risk {
            background-color: #ffdddd;
            border-left: 3px solid #ff0000;
        }
        .medium-risk {
            background-color: #fff9c4;
            border-left: 3px solid #ffeb3b;
        }
        .low-risk {
            background-color: #e8f5e9;
            border-left: 3px solid #4caf50;
        }
    </style>
</head>
<body>
    <h1>萤石云视频流分析</h1>
    
    <div class="container">
        <div class="controls">
            <h2>设备信息</h2>
            <div>
                <label for="device-serial">设备序列号:</label>
                <input type="text" id="device-serial" placeholder="设备序列号，例如：F77671789">
            </div>
            
            <div>
                <label for="camera-no">通道号:</label>
                <input type="number" id="camera-no" value="1" min="1">
            </div>
            
            <div>
                <label for="access-token">访问令牌:</label>
                <input type="text" id="access-token" placeholder="萤石云开放平台访问令牌">
            </div>
            
            <div>
                <label for="custom-prompt">自定义提示词 (可选):</label>
                <input type="text" id="custom-prompt" placeholder="给模型的提示词，默认为检测安全风险">
            </div>
            
            <button id="start-stream">开始视频流分析</button>
            <button id="stop-stream" disabled>停止视频流</button>
            
            <div id="status-info" class="status">
                状态: 等待启动
            </div>
        </div>
        
        <div class="video-container">
            <h2>视频流</h2>
            <img id="video-stream" alt="视频流" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
            <div class="objects-detected" id="objects-detected"></div>
        </div>
        
        <div class="analysis">
            <h2>分析结果</h2>
            <div id="api-results">
                <p>等待API分析结果...</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let ws;
        let streamId;
        let isConnected = false;
        
        // 获取DOM元素
        const startButton = document.getElementById('start-stream');
        const stopButton = document.getElementById('stop-stream');
        const statusInfo = document.getElementById('status-info');
        const videoElement = document.getElementById('video-stream');
        const objectsDetected = document.getElementById('objects-detected');
        const apiResults = document.getElementById('api-results');
        
        // 事件监听
        startButton.addEventListener('click', startStream);
        stopButton.addEventListener('click', stopStream);
        
        // 启动视频流
        async function startStream() {
            // 获取输入
            const deviceSerial = document.getElementById('device-serial').value.trim();
            const cameraNo = parseInt(document.getElementById('camera-no').value) || 1;
            const accessToken = document.getElementById('access-token').value.trim();
            const customPrompt = document.getElementById('custom-prompt').value.trim();
            
            // 验证输入
            if (!deviceSerial || !accessToken) {
                updateStatus('错误: 设备序列号和访问令牌必须提供', true);
                return;
            }
            
            // 禁用开始按钮
            startButton.disabled = true;
            updateStatus('启动中...', false);
            
            // 发送请求到后端
            try {
                const response = await fetch('/api/start_ezviz_stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        device_serial: deviceSerial,
                        camera_no: cameraNo,
                        access_token: accessToken,
                        prompt: customPrompt
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    streamId = data.stream_id;
                    updateStatus(`视频流启动成功，ID: ${streamId}`, false);
                    
                    // 连接WebSocket
                    connectWebSocket(data.ws_url);
                    
                    // 启用停止按钮
                    stopButton.disabled = false;
                } else {
                    updateStatus(`错误: ${data.error}`, true);
                    startButton.disabled = false;
                }
            } catch (error) {
                updateStatus(`请求错误: ${error.message}`, true);
                startButton.disabled = false;
            }
        }
        
        // 停止视频流
        async function stopStream() {
            if (!streamId) return;
            
            // 禁用停止按钮
            stopButton.disabled = true;
            updateStatus('正在停止视频流...', false);
            
            // 发送请求到后端
            try {
                const response = await fetch(`/api/stop_ezviz_stream/${streamId}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus(`视频流已停止: ${data.message}`, false);
                } else {
                    updateStatus(`停止错误: ${data.error}`, true);
                }
            } catch (error) {
                updateStatus(`请求错误: ${error.message}`, true);
            }
            
            // 关闭WebSocket
            if (ws) {
                ws.close();
            }
            
            // 重置状态
            streamId = null;
            isConnected = false;
            startButton.disabled = false;
        }
        
        // 连接WebSocket
        function connectWebSocket(wsUrl) {
            // 关闭已有连接
            if (ws) {
                ws.close();
            }
            
            // 创建WebSocket连接
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsPath = wsUrl.startsWith('/') ? wsUrl.substring(1) : wsUrl;
            const fullWsUrl = `${protocol}//${window.location.host}/${wsPath}`;
            
            ws = new WebSocket(fullWsUrl);
            
            ws.onopen = function() {
                isConnected = true;
                updateStatus('WebSocket连接已建立，等待视频流...', false);
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                
                // 处理不同类型的消息
                switch (message.type) {
                    case 'status':
                        updateStatus(`流状态: ${message.status}`, false);
                        if (message.status === 'disconnected') {
                            stopButton.disabled = true;
                            startButton.disabled = false;
                            isConnected = false;
                        }
                        break;
                        
                    case 'frame':
                        // 更新视频帧
                        videoElement.src = `data:image/jpeg;base64,${message.frame}`;
                        
                        // 更新检测到的对象
                        updateDetectedObjects(message.objects);
                        break;
                        
                    case 'api_result':
                        // 更新API结果
                        updateApiResults(message.api_result);
                        break;
                        
                    case 'error':
                        updateStatus(`错误: ${message.error}`, true);
                        break;
                }
            };
            
            ws.onclose = function() {
                isConnected = false;
                updateStatus('WebSocket连接已关闭', false);
            };
            
            ws.onerror = function(error) {
                updateStatus(`WebSocket错误: ${error.message}`, true);
                isConnected = false;
            };
        }
        
        // 更新状态信息
        function updateStatus(message, isError) {
            statusInfo.textContent = message;
            if (isError) {
                statusInfo.classList.add('error');
            } else {
                statusInfo.classList.remove('error');
            }
        }
        
        // 更新检测到的对象
        function updateDetectedObjects(objects) {
            if (!objects || objects.length === 0) {
                objectsDetected.innerHTML = '<p>未检测到对象</p>';
                return;
            }
            
            let html = `<p>检测到 ${objects.length} 个对象:</p>`;
            
            objects.forEach(obj => {
                const riskClass = obj.risk_level ? 
                    (obj.risk_level.toLowerCase().includes('high') ? 'high-risk' : 
                     obj.risk_level.toLowerCase().includes('medium') ? 'medium-risk' : 'low-risk') : '';
                
                html += `
                <div class="object-item ${riskClass}">
                    类别: ${obj.category || obj.label || '未知'}, 
                    ${obj.event ? `事件: ${obj.event}, ` : ''}
                    ${obj.risk_level ? `风险: ${obj.risk_level}, ` : ''}
                    置信度: ${(obj.confidence || 0).toFixed(2)}
                </div>`;
            });
            
            objectsDetected.innerHTML = html;
        }
        
        // 更新API分析结果
        function updateApiResults(result) {
            if (!result) {
                apiResults.innerHTML = '<p>等待API分析结果...</p>';
                return;
            }
            
            let html = '<div class="api-result">';
            
            // 处理描述
            if (result.description) {
                html += `<h3>场景描述</h3>
                         <div>${result.description}</div>`;
            }
            
            // 处理风险事件
            if (result.high_risk_events && result.high_risk_events.length > 0) {
                html += `<h3>高风险事件 (${result.high_risk_events.length})</h3>`;
                result.high_risk_events.forEach(event => {
                    html += `<div class="object-item high-risk">
                        ${event.category ? `类别: ${event.category}, ` : ''}
                        ${event.event ? `事件: ${event.event}, ` : ''}
                        ${event.description ? `描述: ${event.description}` : ''}
                    </div>`;
                });
            }
            
            if (result.low_risk_events && result.low_risk_events.length > 0) {
                html += `<h3>中低风险事件 (${result.low_risk_events.length})</h3>`;
                result.low_risk_events.forEach(event => {
                    html += `<div class="object-item medium-risk">
                        ${event.category ? `类别: ${event.category}, ` : ''}
                        ${event.event ? `事件: ${event.event}, ` : ''}
                        ${event.description ? `描述: ${event.description}` : ''}
                    </div>`;
                });
            }
            
            // 添加原始JSON展示（可折叠）
            html += `<h3>原始API结果</h3>
                     <details>
                         <summary>查看详情</summary>
                         <pre>${JSON.stringify(result, null, 2)}</pre>
                     </details>`;
            
            html += '</div>';
            apiResults.innerHTML = html;
        }
    </script>
</body>
</html> 
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Traffic Eyes 系统导航</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .card-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            padding: 20px;
            width: 300px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .card h2 {
            color: #3498db;
            margin-top: 0;
        }
        .card p {
            color: #7f8c8d;
            line-height: 1.5;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            text-decoration: none;
            margin-top: 10px;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #2ecc71;
        }
        .btn-secondary:hover {
            background-color: #27ae60;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Traffic Eyes 系统导航</h1>
        
        <div class="card-container">
            <div class="card">
                <h2>原始系统前端</h2>
                <p>访问原有的Traffic Eyes系统前端界面，包括Ezviz云监控和视频管理功能。</p>
                <a href="http://localhost:5173" class="btn" target="_blank">访问前端</a>
            </div>
            
            <div class="card">
                <h2>离线视频处理</h2>
                <p>高质量视频处理系统，可以处理完整视频文件并保存结果，提供高质量的对象检测。</p>
                <a href="http://localhost:8080" class="btn btn-secondary" target="_blank">访问离线处理</a>
            </div>
            
            <div class="card">
                <h2>实时视频流</h2>
                <p>优化的实时视频处理系统，使用MJPEG流提供实时处理的视频帧，支持低延迟显示。</p>
                <a href="http://localhost:8081" class="btn btn-secondary" target="_blank">访问实时流</a>
            </div>
            
            <div class="card">
                <h2>萤石云监控</h2>
                <p>使用YOLOv8x-seg高级分割模型处理萤石云摄像头视频流，提供实时对象检测和分割。</p>
                <a href="http://localhost:8082" class="btn btn-secondary" target="_blank">访问萤石云监控</a>
            </div>
            
            <div class="card">
                <h2>API服务</h2>
                <p>WebSocket API服务，提供YOLO检测结果和视频流处理功能。</p>
                <a href="http://localhost:8001/docs" class="btn" target="_blank">API文档</a>
            </div>
            
            <div class="card">
                <h2>主后端</h2>
                <p>Traffic Eyes主后端服务，处理用户认证、视频管理等功能。</p>
                <a href="http://localhost:8000/docs" class="btn" target="_blank">API文档</a>
            </div>
            
            <div class="card">
                <h2>系统文档</h2>
                <p>查看系统文档，了解如何使用和配置Traffic Eyes系统。</p>
                <a href="VIDEO_PROCESSOR_README.md" class="btn" target="_blank">视频处理文档</a>
                <a href="README.md" class="btn" target="_blank">系统文档</a>
            </div>
        </div>
        
        <div class="footer">
            <p>Traffic Eyes 视频监控系统 &copy; 2025</p>
        </div>
    </div>
    
    <script>
        // 检查各服务是否在线
        async function checkService(url, elementId) {
            try {
                const response = await fetch(url, { 
                    method: 'HEAD',
                    mode: 'no-cors',
                    cache: 'no-cache'
                });
                document.getElementById(elementId).classList.add('online');
            } catch (error) {
                document.getElementById(elementId).classList.add('offline');
            }
        }
        
        // 页面加载时检查服务状态
        window.addEventListener('DOMContentLoaded', () => {
            // 这里可以添加服务状态检查逻辑
        });
    </script>
</body>
</html> 
from typing import Dict
from uuid import uuid4

import asyncio
from starlette.concurrency import run_in_threadpool
from task import sumbit_task_qwen_analysis, sumbit_task_qwen_analysis_local_video
import asyncio,random
import base64
import numpy as np
import cv2

tasks: Dict[str, any] = {}

async def sumbit_task_qwen(should_call_api, img, objects, taskid):
    await asyncio.sleep(2)
    try:
        # 检查图像是否为base64字符串，如果是则解码
        if isinstance(img, str) and ('base64' in img or len(img) > 200):
            try:
                # 解码Base64图像
                image_data = base64.b64decode(img.split(',')[1] if ',' in img else img)
                nparr = np.frombuffer(image_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            except Exception as e:
                print(f"Base64解码错误: {str(e)}")
                # 如果解码失败，设置为None
                img = None
        
        # 处理任务
        result = await run_in_threadpool(sumbit_task_qwen_analysis, should_call_api, img, objects)
        
        # 存储结果及创建时间
        import time
        tasks[taskid] = {
            "result": result,
            "created_at": time.time(),
            "status": "completed"
        }
    except Exception as e:
        print(f"任务处理错误: {str(e)}")
        import time
        tasks[taskid] = {
            "result": None,
            "created_at": time.time(),
            "status": "failed",
            "error": str(e)
        }
    return

# 添加使用qwen-vl-max模型的任务提交函数
async def sumbit_task_qwen_max(should_call_api, img, objects, taskid):
    """
    使用qwen-vl-max-1119模型处理本地视频分析任务
    """
    await asyncio.sleep(2)
    try:
        # 检查图像是否为base64字符串，如果是则解码
        if isinstance(img, str) and ('base64' in img or len(img) > 200):
            try:
                # 解码Base64图像
                image_data = base64.b64decode(img.split(',')[1] if ',' in img else img)
                nparr = np.frombuffer(image_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            except Exception as e:
                print(f"Base64解码错误: {str(e)}")
                # 如果解码失败，设置为None
                img = None
        
        # 使用qwen-vl-max模型处理任务
        result = await run_in_threadpool(sumbit_task_qwen_analysis_local_video, should_call_api, img, objects)
        
        # 存储结果及创建时间
        import time
        tasks[taskid] = {
            "result": result,
            "created_at": time.time(),
            "status": "completed",
            "model": "qwen-vl-max-1119"  # 标记使用的模型
        }
    except Exception as e:
        print(f"本地视频任务处理错误: {str(e)}")
        import time
        tasks[taskid] = {
            "result": None,
            "created_at": time.time(),
            "status": "failed",
            "error": str(e),
            "model": "qwen-vl-max-1119"  # 标记使用的模型
        }
    return

async def run_task(task_id: str, data: str):
    try:
        tasks[task_id]["status"] = "processing"
        await asyncio.sleep(5)  # 模拟耗时任务
        result = f"Processed data: {data}"
        tasks[task_id]["status"] = "completed"
        tasks[task_id]["result"] = result
    except Exception as e:
        tasks[task_id]["status"] = "failed"
        tasks[task_id]["result"] = str(e)

def get_task_status(task_id: str=None) -> Dict:
    if task_id is None:
        return tasks
    return tasks.get(task_id,500)

def dele_task(taskid:str=None):
    """
    删除指定任务ID的任务，如果没有指定任务ID则清除过期任务
    """
    if taskid and taskid in tasks:
        # 删除指定任务
        try:
            del tasks[taskid]
            return True
        except Exception as e:
            print(f"删除任务 {taskid} 时出错: {str(e)}")
            return False
    elif taskid is None:
        # 清理所有过期任务的逻辑
        # 这里可以添加按时间清理任务的逻辑，比如清除24小时前的任务
        expired_tasks = []
        import time
        current_time = time.time()
        
        try:
            # 找出所有过期任务
            for task_id, task_data in list(tasks.items()):
                # 如果任务创建时间超过1小时，则删除
                if isinstance(task_data, dict) and task_data.get('created_at') and current_time - task_data.get('created_at', 0) > 3600:
                    expired_tasks.append(task_id)
                    
            # 删除过期任务
            for task_id in expired_tasks:
                try:
                    del tasks[task_id]
                except KeyError:
                    pass
                    
            return True
        except Exception as e:
            print(f"清理过期任务时出错: {str(e)}")
            return False
            
    return False
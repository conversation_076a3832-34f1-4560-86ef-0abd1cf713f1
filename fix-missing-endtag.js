import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the file with the issue
const filePath = path.join(__dirname, 'src', 'components', 'OnlineMonitor.vue');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Create a backup
fs.writeFileSync(`${filePath}.backup-${Date.now()}`, content, 'utf8');

// Fix the specific issue with missing </template> end tag
// Look for the pattern of template tag with tryParseJsonLog(log) conditional
const fixedContent = content.replace(
  /<template v-if="tryParseJsonLog\(log\)">([\s\S]*?)(?:<\/div>|<div)(?!\s*<\/template>)/g,
  (match, p1, offset, string) => {
    // Check if this template already has a closing tag
    const nextClosingTagPos = string.indexOf('</template>', offset + match.length);
    const nextOpeningTagPos = string.indexOf('<template', offset + match.length);
    
    // If there's no closing tag or there's another opening tag before the next closing tag
    if (nextClosingTagPos === -1 || (nextOpeningTagPos !== -1 && nextOpeningTagPos < nextClosingTagPos)) {
      // Add the missing closing tag
      return `<template v-if="tryParseJsonLog(log)">${p1}</template><div`;
    }
    
    return match;
  }
);

// Write the fixed content back
fs.writeFileSync(filePath, fixedContent, 'utf8');

console.log('Fixed missing </template> end tags in OnlineMonitor.vue'); 
# Traffic Eyes Advanced Startup Script
# Uses Redis and dual-server architecture to enhance real-time cloud video processing performance

Write-Host "Starting Traffic Eyes advanced architecture..." -ForegroundColor Green

# Check Redis installation
$redis_server = "redis-server.exe"
$redis_found = $false
try {
    $found = Get-Command $redis_server -ErrorAction SilentlyContinue
    if ($found) {
        $redis_found = $true
        Write-Host "Found Redis: $($found.Source)" -ForegroundColor Green
    }
} catch {
    Write-Host "Redis not found, will attempt embedded or install..." -ForegroundColor Yellow
}

# Start Redis
if (-not $redis_found) {
    $embedded = ".\redis-server.exe"
    if (Test-Path $embedded) {
        Write-Host "Using embedded Redis..." -ForegroundColor Cyan
        Start-Process -FilePath $embedded -ArgumentList "--port 6379" -NoNewWindow
    } else {
        $installer = "..\Redis-x64-3.0.504.msi"
        if (Test-Path $installer) {
            Write-Host "Installing Redis MSI..." -ForegroundColor Cyan
            Start-Process msiexec.exe -ArgumentList "/i `"$installer`" /qn" -Wait
            Write-Host "Starting Redis service..." -ForegroundColor Green
            Start-Service -Name "Redis"
        } else {
            Write-Host "Redis installer not found. Please download from GitHub and retry." -ForegroundColor Red
            exit 1
        }
    }
}

# Create necessary directories
$dirs = @("../logs", "./static/uploads")
foreach ($d in $dirs) {
    if (-not (Test-Path $d)) {
        Write-Host "Creating directory: $d" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $d -Force | Out-Null
    }
}

# Check Python environment
$py = "python"
try {
    $ver = & $py --version 2>&1
    Write-Host "Python version: $ver" -ForegroundColor Green
} catch {
    Write-Host "Python not detected. Please install Python 3.8 or later." -ForegroundColor Red
    exit 1
}

# Install required Python packages
if (Test-Path "requirements.txt") {
    Write-Host "Installing dependencies from requirements.txt..." -ForegroundColor Yellow
    & $py -m pip install -r requirements.txt
} else {
    $pkgs = @("fastapi","uvicorn","redis","websocket-client","numpy","opencv-python","ultralytics","pillow")
    $toInstall = @()
    foreach ($p in $pkgs) {
        $res = & $py -m pip show $p 2>&1
        if (-not $res) { $toInstall += $p }
    }
    if ($toInstall.Count -gt 0) {
        Write-Host "Installing missing packages: $($toInstall -join ', ')" -ForegroundColor Yellow
        & $py -m pip install $toInstall
    }
}

# Ensure model file exists
$modelDir = "..\src\models"
$modelFile = "$modelDir\yolo11n-seg.pt"
if (-not (Test-Path $modelFile)) {
    if (-not (Test-Path $modelDir)) {
        New-Item -ItemType Directory -Path $modelDir -Force | Out-Null
    }
    $dl = ".\download_models.py"
    if (Test-Path $dl) {
        Write-Host "Downloading model..." -ForegroundColor Cyan
        & $py $dl
    } else {
        Write-Host "Model file missing. Please manually download to $modelFile" -ForegroundColor Red
    }
}

# Start services and monitor
$pids = @()
try {
    Write-Host "Starting API service (8001)..." -ForegroundColor Cyan
    $api = Start-Process -FilePath $py -ArgumentList "api_server.py" -WorkingDirectory "." -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($api.HasExited) { Throw "API service failed to start" }

    Write-Host "Starting YOLO processing service..." -ForegroundColor Cyan
    $proc = Start-Process -FilePath $py -ArgumentList "yolo_redis_processor.py" -WorkingDirectory "." -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($proc.HasExited) { Stop-Process -Id $api.Id; Throw "YOLO processor failed to start" }

    Write-Host "Starting main backend (8000)..." -ForegroundColor Cyan
    $main = Start-Process -FilePath $py -ArgumentList "main.py" -WorkingDirectory "." -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 3
    if ($main.HasExited) { Stop-Process -Id $api.Id,$proc.Id; Throw "Main backend failed to start" }

    $npm = Get-Command npm -ErrorAction SilentlyContinue
    if (-not $npm) {
        Write-Host "npm not found. Please install Node.js and ensure it's in PATH." -ForegroundColor Red
        exit 1
    }
    Write-Host "Starting frontend..." -ForegroundColor Cyan
    Push-Location ".."
    $fe = Start-Process -FilePath npm -ArgumentList "run dev" -PassThru -WindowStyle Hidden
    Pop-Location

    Write-Host "All services started!" -ForegroundColor Green
    Write-Host "Frontend: http://localhost:5173" -ForegroundColor Magenta
    Write-Host "Main backend: http://localhost:8000" -ForegroundColor Magenta
    Write-Host "API: http://localhost:8001" -ForegroundColor Magenta

    $pids = @($api.Id, $proc.Id, $main.Id, $fe.Id)
    $pids | Out-File -FilePath "running_processes.txt"

    while ($true) {
        Start-Sleep -Seconds 1
        if ($api.HasExited -or $proc.HasExited -or $main.HasExited -or $fe.HasExited) {
            Write-Host "Detected service stop. Enter R to restart, any other key to exit..." -ForegroundColor Yellow
            $input = Read-Host
            if ($input -eq "R") {
                powershell -File $MyInvocation.MyCommand.Definition
                exit
            } else {
                break
            }
        }
    }
} finally {
    Write-Host "Cleaning up..." -ForegroundColor Red
    foreach ($id in $pids) { Stop-Process -Id $id -Force -ErrorAction SilentlyContinue }
    if (Test-Path "running_processes.txt") { Remove-Item "running_processes.txt" -Force }
    Write-Host "All services stopped" -ForegroundColor Green
}

{"format": "graph-model", "generatedBy": "2.11.0", "convertedBy": "TensorFlow.js Converter v4.3.0", "signature": {"outputs": {"Identity:0": {"name": "Identity:0"}}}, "modelTopology": {"node": [{"name": "ConstantFolding/PartitionedCall/model/tf.math.divide/truediv_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1328", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "2"}, {"size": "8400"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1320", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "1322", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_19/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1330", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "2"}, {"size": "8400"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1314", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "1316", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_16/StridedSlice/strides", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.reshape_3/Reshape/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_4/transpose/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_5/transpose/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1318", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_8/transpose/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.reshape_4/Reshape/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "1324", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1326", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_18/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.concat_17/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "PartitionedCall/model/tf.math.multiply_128/Mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "8400"}]}}}}}, {"name": "1252", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1264", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1276", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}}}, {"name": "1288", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1300", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1310", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1250", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1262", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}}}, {"name": "1274", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "80"}, {"size": "80"}]}}}}}, {"name": "1286", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}}}, {"name": "1298", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "80"}, {"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1312", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_10/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose/transpose/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.reshape/Reshape/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "1248", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1260", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1272", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}}}, {"name": "1284", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1296", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "64"}]}}}}}, {"name": "1306", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1246", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1258", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1270", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "80"}, {"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1282", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1294", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "80"}, {"size": "80"}]}}}}}, {"name": "1308", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_13/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_1/transpose/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.reshape_1/Reshape/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1244", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1256", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1268", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1280", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1292", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "64"}]}}}}}, {"name": "1302", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1234", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1236", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_14/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1204", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1206", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_12/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1174", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1176", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_10/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/lambda_1//model.13/Resize/size", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.concat_7/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1158", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1160", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1162", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1164", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_11/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1166", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}}}, {"name": "1168", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "1170", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}}}, {"name": "1172", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_8/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1178", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "64"}]}}}}}, {"name": "1180", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1182", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1184", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}}}, {"name": "1186", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1150", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1152", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_8/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/lambda//model.10/Resize/size", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_5/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "1134", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "128"}]}}}}}, {"name": "1136", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1138", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1140", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_9/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1142", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1144", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1146", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1148", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_6/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1154", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "128"}]}}}}}, {"name": "1156", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_9/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1188", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1190", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "1192", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1194", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_13/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1196", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1198", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1200", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}}}, {"name": "1202", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_11/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1208", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1210", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "1212", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1214", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1216", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "1118", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1120", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_6/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1088", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1090", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_4/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1050", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1052", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_2/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1012", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1014", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "984", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "986", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "16"}]}}}}}, {"name": "988", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "990", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "992", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "32"}]}}}}}, {"name": "994", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "996", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "32"}]}}}}}, {"name": "998", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "1000", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1002", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_1/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1004", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "1006", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1008", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1010", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/model/tf.concat/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1016", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1018", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "1020", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1022", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "64"}]}}}}}, {"name": "1024", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1026", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1028", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1030", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1032", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_3/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1034", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}}}, {"name": "1036", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "1038", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1040", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1042", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1044", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "1046", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}}}, {"name": "1048", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_1/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "1054", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "1056", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1058", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "1060", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "1062", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "1064", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1066", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1068", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1070", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_5/ones_like", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1072", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}}}, {"name": "1074", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1076", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1078", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1080", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}}}, {"name": "1082", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "1084", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1086", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_2/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1092", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "128"}]}}}}}, {"name": "1094", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1096", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "1098", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "256"}]}}}}}, {"name": "1100", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "1102", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}}}, {"name": "1104", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "1106", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1108", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_7/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1110", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1112", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1114", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "128"}]}}}}}, {"name": "1116", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_3/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1122", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "256"}]}}}}}, {"name": "1124", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "1126", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "128"}]}}}}}, {"name": "1128", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_4/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "1130", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "512"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1132", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_12/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1218", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1220", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "1222", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "1224", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.strided_slice_15/ones_like", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1226", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "128"}]}}}}}, {"name": "1228", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1230", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1232", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_14/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "1238", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "256"}]}}}}}, {"name": "1240", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1242", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "80"}]}}}}}, {"name": "1254", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1266", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "80"}, {"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "1278", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}}}, {"name": "1290", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "80"}, {"size": "80"}]}}}}}, {"name": "1304", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_15/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_2/transpose/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/model/tf.reshape_2/Reshape/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "PartitionedCall/model/tf.concat_16/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "1334", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "1336", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_17/StridedSlice/strides", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/output0/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "x", "op": "Placeholder", "attr": {"dtype": {"type": "DT_FLOAT"}, "shape": {"shape": {"dim": [{"size": "1"}, {"size": "640"}, {"size": "640"}, {"size": "3"}]}}}}, {"name": "PartitionedCall/model/tf.compat.v1.pad/Pad", "op": "Pad", "input": ["x", "984"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.nn.convolution/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.compat.v1.pad/Pad", "986"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution/convolution", "988"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_1/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add/Add", "PartitionedCall/model/tf.math.sigmoid/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.pad_1/Pad", "op": "Pad", "input": ["PartitionedCall/model/tf.math.multiply_1/Mul", "990"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_1/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.compat.v1.pad_1/Pad", "992"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/model/tf.math.add_1/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_1/convolution", "994"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_1/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_3/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_1/Add", "PartitionedCall/model/tf.math.sigmoid_1/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_2/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_3/Mul", "996"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.math.add_2/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_2/convolution", "998"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_2/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_2/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_5/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_2/Add", "PartitionedCall/model/tf.math.sigmoid_2/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.strided_slice/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_5/Mul", "1012", "1014", "PartitionedCall/model/tf.strided_slice/ones_like"], "attr": {"T": {"type": "DT_FLOAT"}, "Index": {"type": "DT_INT64"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "15"}, "end_mask": {"i": "7"}, "ellipsis_mask": {"i": "0"}}}, {"name": "PartitionedCall/model/tf.strided_slice_1/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_5/Mul", "1000", "1002", "PartitionedCall/model/tf.strided_slice_1/ones_like"], "attr": {"Index": {"type": "DT_INT64"}, "begin_mask": {"i": "7"}, "new_axis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "7"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_3/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.strided_slice_1/StridedSlice", "1004"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.math.add_3/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_3/convolution", "1006"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_3/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_3/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_7/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_3/Add", "PartitionedCall/model/tf.math.sigmoid_3/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_4/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_7/Mul", "1008"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/model/tf.math.add_4/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_4/convolution", "1010"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_4/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_4/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_9/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_4/Add", "PartitionedCall/model/tf.math.sigmoid_4/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_5/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.strided_slice_1/StridedSlice", "PartitionedCall/model/tf.math.multiply_9/Mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.strided_slice/StridedSlice", "PartitionedCall/model/tf.strided_slice_1/StridedSlice", "PartitionedCall/model/tf.math.add_5/Add", "PartitionedCall/model/tf.concat/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "N": {"i": "3"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_5/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat/concat", "1016"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_6/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_5/convolution", "1018"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_5/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_6/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_12/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_6/Add", "PartitionedCall/model/tf.math.sigmoid_5/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.pad_2/Pad", "op": "Pad", "input": ["PartitionedCall/model/tf.math.multiply_12/Mul", "1020"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_6/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.compat.v1.pad_2/Pad", "1022"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_7/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_6/convolution", "1024"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_6/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_7/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_14/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_7/Add", "PartitionedCall/model/tf.math.sigmoid_6/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_7/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_14/Mul", "1026"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_8/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_7/convolution", "1028"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_7/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_8/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_16/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_8/Add", "PartitionedCall/model/tf.math.sigmoid_7/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.strided_slice_2/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_16/Mul", "1050", "1052", "PartitionedCall/model/tf.strided_slice_2/ones_like"], "attr": {"begin_mask": {"i": "15"}, "T": {"type": "DT_FLOAT"}, "end_mask": {"i": "7"}, "ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_3/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_16/Mul", "1030", "1032", "PartitionedCall/model/tf.strided_slice_3/ones_like"], "attr": {"T": {"type": "DT_FLOAT"}, "ellipsis_mask": {"i": "0"}, "end_mask": {"i": "7"}, "shrink_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}, "begin_mask": {"i": "7"}, "new_axis_mask": {"i": "0"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_8/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.strided_slice_3/StridedSlice", "1034"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.math.add_9/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_8/convolution", "1036"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_8/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_9/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_18/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_9/Add", "PartitionedCall/model/tf.math.sigmoid_8/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_9/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_18/Mul", "1038"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.math.add_10/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_9/convolution", "1040"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_9/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_10/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_20/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_10/Add", "PartitionedCall/model/tf.math.sigmoid_9/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_11/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.strided_slice_3/StridedSlice", "PartitionedCall/model/tf.math.multiply_20/Mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_10/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.add_11/Add", "1042"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_12/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_10/convolution", "1044"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_10/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_12/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_23/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_12/Add", "PartitionedCall/model/tf.math.sigmoid_10/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_11/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_23/Mul", "1046"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.math.add_13/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_11/convolution", "1048"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_11/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_13/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_25/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_13/Add", "PartitionedCall/model/tf.math.sigmoid_11/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_14/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.math.add_11/Add", "PartitionedCall/model/tf.math.multiply_25/Mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_1/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.strided_slice_2/StridedSlice", "PartitionedCall/model/tf.strided_slice_3/StridedSlice", "PartitionedCall/model/tf.math.add_11/Add", "PartitionedCall/model/tf.math.add_14/Add", "PartitionedCall/model/tf.concat_1/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "N": {"i": "4"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_12/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_1/concat", "1054"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_15/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_12/convolution", "1056"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_12/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_15/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_28/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_15/Add", "PartitionedCall/model/tf.math.sigmoid_12/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.pad_3/Pad", "op": "Pad", "input": ["PartitionedCall/model/tf.math.multiply_28/Mul", "1058"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_13/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.compat.v1.pad_3/Pad", "1060"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_16/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_13/convolution", "1062"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_13/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_16/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_30/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_16/Add", "PartitionedCall/model/tf.math.sigmoid_13/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_14/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_30/Mul", "1064"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.math.add_17/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_14/convolution", "1066"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_14/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_17/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_32/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_17/Add", "PartitionedCall/model/tf.math.sigmoid_14/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.strided_slice_4/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_32/Mul", "1088", "1090", "PartitionedCall/model/tf.strided_slice_4/ones_like"], "attr": {"shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "7"}, "begin_mask": {"i": "15"}, "Index": {"type": "DT_INT64"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.strided_slice_5/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_32/Mul", "1068", "1070", "PartitionedCall/model/tf.strided_slice_5/ones_like"], "attr": {"end_mask": {"i": "7"}, "T": {"type": "DT_FLOAT"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}, "begin_mask": {"i": "7"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_15/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.strided_slice_5/StridedSlice", "1072"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.math.add_18/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_15/convolution", "1074"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_15/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_18/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_34/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_18/Add", "PartitionedCall/model/tf.math.sigmoid_15/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_16/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_34/Mul", "1076"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.math.add_19/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_16/convolution", "1078"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_16/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_19/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_36/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_19/Add", "PartitionedCall/model/tf.math.sigmoid_16/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_20/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.strided_slice_5/StridedSlice", "PartitionedCall/model/tf.math.multiply_36/Mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_17/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.add_20/Add", "1080"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/model/tf.math.add_21/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_17/convolution", "1082"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_17/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_21/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_39/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_21/Add", "PartitionedCall/model/tf.math.sigmoid_17/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_18/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_39/Mul", "1084"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_22/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_18/convolution", "1086"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_18/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_22/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_41/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_22/Add", "PartitionedCall/model/tf.math.sigmoid_18/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_23/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.math.add_20/Add", "PartitionedCall/model/tf.math.multiply_41/Mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_2/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.strided_slice_4/StridedSlice", "PartitionedCall/model/tf.strided_slice_5/StridedSlice", "PartitionedCall/model/tf.math.add_20/Add", "PartitionedCall/model/tf.math.add_23/Add", "PartitionedCall/model/tf.concat_2/concat/axis"], "attr": {"N": {"i": "4"}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_19/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_2/concat", "1092"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_24/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_19/convolution", "1094"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_19/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_24/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_44/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_24/Add", "PartitionedCall/model/tf.math.sigmoid_19/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.pad_4/Pad", "op": "Pad", "input": ["PartitionedCall/model/tf.math.multiply_44/Mul", "1096"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_20/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.compat.v1.pad_4/Pad", "1098"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_25/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_20/convolution", "1100"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_20/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_25/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_46/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_25/Add", "PartitionedCall/model/tf.math.sigmoid_20/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_21/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_46/Mul", "1102"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.math.add_26/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_21/convolution", "1104"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_21/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_26/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_48/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_26/Add", "PartitionedCall/model/tf.math.sigmoid_21/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.strided_slice_6/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_48/Mul", "1118", "1120", "PartitionedCall/model/tf.strided_slice_6/ones_like"], "attr": {"ellipsis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}, "shrink_axis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "end_mask": {"i": "7"}, "begin_mask": {"i": "15"}, "new_axis_mask": {"i": "0"}}}, {"name": "PartitionedCall/model/tf.strided_slice_7/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_48/Mul", "1106", "1108", "PartitionedCall/model/tf.strided_slice_7/ones_like"], "attr": {"Index": {"type": "DT_INT64"}, "begin_mask": {"i": "7"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "7"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_22/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.strided_slice_7/StridedSlice", "1110"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.math.add_27/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_22/convolution", "1112"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_22/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_27/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_50/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_27/Add", "PartitionedCall/model/tf.math.sigmoid_22/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_23/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_50/Mul", "1114"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.math.add_28/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_23/convolution", "1116"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_23/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_28/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_52/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_28/Add", "PartitionedCall/model/tf.math.sigmoid_23/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_29/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.strided_slice_7/StridedSlice", "PartitionedCall/model/tf.math.multiply_52/Mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_3/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.strided_slice_6/StridedSlice", "PartitionedCall/model/tf.strided_slice_7/StridedSlice", "PartitionedCall/model/tf.math.add_29/Add", "PartitionedCall/model/tf.concat_3/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "N": {"i": "3"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_24/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_3/concat", "1122"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_30/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_24/convolution", "1124"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_24/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_30/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_55/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_30/Add", "PartitionedCall/model/tf.math.sigmoid_24/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_25/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_55/Mul", "1126"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.math.add_31/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_25/convolution", "1128"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_25/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_31/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_57/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_31/Add", "PartitionedCall/model/tf.math.sigmoid_25/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.nn.pool/max_pool", "op": "MaxPool", "input": ["PartitionedCall/model/tf.math.multiply_57/Mul"], "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "ksize": {"list": {"i": ["1", "5", "5", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.compat.v1.nn.pool_1/max_pool", "op": "MaxPool", "input": ["PartitionedCall/model/tf.compat.v1.nn.pool/max_pool"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "ksize": {"list": {"i": ["1", "5", "5", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.compat.v1.nn.pool_2/max_pool", "op": "MaxPool", "input": ["PartitionedCall/model/tf.compat.v1.nn.pool_1/max_pool"], "attr": {"explicit_paddings": {"list": {}}, "ksize": {"list": {"i": ["1", "5", "5", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.concat_4/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.math.multiply_57/Mul", "PartitionedCall/model/tf.compat.v1.nn.pool/max_pool", "PartitionedCall/model/tf.compat.v1.nn.pool_1/max_pool", "PartitionedCall/model/tf.compat.v1.nn.pool_2/max_pool", "PartitionedCall/model/tf.concat_4/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "N": {"i": "4"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_26/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_4/concat", "1130"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.math.add_32/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_26/convolution", "1132"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_26/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_32/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_59/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_32/Add", "PartitionedCall/model/tf.math.sigmoid_26/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/lambda//model.10/Resize", "op": "ResizeNearestNeighbor", "input": ["PartitionedCall/model/tf.math.multiply_59/Mul", "PartitionedCall/model/lambda//model.10/Resize/size"], "attr": {"align_corners": {"b": false}, "half_pixel_centers": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_5/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/lambda//model.10/Resize", "PartitionedCall/model/tf.math.multiply_44/Mul", "PartitionedCall/model/tf.concat_5/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_27/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_5/concat", "1134"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_33/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_27/convolution", "1136"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_27/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_33/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_61/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_33/Add", "PartitionedCall/model/tf.math.sigmoid_27/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.strided_slice_8/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_61/Mul", "1150", "1152", "PartitionedCall/model/tf.strided_slice_8/ones_like"], "attr": {"end_mask": {"i": "7"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}, "T": {"type": "DT_FLOAT"}, "shrink_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "15"}}}, {"name": "PartitionedCall/model/tf.strided_slice_9/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_61/Mul", "1138", "1140", "PartitionedCall/model/tf.strided_slice_9/ones_like"], "attr": {"Index": {"type": "DT_INT64"}, "end_mask": {"i": "7"}, "begin_mask": {"i": "7"}, "new_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_28/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.strided_slice_9/StridedSlice", "1142"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_34/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_28/convolution", "1144"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_28/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_34/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_63/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_34/Add", "PartitionedCall/model/tf.math.sigmoid_28/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_29/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_63/Mul", "1146"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_35/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_29/convolution", "1148"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_29/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_35/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_65/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_35/Add", "PartitionedCall/model/tf.math.sigmoid_29/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_6/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.strided_slice_8/StridedSlice", "PartitionedCall/model/tf.strided_slice_9/StridedSlice", "PartitionedCall/model/tf.math.multiply_65/Mul", "PartitionedCall/model/tf.concat_6/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "N": {"i": "3"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_30/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_6/concat", "1154"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.math.add_36/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_30/convolution", "1156"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_30/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_36/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_67/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_36/Add", "PartitionedCall/model/tf.math.sigmoid_30/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/lambda_1//model.13/Resize", "op": "ResizeNearestNeighbor", "input": ["PartitionedCall/model/tf.math.multiply_67/Mul", "PartitionedCall/model/lambda_1//model.13/Resize/size"], "attr": {"align_corners": {"b": false}, "T": {"type": "DT_FLOAT"}, "half_pixel_centers": {"b": false}}}, {"name": "PartitionedCall/model/tf.concat_7/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/lambda_1//model.13/Resize", "PartitionedCall/model/tf.math.multiply_28/Mul", "PartitionedCall/model/tf.concat_7/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_31/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_7/concat", "1158"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_37/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_31/convolution", "1160"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_31/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_37/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_69/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_37/Add", "PartitionedCall/model/tf.math.sigmoid_31/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.strided_slice_10/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_69/Mul", "1174", "1176", "PartitionedCall/model/tf.strided_slice_10/ones_like"], "attr": {"Index": {"type": "DT_INT64"}, "T": {"type": "DT_FLOAT"}, "end_mask": {"i": "7"}, "shrink_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "15"}, "new_axis_mask": {"i": "0"}}}, {"name": "PartitionedCall/model/tf.strided_slice_11/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_69/Mul", "1162", "1164", "PartitionedCall/model/tf.strided_slice_11/ones_like"], "attr": {"begin_mask": {"i": "7"}, "Index": {"type": "DT_INT64"}, "T": {"type": "DT_FLOAT"}, "end_mask": {"i": "7"}, "shrink_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_32/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.strided_slice_11/StridedSlice", "1166"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.math.add_38/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_32/convolution", "1168"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_32/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_38/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_71/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_38/Add", "PartitionedCall/model/tf.math.sigmoid_32/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_33/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_71/Mul", "1170"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.math.add_39/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_33/convolution", "1172"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_33/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_39/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_73/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_39/Add", "PartitionedCall/model/tf.math.sigmoid_33/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_8/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.strided_slice_10/StridedSlice", "PartitionedCall/model/tf.strided_slice_11/StridedSlice", "PartitionedCall/model/tf.math.multiply_73/Mul", "PartitionedCall/model/tf.concat_8/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "N": {"i": "3"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_34/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_8/concat", "1178"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.math.add_40/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_34/convolution", "1180"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_34/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_40/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_75/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_40/Add", "PartitionedCall/model/tf.math.sigmoid_34/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_36/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_75/Mul", "1252"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.nn.convolution_37/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_75/Mul", "1250"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.compat.v1.pad_5/Pad", "op": "Pad", "input": ["PartitionedCall/model/tf.math.multiply_75/Mul", "1182"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.math.add_42/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_36/convolution", "1264"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_43/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_37/convolution", "1262"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_35/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.compat.v1.pad_5/Pad", "1184"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_36/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_42/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_37/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_43/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_41/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_35/convolution", "1186"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_79/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_42/Add", "PartitionedCall/model/tf.math.sigmoid_36/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_81/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_43/Add", "PartitionedCall/model/tf.math.sigmoid_37/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_35/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_41/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_38/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_79/Mul", "1276"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.nn.convolution_39/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_81/Mul", "1274"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.math.multiply_77/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_41/Add", "PartitionedCall/model/tf.math.sigmoid_35/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_44/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_38/convolution", "1288"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_45/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_39/convolution", "1286"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_9/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.math.multiply_77/Mul", "PartitionedCall/model/tf.math.multiply_67/Mul", "PartitionedCall/model/tf.concat_9/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_38/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_44/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_39/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_45/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_40/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_9/concat", "1188"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.math.multiply_83/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_44/Add", "PartitionedCall/model/tf.math.sigmoid_38/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_85/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_45/Add", "PartitionedCall/model/tf.math.sigmoid_39/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_46/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_40/convolution", "1190"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_41/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_83/Mul", "1300"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.nn.convolution_42/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_85/Mul", "1298"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_40/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_46/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_47/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_41/convolution", "1310"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_48/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_42/convolution", "1312"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_87/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_46/Add", "PartitionedCall/model/tf.math.sigmoid_40/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_10/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.math.add_47/Add", "PartitionedCall/model/tf.math.add_48/Add", "PartitionedCall/model/tf.concat_10/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "PartitionedCall/model/tf.strided_slice_12/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_87/Mul", "1204", "1206", "PartitionedCall/model/tf.strided_slice_12/ones_like"], "attr": {"Index": {"type": "DT_INT64"}, "end_mask": {"i": "7"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "15"}, "ellipsis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.strided_slice_13/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_87/Mul", "1192", "1194", "PartitionedCall/model/tf.strided_slice_13/ones_like"], "attr": {"shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "7"}, "end_mask": {"i": "7"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose/transpose", "op": "Transpose", "input": ["PartitionedCall/model/tf.concat_10/concat", "PartitionedCall/model/tf.compat.v1.transpose/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_43/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.strided_slice_13/StridedSlice", "1196"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/model/tf.reshape/Reshape", "op": "Reshape", "input": ["PartitionedCall/model/tf.compat.v1.transpose/transpose", "PartitionedCall/model/tf.reshape/Reshape/shape"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_49/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_43/convolution", "1198"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_41/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_49/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_89/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_49/Add", "PartitionedCall/model/tf.math.sigmoid_41/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_44/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_89/Mul", "1200"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/model/tf.math.add_50/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_44/convolution", "1202"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_42/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_50/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_91/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_50/Add", "PartitionedCall/model/tf.math.sigmoid_42/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_11/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.strided_slice_12/StridedSlice", "PartitionedCall/model/tf.strided_slice_13/StridedSlice", "PartitionedCall/model/tf.math.multiply_91/Mul", "PartitionedCall/model/tf.concat_11/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "N": {"i": "3"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_45/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_11/concat", "1208"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.math.add_51/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_45/convolution", "1210"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_43/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_51/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_93/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_51/Add", "PartitionedCall/model/tf.math.sigmoid_43/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_47/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_93/Mul", "1248"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.nn.convolution_48/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_93/Mul", "1246"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.pad_6/Pad", "op": "Pad", "input": ["PartitionedCall/model/tf.math.multiply_93/Mul", "1212"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_53/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_47/convolution", "1260"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_54/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_48/convolution", "1258"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_46/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.compat.v1.pad_6/Pad", "1214"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_45/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_53/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_46/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_54/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_52/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_46/convolution", "1216"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_97/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_53/Add", "PartitionedCall/model/tf.math.sigmoid_45/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_99/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_54/Add", "PartitionedCall/model/tf.math.sigmoid_46/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_44/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_52/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_49/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_97/Mul", "1272"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/model/tf.nn.convolution_50/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_99/Mul", "1270"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.math.multiply_95/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_52/Add", "PartitionedCall/model/tf.math.sigmoid_44/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_55/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_49/convolution", "1284"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_56/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_50/convolution", "1282"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_12/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.math.multiply_95/Mul", "PartitionedCall/model/tf.math.multiply_59/Mul", "PartitionedCall/model/tf.concat_12/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_47/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_55/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_48/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_56/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_51/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_12/concat", "1218"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/model/tf.math.multiply_101/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_55/Add", "PartitionedCall/model/tf.math.sigmoid_47/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_103/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_56/Add", "PartitionedCall/model/tf.math.sigmoid_48/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_57/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_51/convolution", "1220"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_52/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_101/Mul", "1296"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.nn.convolution_53/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_103/Mul", "1294"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_49/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_57/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_58/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_52/convolution", "1306"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_59/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_53/convolution", "1308"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_105/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_57/Add", "PartitionedCall/model/tf.math.sigmoid_49/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_13/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.math.add_58/Add", "PartitionedCall/model/tf.math.add_59/Add", "PartitionedCall/model/tf.concat_13/concat/axis"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.strided_slice_14/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_105/Mul", "1234", "1236", "PartitionedCall/model/tf.strided_slice_14/ones_like"], "attr": {"new_axis_mask": {"i": "0"}, "begin_mask": {"i": "15"}, "T": {"type": "DT_FLOAT"}, "ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "7"}, "Index": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/model/tf.strided_slice_15/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.math.multiply_105/Mul", "1222", "1224", "PartitionedCall/model/tf.strided_slice_15/ones_like"], "attr": {"shrink_axis_mask": {"i": "0"}, "begin_mask": {"i": "7"}, "Index": {"type": "DT_INT64"}, "ellipsis_mask": {"i": "0"}, "end_mask": {"i": "7"}, "T": {"type": "DT_FLOAT"}, "new_axis_mask": {"i": "0"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_1/transpose", "op": "Transpose", "input": ["PartitionedCall/model/tf.concat_13/concat", "PartitionedCall/model/tf.compat.v1.transpose_1/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_54/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.strided_slice_15/StridedSlice", "1226"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.reshape_1/Reshape", "op": "Reshape", "input": ["PartitionedCall/model/tf.compat.v1.transpose_1/transpose", "PartitionedCall/model/tf.reshape_1/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.math.add_60/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_54/convolution", "1228"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_50/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_60/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_107/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_60/Add", "PartitionedCall/model/tf.math.sigmoid_50/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_55/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_107/Mul", "1230"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.math.add_61/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_55/convolution", "1232"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_51/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_61/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_109/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_61/Add", "PartitionedCall/model/tf.math.sigmoid_51/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_14/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.strided_slice_14/StridedSlice", "PartitionedCall/model/tf.strided_slice_15/StridedSlice", "PartitionedCall/model/tf.math.multiply_109/Mul", "PartitionedCall/model/tf.concat_14/concat/axis"], "attr": {"N": {"i": "3"}, "T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_56/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.concat_14/concat", "1238"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/model/tf.math.add_62/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_56/convolution", "1240"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_52/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_62/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_111/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_62/Add", "PartitionedCall/model/tf.math.sigmoid_52/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_57/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_111/Mul", "1244"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_58/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_111/Mul", "1242"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/model/tf.math.add_63/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_57/convolution", "1256"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_64/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_58/convolution", "1254"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_53/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_63/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_54/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_64/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_113/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_63/Add", "PartitionedCall/model/tf.math.sigmoid_53/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_115/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_64/Add", "PartitionedCall/model/tf.math.sigmoid_54/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_59/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_113/Mul", "1268"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.nn.convolution_60/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_115/Mul", "1266"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_65/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_59/convolution", "1280"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_66/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_60/convolution", "1278"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_55/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_65/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_56/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_66/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_117/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_65/Add", "PartitionedCall/model/tf.math.sigmoid_55/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.multiply_119/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_66/Add", "PartitionedCall/model/tf.math.sigmoid_56/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_61/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_117/Mul", "1292"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "PartitionedCall/model/tf.nn.convolution_62/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.math.multiply_119/Mul", "1290"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/model/tf.math.add_67/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_61/convolution", "1302"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_68/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.nn.convolution_62/convolution", "1304"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_15/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.math.add_67/Add", "PartitionedCall/model/tf.math.add_68/Add", "PartitionedCall/model/tf.concat_15/concat/axis"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_2/transpose", "op": "Transpose", "input": ["PartitionedCall/model/tf.concat_15/concat", "PartitionedCall/model/tf.compat.v1.transpose_2/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.reshape_2/Reshape", "op": "Reshape", "input": ["PartitionedCall/model/tf.compat.v1.transpose_2/transpose", "PartitionedCall/model/tf.reshape_2/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.concat_16/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.reshape/Reshape", "PartitionedCall/model/tf.reshape_1/Reshape", "PartitionedCall/model/tf.reshape_2/Reshape", "PartitionedCall/model/tf.concat_16/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "N": {"i": "3"}}}, {"name": "PartitionedCall/model/tf.strided_slice_16/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.concat_16/concat", "1314", "1316", "PartitionedCall/model/tf.strided_slice_16/StridedSlice/strides"], "attr": {"ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "5"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}, "T": {"type": "DT_FLOAT"}, "begin_mask": {"i": "7"}}}, {"name": "PartitionedCall/model/tf.strided_slice_17/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.concat_16/concat", "1334", "1336", "PartitionedCall/model/tf.strided_slice_17/StridedSlice/strides"], "attr": {"Index": {"type": "DT_INT64"}, "begin_mask": {"i": "5"}, "ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "end_mask": {"i": "5"}, "new_axis_mask": {"i": "0"}}}, {"name": "PartitionedCall/model/tf.reshape_3/Reshape", "op": "Reshape", "input": ["PartitionedCall/model/tf.strided_slice_16/StridedSlice", "PartitionedCall/model/tf.reshape_3/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.math.sigmoid_57/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/model/tf.strided_slice_17/StridedSlice"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_4/transpose", "op": "Transpose", "input": ["PartitionedCall/model/tf.reshape_3/Reshape", "PartitionedCall/model/tf.compat.v1.transpose_4/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_5/transpose", "op": "Transpose", "input": ["PartitionedCall/model/tf.compat.v1.transpose_4/transpose", "PartitionedCall/model/tf.compat.v1.transpose_5/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.nn.softmax/Softmax", "op": "Softmax", "input": ["PartitionedCall/model/tf.compat.v1.transpose_5/transpose"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.nn.convolution_63/convolution", "op": "Conv2D", "input": ["PartitionedCall/model/tf.nn.softmax/Softmax", "1318"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_8/transpose", "op": "Transpose", "input": ["PartitionedCall/model/tf.nn.convolution_63/convolution", "PartitionedCall/model/tf.compat.v1.transpose_8/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.reshape_4/Reshape", "op": "Reshape", "input": ["PartitionedCall/model/tf.compat.v1.transpose_8/transpose", "PartitionedCall/model/tf.reshape_4/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.strided_slice_19/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.reshape_4/Reshape", "1320", "1322", "PartitionedCall/model/tf.strided_slice_19/ones_like"], "attr": {"shrink_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "end_mask": {"i": "5"}, "T": {"type": "DT_FLOAT"}, "Index": {"type": "DT_INT64"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "5"}}}, {"name": "PartitionedCall/model/tf.strided_slice_18/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/model/tf.reshape_4/Reshape", "1324", "1326", "PartitionedCall/model/tf.strided_slice_18/ones_like"], "attr": {"end_mask": {"i": "5"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "7"}, "Index": {"type": "DT_INT64"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_69/Add", "op": "AddV2", "input": ["1328", "PartitionedCall/model/tf.strided_slice_19/StridedSlice"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.subtract/Sub", "op": "Sub", "input": ["1330", "PartitionedCall/model/tf.strided_slice_18/StridedSlice"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.add_70/Add", "op": "AddV2", "input": ["PartitionedCall/model/tf.math.subtract/Sub", "PartitionedCall/model/tf.math.add_69/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.subtract_1/Sub", "op": "Sub", "input": ["PartitionedCall/model/tf.math.add_69/Add", "PartitionedCall/model/tf.math.subtract/Sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.math.divide/truediv", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.math.add_70/Add", "ConstantFolding/PartitionedCall/model/tf.math.divide/truediv_recip"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/tf.concat_17/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.math.divide/truediv", "PartitionedCall/model/tf.math.subtract_1/Sub", "PartitionedCall/model/tf.concat_17/concat/axis"], "attr": {"T": {"type": "DT_FLOAT"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/model/tf.math.multiply_128/Mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/model/tf.concat_17/concat", "PartitionedCall/model/tf.math.multiply_128/Mul/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/model/output0/concat", "op": "ConcatV2", "input": ["PartitionedCall/model/tf.math.multiply_128/Mul", "PartitionedCall/model/tf.math.sigmoid_57/Sigmoid", "PartitionedCall/model/output0/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "N": {"i": "2"}}}, {"name": "Identity", "op": "Identity", "input": ["PartitionedCall/model/output0/concat"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 1286}}, "weightsManifest": [{"paths": ["group1-shard1of4.bin", "group1-shard2of4.bin", "group1-shard3of4.bin", "group1-shard4of4.bin"], "weights": [{"name": "ConstantFolding/PartitionedCall/model/tf.math.divide/truediv_recip", "shape": [1, 1, 1], "dtype": "float32"}, {"name": "1328", "shape": [1, 2, 8400], "dtype": "float32"}, {"name": "1320", "shape": [3], "dtype": "int32"}, {"name": "1322", "shape": [3], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_19/ones_like", "shape": [3], "dtype": "int32"}, {"name": "1330", "shape": [1, 2, 8400], "dtype": "float32"}, {"name": "1314", "shape": [3], "dtype": "int32"}, {"name": "1316", "shape": [3], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_16/StridedSlice/strides", "shape": [3], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.reshape_3/Reshape/shape", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_4/transpose/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_5/transpose/perm", "shape": [4], "dtype": "int32"}, {"name": "1318", "shape": [1, 1, 16, 1], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_8/transpose/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.reshape_4/Reshape/shape", "shape": [3], "dtype": "int32"}, {"name": "1324", "shape": [3], "dtype": "int32"}, {"name": "1326", "shape": [3], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_18/ones_like", "shape": [3], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.concat_17/concat/axis", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.math.multiply_128/Mul/y", "shape": [1, 1, 8400], "dtype": "float32"}, {"name": "1252", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1264", "shape": [64], "dtype": "float32"}, {"name": "1276", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1288", "shape": [64], "dtype": "float32"}, {"name": "1300", "shape": [1, 1, 64, 64], "dtype": "float32"}, {"name": "1310", "shape": [64], "dtype": "float32"}, {"name": "1250", "shape": [3, 3, 64, 80], "dtype": "float32"}, {"name": "1262", "shape": [80], "dtype": "float32"}, {"name": "1274", "shape": [3, 3, 80, 80], "dtype": "float32"}, {"name": "1286", "shape": [80], "dtype": "float32"}, {"name": "1298", "shape": [1, 1, 80, 80], "dtype": "float32"}, {"name": "1312", "shape": [80], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_10/concat/axis", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.compat.v1.transpose/transpose/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.reshape/Reshape/shape", "shape": [3], "dtype": "int32"}, {"name": "1248", "shape": [3, 3, 128, 64], "dtype": "float32"}, {"name": "1260", "shape": [64], "dtype": "float32"}, {"name": "1272", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1284", "shape": [64], "dtype": "float32"}, {"name": "1296", "shape": [1, 1, 64, 64], "dtype": "float32"}, {"name": "1306", "shape": [64], "dtype": "float32"}, {"name": "1246", "shape": [3, 3, 128, 80], "dtype": "float32"}, {"name": "1258", "shape": [80], "dtype": "float32"}, {"name": "1270", "shape": [3, 3, 80, 80], "dtype": "float32"}, {"name": "1282", "shape": [80], "dtype": "float32"}, {"name": "1294", "shape": [1, 1, 80, 80], "dtype": "float32"}, {"name": "1308", "shape": [80], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_13/concat/axis", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_1/transpose/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.reshape_1/Reshape/shape", "shape": [3], "dtype": "int32"}, {"name": "1244", "shape": [3, 3, 256, 64], "dtype": "float32"}, {"name": "1256", "shape": [64], "dtype": "float32"}, {"name": "1268", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1280", "shape": [64], "dtype": "float32"}, {"name": "1292", "shape": [1, 1, 64, 64], "dtype": "float32"}, {"name": "1302", "shape": [64], "dtype": "float32"}, {"name": "1234", "shape": [4], "dtype": "int32"}, {"name": "1236", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_14/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1204", "shape": [4], "dtype": "int32"}, {"name": "1206", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_12/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1174", "shape": [4], "dtype": "int32"}, {"name": "1176", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_10/ones_like", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/lambda_1//model.13/Resize/size", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.concat_7/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1158", "shape": [1, 1, 192, 64], "dtype": "float32"}, {"name": "1160", "shape": [64], "dtype": "float32"}, {"name": "1162", "shape": [4], "dtype": "int32"}, {"name": "1164", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_11/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1166", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "1168", "shape": [32], "dtype": "float32"}, {"name": "1170", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "1172", "shape": [32], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_8/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1178", "shape": [1, 1, 96, 64], "dtype": "float32"}, {"name": "1180", "shape": [64], "dtype": "float32"}, {"name": "1182", "shape": [4, 2], "dtype": "int32"}, {"name": "1184", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1186", "shape": [64], "dtype": "float32"}, {"name": "1150", "shape": [4], "dtype": "int32"}, {"name": "1152", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_8/ones_like", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/lambda//model.10/Resize/size", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.concat_5/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1134", "shape": [1, 1, 384, 128], "dtype": "float32"}, {"name": "1136", "shape": [128], "dtype": "float32"}, {"name": "1138", "shape": [4], "dtype": "int32"}, {"name": "1140", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_9/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1142", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1144", "shape": [64], "dtype": "float32"}, {"name": "1146", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1148", "shape": [64], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_6/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1154", "shape": [1, 1, 192, 128], "dtype": "float32"}, {"name": "1156", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_9/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1188", "shape": [1, 1, 192, 128], "dtype": "float32"}, {"name": "1190", "shape": [128], "dtype": "float32"}, {"name": "1192", "shape": [4], "dtype": "int32"}, {"name": "1194", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_13/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1196", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1198", "shape": [64], "dtype": "float32"}, {"name": "1200", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1202", "shape": [64], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_11/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1208", "shape": [1, 1, 192, 128], "dtype": "float32"}, {"name": "1210", "shape": [128], "dtype": "float32"}, {"name": "1212", "shape": [4, 2], "dtype": "int32"}, {"name": "1214", "shape": [3, 3, 128, 128], "dtype": "float32"}, {"name": "1216", "shape": [128], "dtype": "float32"}, {"name": "1118", "shape": [4], "dtype": "int32"}, {"name": "1120", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_6/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1088", "shape": [4], "dtype": "int32"}, {"name": "1090", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_4/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1050", "shape": [4], "dtype": "int32"}, {"name": "1052", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_2/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1012", "shape": [4], "dtype": "int32"}, {"name": "1014", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice/ones_like", "shape": [4], "dtype": "int32"}, {"name": "984", "shape": [4, 2], "dtype": "int32"}, {"name": "986", "shape": [3, 3, 3, 16], "dtype": "float32"}, {"name": "988", "shape": [16], "dtype": "float32"}, {"name": "990", "shape": [4, 2], "dtype": "int32"}, {"name": "992", "shape": [3, 3, 16, 32], "dtype": "float32"}, {"name": "994", "shape": [32], "dtype": "float32"}, {"name": "996", "shape": [1, 1, 32, 32], "dtype": "float32"}, {"name": "998", "shape": [32], "dtype": "float32"}, {"name": "1000", "shape": [4], "dtype": "int32"}, {"name": "1002", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_1/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1004", "shape": [3, 3, 16, 16], "dtype": "float32"}, {"name": "1006", "shape": [16], "dtype": "float32"}, {"name": "1008", "shape": [3, 3, 16, 16], "dtype": "float32"}, {"name": "1010", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1016", "shape": [1, 1, 48, 32], "dtype": "float32"}, {"name": "1018", "shape": [32], "dtype": "float32"}, {"name": "1020", "shape": [4, 2], "dtype": "int32"}, {"name": "1022", "shape": [3, 3, 32, 64], "dtype": "float32"}, {"name": "1024", "shape": [64], "dtype": "float32"}, {"name": "1026", "shape": [1, 1, 64, 64], "dtype": "float32"}, {"name": "1028", "shape": [64], "dtype": "float32"}, {"name": "1030", "shape": [4], "dtype": "int32"}, {"name": "1032", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_3/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1034", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "1036", "shape": [32], "dtype": "float32"}, {"name": "1038", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "1040", "shape": [32], "dtype": "float32"}, {"name": "1042", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "1044", "shape": [32], "dtype": "float32"}, {"name": "1046", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "1048", "shape": [32], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_1/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1054", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "1056", "shape": [64], "dtype": "float32"}, {"name": "1058", "shape": [4, 2], "dtype": "int32"}, {"name": "1060", "shape": [3, 3, 64, 128], "dtype": "float32"}, {"name": "1062", "shape": [128], "dtype": "float32"}, {"name": "1064", "shape": [1, 1, 128, 128], "dtype": "float32"}, {"name": "1066", "shape": [128], "dtype": "float32"}, {"name": "1068", "shape": [4], "dtype": "int32"}, {"name": "1070", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_5/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1072", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1074", "shape": [64], "dtype": "float32"}, {"name": "1076", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1078", "shape": [64], "dtype": "float32"}, {"name": "1080", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1082", "shape": [64], "dtype": "float32"}, {"name": "1084", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "1086", "shape": [64], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_2/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1092", "shape": [1, 1, 256, 128], "dtype": "float32"}, {"name": "1094", "shape": [128], "dtype": "float32"}, {"name": "1096", "shape": [4, 2], "dtype": "int32"}, {"name": "1098", "shape": [3, 3, 128, 256], "dtype": "float32"}, {"name": "1100", "shape": [256], "dtype": "float32"}, {"name": "1102", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "1104", "shape": [256], "dtype": "float32"}, {"name": "1106", "shape": [4], "dtype": "int32"}, {"name": "1108", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_7/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1110", "shape": [3, 3, 128, 128], "dtype": "float32"}, {"name": "1112", "shape": [128], "dtype": "float32"}, {"name": "1114", "shape": [3, 3, 128, 128], "dtype": "float32"}, {"name": "1116", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_3/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1122", "shape": [1, 1, 384, 256], "dtype": "float32"}, {"name": "1124", "shape": [256], "dtype": "float32"}, {"name": "1126", "shape": [1, 1, 256, 128], "dtype": "float32"}, {"name": "1128", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_4/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1130", "shape": [1, 1, 512, 256], "dtype": "float32"}, {"name": "1132", "shape": [256], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_12/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1218", "shape": [1, 1, 384, 256], "dtype": "float32"}, {"name": "1220", "shape": [256], "dtype": "float32"}, {"name": "1222", "shape": [4], "dtype": "int32"}, {"name": "1224", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_15/ones_like", "shape": [4], "dtype": "int32"}, {"name": "1226", "shape": [3, 3, 128, 128], "dtype": "float32"}, {"name": "1228", "shape": [128], "dtype": "float32"}, {"name": "1230", "shape": [3, 3, 128, 128], "dtype": "float32"}, {"name": "1232", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_14/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1238", "shape": [1, 1, 384, 256], "dtype": "float32"}, {"name": "1240", "shape": [256], "dtype": "float32"}, {"name": "1242", "shape": [3, 3, 256, 80], "dtype": "float32"}, {"name": "1254", "shape": [80], "dtype": "float32"}, {"name": "1266", "shape": [3, 3, 80, 80], "dtype": "float32"}, {"name": "1278", "shape": [80], "dtype": "float32"}, {"name": "1290", "shape": [1, 1, 80, 80], "dtype": "float32"}, {"name": "1304", "shape": [80], "dtype": "float32"}, {"name": "PartitionedCall/model/tf.concat_15/concat/axis", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.compat.v1.transpose_2/transpose/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.reshape_2/Reshape/shape", "shape": [3], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.concat_16/concat/axis", "shape": [], "dtype": "int32"}, {"name": "1334", "shape": [3], "dtype": "int32"}, {"name": "1336", "shape": [3], "dtype": "int32"}, {"name": "PartitionedCall/model/tf.strided_slice_17/StridedSlice/strides", "shape": [3], "dtype": "int32"}, {"name": "PartitionedCall/model/output0/concat/axis", "shape": [], "dtype": "int32"}]}]}
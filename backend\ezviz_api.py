import base64
import io
import json
import logging
import os
import signal
import sys
import threading
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Union
from concurrent.futures import ThreadPoolExecutor

import cv2
import numpy as np
import redis
from fastapi import FastAPI, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from PIL import Image
from pydantic import BaseModel

# 尝试导入ultralytics，如果失败则提供安装指南
try:
    from ultralytics import YOLO
except ImportError:
    print("请安装ultralytics: pip install ultralytics")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("ezviz-api")

# 创建FastAPI应用
app = FastAPI(title="萤石云视频处理API")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class FrameData(BaseModel):
    frame: str  # Base64编码的图像数据
    client_id: str  # 客户端ID
    timestamp: Optional[int] = None  # 时间戳

class DetectionResult(BaseModel):
    objects: List[Dict]  # 检测到的对象列表
    timestamp: int  # 时间戳

# 全局变量
redis_client = None
yolo_model = None
latest_results: Dict[str, DetectionResult] = {}  # 按客户端ID存储最新结果
processing_lock = threading.Lock()  # 处理锁，防止并发处理同一客户端的帧
clients: Dict[str, List] = {}  # MJPEG流客户端
clients_lock = threading.Lock()  # 客户端锁

# 视频保存相关变量
video_writers: Dict[str, cv2.VideoWriter] = {}  # 按客户端ID存储视频写入器
video_save_path = "./saved_videos"  # 视频保存路径

# 帧缓冲相关变量
buffer_duration = 2.0  # 缓冲时长（秒）
fps = 10  # 帧率
buffer_size = int(buffer_duration * fps)  # 缓冲区大小（帧数）
frame_buffer: Dict[str, List] = {}  # 按客户端ID存储原始帧缓冲区
processed_buffers: Dict[str, List] = {}  # 按客户端ID存储处理后的帧缓冲区
buffer_locks: Dict[str, threading.Lock] = {}  # 按客户端ID存储缓冲区锁
last_frame_time: Dict[str, float] = {}  # 按客户端ID存储最后一帧的时间
max_buffer_size = 30  # 最大视频保存缓冲区大小（帧数）

# Qwen API相关变量
QWEN_API_URL = os.environ.get("QWEN_API_URL", "http://localhost:5000/api/qwen-analysis")
USE_QWEN_API = os.environ.get("USE_QWEN_API", "True").lower() == "true"
qwen_results: Dict[str, Dict] = {}  # 按客户端ID存储Qwen分析结果
qwen_locks: Dict[str, threading.Lock] = {}  # 按客户端ID存储Qwen结果锁
thread_pool = ThreadPoolExecutor(max_workers=2)  # 线程池用于并行处理

# 添加全局变量以跟踪客户端流状态
active_streams = {}  # 跟踪活跃的流

# 确保视频保存目录存在
os.makedirs(video_save_path, exist_ok=True)

# 风险级别评估规则
RISK_RULES = {
    "person": "medium",  # 人员默认中风险
    "car": "medium",     # 车辆默认中风险
    "truck": "high",     # 卡车高风险
    "bus": "high",       # 巴士高风险
    "motorcycle": "high", # 摩托车高风险
    "bicycle": "medium", # 自行车中风险
    # 其他类别默认低风险
}

# 调用Qwen API进行图像分析
def call_qwen_api(image, client_id: str):
    global qwen_results, qwen_locks
    
    try:
        # 初始化客户端的Qwen结果锁（如果不存在）
        if client_id not in qwen_locks:
            qwen_locks[client_id] = threading.Lock()
        
        # 将OpenCV图像转换为Base64编码
        _, buffer = cv2.imencode('.jpg', image)
        image_base64 = base64.b64encode(buffer).decode('utf-8')
        
        # 准备请求数据
        payload = {
            "image": image_base64,
            "prompt": "分析这个交通场景中的安全风险和异常情况。识别所有车辆、行人和其他交通参与者，评估他们的行为是否存在安全隐患。"
        }
        
        # 发送请求到Qwen API
        response = requests.post(QWEN_API_URL, json=payload, timeout=5)
        response.raise_for_status()
        
        # 解析响应
        result = response.json()
        
        # 更新Qwen分析结果
        with qwen_locks[client_id]:
            qwen_results[client_id] = {
                "result": result,
                "timestamp": int(time.time() * 1000)
            }
        
        logger.info(f"Qwen API分析完成: {client_id}")
        return result
    except Exception as e:
        logger.error(f"调用Qwen API失败: {str(e)}")
        return None

# 初始化Redis客户端
def init_redis():
    global redis_client
    try:
        redis_host = os.environ.get("REDIS_HOST", "localhost")
        redis_port = int(os.environ.get("REDIS_PORT", 6379))
        redis_password = os.environ.get("REDIS_PASSWORD", None)
        
        redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            password=redis_password,
            decode_responses=False  # 不解码二进制数据
        )
        redis_client.ping()  # 测试连接
        logger.info(f"Redis连接成功: {redis_host}:{redis_port}")
        return True
    except Exception as e:
        logger.error(f"Redis连接失败: {str(e)}")
        return False

# 初始化YOLO模型
def init_yolo_model():
    global yolo_model
    try:
        # 尝试加载模型，首先尝试本地路径，然后尝试下载
        model_paths = [
            "./models/yolov8n.pt",  # 本地轻量级模型
            "./yolov8n.pt",         # 当前目录
            "yolov8n"               # 从ultralytics下载
        ]
        
        for path in model_paths:
            try:
                logger.info(f"尝试加载YOLO模型: {path}")
                yolo_model = YOLO(path)
                logger.info(f"YOLO模型加载成功: {path}")
                return True
            except Exception as e:
                logger.warning(f"无法加载模型 {path}: {str(e)}")
                continue
        
        logger.error("所有模型路径都加载失败")
        return False
    except Exception as e:
        logger.error(f"YOLO模型初始化失败: {str(e)}")
        return False

# 处理Base64编码的图像
def process_base64_image(base64_data: str, client_id: str):
    global frame_buffer, buffer_locks, last_frame_time
    
    try:
        # 解码Base64数据
        image_data = base64.b64decode(base64_data)
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为OpenCV格式
        opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # 保存原始帧到视频文件
        save_frame_to_video(opencv_image, client_id)
        
        # 初始化客户端的缓冲区和锁（如果不存在）
        if client_id not in frame_buffer:
            frame_buffer[client_id] = []
            processed_buffers[client_id] = []
            buffer_locks[client_id] = threading.Lock()
            last_frame_time[client_id] = time.time()
            active_streams[client_id] = True
        
        # 获取当前时间
        current_time = time.time()
        
        # 添加帧到缓冲区
        with buffer_locks[client_id]:
            frame_buffer[client_id].append({
                'frame': opencv_image,
                'timestamp': current_time
            })
            
            # 如果缓冲区达到指定大小（约2秒的视频），处理缓冲区中的帧
            if len(frame_buffer[client_id]) >= buffer_size:
                logger.info(f"缓冲区已满 ({buffer_size} 帧)，开始处理")
                # 在新线程中处理帧缓冲区，避免阻塞主线程
                threading.Thread(
                    target=process_frame_buffer,
                    args=(client_id,),
                    daemon=True
                ).start()
            
            # 更新最后一帧的时间
            last_frame_time[client_id] = current_time
        
        return True
    except Exception as e:
        logger.error(f"处理图像失败: {str(e)}")
        return False

# 处理帧缓冲区
def process_frame_buffer(client_id: str):
    global frame_buffer, processed_buffers, buffer_locks, latest_results, qwen_results
    
    try:
        # 获取缓冲区中的帧
        frames_to_process = []
        with buffer_locks[client_id]:
            if len(frame_buffer[client_id]) >= buffer_size:
                frames_to_process = frame_buffer[client_id][:]
                frame_buffer[client_id] = []
        
        if not frames_to_process:
            return
        
        # 处理每一帧
        processed_frames = []
        detection_objects = []
        
        # 创建一个函数用于并行处理Qwen API
        def process_with_qwen(frame):
            try:
                return call_qwen_api(frame.copy(), client_id)
            except Exception as e:
                logger.error(f"Qwen API处理失败: {str(e)}")
                return None
        
        # 如果启用了Qwen API，为中间帧启动并行处理
        qwen_future = None
        if USE_QWEN_API and len(frames_to_process) > 0:
            # 选择中间帧进行Qwen分析，以获得更具代表性的结果
            middle_frame_index = len(frames_to_process) // 2
            middle_frame = frames_to_process[middle_frame_index]['frame']
            qwen_future = thread_pool.submit(process_with_qwen, middle_frame)
        
        # 使用YOLO处理所有帧
        for frame_data in frames_to_process:
            opencv_image = frame_data['frame']
            timestamp = frame_data['timestamp']
            
            # 使用YOLO模型进行检测
            results = yolo_model(opencv_image, verbose=False)
            
            # 处理检测结果
            frame_objects = []
            for result in results:
                boxes = result.boxes
                for i, box in enumerate(boxes):
                    # 获取类别、置信度和边界框
                    cls_id = int(box.cls.item())
                    cls_name = result.names[cls_id]
                    confidence = box.conf.item()
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    
                    # 评估风险级别
                    risk_level = RISK_RULES.get(cls_name, "low")
                    
                    # 添加到检测对象列表
                    obj = {
                        "category": cls_name,
                        "confidence": confidence,
                        "bbox": [x1, y1, x2, y2],
                        "risk_level": risk_level
                    }
                    frame_objects.append(obj)
                    detection_objects.append(obj)
            
            # 绘制检测结果
            annotated_frame = results[0].plot()
            
            # 如果有Qwen结果，在图像上添加分析结果
            if client_id in qwen_results and qwen_results[client_id].get('result'):
                qwen_data = qwen_results[client_id]['result']
                if isinstance(qwen_data, dict) and 'analysis' in qwen_data:
                    analysis_text = qwen_data['analysis']
                    # 截断过长的文本
                    if len(analysis_text) > 100:
                        analysis_text = analysis_text[:97] + "..."
                    
                    # 添加半透明背景
                    overlay = annotated_frame.copy()
                    cv2.rectangle(overlay, (0, annotated_frame.shape[0]-60), 
                                 (annotated_frame.shape[1], annotated_frame.shape[0]), (0, 0, 0), -1)
                    cv2.addWeighted(overlay, 0.6, annotated_frame, 0.4, 0, annotated_frame)
                    
                    # 添加文本
                    cv2.putText(annotated_frame, "AI分析: " + analysis_text, (10, annotated_frame.shape[0]-20), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 转换回PIL图像并保存为JPEG
            annotated_image = Image.fromarray(cv2.cvtColor(annotated_frame, cv2.COLOR_BGR2RGB))
            img_byte_arr = io.BytesIO()
            annotated_image.save(img_byte_arr, format='JPEG', quality=80)
            img_byte_arr.seek(0)
            
            # 添加到处理后的帧列表
            processed_frames.append({
                'frame_data': img_byte_arr.getvalue(),
                'timestamp': timestamp,
                'objects': frame_objects
            })
        
        # 等待并获取Qwen API的结果（如果有）
        if qwen_future:
            try:
                # 设置超时时间，避免长时间等待
                qwen_future.result(timeout=3.0)
            except Exception as e:
                logger.warning(f"等待Qwen API结果超时: {str(e)}")
        
        # 更新最新结果
        if detection_objects:
            timestamp_ms = int(time.time() * 1000)
            latest_results[client_id] = DetectionResult(
                objects=detection_objects,
                timestamp=timestamp_ms
            )
        
        # 更新处理后的帧缓冲区
        with buffer_locks[client_id]:
            processed_buffers[client_id].extend(processed_frames)
            
            # 如果处理后的缓冲区太大，移除最旧的帧
            max_processed_buffer = buffer_size * 2  # 保留更多处理后的帧以确保流畅播放
            if len(processed_buffers[client_id]) > max_processed_buffer:
                processed_buffers[client_id] = processed_buffers[client_id][-max_processed_buffer:]
    
    except Exception as e:
        logger.error(f"处理帧缓冲区失败: {str(e)}")
        # 出错时清空缓冲区，避免积累错误帧
        with buffer_locks[client_id]:
            frame_buffer[client_id] = []

# 保存帧到视频文件
def save_frame_to_video(frame, client_id: str):
    global video_writers, frame_buffer
    
    # 初始化帧缓冲区
    if client_id not in frame_buffer:
        frame_buffer[client_id] = []
    
    # 添加帧到缓冲区
    frame_buffer[client_id].append(frame)
    
    # 如果缓冲区达到最大大小，开始写入视频
    if len(frame_buffer[client_id]) >= max_buffer_size:
        # 获取第一帧的尺寸
        height, width = frame_buffer[client_id][0].shape[:2]
        
        # 如果没有视频写入器，创建一个
        if client_id not in video_writers or video_writers[client_id] is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            video_filename = f"{video_save_path}/{client_id}_{timestamp}.mp4"
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # MP4编码
            fps = 10  # 帧率
            video_writers[client_id] = cv2.VideoWriter(video_filename, fourcc, fps, (width, height))
            logger.info(f"开始保存视频: {video_filename}")
        
        # 写入所有帧
        for buffered_frame in frame_buffer[client_id]:
            video_writers[client_id].write(buffered_frame)
        
        # 清空缓冲区
        frame_buffer[client_id] = []
        
        # 每隔一段时间创建新的视频文件（例如每10分钟）
        # 这里可以添加定时器逻辑

# MJPEG流生成器
def generate_mjpeg(client_id: str):
    """MJPEG流生成器，从处理后的帧缓冲区获取帧并发送给前端"""
    
    # 初始化客户端的缓冲区和锁（如果不存在）
    if client_id not in processed_buffers:
        with buffer_locks.get(client_id, threading.Lock()):
            processed_buffers[client_id] = []
            buffer_locks[client_id] = threading.Lock()
    
    # 记录上次发送的帧索引
    last_sent_index = 0
    frame_rate = 10  # 目标帧率
    frame_interval = 1.0 / frame_rate  # 帧间隔（秒）
    last_frame_time = time.time()
    
    try:
        while True:
            current_time = time.time()
            elapsed = current_time - last_frame_time
            
            # 控制帧率
            if elapsed < frame_interval:
                time.sleep(frame_interval - elapsed)
            
            frame_data = None
            
            # 从处理后的帧缓冲区获取帧
            with buffer_locks.get(client_id, threading.Lock()):
                buffer_length = len(processed_buffers.get(client_id, []))
                
                # 如果有处理后的帧
                if buffer_length > 0:
                    # 获取下一帧
                    if last_sent_index < buffer_length:
                        frame_data = processed_buffers[client_id][last_sent_index]['frame_data']
                        last_sent_index += 1
                    
                    # 如果已经发送了所有帧，但缓冲区中有新帧，重置索引
                    elif last_sent_index >= buffer_length:
                        # 保留最新的一半帧，避免缓冲区过大
                        buffer_half = buffer_length // 2
                        if buffer_half > 0:
                            processed_buffers[client_id] = processed_buffers[client_id][buffer_half:]
                            last_sent_index = 0
                            if processed_buffers[client_id]:
                                frame_data = processed_buffers[client_id][last_sent_index]['frame_data']
                                last_sent_index += 1
            
            if frame_data:  # 有帧可发送
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_data + b'\r\n')
            else:  # 没有帧可发送，发送占位帧
                # 创建一个简单的占位帧
                placeholder = np.ones((480, 640, 3), dtype=np.uint8) * 50  # 深灰色背景
                # 添加文本
                cv2.putText(placeholder, "等待视频帧...", (180, 240), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                
                # 编码为JPEG
                _, buffer = cv2.imencode('.jpg', placeholder)
                frame_data = buffer.tobytes()
                
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_data + b'\r\n')
            
            # 更新最后发送帧的时间
            last_frame_time = time.time()
    except Exception as e:
        logger.error(f"MJPEG流生成错误: {str(e)}")
        # 在发生错误时返回错误帧
        error_frame = np.ones((480, 640, 3), dtype=np.uint8) * 20  # 暗红色背景
        cv2.putText(error_frame, "流处理错误", (180, 240), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        _, buffer = cv2.imencode('.jpg', error_frame)
        frame_data = buffer.tobytes()
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame_data + b'\r\n')

# API路由
@app.get("/")
async def root():
    return {"message": "萤石云视频处理API", "status": "running"}

@app.post("/api/ezviz-frame")
async def receive_frame(frame_data: FrameData):
    if not yolo_model:
        raise HTTPException(status_code=500, detail="YOLO模型未初始化")
    
    client_id = frame_data.client_id
    
    # 使用锁防止并发处理同一客户端的帧
    if processing_lock.acquire(blocking=False):
        try:
            # 处理帧
            success = process_base64_image(frame_data.frame, client_id)
            if not success:
                raise HTTPException(status_code=500, detail="处理图像失败")
            
            return {"status": "success", "message": "帧已处理"}
        finally:
            processing_lock.release()
    else:
        # 如果已经在处理，返回忙碌状态
        return {"status": "busy", "message": "服务器正忙，请稍后重试"}

# 定义开始获取视频帧的请求模型
class StartStreamingRequest(BaseModel):
    client_id: str
    
@app.post("/api/start-streaming")
async def start_streaming(request: StartStreamingRequest):
    """开始获取萤石云视频帧"""
    client_id = request.client_id
    
    # 检查此客户端是否已经有活跃的流
    if client_id in active_streams and active_streams[client_id]:
        logger.info(f"客户端 {client_id} 已有活跃流，不重新初始化")
        return {"status": "success", "message": f"客户端 {client_id} 流已经在运行中", "already_active": True}
    
    # 初始化客户端的缓冲区和锁
    if client_id not in buffer_locks:
        buffer_locks[client_id] = threading.Lock()
    
    with buffer_locks[client_id]:
        # 清空现有缓冲区
        if client_id in frame_buffer:
            frame_buffer[client_id] = []
        else:
            frame_buffer[client_id] = []
            
        if client_id in processed_buffers:
            processed_buffers[client_id] = []
        else:
            processed_buffers[client_id] = []
        
        # 记录开始时间
        last_frame_time[client_id] = time.time()
        
        # 标记此客户端流为活跃
        active_streams[client_id] = True
    
    logger.info(f"开始获取萤石云视频帧: {client_id}")
    
    return {"status": "success", "message": f"Started streaming for client {client_id}", "already_active": False}

@app.get("/api/ezviz-results")
async def get_results(client_id: str):
    if client_id in latest_results:
        return latest_results[client_id]
    else:
        return {"objects": [], "timestamp": int(time.time() * 1000)}

@app.get("/api/qwen-results")
async def get_qwen_results(client_id: str = None):
    """获取Qwen API分析结果"""
    if not USE_QWEN_API:
        return {"error": "Qwen API is disabled"}
    
    if client_id and client_id not in qwen_results:
        raise HTTPException(status_code=404, detail=f"Client ID {client_id} not found or no Qwen results available")
    
    if client_id:
        return qwen_results.get(client_id, {})
    else:
        return qwen_results

@app.get("/stream")
async def video_stream(request: Request, client_id: str = "default"):
    """提供MJPEG视频流"""
    logger.info(f"客户端 {client_id} 请求视频流")
    
    # 确保客户端ID有效
    if not client_id:
        client_id = "default"
    
    # 如果客户端没有活跃的流，返回错误
    if client_id not in active_streams or not active_streams[client_id]:
        logger.warning(f"客户端 {client_id} 没有活跃的视频流")
        # 创建一个错误帧
        error_frame = np.ones((480, 640, 3), dtype=np.uint8) * 20  # 暗红色背景
        cv2.putText(error_frame, "无活跃视频流", (180, 240), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        _, buffer = cv2.imencode('.jpg', error_frame)
        frame_data = buffer.tobytes()
        
        # 返回单帧错误图像
        return Response(
            content=frame_data,
            media_type="image/jpeg"
        )
    
    # 返回MJPEG流
    return StreamingResponse(
        generate_mjpeg(client_id),
        media_type="multipart/x-mixed-replace; boundary=frame"
    )

@app.get("/status")
async def get_status():
    # 获取缓冲区状态
    buffer_status = {}
    for client_id in frame_buffer.keys():
        if client_id in buffer_locks:
            with buffer_locks[client_id]:
                # 获取Qwen API最新结果的时间戳
                qwen_timestamp = 0
                if client_id in qwen_results:
                    qwen_timestamp = qwen_results[client_id].get("timestamp", 0)
                
                buffer_status[client_id] = {
                    "raw_buffer_size": len(frame_buffer.get(client_id, [])),
                    "processed_buffer_size": len(processed_buffers.get(client_id, [])),
                    "last_frame_time": last_frame_time.get(client_id, 0),
                    "last_qwen_analysis_time": qwen_timestamp
                }
    
    return {
        "status": "running",
        "redis_connected": redis_client is not None,
        "yolo_model_loaded": yolo_model is not None,
        "qwen_api_enabled": USE_QWEN_API,
        "qwen_api_url": QWEN_API_URL,
        "buffer_settings": {
            "duration": buffer_duration,
            "fps": fps,
            "buffer_size": buffer_size
        },
        "buffer_status": buffer_status,
        "timestamp": datetime.now().isoformat()
    }

# 应用程序启动事件
@app.on_event("startup")
async def startup_event():
    global redis_client, yolo_model, buffer_duration, fps, buffer_size
    
    # 从环境变量读取缓冲设置
    try:
        buffer_duration = float(os.environ.get("BUFFER_DURATION", buffer_duration))
        fps = int(os.environ.get("FPS", fps))
        buffer_size = int(buffer_duration * fps)
        logger.info(f"视频缓冲设置: 缓冲时长={buffer_duration}秒, 帧率={fps}fps, 缓冲区大小={buffer_size}帧")
    except Exception as e:
        logger.warning(f"读取缓冲设置失败: {str(e)}，将使用默认值")
    
    # 初始化Redis客户端
    try:
        redis_host = os.environ.get("REDIS_HOST", "localhost")
        redis_port = int(os.environ.get("REDIS_PORT", 6379))
        redis_password = os.environ.get("REDIS_PASSWORD", None)
        
        redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            password=redis_password,
            decode_responses=True
        )
        redis_client.ping()  # 测试连接
        logger.info(f"Redis连接成功: {redis_host}:{redis_port}")
    except Exception as e:
        logger.warning(f"Redis连接失败: {str(e)}，将使用本地处理模式")
        redis_client = None
    
    # 初始化YOLO模型
    try:
        # 尝试加载模型，首先尝试本地路径，然后尝试下载
        model_paths = ["./models/yolov8n.pt", "yolov8n"]
        for model_path in model_paths:
            try:
                yolo_model = YOLO(model_path)
                logger.info(f"YOLO模型加载成功: {model_path}")
                break
            except Exception as e:
                logger.warning(f"无法加载YOLO模型 {model_path}: {str(e)}")
        
        if yolo_model is None:
            logger.error("所有YOLO模型加载尝试均失败")
    except Exception as e:
        logger.error(f"初始化YOLO模型失败: {str(e)}")

# 应用程序关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行的操作"""
    global video_writers, active_streams
    
    logger.info("应用正在关闭，清理资源...")
    
    # 关闭所有视频写入器
    for client_id, writer in video_writers.items():
        if writer:
            try:
                writer.release()
                logger.info(f"已关闭客户端 {client_id} 的视频写入器")
            except Exception as e:
                logger.error(f"关闭视频写入器失败: {str(e)}")
    
    # 清理活跃流
    for client_id in active_streams:
        active_streams[client_id] = False
        logger.info(f"已停止客户端 {client_id} 的流")
    
    # 关闭线程池
    try:
        thread_pool.shutdown(wait=False)
        logger.info("已关闭线程池")
    except Exception as e:
        logger.error(f"关闭线程池失败: {str(e)}")
    
    logger.info("资源清理完成")

# 主函数
def main():
    import uvicorn
    
    # 设置信号处理
    def signal_handler(sig, frame):
        logger.info("接收到终止信号，正在关闭...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动服务器
    port = int(os.environ.get("API_PORT", 8000))
    logger.info(f"启动API服务器在端口 {port}")
    uvicorn.run("ezviz_api:app", host="0.0.0.0", port=port, reload=False)

@app.get("/api/saved-videos")
async def get_saved_videos():
    """获取已保存的视频文件列表"""
    try:
        videos = []
        if os.path.exists(video_save_path):
            for file in os.listdir(video_save_path):
                if file.endswith(".mp4"):
                    file_path = os.path.join(video_save_path, file)
                    file_stats = os.stat(file_path)
                    videos.append({
                        "filename": file,
                        "path": file_path,
                        "size": file_stats.st_size,
                        "created_at": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                        "url": f"/api/download-video/{file}"
                    })
        return {"videos": videos}
    except Exception as e:
        logger.error(f"获取视频列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取视频列表失败: {str(e)}")

@app.get("/api/download-video/{filename}")
async def download_video(filename: str):
    """下载保存的视频文件"""
    try:
        file_path = os.path.join(video_save_path, filename)
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="视频文件不存在")
        
        return StreamingResponse(
            open(file_path, "rb"),
            media_type="video/mp4",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载视频失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载视频失败: {str(e)}")

if __name__ == "__main__":
    main()
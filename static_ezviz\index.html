
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Ezviz Cloud Camera - YOLO Processing</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                h1 { color: #333; }
                .container { max-width: 1200px; margin: 0 auto; }
                .video-container { margin-bottom: 30px; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .video-title { font-size: 18px; margin-bottom: 10px; }
                #mjpeg-stream { width: 100%; max-width: 800px; height: auto; }
                .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
                .status.connected { background-color: #d4edda; color: #155724; }
                .status.disconnected { background-color: #f8d7da; color: #721c24; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Ezviz Cloud Camera - YOLO Processing</h1>
                <div class="video-container">
                    <div class="video-title">YOLO Detection & Segmentation</div>
                    <img id="mjpeg-stream" src="/stream" alt="Video Stream">
                    <div id="status" class="status">Connecting...</div>
                </div>
            </div>
            
            <script>
                // Monitor image loading status
                const streamImg = document.getElementById('mjpeg-stream');
                const statusDiv = document.getElementById('status');
                
                // Image loaded successfully
                streamImg.onload = function() {
                    statusDiv.textContent = 'Connected';
                    statusDiv.className = 'status connected';
                };
                
                // Image failed to load
                streamImg.onerror = function() {
                    statusDiv.textContent = 'Connection lost, trying to reconnect...';
                    statusDiv.className = 'status disconnected';
                    
                    // Retry after 5 seconds
                    setTimeout(() => {
                        streamImg.src = '/stream?' + new Date().getTime();
                    }, 5000);
                };
            </script>
        </body>
        </html>
        
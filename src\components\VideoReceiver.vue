<template>
  <div class="video-receiver-container">
    <div class="card-container">
      <div class="card-header">
        <span class="card-title">视频接入</span>
        <div class="card-tools">
          <button class="connect-button hover-lift" @click="connectStream" v-if="!isStreaming">
            <i class="connect-icon-small"></i>
            连接视频流
          </button>
          <button class="stop-button hover-lift" @click="disconnectStream" v-if="isStreaming">
            停止接入
          </button>
        </div>
      </div>
      
      <div class="card-body">
        <div class="input-area hover-lift" v-if="!streamUrl">
          <div class="input-content">
            <div class="input-icon pulse">+</div>
            <div class="input-label">输入RTSP/HLS视频流地址</div>
            <div class="url-input-container">
              <input 
                v-model="inputStreamUrl"
                class="stream-url-input" 
                placeholder="rtsp:// 或 http://"
                @keyup.enter="handleInputConnect"
              />
              <button class="url-submit-button" @click="handleInputConnect">连接</button>
            </div>
            <div class="source-selector">
              <div class="source-option" @click="selectSource('rtsp')">
                <span class="source-icon rtsp-icon"></span>
                <span class="source-name">RTSP流</span>
              </div>
              <div class="source-option" @click="selectSource('hls')">
                <span class="source-icon hls-icon"></span>
                <span class="source-name">HLS流</span>
              </div>
              <div class="source-option" @click="selectSource('custom')">
                <span class="source-icon custom-icon"></span>
                <span class="source-name">自定义</span>
              </div>
            </div>
          </div>
        </div>
        <div class="video-container" v-else>
          <video 
            ref="videoElement" 
            id="video-player" 
            class="video-player" 
            autoplay
            muted
            playsinline
          ></video>
          <canvas ref="detectionCanvas" class="detection-canvas"></canvas>
          <div class="stream-overlay" v-if="!isConnected">
            <div class="connecting-badge">连接中...</div>
          </div>
        </div>
      </div>
      
      <div class="card-footer">
        <div class="streaming-info" v-if="isStreaming">
          <div class="streaming-status">
            <div class="status-indicator" :class="{'connected': isConnected, 'connecting': !isConnected}"></div>
            <span>{{ isConnected ? '视频接入中' : '正在连接...' }}</span>
          </div>
          <div class="streaming-time">连接时长: {{ formattedStreamingTime }}</div>
        </div>
        <div class="connection-details" v-if="isStreaming && isConnected">
          <div class="detail-item">
            <span class="detail-label">源地址:</span>
            <span class="detail-value">{{ streamUrl }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">分辨率:</span>
            <span class="detail-value">{{ videoResolution }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">码率:</span>
            <span class="detail-value">{{ videoBitrate }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 实时检测结果面板 -->
    <div class="detection-results-panel" v-if="isStreaming && isConnected">
      <div class="panel-header">实时检测结果</div>
      <div class="detection-events">
        <div class="event-item" v-for="(event, index) in detectionEvents" :key="index" :class="event.risk_level">
          <div class="event-time">{{ event.time }}</div>
          <div class="event-content">
            <div class="event-type">{{ event.category }}</div>
            <div class="event-description">{{ event.event }}</div>
          </div>
          <div class="event-risk-level" :class="event.risk_level">{{ event.risk_level === 'high' ? '高风险' : '低风险' }}</div>
        </div>
        <div class="no-events" v-if="detectionEvents.length === 0">
          尚未检测到风险事件
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { detectObjects } from '../services/DetectionService';

// 视频流URL和状态
const inputStreamUrl = ref('');
const streamUrl = ref('');
const videoElement = ref<HTMLVideoElement | null>(null);
const detectionCanvas = ref<HTMLCanvasElement | null>(null);
const isStreaming = ref(false);
const isConnected = ref(false);
const streamStartTime = ref(0);
const detectionInterval = ref<number | null>(null);
const videoResolution = ref('--');
const videoBitrate = ref('--');

// 预设流地址示例
const selectedSource = ref('');
const sourceExamples = {
  rtsp: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream',
  hls: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
  custom: ''
};

// 选择预设源
function selectSource(source: keyof typeof sourceExamples) {
  selectedSource.value = source;
  inputStreamUrl.value = sourceExamples[source];
}

// 检测结果
const detectionEvents = ref<any[]>([]);

// 格式化流媒体时间
const formattedStreamingTime = computed(() => {
  if (!streamStartTime.value) return '00:00:00';
  
  const totalSeconds = Math.floor((Date.now() - streamStartTime.value) / 1000);
  const hours = Math.floor(totalSeconds / 3600).toString().padStart(2, '0');
  const minutes = Math.floor((totalSeconds % 3600) / 60).toString().padStart(2, '0');
  const seconds = (totalSeconds % 60).toString().padStart(2, '0');
  
  return `${hours}:${minutes}:${seconds}`;
});

// 处理输入连接
function handleInputConnect() {
  if (!inputStreamUrl.value) return;
  streamUrl.value = inputStreamUrl.value;
  connectStream();
}

// 连接流
function connectStream() {
  if (!streamUrl.value && !inputStreamUrl.value) return;
  
  if (!streamUrl.value && inputStreamUrl.value) {
    streamUrl.value = inputStreamUrl.value;
  }
  
  isStreaming.value = true;
  isConnected.value = false;
  streamStartTime.value = Date.now();
  detectionEvents.value = [];
  
  // 模拟连接延迟
  setTimeout(() => {
    isConnected.value = true;
    loadVideoStream();
    
    // 每隔5秒进行一次检测
    detectionInterval.value = window.setInterval(detectFrame, 5000);
    
    // 生成随机分辨率和码率数据
    videoResolution.value = ['1920x1080', '1280x720', '854x480'][Math.floor(Math.random() * 3)];
    videoBitrate.value = ['2.5 Mbps', '1.8 Mbps', '1.2 Mbps'][Math.floor(Math.random() * 3)];
  }, 3000);
}

// 加载视频流
function loadVideoStream() {
  if (!videoElement.value) return;
  
  try {
    // 实际项目中，这里需要根据流类型使用合适的播放器
    // 例如使用hls.js处理HLS流，或使用flv.js处理flv流
    // 这里简化处理，直接设置src
    
    // 检查是否为HLS流
    if (streamUrl.value.includes('.m3u8')) {
      // 实际使用时需要加载hls.js库
      console.log('HLS流需要使用hls.js进行处理');
      
      // 模拟视频加载，使用测试视频源
      videoElement.value.src = 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8';
    } else {
      // 其他类型流，在实际项目中可能需要后端代理或特殊处理
      console.log('非HLS流可能需要后端代理');
      
      // 这里使用模拟视频代替
      videoElement.value.src = 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8';
    }
    
    videoElement.value.play().catch(error => {
      console.error('视频播放失败:', error);
      isConnected.value = false;
    });
  } catch (error) {
    console.error('加载视频流失败:', error);
    isConnected.value = false;
  }
}

// 断开连接
function disconnectStream() {
  if (detectionInterval.value) {
    clearInterval(detectionInterval.value);
    detectionInterval.value = null;
  }
  
  if (videoElement.value) {
    videoElement.value.pause();
    videoElement.value.src = '';
  }
  
  isStreaming.value = false;
  isConnected.value = false;
  streamUrl.value = '';
}

// 检测当前帧
async function detectFrame() {
  try {
    // 获取视频当前帧
    const canvas = document.createElement('canvas');
    const video = videoElement.value;
    
    if (!video || video.videoWidth === 0) return;
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 在canvas上绘制当前视频帧
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // 获取帧的base64数据
    const imageData = canvas.toDataURL('image/jpeg');
    
    // 生成检测提示词
    const customPrompt = `
    1. 详细分析图像中的交通场景，重点关注以下几类目标：
       - 车辆：识别所有车辆类型，包括小汽车、卡车、摩托车等，特别关注违规行为（标记为高风险）
       - 人员：识别所有行人，特别关注横穿马路、闯红灯的行人（标记为高风险）
       - 道路状况：识别道路拥堵、事故、施工等情况（根据严重程度标记风险等级）
    
    2. 对每个检测到的目标，提供以下信息：
       - 类别：车辆/行人/道路状况等
       - 事件：具体事件描述
       - 风险等级：高风险(high)/低风险(low)/安全(safe)
       - 置信度：0-1之间的数值
       - 边界框坐标：bbox_2d格式
       - 标签：用于显示的文本标签
    
    3. 提供交通场景的整体风险分析，使用简洁描述。
    
    4. 识别并分别列出场景中的高风险事件和低风险事件。
    `;
    
    // 调用检测API
    const modelId = 'qwen2.5-vl-7b-instruct'; // 使用轻量模型以提高速度
    const selectedOptions = ['vehicle_count', 'vehicle_status', 'person_count', 'traffic_status'];
    
    // 随机模拟检测结果
    if (Math.random() > 0.7) {
      const time = new Date().toLocaleTimeString();
      
      if (Math.random() > 0.5) {
        // 添加高风险事件
        detectionEvents.value.unshift({
          time,
          category: '车辆',
          event: '车辆违规变道',
          risk_level: 'high'
        });
      } else {
        // 添加低风险事件
        detectionEvents.value.unshift({
          time,
          category: '道路状况',
          event: '道路拥堵',
          risk_level: 'low'
        });
      }
      
      // 限制事件列表长度
      if (detectionEvents.value.length > 50) {
        detectionEvents.value = detectionEvents.value.slice(0, 50);
      }
      
      // 绘制随机检测框
      drawRandomDetectionBoxes();
    }
    
  } catch (error) {
    console.error('检测失败:', error);
  }
}

// 绘制随机检测框模拟检测结果
function drawRandomDetectionBoxes() {
  if (!detectionCanvas.value || !videoElement.value) return;
  
  const canvas = detectionCanvas.value;
  const video = videoElement.value;
  
  // 调整canvas尺寸匹配视频
  canvas.width = video.clientWidth;
  canvas.height = video.clientHeight;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // 清除canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // 定义颜色
  const colors = {
    high: 'rgba(255, 0, 0, 0.5)', // 高风险红色
    low: 'rgba(255, 193, 7, 0.5)' // 低风险黄色
  };
  
  // 绘制1-3个随机位置的检测框
  const boxCount = Math.floor(Math.random() * 3) + 1;
  
  for (let i = 0; i < boxCount; i++) {
    const width = Math.floor(Math.random() * (canvas.width / 3)) + 50;
    const height = Math.floor(Math.random() * (canvas.height / 3)) + 50;
    const x = Math.floor(Math.random() * (canvas.width - width));
    const y = Math.floor(Math.random() * (canvas.height - height));
    
    const isHighRisk = Math.random() > 0.5;
    const color = isHighRisk ? colors.high : colors.low;
    
    // 绘制边框
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.fillStyle = color.replace('0.5', '0.2');
    ctx.strokeRect(x, y, width, height);
    ctx.fillRect(x, y, width, height);
    
    // 绘制标签
    ctx.fillStyle = color;
    ctx.font = '14px "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "SimHei", "WenQuanYi Micro Hei", sans-serif';
    const label = isHighRisk ? '车辆违规' : '道路拥堵';
    ctx.fillRect(x, y - 20, ctx.measureText(label).width + 10, 20);
    ctx.fillStyle = 'white';
    ctx.fillText(label, x + 5, y - 5);
  }
}

// 组件挂载时
onMounted(() => {
  // 初始化视频组件引用
  videoElement.value = document.getElementById('video-player') as HTMLVideoElement;
  detectionCanvas.value = document.getElementById('detection-canvas') as HTMLCanvasElement;
});

// 组件卸载时
onUnmounted(() => {
  // 停止检测
  if (detectionInterval.value) {
    clearInterval(detectionInterval.value);
  }
  
  // 释放视频资源
  if (videoElement.value) {
    videoElement.value.pause();
    videoElement.value.src = '';
  }
});
</script>

<style scoped>
.video-receiver-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  gap: 15px;
}

.card-container {
  display: flex;
  flex-direction: column;
  background: rgba(0, 33, 64, 0.5);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
  box-shadow: 0 8px 24px rgba(0, 21, 41, 0.5);
  height: 70%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #1890ff;
  margin-right: 10px;
  border-radius: 2px;
}

.card-tools {
  display: flex;
  gap: 10px;
}

.card-body {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.card-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(0, 168, 255, 0.15);
  background: rgba(0, 21, 41, 0.4);
}

.input-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #003a8c;
  border-radius: 12px;
  background-color: rgba(0, 33, 64, 0.3);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.input-area::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

.input-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  transition: all 0.3s;
  position: relative;
  z-index: 1;
  width: 80%;
  max-width: 600px;
}

.input-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #1890ff;
  transition: transform 0.3s;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
}

.input-label {
  font-size: 16px;
  color: #ffffff;
  margin-bottom: 20px;
  text-align: center;
}

.url-input-container {
  display: flex;
  width: 100%;
  margin-bottom: 30px;
}

.stream-url-input {
  flex: 1;
  padding: 12px 15px;
  border-radius: 8px 0 0 8px;
  border: 1px solid rgba(0, 168, 255, 0.3);
  background: rgba(0, 21, 41, 0.6);
  color: #ffffff;
  font-size: 14px;
  outline: none;
  transition: all 0.3s;
}

.stream-url-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.url-submit-button {
  padding: 12px 20px;
  border-radius: 0 8px 8px 0;
  border: none;
  background: linear-gradient(to right, #0c8eff, #0058ff);
  color: white;
  cursor: pointer;
  transition: all 0.3s;
}

.url-submit-button:hover {
  background: linear-gradient(to right, #0a6fcc, #0046cc);
}

.source-selector {
  display: flex;
  gap: 15px;
  width: 100%;
  justify-content: center;
}

.source-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background: rgba(0, 33, 64, 0.6);
  border: 1px solid rgba(0, 168, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s;
  min-width: 100px;
}

.source-option:hover {
  transform: translateY(-5px);
  background: rgba(0, 58, 140, 0.5);
  border-color: rgba(0, 168, 255, 0.4);
  box-shadow: 0 10px 20px rgba(0, 21, 41, 0.3);
}

.source-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 10px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.rtsp-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M4 8h16v10H4z M2 6h20v14H2z M8 4l8-4v4h-8z'/%3E%3C/svg%3E");
}

.hls-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 14H9V8h2v8zm5 0h-2V8h2v8z'/%3E%3C/svg%3E");
}

.custom-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
}

.source-name {
  font-size: 14px;
  color: #ffffff;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  background-color: #000;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #000;
}

.detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.stream-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.connecting-badge {
  background: rgba(24, 144, 255, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 16px;
  box-shadow: 0 0 15px rgba(24, 144, 255, 0.5);
  animation: pulse 2s infinite;
}

.connect-button, .stop-button {
  padding: 10px 20px;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.connect-button {
  background: linear-gradient(to right, #0c8eff, #0058ff);
  color: white;
  box-shadow: 0 4px 10px rgba(12, 142, 255, 0.2);
}

.connect-button:hover {
  background: linear-gradient(to right, #0a6fcc, #0046cc);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(12, 142, 255, 0.3);
}

.stop-button {
  background: linear-gradient(to right, #ff4d4f, #cf1322);
  color: white;
  box-shadow: 0 4px 15px rgba(207, 19, 34, 0.3);
}

.stop-button:hover {
  background: linear-gradient(to right, #ff7875, #f5222d);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(207, 19, 34, 0.5);
}

.connect-icon-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M15.5 14h-.79l-.28-.27c1.2-1.4 1.82-3.31 1.48-5.34-.47-2.78-2.79-5-5.59-5.34-4.23-.52-7.79 3.04-7.27 7.27.34 2.8 2.56 5.12 5.34 5.59 2.03.34 3.94-.28 5.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.streaming-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.streaming-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-indicator.connected {
  background-color: #52c41a;
  animation: pulse 1.5s infinite;
}

.status-indicator.connecting {
  background-color: #faad14;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.6);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.streaming-time {
  font-weight: bold;
  color: #ffffff;
}

.connection-details {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.detail-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
}

.detail-value {
  color: #ffffff;
  font-size: 13px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detection-results-panel {
  background: rgba(0, 33, 64, 0.5);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
  box-shadow: 0 8px 24px rgba(0, 21, 41, 0.5);
  display: flex;
  flex-direction: column;
  height: 28%;
}

.panel-header {
  padding: 12px 20px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
}

.detection-events {
  padding: 15px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.event-item {
  display: flex;
  padding: 10px 15px;
  background: rgba(0, 21, 41, 0.3);
  border-radius: 8px;
  gap: 15px;
  align-items: center;
  transition: transform 0.3s;
  border-left: 3px solid transparent;
}

.event-item:hover {
  transform: translateX(5px);
}

.event-item.high {
  border-left-color: #f5222d;
  background: linear-gradient(90deg, rgba(245, 34, 45, 0.05), rgba(0, 21, 41, 0.3));
}

.event-item.low {
  border-left-color: #faad14;
  background: linear-gradient(90deg, rgba(250, 173, 20, 0.05), rgba(0, 21, 41, 0.3));
}

.event-time {
  min-width: 80px;
  color: #bdc3c7;
  font-size: 12px;
}

.event-content {
  flex: 1;
}

.event-type {
  font-weight: bold;
  margin-bottom: 4px;
  color: #ffffff;
}

.event-description {
  color: #bdc3c7;
  font-size: 13px;
}

.event-risk-level {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.event-risk-level.high {
  background: rgba(245, 34, 45, 0.2);
  color: #f5222d;
  border: 1px solid rgba(245, 34, 45, 0.3);
}

.event-risk-level.low {
  background: rgba(250, 173, 20, 0.2);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.no-events {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #bdc3c7;
  font-style: italic;
}

/* 动画类 */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.pulse {
  animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 
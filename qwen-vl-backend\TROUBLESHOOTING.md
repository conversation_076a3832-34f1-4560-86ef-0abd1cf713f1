# YOLO专用系统故障排除指南

## 问题诊断步骤

### 1. 检查依赖项和模型
```bash
cd qwen-vl-backend
python debug_yolo.py
```

这个脚本会检查：
- 所有必需的Python包
- YOLO模型文件是否存在
- 模型是否能正常加载
- YOLO处理器是否能正常工作

### 2. 启动YOLO专用后端
```bash
# 方法1：使用启动脚本
python start_yolo_backend.py

# 方法2：直接启动
python yolo_only_backend.py

# 方法3：使用uvicorn
python -m uvicorn yolo_only_backend:app --host 0.0.0.0 --port 8001
```

### 3. 测试WebSocket连接
```bash
python test_yolo_backend.py
```

## 常见问题解决方案

### 问题1: WebSocket连接失败
**错误信息**: `WebSocket connection to 'ws://localhost:8001/ws/yolo-only-process' failed`

**解决方案**:
1. 确认YOLO后端正在运行：
   ```bash
   python yolo_only_backend.py
   ```
2. 检查端口8001是否被占用：
   ```bash
   netstat -an | findstr 8001  # Windows
   lsof -i :8001              # Linux/Mac
   ```
3. 检查防火墙设置
4. 尝试使用不同端口（修改代码中的端口号）

### 问题2: YOLO处理错误
**错误信息**: `YOLO处理错误: 图像处理失败`

**解决方案**:
1. 检查YOLO模型文件：
   ```bash
   ls -la ../src/models/  # 检查模型文件
   ```
2. 确认模型文件完整性（重新下载如果需要）
3. 检查GPU/CUDA设置：
   ```python
   import torch
   print(torch.cuda.is_available())
   ```
4. 尝试使用CPU模式（在代码中设置device='cpu'）

### 问题3: 右侧栏不更新
**现象**: 视频播放但右侧检测结果不显示

**解决方案**:
1. 打开浏览器开发者工具（F12）
2. 查看Console标签页的错误信息
3. 查看Network标签页的WebSocket连接状态
4. 确认视频文件格式支持（推荐MP4）
5. 检查视频文件大小（过大可能导致处理缓慢）

### 问题4: 模型加载失败
**错误信息**: `无法加载模型`

**解决方案**:
1. 下载正确的YOLO模型：
   ```bash
   # 下载YOLOv11模型
   wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolo11n-seg.pt
   ```
2. 将模型文件放在正确位置：
   - `../src/models/yolo11n-seg.pt`
   - `models/yolo11n-seg.pt`
3. 检查文件权限
4. 确认ultralytics版本兼容性

## 调试技巧

### 1. 启用详细日志
在代码中添加：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 检查WebSocket消息
在浏览器开发者工具中：
1. 打开Network标签
2. 筛选WS（WebSocket）
3. 查看发送和接收的消息

### 3. 测试简单图像
使用debug_yolo.py脚本测试基本功能

### 4. 检查系统资源
- CPU使用率
- 内存使用情况
- GPU使用率（如果使用GPU）

## 性能优化建议

### 1. 调整帧率
在前端页面中调整处理帧率：
- 降低帧率可以减少处理负载
- 建议从5 FPS开始测试

### 2. 调整图像质量
在代码中修改：
```javascript
const frameData = canvas.toDataURL('image/jpeg', 0.5); // 降低质量
```

### 3. 使用GPU加速
确保PyTorch和CUDA正确安装：
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 系统要求

### 最低要求
- Python 3.8+
- 4GB RAM
- 2GB 可用磁盘空间

### 推荐配置
- Python 3.9+
- 8GB+ RAM
- NVIDIA GPU（支持CUDA）
- SSD存储

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 操作系统版本
2. Python版本
3. 错误日志完整输出
4. debug_yolo.py的运行结果
5. 浏览器开发者工具的错误信息

## 快速修复检查清单

- [ ] YOLO模型文件存在且完整
- [ ] 所有Python依赖已安装
- [ ] YOLO后端服务器正在运行（端口8001）
- [ ] 前端服务器正在运行（端口8000）
- [ ] WebSocket连接成功建立
- [ ] 浏览器支持WebSocket和Canvas
- [ ] 视频文件格式正确（MP4推荐）
- [ ] 防火墙允许端口8001通信

# Traffic Eyes 视频处理系统启动脚本
Write-Host "启动 Traffic Eyes 视频处理系统..." -ForegroundColor Green

# 设置环境变量，避免OpenCV错误
$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"

# 定义处理函数
function Start-VideoProcessor {
    param (
        [string]$Type,
        [string]$Source,
        [int]$Port
    )
    
    $windowTitle = "Traffic Eyes - $Type"
    
    if ($Type -eq "直接视频处理") {
        Start-Process powershell -ArgumentList "-NoProfile", "-Command", "python direct_video_processor.py --port $Port" -WindowStyle Normal
    } elseif ($Type -eq "实时视频处理") {
        if ($Source -eq "camera") {
            Start-Process powershell -ArgumentList "-NoProfile", "-Command", "python realtime_video_processor.py --source 0 --port $Port" -WindowStyle Normal
        } else {
            Start-Process powershell -ArgumentList "-NoProfile", "-Command", "python realtime_video_processor.py --source $Source --port $Port" -WindowStyle Normal
        }
    }
    
    Write-Host "$Type 已启动在端口 $Port" -ForegroundColor Cyan
}

# 创建必要的目录
New-Item -ItemType Directory -Path "processed_videos" -Force | Out-Null
New-Item -ItemType Directory -Path "static_realtime" -Force | Out-Null

# 检查视频文件是否存在
$videoFiles = Get-ChildItem -Path . -Filter *.mp4 | Select-Object -ExpandProperty Name
if ($videoFiles.Count -eq 0) {
    Write-Host "未找到视频文件，将使用摄像头作为源" -ForegroundColor Yellow
    $videoSource = "camera"
} else {
    $videoSource = $videoFiles[0]
    Write-Host "找到视频文件: $videoSource" -ForegroundColor Yellow
}

# 启动直接视频处理服务
Start-VideoProcessor -Type "直接视频处理" -Port 8080

# 等待几秒钟
Start-Sleep -Seconds 2

# 启动实时视频处理服务
Start-VideoProcessor -Type "实时视频处理" -Source $videoSource -Port 8081

Write-Host "所有服务已启动!" -ForegroundColor Green
Write-Host "请访问以下地址:" -ForegroundColor Green
Write-Host "- 直接视频处理: http://localhost:8080" -ForegroundColor Yellow
Write-Host "- 实时视频流: http://localhost:8081" -ForegroundColor Yellow 
import threading
import time
import logging
import asyncio
import subprocess
import os
import signal
import sys

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("logs/controller.log")
    ]
)
logger = logging.getLogger("dual-detection-controller")

class ServiceController:
    def __init__(self):
        self.yolo_process = None
        self.qwen_process = None
        self.running = False
        
    def start_services(self):
        """Start both YOLO and Qwen services in separate processes."""
        logger.info("Starting YOLO and Qwen services")
        self.running = True
        
        # Create logs directory if it doesn't exist
        if not os.path.exists("logs"):
            os.makedirs("logs")
        
        # Start YOLO WebSocket server
        yolo_log = open("logs/yolo_server.log", "w")
        yolo_cmd = [sys.executable, os.path.join("backend", "yolo_server.py")]
        self.yolo_process = subprocess.Popen(
            yolo_cmd,
            stdout=yolo_log,
            stderr=yolo_log,
            text=True,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        logger.info(f"Started YOLO server (PID: {self.yolo_process.pid})")
        
        # Start Qwen API server
        qwen_log = open("logs/qwen_api.log", "w")
        qwen_cmd = [sys.executable, os.path.join("backend", "qwen_api.py")]
        self.qwen_process = subprocess.Popen(
            qwen_cmd,
            stdout=qwen_log,
            stderr=qwen_log,
            text=True,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        logger.info(f"Started Qwen API server (PID: {self.qwen_process.pid})")
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_services, daemon=True)
        monitor_thread.start()
        
    def stop_services(self):
        """Stop all services gracefully."""
        logger.info("Stopping services...")
        self.running = False
        
        # Stop YOLO server
        if self.yolo_process:
            logger.info(f"Stopping YOLO server (PID: {self.yolo_process.pid})...")
            if os.name == 'nt':
                self.yolo_process.terminate()
            else:
                os.killpg(os.getpgid(self.yolo_process.pid), signal.SIGTERM)
            self.yolo_process = None
        
        # Stop Qwen API server
        if self.qwen_process:
            logger.info(f"Stopping Qwen API server (PID: {self.qwen_process.pid})...")
            if os.name == 'nt':
                self.qwen_process.terminate()
            else:
                os.killpg(os.getpgid(self.qwen_process.pid), signal.SIGTERM)
            self.qwen_process = None
        
        logger.info("All services stopped.")
    
    def monitor_services(self):
        """Monitor services and restart if they crash."""
        while self.running:
            # Check YOLO server
            if self.yolo_process and self.yolo_process.poll() is not None:
                logger.warning("YOLO server crashed, restarting...")
                yolo_log = open("logs/yolo_server.log", "a")
                yolo_cmd = [sys.executable, os.path.join("backend", "yolo_server.py")]
                self.yolo_process = subprocess.Popen(
                    yolo_cmd,
                    stdout=yolo_log,
                    stderr=yolo_log,
                    text=True,
                    creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
                )
                logger.info(f"Restarted YOLO server (PID: {self.yolo_process.pid})")
            
            # Check Qwen API server
            if self.qwen_process and self.qwen_process.poll() is not None:
                logger.warning("Qwen API server crashed, restarting...")
                qwen_log = open("logs/qwen_api.log", "a")
                qwen_cmd = [sys.executable, os.path.join("backend", "qwen_api.py")]
                self.qwen_process = subprocess.Popen(
                    qwen_cmd,
                    stdout=qwen_log,
                    stderr=qwen_log,
                    text=True,
                    creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
                )
                logger.info(f"Restarted Qwen API server (PID: {self.qwen_process.pid})")
            
            # Sleep to avoid high CPU usage
            time.sleep(5)

if __name__ == "__main__":
    controller = ServiceController()
    
    try:
        controller.start_services()
        logger.info("Services started successfully.")
        logger.info("Press Ctrl+C to stop all services.")
        
        # Keep main thread running
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, shutting down...")
        controller.stop_services()
    except Exception as e:
        logger.error(f"Error in controller: {e}")
        controller.stop_services() 
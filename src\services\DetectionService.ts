// DetectionService.ts
// 定义检测结果类型
export interface DetectionResult {
  objects: DetectionObject[];
  summary: Record<string, number>; // 每个类别的数量统计
  description?: string; // 图像描述
  rawJson?: string; // 原始JSON字符串
  input_width?: number; // 后端处理的图像宽度
  input_height?: number; // 后端处理的图像高度
  high_risk_events?: HighRiskEvent[]; // 高风险事件列表
  low_risk_events?: LowRiskEvent[]; // 低风险事件列表
  processed_url?: string; // 处理后的图像URL
  visualized_image_url?: string; // 可视化图像URL
  use_visualized_image?: boolean; // 是否使用可视化图像
  yolo_processed_frame?: string; // WebSocket返回的YOLO处理后的帧（Base64格式）
}

export interface DetectionObject {
  category: string; // 类别：人员、车辆、机械
  event?: string; // 事件类型
  confidence: number;
  bbox: [number, number, number, number]; // [x1, y1, x2, y2]格式
  risk_level?: string; // 风险等级: high, medium, low
  label?: string; // 显示标签
}

export interface HighRiskEvent {
  category: string; // 类别：人员、车辆、机械
  event: string; // 事件描述
  risk_level: string; // 风险等级: high
  confidence: number; // 置信度
  bbox_2d?: [number, number, number, number]; // 边界框坐标
}

export interface LowRiskEvent {
  category: string; // 类别：人员、车辆、机械
  event: string; // 事件描述
  risk_level: string; // 风险等级: medium, low
  confidence: number; // 置信度
  bbox_2d?: [number, number, number, number]; // 边界框坐标
}

// 调用后端API进行对象检测
export async function detectObjects(
  imageData: string,
  selectedOptions: string[] = [],
  customPrompt?: string,
  modelId?: string
): Promise<DetectionResult> {
  try {
    console.log('调用检测API，选项:', selectedOptions);
    if (customPrompt) {
      console.log('使用自定义提示词');
    }
    if (modelId) {
      console.log('使用模型ID:', modelId);
    }

    // 获取图像尺寸
    const img = new Image();
    img.src = imageData;

    // 等待图像加载完成
    await new Promise((resolve) => {
      if (img.complete) {
        resolve(null);
      } else {
        img.onload = () => resolve(null);
      }
    });

    // 计算合适的输入尺寸
    const originalWidth = img.width;
    const originalHeight = img.height;

    // 计算合适的输入尺寸，保持宽高比
    const maxDimension = 1024; // 最大尺寸
    let input_width = originalWidth;
    let input_height = originalHeight;

    if (originalWidth > maxDimension || originalHeight > maxDimension) {
      if (originalWidth > originalHeight) {
        input_width = maxDimension;
        input_height = Math.round(originalHeight * (maxDimension / originalWidth));
      } else {
        input_height = maxDimension;
        input_width = Math.round(originalWidth * (maxDimension / originalHeight));
      }
    }

    console.log(`原始图像尺寸: ${originalWidth}x${originalHeight}, 输入尺寸: ${input_width}x${input_height}`);

    // 准备请求体
    const requestBody: any = {
      image: imageData,
      options: selectedOptions,
      input_width: input_width,
      input_height: input_height
    };

    // 如果有自定义提示词，添加到请求中
    if (customPrompt) {
      requestBody.prompt = customPrompt;
    }

    // 如果有模型ID，添加到请求中
    if (modelId) {
      requestBody.model_id = modelId;
    }

    // 调用后端API
    const response = await fetch('http://localhost:8000/detect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API响应错误:', response.status, errorText);
      throw new Error(`API请求失败: ${response.status} - ${errorText || '未知错误'}`);
    }

    // 获取原始响应文本用于调试
    const responseText = await response.text();
    console.log('API原始响应文本:', responseText);

    // 尝试解析JSON
    let data;
    try {
      data = JSON.parse(responseText);
      console.log('成功解析JSON响应:', data);
    } catch (e) {
      console.error('JSON解析失败，尝试从响应中提取JSON部分:', e);
      // 尝试从响应中提取JSON部分（如果响应包含Markdown代码块）
      const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        try {
          data = JSON.parse(jsonMatch[1]);
          console.log('从Markdown代码块成功提取JSON:', data);
        } catch (e2) {
          console.error('从Markdown代码块提取JSON失败:', e2);
          throw new Error('无法解析API响应');
        }
      } else {
        throw new Error('无法解析API响应');
      }
    }

    console.log('解析后的API数据:', data);

    // 检查API是否返回错误
    if (!data.success) {
      console.error('API返回失败状态:', data.error);
      throw new Error(data.error || '检测失败，请重试');
    }

    // 处理API返回的数据
    const result: DetectionResult = {
      objects: [],
      summary: {},
      rawJson: data.raw_json || JSON.stringify(data.detections || [], null, 2), // 保存原始JSON
      input_width: data.input_width || input_width,
      input_height: data.input_height || input_height
    };

    // 添加处理后的图像URL
    if (data.processed_url) {
      console.log('API返回的处理后图像URL:', data.processed_url);
      result.processed_url = data.processed_url;
    }

    // 添加可视化图像URL
    if (data.visualized_image_url) {
      console.log('API返回的可视化图像URL:', data.visualized_image_url);
      result.visualized_image_url = data.visualized_image_url;
      // 检查是否应该使用可视化图像
      if (data.use_visualized_image) {
        result.use_visualized_image = true;
      }
    }

    // 解析检测结果
    if (data.detections && Array.isArray(data.detections)) {
      console.log('处理检测结果数组:', data.detections.length, '个对象');
      result.objects = data.detections.map((obj: any) => {
        // 处理不同格式的边界框坐标
        let bbox: [number, number, number, number];

        // 优先使用 bbox_2d 字段
        if (obj.bbox_2d && Array.isArray(obj.bbox_2d) && obj.bbox_2d.length === 4) {
          // 如果是bbox_2d格式
          console.log('使用bbox_2d字段:', obj.bbox_2d);
          bbox = obj.bbox_2d as [number, number, number, number];
        } else if (obj.bbox && Array.isArray(obj.bbox) && obj.bbox.length === 4) {
          // 如果是标准的[x1, y1, x2, y2]格式
          console.log('使用bbox字段:', obj.bbox);
          bbox = obj.bbox as [number, number, number, number];
        } else {
          // 如果没有有效的边界框坐标，使用默认值
          console.warn('检测对象没有有效的边界框坐标:', obj);
          bbox = [0, 0, 1, 1]; // 默认使用整个图像
        }

        return {
          category: obj.category || '未知', // 类别：人员、车辆、机械
          event: obj.event || '', // 事件类型
          confidence: obj.confidence || 1.0,
          bbox: bbox,
          risk_level: obj.risk_level || 'medium',
          label: obj.label || obj.category || '未知' // 优先使用label字段，否则使用category
        };
      });

      // 计算每个类别的数量
      result.summary = result.objects.reduce((acc: Record<string, number>, obj) => {
        const category = obj.category;
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {});
    } else {
      console.warn('API返回的检测结果格式不正确:', data);
    }

    // 添加图像描述
    if (data.description) {
      console.log('API返回的描述:', data.description);
      result.description = data.description;
    } else {
      console.warn('API响应中没有description字段');
      // 尝试从原始响应中提取描述
      const descMatch = responseText.match(/描述[:：]\s*(.*?)(?=\n|$)/);
      if (descMatch && descMatch[1]) {
        console.log('从原始响应中提取到描述:', descMatch[1]);
        result.description = descMatch[1].trim();
      }
    }

    // 添加高风险事件
    if (data.high_risk_events && Array.isArray(data.high_risk_events)) {
      console.log('API返回的高风险事件:', data.high_risk_events);
      result.high_risk_events = data.high_risk_events;
    } else {
      console.warn('API响应中没有high_risk_events字段或格式不正确');
      result.high_risk_events = []; // 设置为空数组
    }

    // 添加低风险事件
    if (data.low_risk_events && Array.isArray(data.low_risk_events)) {
      console.log('API返回的低风险事件:', data.low_risk_events);
      result.low_risk_events = data.low_risk_events;
    } else {
      console.warn('API响应中没有low_risk_events字段或格式不正确');
      result.low_risk_events = []; // 设置为空数组
    }

    // 添加调试日志，查看最终结果中是否包含描述
    console.log('最终处理后的检测结果:', result);
    console.log('描述字段:', result.description);
    console.log('高风险事件数量:', result.high_risk_events?.length || 0);
    console.log('低风险事件数量:', result.low_risk_events?.length || 0);
    console.log('输入尺寸:', result.input_width, 'x', result.input_height);
    console.log('检测对象数量:', result.objects.length);

    return result;
  } catch (error) {
    console.error('对象检测API调用失败:', error);
    throw error;
  }
}
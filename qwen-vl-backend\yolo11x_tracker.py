"""
YOLOv11分割跟踪识别系统 - 修复版（所有参数写在内部，不通过命令行传递）
使用YOLOv11进行目标检测、分割和跟踪
"""

import cv2
import numpy as np
import os
import sys
from ultralytics import YOLO
import time
import traceback

# 添加全局模型缓存字典
MODEL_CACHE = {}

import torch

def get_cached_model(model_path, device=None):
    """获取缓存的模型实例，如果不存在则加载"""
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    cache_key = (model_path, device)
    if cache_key not in MODEL_CACHE:
        print(f"尝试加载模型: {model_path} 到设备: {device}")
        if not os.path.exists(model_path):
            # 标准化模型路径: 始终使用src/models目录
            current_dir = os.path.abspath(os.path.dirname(__file__))
            standard_path = os.path.abspath(os.path.join(current_dir, "../src/models/yolo11n-seg.pt"))

            if os.path.exists(standard_path):
                print(f"使用标准模型路径: {standard_path}")
                model_path = standard_path
            else:
                print(f"错误：模型文件 {model_path} 不存在，标准路径 {standard_path} 也不存在")
                print("请确保模型文件已下载并放置在src/models目录中")
                return None
        try:
            model = YOLO(model_path)
            model.to(device)  # 将模型移动到指定设备
            MODEL_CACHE[cache_key] = model
            print(f"模型加载成功: {model_path} 到设备: {device}")
        except Exception as e:
            print(f"模型加载失败: {str(e)}")
            return None
    return MODEL_CACHE[cache_key]

def handle_error(e):
    print(f"发生错误: {str(e)}")
    print("详细错误信息:")
    traceback.print_exc()
    return False

class YOLOTracker:
    def __init__(self, model_path, tracker_config="bytetrack.yaml", max_track_length=40, mask_alpha=0.6, device=None):
        self.model_path = model_path
        self.tracker_config = tracker_config
        self.max_track_length = max_track_length
        self.mask_alpha = mask_alpha
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')

        self.track_history = {}  # 用于记录每个目标的轨迹
        self.track_colors = {}   # 为每个跟踪ID分配固定颜色

        # 使用缓存模型而不是每次都加载
        self.model = get_cached_model(self.model_path, device=self.device)
        if self.model is None:
            handle_error(f"无法加载模型: {self.model_path} 到设备: {self.device}")
            sys.exit(1)

    def process_frame(self, frame):
        try:
            # 深拷贝输入帧以避免直接修改
            frame = frame.copy()

            # 调用模型进行跟踪检测
            results = self.model.track(frame, persist=True, verbose=False, tracker=self.tracker_config)

            if not results or len(results) == 0:
                return frame

            result = results[0]  # Get first result
            if not hasattr(result.boxes, 'id') or result.boxes.id is None:
                return frame

            # 获取跟踪ID
            track_ids = result.boxes.id.int().cpu().tolist()

            # 处理分割掩膜
            if hasattr(result, 'masks') and result.masks is not None:
                # 获取掩膜数据
                try:
                    masks = result.masks.data.cpu().numpy()
                    for i, mask in enumerate(masks):
                        if i >= len(track_ids):
                            continue

                        # 调整掩膜大小并二值化
                        mask = cv2.resize(mask, (frame.shape[1], frame.shape[0]))
                        mask = (mask > 0.5).astype(np.uint8)

                        # 获取颜色
                        track_id = track_ids[i]
                        if track_id not in self.track_colors:
                            self.track_colors[track_id] = tuple(np.random.randint(0, 255, 3).tolist())
                        color = self.track_colors[track_id]

                        # 创建彩色遮罩
                        overlay = frame.copy()
                        overlay[mask == 1] = color

                        # 混合原始帧和遮罩
                        cv2.addWeighted(overlay, 1 - self.mask_alpha, frame, self.mask_alpha, 0, frame)
                except Exception as e:
                    print(f"掩膜处理错误: {str(e)}")

            # 绘制边界框和轨迹
            for i, track_id in enumerate(track_ids):
                if i >= len(result.boxes.xyxy):
                    continue

                # 获取边界框
                box = result.boxes.xyxy[i].cpu().numpy().astype(int)

                # 获取类别和置信度
                cls_id = int(result.boxes.cls[i].item()) if i < len(result.boxes.cls) else -1
                cls_name = self.model.names.get(cls_id, "未知")
                conf = float(result.boxes.conf[i].item()) if i < len(result.boxes.conf) else 0.0

                # 设置颜色
                if track_id not in self.track_colors:
                    self.track_colors[track_id] = tuple(np.random.randint(0, 255, 3).tolist())
                color = self.track_colors[track_id]

                # 绘制边界框
                cv2.rectangle(frame, (box[0], box[1]), (box[2], box[3]), color, 2)

                # 绘制标签
                label = f"{cls_name} {conf:.2f} ID:{track_id}"
                cv2.putText(frame, label, (box[0], box[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

                # 更新和绘制轨迹
                center = ((box[0] + box[2]) // 2, (box[1] + box[3]) // 2)
                if track_id not in self.track_history:
                    self.track_history[track_id] = []
                self.track_history[track_id].append(center)

                # 绘制轨迹线
                if len(self.track_history[track_id]) > 1:
                    points = np.array(self.track_history[track_id], np.int32)
                    cv2.polylines(frame, [points], False, color, 2)

                # 限制轨迹长度
                if len(self.track_history[track_id]) > self.max_track_length:
                    self.track_history[track_id].pop(0)

            return frame

        except Exception as e:
            print(f"处理帧时发生错误: {str(e)}")
            traceback.print_exc()
            return frame

def process_video(video_path, output_path=None, model_path="../src/models/yolo11n-seg.pt", max_track_length=40, mask_alpha=0.5):
    """处理单个视频文件并返回处理后的视频路径"""
    # 内部设置所有参数
    current_dir = os.path.abspath(os.path.dirname(__file__))
    if not os.path.isabs(model_path):
        model_path = os.path.join(current_dir, model_path)

    if not os.path.isabs(video_path):
        video_path = os.path.join(current_dir, video_path)

    if output_path is None:
        file_name, file_ext = os.path.splitext(os.path.basename(video_path))
        output_path = os.path.join(current_dir, f"{file_name}_processed.avi")
    elif not os.path.isabs(output_path):
        output_path = os.path.join(current_dir, output_path)

    headless = True  # 不显示视频窗口
    max_retries = 3  # 添加重试次数

    # 验证文件读取权限
    if not os.access(video_path, os.R_OK):
        print(f"错误：无法读取视频文件 {video_path}")
        print("请检查文件权限")
        return None

    # 验证视频文件是否存在
    if not os.path.exists(video_path):
        print(f"错误：视频文件 {video_path} 不存在")
        print("请确保视频文件放置在正确位置")
        return None

    # 验证模型文件是否存在
    if not os.path.exists(model_path):
        # 尝试使用标准路径
        standard_path = os.path.abspath(os.path.join(current_dir, "../src/models/yolo11n-seg.pt"))
        if os.path.exists(standard_path):
            print(f"使用标准模型路径: {standard_path}")
            model_path = standard_path
        else:
            print(f"错误：模型文件 {model_path} 不存在，标准路径 {standard_path} 也不存在")
            print("请确保模型文件已下载并放置在src/models目录中")
            return None

    # 初始化跟踪对象
    try:
        yolo_tracker = YOLOTracker(model_path=model_path,
                                  tracker_config="bytetrack.yaml",
                                  max_track_length=max_track_length,
                                  mask_alpha=mask_alpha)
    except Exception as e:
        print(f"跟踪器初始化失败: {str(e)}")
        print("请检查模型文件是否损坏或格式不正确")
        return None

    # 打开视频并获取详细信息
    print(f"尝试打开视频源: {video_path}")
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误：无法打开视频文件 {video_path}")
        print("请检查视频文件是否损坏或格式不支持")
        return None

    # 获取视频信息
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)

    print(f"视频信息:")
    print(f"- 尺寸: {frame_width}x{frame_height}")
    print(f"- 总帧数: {frame_count}")
    print(f"- FPS: {fps}")

    if frame_count == 0:
        print("错误：视频帧数为0")
        cap.release()
        return None

    # 初始化写入器
    writer = None
    if headless:
        try:
            # 尝试使用XVID编码器
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            if fourcc == -1:
                # 如果XVID不可用，尝试使用MJPG
                fourcc = cv2.VideoWriter_fourcc(*'MJPG')
                if fourcc == -1:
                    raise Exception("无法找到可用的视频编码器")

            # 修改输出文件扩展名为.avi以匹配XVID编码器
            output_path = output_path.replace('.mp4', '.avi')
            writer = cv2.VideoWriter(output_path, fourcc, fps, (frame_width, frame_height))
            if not writer.isOpened():
                raise Exception("无法创建视频写入对象")
            print(f"成功创建视频写入器，使用编码器: {'XVID' if fourcc == cv2.VideoWriter_fourcc(*'XVID') else 'MJPG'}")
            print(f"输出文件将保存为: {output_path}")
        except Exception as e:
            print(f"错误：创建视频写入器失败 - {str(e)}")
            print("请确保系统已安装适当的视频编码器")
            print("如果问题持续存在，请尝试安装FFmpeg或使用不同的视频格式")
            cap.release()
            return None

    # 主循环处理视频帧
    frame_idx = 0
    retry_count = 0
    print("开始处理视频...")

    while frame_idx < frame_count:
        ret, frame = cap.read()
        if not ret:
            retry_count += 1
            if retry_count > max_retries:
                print(f"错误：无法读取帧 {frame_idx}，已重试{max_retries}次")
                break
            print(f"警告：读取帧 {frame_idx} 失败，尝试重试 ({retry_count}/{max_retries})")
            continue

        retry_count = 0
        frame_idx += 1

        if frame_idx % 100 == 0:
            print(f"正在处理：{frame_idx}/{frame_count} ({(frame_idx/frame_count)*100:.1f}%)")

        start_time = time.time()
        processed_frame = yolo_tracker.process_frame(frame)
        end_time = time.time()
        fps = 1 / max(end_time - start_time, 0.001)

        cv2.putText(processed_frame, f"FPS: {fps:.1f}", (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        if not headless:
            window_name = "YOLOv11 Tracker"
            cv2.imshow(window_name, processed_frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                print("用户按下了退出键")
                break
        else:
            writer.write(processed_frame)

    print("视频处理完成")

    cap.release()
    if writer is not None:
        writer.release()
    if not headless:
        cv2.destroyAllWindows()
    yolo_tracker.track_history.clear()
    yolo_tracker.track_colors.clear()
    print("程序已完成")

    return output_path

def process_image(image_path, output_path=None, model_path="../src/models/yolo11n-seg.pt", mask_alpha=0.5):
    """处理单张图像并返回处理后的图像路径"""
    # 内部设置所有参数
    current_dir = os.path.abspath(os.path.dirname(__file__))
    if not os.path.isabs(model_path):
        model_path = os.path.join(current_dir, model_path)

    if not os.path.isabs(image_path):
        image_path = os.path.join(current_dir, image_path)

    if output_path is None:
        file_name, file_ext = os.path.splitext(os.path.basename(image_path))
        output_path = os.path.join(current_dir, f"{file_name}_processed{file_ext}")
    elif not os.path.isabs(output_path):
        output_path = os.path.join(current_dir, output_path)

    # 验证文件读取权限
    if not os.access(image_path, os.R_OK):
        print(f"错误：无法读取图像文件 {image_path}")
        print("请检查文件权限")
        return None

    # 验证图像文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：图像文件 {image_path} 不存在")
        print("请确保图像文件放置在正确位置")
        return None

    # 验证模型文件是否存在
    if not os.path.exists(model_path):
        # 尝试使用标准路径
        standard_path = os.path.abspath(os.path.join(current_dir, "../src/models/yolo11n-seg.pt"))
        if os.path.exists(standard_path):
            print(f"使用标准模型路径: {standard_path}")
            model_path = standard_path
        else:
            print(f"错误：模型文件 {model_path} 不存在，标准路径 {standard_path} 也不存在")
            print("请确保模型文件已下载并放置在src/models目录中")
            return None

    # 初始化跟踪对象
    try:
        yolo_tracker = YOLOTracker(model_path=model_path,
                                  tracker_config="bytetrack.yaml",
                                  max_track_length=40,
                                  mask_alpha=mask_alpha)
    except Exception as e:
        print(f"跟踪器初始化失败: {str(e)}")
        print("请检查模型文件是否损坏或格式不正确")
        return None

    # 读取图像
    print(f"尝试打开图像: {image_path}")
    frame = cv2.imread(image_path)
    if frame is None:
        print(f"错误：无法读取图像文件 {image_path}")
        print("请检查图像文件是否损坏或格式不支持")
        return None

    # 处理图像
    print(f"开始处理图像...")
    processed_frame = yolo_tracker.process_frame(frame)

    # 保存处理后的图像
    cv2.imwrite(output_path, processed_frame)
    print(f"图像处理完成，保存到: {output_path}")

    return output_path

def main():
    # 内部设置所有参数
    current_dir = os.path.abspath(os.path.dirname(__file__))
    # 使用标准化模型路径
    model_path = os.path.abspath(os.path.join(current_dir, "../src/models/yolo11n-seg.pt"))
    video_path = os.path.join(current_dir, "hat.mp4")
    output = os.path.join(current_dir, "hat2.mp4")

    max_track_length = 40
    mask_alpha = 0.5
    headless = True

    # 处理视频
    process_video(
        video_path=video_path,
        output_path=output,
        model_path=model_path,
        max_track_length=max_track_length,
        mask_alpha=mask_alpha
    )

if __name__ == "__main__":
    main()
# Traffic-Eyes 后端启动脚本

Write-Host "检查Redis服务..."
$redisService = Get-Service -Name "Redis" -ErrorAction SilentlyContinue

if ($null -eq $redisService) {
    Write-Host "错误: 未找到Redis服务!" -ForegroundColor Red
    exit 1
} elseif ($redisService.Status -ne "Running") {
    Write-Host "启动Redis服务..."
    Start-Service -Name "Redis"
} else {
    Write-Host "Redis服务已运行" -ForegroundColor Green
}

# 设置后端路径
$backendPath = Join-Path $PSScriptRoot "qwen-vl-backend"
Set-Location $backendPath

# 启动帧处理工作进程
Write-Host "启动帧处理工作进程..." -ForegroundColor Yellow
Start-Process -FilePath "python" -ArgumentList "frame_processor_worker.py" -NoNewWindow

# 等待工作进程启动
Start-Sleep -Seconds 2

# 启动主后端服务
Write-Host "启动主后端服务..." -ForegroundColor Yellow
python main.py 
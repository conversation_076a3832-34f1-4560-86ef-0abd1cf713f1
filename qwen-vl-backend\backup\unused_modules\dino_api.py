# dds cloudapi for DINO-X
from dds_cloudapi_sdk import Config
from dds_cloudapi_sdk import Client
# Import base task components
from dds_cloudapi_sdk.tasks.base import BaseTask
from dds_cloudapi_sdk.tasks import LabelTypes

# using supervision for visualization
import os
import cv2
import numpy as np
import supervision as sv
from pathlib import Path

"""
Hyper Parameters
"""
API_TOKEN = "7ed92eaaa78f17945a77ab2c8eae1f09"
IMG_PATH = "qwen-vl-backend\.assets\images\luotu2.jpg"
TEXT_PROMPT = "<prompt_free>"
OUTPUT_DIR = Path("./outputs/prompt_free_detection_segmentation")

OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

"""
Example Code - Not Used in Production
This is a demonstration of how to use the SDK, but won't be called directly
in the API due to incompatibilities with the current SDK version
"""

# Helper class for demonstration purposes
class SimpleDetectionDemo:
    @staticmethod
    def run_demo():
        # Step 1: initialize the config
        token = API_TOKEN
        config = Config(token)
        
        # Step 2: initialize the client
        client = Client(config)
        
        # Step 3: Use basic task since specific ones may not be available
        # This is sample code for demonstration - actual API may differ
        print("Detection API demo - this will not be executed in production")

# Function to detect road defects using DINO
def detect_road_defects(image_path):
    try:
        # 初始化配置
        token = API_TOKEN
        config = Config(token)
        
        # 初始化客户端
        client = Client(config)
        
        # 上传图像
        image_url = client.upload_file(image_path)
        
        # 处理不同API版本 - 检查可用的类
        try:
            # 尝试使用DinoxTask（如果可用）
            from dds_cloudapi_sdk.tasks import DinoxTask
            
            # 使用DinoxTask定义任务
            task = DinoxTask(
                image_url=image_url,
                prompt="路面裂缝，坑洼，积水，路面沉降，路面损坏，异物",
                confidence_threshold=0.35,
                targets=[
                    DetectionTarget.BBox, 
                    DetectionTarget.Mask
                ]
            )
        except ImportError:
            # 如果DinoxTask不可用，回退到DetectionTask
            from dds_cloudapi_sdk.tasks import DetectionTask
            
            # 使用DetectionTask定义任务
            task = DetectionTask(
                image_url=image_url,
                prompt="路面裂缝，坑洼，积水，路面沉降，路面损坏，异物",
                confidence_threshold=0.35,
                targets=[
                    DetectionTarget.BBox, 
                    DetectionTarget.Mask
                ]
            )
        
        # 运行任务
        client.run_task(task)
        predictions = task.result.objects
        
        # 解析预测结果
        classes = [pred.category for pred in predictions]
        classes = list(set(classes))
        class_name_to_id = {name: id for id, name in enumerate(classes)}
        
        defects = []
        defect_classes = []
        
        for obj in predictions:
            # 仅处理与路面相关的缺陷
            cls_name = obj.category.lower().strip()
            
            if any(keyword in cls_name for keyword in ["裂缝", "坑洼", "积水", "沉降", "损坏", "异物"]):
                bbox = obj.bbox
                # 将DINO检测结果格式化为API返回格式
                defect = {
                    "category": obj.category,
                    "bbox": bbox,
                    "score": obj.score,
                    "mask": None
                }
                
                if hasattr(obj, 'mask') and obj.mask:
                    try:
                        mask_np = DetectionTask.rle2mask(
                            DetectionTask.string2rle(obj.mask.counts), 
                            obj.mask.size
                        )
                        defect["mask"] = mask_np.tolist()
                    except:
                        pass
                
                defects.append(defect)
                defect_classes.append(obj.category)
        
        return defects, list(set(defect_classes))
    
    except Exception as e:
        print(f"DINO检测失败: {str(e)}")
        # 返回空结果
        return [], []

# Keep simple visualization code for reference
def visualize_detections(image_path, boxes, masks=None, class_ids=None, labels=None):
    img = cv2.imread(image_path)
    detections = sv.Detections(
        xyxy=boxes,
        mask=masks.astype(bool) if masks is not None else None,
        class_id=class_ids if class_ids is not None else None,
    )
    
    box_annotator = sv.BoxAnnotator()
    annotated_frame = box_annotator.annotate(scene=img.copy(), detections=detections)
    
    if labels is not None:
        label_annotator = sv.LabelAnnotator()
        annotated_frame = label_annotator.annotate(scene=annotated_frame, detections=detections, labels=labels)
    
    if masks is not None:
        mask_annotator = sv.MaskAnnotator()
        annotated_frame = mask_annotator.annotate(scene=annotated_frame, detections=detections)
    
    return annotated_frame

"""
Prompting DINO-X with Text for Box and Mask Generation with Cloud API
"""

# Step 1: initialize the config
token = API_TOKEN
config = Config(token)

# Step 2: initialize the client
client = Client(config)

# Step 3: Run DINO-X task
# if you are processing local image file, upload them to DDS server to get the image url
image_url = client.upload_file(IMG_PATH)

# Handle different API versions - check what class is available
try:
    # Try to use DinoxTask if available
    from dds_cloudapi_sdk.tasks import DinoxTask
    
    # Define task with DinoxTask
    task = DinoxTask(
        image_url=image_url,
        prompt=TEXT_PROMPT,
        confidence_threshold=0.35,
        targets=[
            DetectionTarget.BBox, 
            DetectionTarget.Mask
        ]
    )
except ImportError:
    # Fall back to DetectionTask if DinoxTask is not available
    from dds_cloudapi_sdk.tasks import DetectionTask
    
    # Define task with DetectionTask
    task = DetectionTask(
        image_url=image_url,
        prompt=TEXT_PROMPT,
        confidence_threshold=0.35,
        targets=[
            DetectionTarget.BBox, 
            DetectionTarget.Mask
        ]
    )

# Run task and get predictions
client.run_task(task)
predictions = task.result.objects

"""
Visualization
"""
# decode the prediction results
classes = [pred.category for pred in predictions]
classes = list(set(classes))
class_name_to_id = {name: id for id, name in enumerate(classes)}
class_id_to_name = {id: name for name, id in class_name_to_id.items()}

boxes = []
masks = []
confidences = []
class_names = []
class_ids = []

for idx, obj in enumerate(predictions):
    boxes.append(obj.bbox)
    masks.append(DetectionTask.rle2mask(DetectionTask.string2rle(obj.mask.counts), obj.mask.size))  # convert mask to np.array using DDS API
    confidences.append(obj.score)
    cls_name = obj.category.lower().strip()
    class_names.append(cls_name)
    class_ids.append(class_name_to_id[cls_name])

print(set(class_names))

boxes = np.array(boxes)
masks = np.array(masks)
class_ids = np.array(class_ids)
labels = [
    f"{class_name} {confidence:.2f}"
    for class_name, confidence
    in zip(class_names, confidences)
]

img = cv2.imread(IMG_PATH)
detections = sv.Detections(
    xyxy = boxes,
    mask = masks.astype(bool),
    class_id = class_ids,
)

box_annotator = sv.BoxAnnotator()
annotated_frame = box_annotator.annotate(scene=img.copy(), detections=detections)

label_annotator = sv.LabelAnnotator()
annotated_frame = label_annotator.annotate(scene=annotated_frame, detections=detections, labels=labels)
# cv2.imwrite(os.path.join(OUTPUT_DIR, "annotated_demo_image.jpg"), annotated_frame)


mask_annotator = sv.MaskAnnotator()
annotated_frame = mask_annotator.annotate(scene=annotated_frame, detections=detections)
cv2.imshow("annotated_demo_image_with_mask", annotated_frame)
cv2.waitKey(0)
cv2.destroyAllWindows()
# cv2.imwrite(os.path.join(OUTPUT_DIR, "annotated_demo_image_with_mask.jpg"), annotated_frame)

print(f"Annotated image has already been saved to {OUTPUT_DIR}")
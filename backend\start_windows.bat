@echo off
echo ================================================
echo Starting Traffic-Eyes Dual Detection System...
echo ================================================

REM Create logs directory if it doesn't exist
if not exist "logs" (
    echo Creating logs directory...
    mkdir logs
)

REM Check if Python virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    
    echo Installing required packages...
    call venv\Scripts\activate
    
    REM Check if requirements.txt exists
    if not exist "requirements.txt" (
        echo Creating requirements.txt...
        (
            echo flask==2.0.1
            echo flask-cors==3.0.10
            echo ultralytics==8.0.0
            echo websockets==10.3
            echo opencv-python==********
            echo pillow==9.0.1
            echo requests==2.27.1
            echo numpy==1.22.3
        ) > requirements.txt
    )
    
    pip install -r requirements.txt
) else (
    REM Activate existing virtual environment
    call venv\Scripts\activate
)

REM Start the controller service which manages both YOLO and Qwen
echo Starting controller service...
python backend\controller.py

echo ================================================
echo Controller has been started!
echo ================================================
echo Press Ctrl+C in the controller window to stop
echo all services. 
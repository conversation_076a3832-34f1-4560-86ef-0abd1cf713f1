<template>
  <div class="operation-chat-interface">
    <!-- 导入历史侧边栏 -->
    <div class="interface-layout">
      <ChatHistorySidebar 
        class="history-sidebar"
        @new-chat="startNewChat"
        @select-chat="loadChat"
        @file-upload="handleFileUpload"
      />
      
      <div class="chat-main">
        <div class="chat-header">
          <div class="header-icon"></div>
          <div class="header-title">运营智能顾问</div>
          <div class="header-status online">在线</div>
          <div class="header-actions">
            <button class="action-button model-select" @click="toggleModelSelect">
              <span class="model-icon"></span>
              <span class="model-name">{{ currentModel.name }}</span>
              <span class="dropdown-icon"></span>
            </button>
            <div v-if="showModelSelect" class="model-dropdown">
              <div 
                v-for="model in availableModels" 
                :key="model.id"
                :class="['model-option', { active: currentModel.id === model.id }]"
                @click="selectModel(model)"
              >
                <div class="model-option-name">{{ model.name }}</div>
                <div class="model-option-desc">{{ model.description }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="chat-content">
          <div class="chat-body" ref="chatBodyRef">
            <div v-if="activeChatMessages.length === 0" class="chat-empty-state">
              <div class="empty-logo"></div>
              <h2 class="empty-title">智能运营顾问</h2>
              <p class="empty-description">我可以回答关于运营风险管控的问题，帮助您优化交通流量、识别潜在风险并提供专业建议。</p>
              
              <div class="empty-examples">
                <h3 class="examples-title">您可以尝试以下问题：</h3>
                <div class="examples-list">
                  <div 
                    v-for="(question, index) in suggestedQuestions" 
                    :key="index"
                    class="example-item"
                    @click="selectSuggestion(question)"
                  >
                    {{ question }}
                  </div>
                </div>
              </div>
            </div>
            
            <div v-else class="chat-messages">
              <ChatMessageItem 
                v-for="(message, index) in activeChatMessages"
                :key="index"
                :message="message"
              />
              
              <div v-if="isThinking" class="thinking-indicator">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
                <span>顾问思考中</span>
              </div>
            </div>
          </div>
          
          <div class="chat-input-panel">
            <div class="chat-toolbar">
              <button class="toolbar-button image-button" title="上传图片">
                <div class="button-icon image-icon"></div>
              </button>
              <button class="toolbar-button voice-button" title="语音输入">
                <div class="button-icon voice-icon"></div>
              </button>
              <button class="toolbar-button clear-button" title="清空对话" @click="clearChat">
                <div class="button-icon clear-icon"></div>
              </button>
            </div>
            
            <div class="chat-input-wrapper">
              <textarea 
                v-model="userInput" 
                class="chat-input" 
                placeholder="请输入您的问题，例如：如何优化交通流量管理？" 
                @keyup.enter="sendMessage"
              ></textarea>
            </div>
            
            <button 
              class="send-button" 
              :disabled="!userInput.trim()" 
              @click="sendMessage"
            >
              <div class="send-icon"></div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import ChatHistorySidebar from './ChatHistorySidebar.vue';
import ChatMessageItem from './ChatMessageItem.vue';

// 聊天消息类型定义
interface ChatMessage {
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

// 聊天历史
interface ChatHistory {
  id: string;
  title: string;
  preview: string;
  lastUpdated: Date;
  messages: ChatMessage[];
}

// 模型定义
interface AIModel {
  id: string;
  name: string;
  description: string;
}

// 用户输入和UI状态
const userInput = ref('');
const chatBodyRef = ref<HTMLElement | null>(null);
const isThinking = ref(false);
const showModelSelect = ref(false);

// 聊天历史与当前活动聊天
const chatHistories = ref<ChatHistory[]>([]);
const activeChatId = ref('');
const activeChatMessages = ref<ChatMessage[]>([]);

// 模型选择
const currentModel = ref<AIModel>({
  id: 'gpt-4',
  name: '智能顾问 Pro',
  description: '高级运营风险顾问'
});

const availableModels = [
  {
    id: 'gpt-4',
    name: '智能顾问 Pro',
    description: '高级运营风险顾问'
  },
  {
    id: 'gpt-3.5',
    name: '智能顾问 Lite',
    description: '基础运营顾问'
  }
];

// 推荐问题
const suggestedQuestions = ref([
  '如何优化交通流量管理？',
  '道路维护最佳实践有哪些？',
  '高峰期交通拥堵解决方案',
  '如何提高交通标识的可视性？',
  '恶劣天气条件下的道路安全措施'
]);

// 开始新对话
const startNewChat = () => {
  const newChatId = Date.now().toString();
  const welcomeMessage = {
    text: '您好，我是运营风险管控智能顾问，请问有什么可以帮助您的？',
    sender: 'bot' as const,
    timestamp: new Date()
  };
  
  // 创建新聊天并设为活动
  chatHistories.value.push({
    id: newChatId,
    title: '新的对话',
    preview: welcomeMessage.text,
    lastUpdated: new Date(),
    messages: [welcomeMessage]
  });
  
  activeChatId.value = newChatId;
  activeChatMessages.value = [welcomeMessage];
  
  // 滚动到底部
  nextTick(() => {
    scrollToBottom();
  });
};

// 加载选中的聊天
const loadChat = (chatId: string) => {
  const chat = chatHistories.value.find(c => c.id === chatId);
  if (chat) {
    activeChatId.value = chatId;
    activeChatMessages.value = [...chat.messages];
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }
};

// 清空当前对话
const clearChat = () => {
  if (confirm('确定要清空当前对话吗？')) {
    const welcomeMessage = {
      text: '您好，我是运营风险管控智能顾问，请问有什么可以帮助您的？',
      sender: 'bot' as const,
      timestamp: new Date()
    };
    
    activeChatMessages.value = [welcomeMessage];
    
    // 更新聊天历史
    const chatIndex = chatHistories.value.findIndex(c => c.id === activeChatId.value);
    if (chatIndex !== -1) {
      chatHistories.value[chatIndex].messages = [...activeChatMessages.value];
      chatHistories.value[chatIndex].preview = welcomeMessage.text;
      chatHistories.value[chatIndex].lastUpdated = new Date();
    }
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }
};

// 发送消息
const sendMessage = async () => {
  if (!userInput.value.trim()) return;
  
  // 如果没有活动聊天，创建一个新的
  if (!activeChatId.value || activeChatMessages.value.length === 0) {
    startNewChat();
  }
  
  // 添加用户消息
  const userMessage = {
    text: userInput.value,
    sender: 'user' as const,
    timestamp: new Date()
  };
  
  activeChatMessages.value.push(userMessage);
  
  // 清空输入框
  const sentMessage = userInput.value;
  userInput.value = '';
  
  // 滚动到底部
  await nextTick();
  scrollToBottom();
  
  // 显示思考状态
  isThinking.value = true;
  
  // 模拟AI响应延迟
  setTimeout(() => {
    // 添加AI回复
    const botMessage = {
      text: generateResponse(sentMessage),
      sender: 'bot' as const,
      timestamp: new Date()
    };
    
    activeChatMessages.value.push(botMessage);
    
    // 更新聊天历史
    const chatIndex = chatHistories.value.findIndex(c => c.id === activeChatId.value);
    if (chatIndex !== -1) {
      chatHistories.value[chatIndex].messages = [...activeChatMessages.value];
      chatHistories.value[chatIndex].preview = sentMessage;
      chatHistories.value[chatIndex].lastUpdated = new Date();
      
      // 如果是第一条用户消息，更新标题
      if (chatHistories.value[chatIndex].title === '新的对话') {
        // 使用前20个字符作为标题
        chatHistories.value[chatIndex].title = sentMessage.length > 20 
          ? sentMessage.substring(0, 20) + '...'
          : sentMessage;
      }
    }
    
    // 关闭思考状态
    isThinking.value = false;
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }, 1500);
};

// 处理文件上传
const handleFileUpload = (file: File) => {
  // 这里可以处理文件上传逻辑
  console.log('上传的文件:', file.name);
  
  // 创建新对话或在当前对话中添加文件上传消息
  if (!activeChatId.value) {
    startNewChat();
  }
  
  // 添加文件上传消息
  activeChatMessages.value.push({
    text: `上传了文件: ${file.name}`,
    sender: 'user',
    timestamp: new Date()
  });
  
  // 模拟AI响应
  isThinking.value = true;
  
  setTimeout(() => {
    activeChatMessages.value.push({
      text: `我已收到您上传的文件 "${file.name}"。您想要我如何分析这个文件的内容？我可以帮您提取关键信息，或者回答关于文件内容的问题。`,
      sender: 'bot',
      timestamp: new Date()
    });
    
    isThinking.value = false;
    
    // 更新聊天历史
    const chatIndex = chatHistories.value.findIndex(c => c.id === activeChatId.value);
    if (chatIndex !== -1) {
      chatHistories.value[chatIndex].messages = [...activeChatMessages.value];
      chatHistories.value[chatIndex].preview = `上传了文件: ${file.name}`;
      chatHistories.value[chatIndex].lastUpdated = new Date();
    }
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }, 1500);
};

// 滚动到底部
const scrollToBottom = () => {
  if (chatBodyRef.value) {
    chatBodyRef.value.scrollTop = chatBodyRef.value.scrollHeight;
  }
};

// 选择推荐问题
const selectSuggestion = (question: string) => {
  userInput.value = question;
  sendMessage();
};

// 切换模型选择下拉框
const toggleModelSelect = () => {
  showModelSelect.value = !showModelSelect.value;
};

// 选择模型
const selectModel = (model: AIModel) => {
  currentModel.value = model;
  showModelSelect.value = false;
};

// 模拟AI响应生成（在实际应用中，这里应该调用后端API）
const generateResponse = (message: string): string => {
  // 根据用户输入关键词生成响应
  if (message.includes('交通流量') || message.includes('拥堵')) {
    return '优化交通流量管理的几种方法：\n\n1. **智能交通信号系统**：根据实时交通流量自动调整信号灯时间\n2. **分时段交通管制**：在高峰期实施单向通行或限行措施\n3. **交通流量预测**：利用历史数据和AI预测未来交通状况\n4. **智能路径规划**：为驾驶员提供最佳路线建议\n5. **公共交通优化**：增加公交专用道，提高公共交通吸引力\n\n您可以根据具体情况选择适合的策略组合实施。需要了解更多细节吗？';
  } else if (message.includes('道路维护') || message.includes('路面')) {
    return '道路维护最佳实践包括：\n\n1. **预防性维护**：定期检查和小规模修复，防止问题扩大\n2. **全生命周期管理**：从设计到施工到维护的整体规划\n3. **智能监测系统**：使用传感器实时监测路面状况\n4. **季节性维护计划**：根据不同季节制定专门的维护策略\n5. **快速响应机制**：对紧急问题建立快速响应流程\n\n这些方法可以显著延长道路使用寿命并降低长期维护成本。';
  } else if (message.includes('标识') || message.includes('可视性')) {
    return '提高交通标识可视性的方法：\n\n1. **反光材料升级**：使用高性能反光材料提高夜间可见度\n2. **合理尺寸设计**：根据道路类型和车速确定最佳标识尺寸\n3. **清晰的布局**：避免标识过度密集，确保信息清晰传达\n4. **定期清洁和维护**：保持标识清洁和完好\n5. **智能可变信息标识**：在特殊情况下提供动态信息\n\n这些措施不仅能提高交通安全，还能减少驾驶员的认知负担。';
  } else if (message.includes('天气') || message.includes('安全措施')) {
    return '恶劣天气下的道路安全措施：\n\n1. **智能预警系统**：根据气象数据提前发布预警信息\n2. **可变限速标志**：根据天气状况自动调整建议车速\n3. **防滑路面处理**：关键区域应用特殊路面材料\n4. **排水系统优化**：确保道路快速排水，防止积水\n5. **紧急避险区域**：设计安全的紧急停车或避险区域\n\n在实施这些措施的同时，加强驾驶员教育也非常重要。您需要针对特定天气类型的建议吗？';
  } else {
    return '感谢您的问题。作为运营风险管控智能顾问，我可以提供关于交通流量优化、道路维护、标识系统、安全措施等方面的建议。\n\n请问您想了解更具体的哪方面内容？';
  }
};

// 组件挂载后自动创建新聊天
onMounted(() => {
  // 创建新聊天以初始化界面
  startNewChat();
  
  // 点击页面其他区域关闭模型选择下拉框
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    if (!target.closest('.model-select') && !target.closest('.model-dropdown')) {
      showModelSelect.value = false;
    }
  });
});
</script>

<style scoped>
.operation-chat-interface {
  display: flex;
  height: 100%;
  background: linear-gradient(135deg, #001529, #002140);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
}

.interface-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

.history-sidebar {
  width: 280px;
  min-width: 280px;
  border-right: 1px solid rgba(0, 168, 255, 0.2);
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  z-index: 5;
}

.header-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 5px rgba(0, 168, 255, 0.5));
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  letter-spacing: 1px;
  flex: 1;
}

.header-status {
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 12px;
  color: white;
  margin-right: 15px;
}

.header-status.online {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.header-actions {
  position: relative;
}

.model-select {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(0, 33, 64, 0.7);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  color: #fff;
}

.model-select:hover {
  background: rgba(0, 58, 140, 0.7);
  border-color: rgba(0, 168, 255, 0.5);
}

.model-icon {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.dropdown-icon {
  width: 10px;
  height: 10px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 4px;
}

.model-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: rgba(0, 21, 41, 0.95);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  width: 200px;
  overflow: hidden;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.model-option {
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 1px solid rgba(0, 168, 255, 0.1);
}

.model-option:last-child {
  border-bottom: none;
}

.model-option:hover {
  background: rgba(0, 58, 140, 0.5);
}

.model-option.active {
  background: rgba(0, 168, 255, 0.2);
}

.model-option-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.model-option-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.chat-content {
  display: flex;
  flex: 1;
  height: calc(100% - 70px); /* Adjust based on header height */
  overflow: hidden;
}

.chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 168, 255, 0.5) rgba(0, 21, 41, 0.5);
  position: relative;
}

.chat-body::-webkit-scrollbar {
  width: 6px;
}

.chat-body::-webkit-scrollbar-track {
  background: rgba(0, 21, 41, 0.5);
}

.chat-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 168, 255, 0.5);
  border-radius: 3px;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chat-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 20px;
}

.empty-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12zM7 9h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
}

.empty-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
  background: linear-gradient(to right, #40a9ff, #096dd9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.empty-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin-bottom: 30px;
  line-height: 1.6;
}

.empty-examples {
  width: 100%;
  max-width: 600px;
}

.examples-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: rgba(255, 255, 255, 0.9);
}

.examples-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.example-item {
  background: rgba(0, 58, 140, 0.3);
  border: 1px solid rgba(0, 168, 255, 0.3);
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.example-item:hover {
  background: rgba(0, 58, 140, 0.5);
  border-color: rgba(0, 168, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 10px;
  background: rgba(0, 33, 64, 0.7);
  border-radius: 12px;
  width: fit-content;
  margin-top: 10px;
  margin-left: 60px;
}

.thinking-indicator .dot {
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  display: inline-block;
  animation: bounce 1.4s infinite ease-in-out both;
}

.thinking-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.thinking-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

.thinking-indicator span {
  font-size: 12px;
  color: #1890ff;
  margin-left: 5px;
}

@keyframes bounce {
  0%, 100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid rgba(0, 168, 255, 0.2);
  background: rgba(0, 21, 41, 0.7);
  padding: 15px;
  gap: 10px;
}

.chat-toolbar {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
}

.toolbar-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(0, 33, 64, 0.7);
  border: 1px solid rgba(0, 168, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toolbar-button:hover {
  background: rgba(0, 58, 140, 0.7);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.button-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.image-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'%3E%3C/path%3E%3C/svg%3E");
}

.voice-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.91-3c-.49 0-.9.36-.98.85C16.52 14.2 14.47 16 12 16s-4.52-1.8-4.93-4.15c-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14.49 3 2.89 5.35 5.91 5.78V20c0 .55.45 1 1 1s1-.45 1-1v-2.08c3.02-.43 5.42-2.78 5.91-5.78.1-.6-.39-1.14-1-1.14z'%3E%3C/path%3E%3C/svg%3E");
}

.clear-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z'/%3E%3C/svg%3E");
}

.chat-input-wrapper {
  flex: 1;
  position: relative;
  margin-bottom: 10px;
}

.chat-input {
  width: 100%;
  height: 150px;
  padding: 12px 15px;
  border-radius: 8px;
  border: 1px solid rgba(0, 168, 255, 0.3);
  background: rgba(0, 33, 64, 0.7);
  color: #ffffff;
  font-size: 14px;
  resize: none;
  outline: none;
  transition: all 0.3s ease;
}

.chat-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.send-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(45deg, #1890ff, #096dd9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  align-self: center;
}

.send-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.send-button:disabled {
  background: linear-gradient(45deg, #8c8c8c, #595959);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.send-icon {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M2.01 21L23 12 2.01 3 2 10l15 2-15 2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
</style> 
﻿<script setup lang="ts">
import { ref, provide, computed, onMounted, watch, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import ObjectDetectionPanel from './components/ObjectDetectionPanel.vue'
import ChatInterface from './components/ChatInterface.vue' // Added import
import OnlineMonitor from './components/OnlineMonitor.vue'; // Import the new component
import type { DetectionResult, HighRiskEvent } from './services/DetectionService'
import { detectObjects } from './services/DetectionService'
import OperationChatInterface from './components/OperationChatInterface.vue'
// 导入组件
import OperationDetectionPanel from './components/OperationDetectionPanel.vue';
import OperationMonitor from './components/OperationMonitor.vue';
import VideoMonitor from './components/VideoMonitor.vue'; // Import the VideoMonitor component


// Define detection options
interface DetectionOption {
  id: string;
  label: string;
  checked: boolean;
}

interface DetectionCategory {
  id: string;
  label: string;
  expanded: boolean;
  options: DetectionOption[];
}

// Detection options state
const constructionDetectionOptions = ref<DetectionCategory[]>([
  {
    id: 'person',
    label: '人员监测',
    expanded: true,
    options: [
      { id: 'person_count', label: '人员统计', checked: false },
      { id: 'person_wear', label: '人员穿戴', checked: false },
      { id: 'person_status', label: '人员状态', checked: false },
      { id: 'hoisting_personnel', label: '吊装作业人员', checked: false },
      { id: 'high_altitude_personnel', label: '高空作业人员', checked: false }
    ]
  },
  {
    id: 'machine',
    label: '机械监测',
    expanded: true,
    options: [
      { id: 'vehicle_count', label: '车辆统计', checked: false },
      { id: 'vehicle_status', label: '车辆状态', checked: false },
      { id: 'equipment_status', label: '机械运行状态', checked: false },
      { id: 'hoisting_status', label: '吊装状态', checked: false }
    ]
  },
  {
    id: 'material',
    label: '物料监测',
    expanded: true,
    options: [
      { id: 'subgrade_monitoring', label: '路基监测', checked: false },
      { id: 'slope_monitoring', label: '边坡监测', checked: false },
      { id: 'pavement_monitoring', label: '路面监测', checked: false }
    ]
  },
  {
    id: 'regulation',
    label: '法规监测',
    expanded: true,
    options: [
      { id: 'operation_area_protection', label: '作业区防护', checked: false }
    ]
  },
  {
    id: 'environment',
    label: '环境监测',
    expanded: true,
    options: [
      { id: 'fire_detection', label: '火情监测', checked: false },
      { id: 'smoke_detection', label: '烟雾监测', checked: false }
    ]
  }
]);

// 运营平台检测选项
const operationDetectionOptions = ref<DetectionCategory[]>([
  {
    id: 'road_structure',
    label: '路面结构风险',
    expanded: true,
    options: [
      { id: 'pavement_damage', label: '路面损坏检测', checked: false },
      { id: 'road_cracks', label: '路面裂缝检测', checked: false },
      { id: 'potholes', label: '坑洼检测', checked: false },
      { id: 'water_accumulation', label: '积水检测', checked: false },
      { id: 'road_subsidence', label: '路面沉降检测', checked: false },
      { id: 'roadside_damage', label: '路侧损坏检测', checked: false },
      { id: 'online_monitoring', label: '在线监控模式', checked: false }
    ]
  },
  {
    id: 'vehicle_accident',
    label: '车辆事故风险',
    expanded: true,
    options: [
      { id: 'collision_risk', label: '碰撞风险检测', checked: false },
      { id: 'emergency_braking', label: '紧急制动检测', checked: false },
      { id: 'traffic_congestion', label: '交通拥堵检测', checked: false },
      { id: 'roadside_parking', label: '路侧停车检测', checked: false },
      { id: 'accident_scene', label: '事故现场检测', checked: false },
      { id: 'online_monitoring_vehicle', label: '在线监控模式', checked: false }
    ]
  },
  {
    id: 'abnormal_driving',
    label: '异常驾驶行为',
    expanded: true,
    options: [
      { id: 'speeding', label: '超速驾驶检测', checked: false },
      { id: 'lane_departure', label: '车道偏离检测', checked: false },
      { id: 'illegal_turning', label: '违规转弯检测', checked: false },
      { id: 'phone_use', label: '驾驶分心检测', checked: false },
      { id: 'fatigue_driving', label: '疲劳驾驶检测', checked: false },
      { id: 'signal_violation', label: '信号违规检测', checked: false },
      { id: 'online_monitoring_driving', label: '在线监控模式', checked: false }
    ]
  }
]);

// 根据当前平台切换检测选项
const detectionOptions = computed(() => {
  return currentPlatform.value === 'construction' ? constructionDetectionOptions.value : operationDetectionOptions.value;
});

// 定义风险监测类别
const constructionRiskCategories = [
  {
    id: 'all',
    label: '全部风险监测',
    options: ['person_count', 'person_wear', 'person_status', 'hoisting_personnel', 'high_altitude_personnel',
              'vehicle_count', 'vehicle_status', 'equipment_status', 'hoisting_status',
              'subgrade_monitoring', 'slope_monitoring', 'pavement_monitoring',
              'operation_area_protection',
              'fire_detection', 'smoke_detection']
  },
  {
    id: 'general',
    label: '通用风险监测',
    options: ['person_count', 'person_wear', 'person_status',
              'vehicle_count', 'vehicle_status', 'equipment_status',
              'fire_detection', 'smoke_detection']
  },
  {
    id: 'subgrade',
    label: '路基风险监测',
    options: ['person_count', 'person_wear', 'person_status',
              'vehicle_count', 'vehicle_status', 'equipment_status',
              'fire_detection', 'smoke_detection',
              'subgrade_monitoring', 'slope_monitoring']
  },
  {
    id: 'pavement',
    label: '路面风险监测',
    options: ['person_count', 'person_wear', 'person_status',
              'vehicle_count', 'vehicle_status', 'equipment_status',
              'fire_detection', 'smoke_detection',
              'pavement_monitoring']
  },
  {
    id: 'bridge',
    label: '桥梁风险监测',
    options: ['person_count', 'person_wear', 'person_status',
              'vehicle_count', 'vehicle_status', 'equipment_status',
              'fire_detection', 'smoke_detection',
              'hoisting_personnel', 'high_altitude_personnel',
              'hoisting_status',
              'operation_area_protection']
  }
];

// 定义运营风险监测类别
const operationRiskCategories = [
  {
    id: 'road_structure',
    label: '路面结构风险监测',
    options: ['pavement_damage', 'road_cracks', 'potholes', 'water_accumulation',
              'road_subsidence', 'roadside_damage', 'online_monitoring']
  },
  {
    id: 'vehicle_accident',
    label: '车辆事故风险',
    options: ['collision_risk', 'emergency_braking', 'traffic_congestion',
              'roadside_parking', 'accident_scene', 'online_monitoring_vehicle']
  },
  {
    id: 'abnormal_driving',
    label: '异常驾驶行为',
    options: ['speeding', 'lane_departure', 'illegal_turning',
              'phone_use', 'fatigue_driving', 'signal_violation', 'online_monitoring_driving']
  }
];

// 根据当前平台获取对应的风险类别
const riskCategories = computed(() => {
  return currentPlatform.value === 'construction' ? constructionRiskCategories : operationRiskCategories;
});

// 当前选中的导航类别
const selectedCategory = ref('all');

// 根据选中的导航类别过滤检测选项
const filteredDetectionOptions = computed(() => {
  return detectionOptions.value;
});

// 选择导航类别
const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId;

  // 找到对应的风险类别
  const category = riskCategories.value.find(cat => cat.id === categoryId);
  if (category) {
    // 获取当前平台的检测选项
    const currentDetectionOptions = detectionOptions.value;

    // 重置所有选项
    currentDetectionOptions.forEach(detCat => {
      detCat.options.forEach(opt => {
        opt.checked = false;
      });
    });

    // 根据类别选中对应选项
    category.options.forEach(optId => {
      currentDetectionOptions.forEach(detCat => {
        const option = detCat.options.find(opt => opt.id === optId);
        if (option) {
          option.checked = true;
        }
      });
    });
  }

  // 重置图像预览和检测结果
  resetUIState();
};

// 重置UI状态
function resetUIState() {
  imagePreview.value = '';
  imageFile.value = null;
  detectionResults.value = null;
  error.value = '';

  // 重置处理后图像状态
  isShowingProcessedImage.value = false;
  originalImageData.value = '';

  // 重置检测框的显示状态
  showHighRiskBoxes.value = true;
  showLowRiskBoxes.value = true;
  showPersonBoxes.value = false;
  showVehicleBoxes.value = false;
  showMachineBoxes.value = false;

  // 清除在线监控定时器
  if (onlineMonitoringInterval.value) {
    clearInterval(onlineMonitoringInterval.value);
    onlineMonitoringInterval.value = null;
  }

  // 清除Canvas
  setTimeout(() => {
    const canvas = document.getElementById('detection-canvas') as HTMLCanvasElement;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  }, 100);
}

// 计算当前选中的选项
const selectedOptions = computed(() => {
  const options: string[] = [];
  detectionOptions.value.forEach(category => {
    // 添加类别ID作为一个选项
    options.push(category.id);

    // 添加所有选中的子选项
    category.options.forEach(option => {
      if (option.checked) {
        options.push(option.id);
      }
    });
  });
  return options;
});

// Toggle category expansion
const toggleCategory = (categoryId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    category.expanded = !category.expanded;
  }
};

// Toggle option selection
const toggleOption = (categoryId: string, optionId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    const option = category.options.find(opt => opt.id === optionId);
    if (option) {
      option.checked = !option.checked;

      // 只有在切换在线监控相关选项时才重置UI状态
      if (optionId === 'online_monitoring' ||
          optionId === 'online_monitoring_vehicle' ||
          optionId === 'online_monitoring_driving') {
        resetUIState();
      }
    }
  }
};

// Image upload and preview
const imagePreview = ref<string>('');
const imageFile = ref<File | null>(null);
const isLoading = ref(false);
const error = ref('');

// 处理图片上传
function handleImageUpload(event: Event) {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    imageFile.value = file;

    // 重置处理后图像状态
    isShowingProcessedImage.value = false;
    originalImageData.value = '';

    // 创建预览URL
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target) {
        imagePreview.value = e.target.result as string;
      }
    };
    reader.readAsDataURL(file);

    // 重置检测结果
    detectionResults.value = null;
    error.value = '';

    // 清除画布上的检测框
    const canvas = document.getElementById('detection-canvas') as HTMLCanvasElement;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  }
}

// Detection results
const detectionResults = ref<DetectionResult | null>(null);
const showDetectionBoxes = ref(true);
const showHighRiskBoxes = ref(true);
const showLowRiskBoxes = ref(true);
const showPersonBoxes = ref(false); // 初始化为false，需要点击才显示
const showVehicleBoxes = ref(false); // 初始化为false，需要点击才显示
const showMachineBoxes = ref(false); // 初始化为false，需要点击才显示
const updateDetectionResults = (results: DetectionResult) => {
  console.log('更新检测结果:', results);
  console.log('检测到的对象数量:', results.objects.length);
  console.log('描述字段:', results.description);
  console.log('高风险事件:', results.high_risk_events || []);

  // 检查是否有处理后的图像
  const processedUrl = results.visualized_image_url || results.processed_url;
  if (processedUrl) {
    console.log('检测到处理后的图像:', processedUrl);

    // 保存当前显示的图像作为原始图像（如果尚未保存）
    if (!originalImageData.value) {
      originalImageData.value = imagePreview.value;
      console.log('已保存原始图像数据');
    }

    // 立即更新UI显示处理后的图像
    const cacheBuster = new Date().getTime();
    imagePreview.value = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
    isShowingProcessedImage.value = true;
    console.log('已更新UI显示处理后的图像');

    // 在在线监控模式下，即使检测到处理后的图像，也继续监控
    if (onlineMonitoringInterval.value) {
      console.log('检测到处理后图像，但在线监控模式继续运行');
      // 不中断监控循环，允许继续运行
    }
  }

  detectionResults.value = results;
};

// 高风险事件总数
const totalHighRiskEvents = computed(() => {
  if (!detectionResults.value || !detectionResults.value.high_risk_events) {
    return 0;
  }
  return detectionResults.value.high_risk_events.length;
});

// 提供检测结果给子组件
provide('detectionResults', detectionResults);
provide('showDetectionBoxes', showDetectionBoxes);
provide('updateDetectionResults', updateDetectionResults);

// 监听检测结果变化，自动绘制检测框
watch(() => detectionResults.value, (newValue) => {
  if (newValue) {
    console.log('检测结果更新，是否有处理后的图像:', !!newValue.processed_url, !!newValue.visualized_image_url);

    // 如果有处理后的图像URL，并且尚未显示处理后的图像，则替换原始图像
    if (!isShowingProcessedImage.value) {
      const processedUrl = newValue.visualized_image_url || newValue.processed_url;
      if (processedUrl) {
        console.log('使用处理后的图像替换原始图像:', processedUrl);

        // 保存原始图像（如果尚未保存）
        if (!originalImageData.value) {
          originalImageData.value = imagePreview.value;
        }

        // 更新imagePreview以显示处理后的图像
        const cacheBuster = new Date().getTime();
        imagePreview.value = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
        isShowingProcessedImage.value = true;

        // 显示处理后的图像时启用检测框（因为处理后的图像已经包含了检测结果）
        showDetectionBoxes.value = true;
        return;
      }
    }

    // 如果没有处理后的图像，并且启用了检测框，则使用常规的绘制方法
    if (showDetectionBoxes.value && !isShowingProcessedImage.value) {
      drawDetectionBoxes(newValue.objects);
    }
  }
}, { deep: true });

// 监视图像预览的变化
watch(() => imagePreview.value, (newValue) => {
  if (newValue && detectionResults.value && showDetectionBoxes.value) {
    // 如果图像已经是处理后的图像，不需要再绘制检测框
    if (newValue.includes('processed') || newValue.includes('_processed.jpg')) {
      console.log('图像已是处理后的图像，不再绘制检测框');
      return;
    }
    drawDetectionBoxes(detectionResults.value!.objects);
  }
});

// 执行对象检测
async function performDetection() {
  // 检查是否有图像
  if (!imagePreview.value) {
    error.value = '请先上传图像';
    return;
  }

  try {
    // 设置加载状态
    isLoading.value = true;
    error.value = '';

    // 设置检测框的默认显示状态 - 默认只显示风险事件，隐藏普通类别
    showHighRiskBoxes.value = true;
    showLowRiskBoxes.value = true;
    showPersonBoxes.value = false;
    showVehicleBoxes.value = false;
    showMachineBoxes.value = false;

    // 获取当前选择的模型ID
    const modelId = currentModel.value.id === 'pro' ? 'qwen2.5-vl-72b-instruct' : 'qwen2.5-vl-7b-instruct';
    console.log(`使用模型: ${currentModel.value.name}, model_id: ${modelId}`);

    // 生成自定义提示词
    const customPrompt = generatePrompt();

    // 直接使用App.vue中的检测逻辑
    console.log('开始检测，选项:', selectedOptions.value);
    console.log('使用图片:', imagePreview.value.substring(0, 50) + '...');
    console.log('使用自定义提示词:', customPrompt);

    // 构建FormData对象
    const formData = new FormData();
    formData.append('image', imagePreview.value);

    // 将选中的选项添加到FormData
    selectedOptions.value.forEach(option => {
      formData.append('options[]', option);
    });

    // 添加提示词到FormData
    formData.append('prompt', customPrompt);

    // 根据当前平台和选中的风险类别决定API端点
    let apiEndpoint = '/detect';
    let results;
    let isOnlineMonitoring = false;

    // 检查是否为"运营风险识别与管控大模型平台"下的各种风险监测
    if (currentPlatform.value === 'operation') {
      // 检查路面结构风险监测
      if (selectedCategory.value === 'road_structure' ||
          (selectedOptions.value.includes('pavement_damage') ||
           selectedOptions.value.includes('road_cracks') ||
           selectedOptions.value.includes('potholes') ||
           selectedOptions.value.includes('water_accumulation') ||
           selectedOptions.value.includes('road_subsidence') ||
           selectedOptions.value.includes('roadside_damage'))) {

        // 检查是否选择了在线监控选项
        isOnlineMonitoring = selectedOptions.value.includes('online_monitoring');

        // 根据是否为在线监控选择不同的API端点
        apiEndpoint = isOnlineMonitoring ? '/online-monitor-road-defects' : '/detect-road-defects';
        console.log(`使用路面结构风险监测API: ${apiEndpoint}${isOnlineMonitoring ? ' (在线监控模式)' : ''}`);
      }
      // 检查车辆事故风险监测
      else if (selectedCategory.value === 'vehicle_accident' ||
               (selectedOptions.value.includes('collision_risk') ||
                selectedOptions.value.includes('emergency_braking') ||
                selectedOptions.value.includes('traffic_congestion') ||
                selectedOptions.value.includes('roadside_parking') ||
                selectedOptions.value.includes('accident_scene'))) {

        // 检查是否选择了在线监控选项
        isOnlineMonitoring = selectedOptions.value.includes('online_monitoring_vehicle');

        // 根据是否为在线监控选择不同的API端点
        apiEndpoint = isOnlineMonitoring ? '/online-monitor-vehicle-accidents' : '/detect-vehicle-accidents';
        console.log(`使用车辆事故风险监测API: ${apiEndpoint}${isOnlineMonitoring ? ' (在线监控模式)' : ''}`);
      }
      // 检查异常驾驶行为监测
      else if (selectedCategory.value === 'abnormal_driving' ||
               (selectedOptions.value.includes('speeding') ||
                selectedOptions.value.includes('lane_departure') ||
                selectedOptions.value.includes('illegal_turning') ||
                selectedOptions.value.includes('phone_use') ||
                selectedOptions.value.includes('fatigue_driving') ||
                selectedOptions.value.includes('signal_violation'))) {

        // 检查是否选择了在线监控选项
        isOnlineMonitoring = selectedOptions.value.includes('online_monitoring_driving');

        // 根据是否为在线监控选择不同的API端点
        apiEndpoint = isOnlineMonitoring ? '/online-monitor-abnormal-driving' : '/detect-abnormal-driving';
        console.log(`使用异常驾驶行为监测API: ${apiEndpoint}${isOnlineMonitoring ? ' (在线监控模式)' : ''}`);
      }

      // 针对所有风险监测类别的请求处理
      if (apiEndpoint !== '/detect') {
        // 发送请求到选定的风险监测API端点
        const response = await fetch(`http://localhost:8000${apiEndpoint}`, {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        // 处理DINO和qwen-vl组合检测结果
        const formattedResult = {
          objects: [],
          description: result.qwen_explanation || "风险检测完成",
          high_risk_events: [],
          low_risk_events: [],
          input_width: result.input_width || 1024,
          input_height: result.input_height || 1024,
          timestamp: result.timestamp,     // 添加时间戳，在线监控模式会返回
          stream_id: result.stream_id,      // 添加流ID，在线监控模式会返回
          visualized_image_url: result.visualized_image_url || null, // Ensure this is passed
          processed_url: result.processed_url || null             // Ensure this is passed
        };

        // 将DINO检测结果转换为标准格式
        if (result.dino_detections && Array.isArray(result.dino_detections)) {
          result.dino_detections.forEach(detection => {
            const obj = {
              category: detection.category || "检测对象",
              event: detection.category || "检测对象",
              risk_level: detection.risk_level || "high", // 默认为高风险
              confidence: detection.score || 0.9,
              bbox_2d: detection.bbox || [0, 0, 0, 0],
              label: detection.label || detection.category || "检测对象"
            };

            formattedResult.objects.push(obj);

            // 根据风险等级分类
            if (obj.risk_level === "high") {
              formattedResult.high_risk_events.push(obj);
            } else if (obj.risk_level === "low") {
              formattedResult.low_risk_events.push(obj);
            }
          });
        }

        results = formattedResult;

        // 如果是在线监控模式，并且当前不在"图片检测"模式下 (currentOption.value !== 'detection')，才设置定时刷新
        if (isOnlineMonitoring && !onlineMonitoringInterval.value && currentOption.value !== 'detection') {
          console.log('启动在线监控模式（非图片检测模式），每5秒自动刷新');
          onlineMonitoringInterval.value = setInterval(() => {
            console.log('在线监控自动刷新...');
            performDetection();
          }, 5000); // 每5秒刷新一次
        }
      } else {
        // 非特定风险监测模式，清除可能存在的定时器
        if (onlineMonitoringInterval.value) {
          console.log('退出在线监控模式');
          clearInterval(onlineMonitoringInterval.value);
          onlineMonitoringInterval.value = null;
        }

        // 使用标准对象检测API
        results = await detectObjects(imagePreview.value, selectedOptions.value, customPrompt, modelId);
      }
    } else {
      // 非运营风险监测平台，清除可能存在的定时器
      if (onlineMonitoringInterval.value) {
        console.log('退出在线监控模式');
        clearInterval(onlineMonitoringInterval.value);
        onlineMonitoringInterval.value = null;
      }

      // 使用标准对象检测API
      results = await detectObjects(imagePreview.value, selectedOptions.value, customPrompt, modelId);
    }

    // 更新检测结果
    updateDetectionResults(results);
    console.log('检测完成，结果:', results);
    console.log('高风险事件:', results.high_risk_events || []);
    console.log('低风险事件:', results.low_risk_events || []);

    // 检查是否有处理后的图像 - 如果updateDetectionResults已经处理了图像，这里不需要额外操作
    const processedUrl = results.visualized_image_url || results.processed_url;
    if (processedUrl) {
      console.log('检测已完成，处理后的图像已显示');
      // 设置检测框为显示状态，以便在有处理后图像URL时显示处理后的图像
      showDetectionBoxes.value = true;

      // 在在线监控模式下，即使检测到处理后的图像，也继续监控
      if (isOnlineMonitoring && onlineMonitoringInterval.value) {
        console.log('检测到处理后图像，但在线监控模式继续运行');
        // 不中断监控循环，允许继续运行
      }
    }
    // 只有当没有处理后的图像时，才需要绘制检测框
    else if (showDetectionBoxes.value) {
      console.log('绘制检测框，默认只显示高风险和低风险事件');
      setTimeout(() => {
        // 传递空数组，让drawDetectionBoxes函数只从事件列表中获取对象
        drawDetectionBoxes([]);
      }, 100);
    } else {
      console.log('检测框已隐藏');
    }
  } catch (err: any) {
    console.error('检测失败:', err);
    error.value = err.message || '检测失败，请重试';
    detectionResults.value = null;
  } finally {
    // 重置加载状态
    isLoading.value = false;
  }
}

// 根据选中的监测内容生成提示词
function generatePrompt() {
  // 提示词映射表
  const promptMap: Record<string, string> = {
    // 人员监测
    'person_count': '检测并统计图像中的所有人员，用蓝色框标注。',
    'person_wear': '检测人员安全装备穿戴情况，包括安全帽、反光背心等，未佩戴安全帽或反光背心的人员标记为高风险（红色框）。',
    'person_status': '检测人员工作状态，包括站立、行走、作业等，识别异常姿势如躺卧（标记为高风险）。',
    'hoisting_personnel': '检测吊装作业人员位置，评估是否存在安全风险，高风险情况用红色框标注。',
    'high_altitude_personnel': '检测高空作业人员，评估是否存在坠落风险，高风险情况用红色框标注。',

    // 机械监测
    'vehicle_count': '检测并统计图像中的所有车辆，用深蓝色框标注。',
    'vehicle_status': '检测车辆状态，包括行驶、停放等，识别危险驾驶行为（标记为高风险）。',
    'equipment_status': '检测机械设备运行状态，包括起重机、挖掘机等，用橙色框标注，识别不安全操作（标记为高风险）。',
    'hoisting_status': '检测吊装作业状态，评估是否存在安全风险，高风险情况用红色框标注。',

    // 物料监测
    'subgrade_monitoring': '检测路基状态，识别塌陷、裂缝等异常情况（标记为高风险）。',
    'slope_monitoring': '检测边坡状态，识别滑坡、崩塌等异常情况（标记为高风险）。',
    'pavement_monitoring': '检测路面状态，识别裂缝、坑洼等异常情况（标记为高风险）。',

    // 法规监测
    'operation_area_protection': '检测作业区防护措施，包括警示标志、隔离设施等，缺失防护措施的区域标记为高风险。',

    // 环境监测
    'fire_detection': '检测火情，包括明火、烟雾等，标记为高风险。',
    'smoke_detection': '检测烟雾，评估是否存在火灾风险，标记为高风险。',

    // 路面结构风险
    'pavement_damage': '检测路面损坏区域，包括路面破碎、脱落、老化等情况，标记为高风险。',
    'road_cracks': '检测路面裂缝，包括纵向裂缝、横向裂缝、网状裂缝等，分析裂缝宽度、深度和分布情况。',
    'potholes': '检测路面坑洼，分析坑洼大小、深度和分布情况，评估对行车安全的影响程度。',
    'water_accumulation': '检测路面积水区域，分析积水原因（如排水不畅、路面下沉等）和对道路使用的影响。',
    'road_subsidence': '检测路面沉降区域，分析沉降程度、范围和潜在风险，评估对行车安全的影响。',
    'roadside_damage': '检测路侧损坏情况，包括护栏损坏、路肩塌陷、边沟堵塞等情况。',
    'online_monitoring': '启用在线监控模式，实时分析路面状况变化，检测新出现的缺陷并标记优先处理级别。',
    'online_monitoring_vehicle': '启用车辆事故风险在线监控模式，实时监测碰撞风险、紧急制动、交通拥堵等异常情况，提供实时预警。',
    'online_monitoring_driving': '启用异常驾驶行为在线监控模式，实时检测超速、车道偏离、违规转弯等行为，提供实时驾驶风险评估。'
  };

  // 收集选中选项的提示词
  const selectedPrompts: string[] = [];

  // 遍历所有检测类别
  detectionOptions.value.forEach(category => {
    // 遍历每个类别中的选项
    category.options.forEach(option => {
      // 如果选项被选中且在映射表中存在
      if (option.checked && promptMap[option.id]) {
        selectedPrompts.push(promptMap[option.id]);
      }
    });
  });

  // 检查是否为"运营风险识别与管控大模型平台"下的"路面结构风险监测"
  if (currentPlatform.value === 'operation' &&
      (selectedCategory.value === 'road_structure' ||
       (selectedOptions.value.includes('pavement_damage') ||
        selectedOptions.value.includes('road_cracks') ||
        selectedOptions.value.includes('potholes') ||
        selectedOptions.value.includes('water_accumulation') ||
        selectedOptions.value.includes('road_subsidence') ||
        selectedOptions.value.includes('roadside_damage')))) {

    // 针对路面结构风险监测的专用提示词
    return `
    请详细分析图像中的路面结构风险，重点关注:

    1. 路面裂缝：
       - 检测图像中的裂缝位置和类型（纵向、横向、网状裂缝等）
       - 分析裂缝的严重程度（微裂、中度裂缝、严重裂缝）
       - 标记裂缝的分布范围及潜在扩展趋势
       - 评估裂缝对道路使用安全的影响程度

    2. 坑洼：
       - 检测图像中的坑洼位置
       - 分析坑洼大小、深度及形态特征
       - 评估坑洼对车辆行驶的影响程度和安全风险
       - 确定坑洼的修复优先级

    3. 积水：
       - 识别图像中的路面积水区域
       - 分析积水原因（如排水不畅、路面下沉等）
       - 评估积水对路面结构的侵害和行车安全的影响
       - 提出积水问题的解决建议

    4. 路面损坏：
       - 检测沥青脱落、路面拥包、接缝损坏等其他类型的损坏
       - 分析损坏区域的结构特征和成因
       - 评估对道路使用寿命和安全性的影响
       - 提出修复建议

    5. 路面沉降：
       - 检测路面不均匀沉降区域
       - 分析沉降程度、范围和潜在风险
       - 评估对行车安全的影响
       - 建议适当的修复方法

    6. 路侧设施：
       - 检测护栏损坏、路肩塌陷、边沟堵塞等情况
       - 评估对整体道路安全的影响

    为每个检测到的缺陷提供：
    - 风险等级评估(高/中/低)，根据对道路使用安全的影响程度
    - 准确的位置标记（使用边界框标注）
    - 缺陷类型的明确描述
    - 修复的紧急程度建议

    最后，请总结路面整体状况，提供维护管理建议，包括：
    - 路面结构总体评级（优/良/中/差）
    - 潜在风险的发展趋势预测
    - 维修优先级排序建议
    - 长期养护管理建议
    `;
  }

  // 如果没有选中任何选项，返回默认提示词
  if (selectedPrompts.length === 0) {
    return `
    1. 详细分析图像中的安全风险，重点关注以下几类目标：
       - 人员：识别所有人员，用蓝色框标注，特别关注是否佩戴安全帽、反光背心的工人（标记为高风险），摔倒的工人（标记为高风险）
       - 车辆：识别所有车辆，用深蓝色框标注，特别关注危险驾驶行为（标记为高风险）
       - 机械：识别所有机械设备，用绿色框标注，特别关注不安全操作（标记为高风险）

    2. 对每个检测到的目标，提供以下信息：
       - 类别：仅限于人员/车辆/机械三种
       - 事件：具体事件描述，如worker_no_helmet（未佩戴安全帽）
       - 风险等级：高风险(high)/低风险(low)/安全(safe)
         * 高风险(high)：如未佩戴安全帽、摔倒的工人、危险驾驶、不安全操作等
         * 低风险(low)：如轻微违规但不会立即导致事故的情况
         * 安全(safe)：如正常工作的工人、正常停放的车辆等
       - 置信度：0-1之间的数值
       - 边界框坐标：bbox_2d格式为[x1, y1, x2, y2]
       - 标签：用于显示的文本标签

    3. 提供图像的整体安全风险分析和管控建议，使用Markdown格式进行分段。

    4. 识别并分别列出图像中的高风险事件和低风险事件，注意安全(safe)的项目不应包含在风险事件中。

    返回格式示例如下，仅供参考：
    {
      "detections": [
        {
          "category": "人员",
          "event": "未佩戴安全帽的工人",
          "risk_level": "high",
          "confidence": 0.95,
          "bbox_2d": ,
          "label": "未佩戴安全帽的工人"
        },
        {
          "category": "机械",
          "event": "不安全操作的机械",
          "risk_level": "high",
          "confidence": 0.87,
          "bbox_2d": ,
          "label": "不安全操作的机械"
        },
        {
          "category": "车辆",
          "event": "停放的车辆",
          "risk_level": "safe",
          "confidence": 0.89,
          "bbox_2d": ,
          "label": "停放的车辆"
        },
        {
          "category": "火灾",
          "event": "火灾",
          "risk_level": "high",
          "confidence": 0.89,
          "bbox_2d": ,
          "label": "火灾"
        }
      ],
      "description": "#### 风险分析\\n这是一个XX场景，该XX存在安全隐患：X名工人XX，X台吊车正在XX。\\n\\n#### 管控建议\\n建议立即要求所有人员佩戴安全帽，并确保吊车操作符合安全规范。",
      "high_risk_events": [
        {
          "category": "人员",
          "event": "未佩戴安全帽的工人",
          "risk_level": "high",
          "confidence": 0.95,
          "bbox_2d":
        },
        {
          "category": "火灾",
          "event": "火灾",
          "risk_level": "high",
          "confidence": 0.89,
          "bbox_2d": ,
          "label": "火灾"
        },
        {
          "category": "机械",
          "event": ,
          "risk_level": "high",
          "confidence": 0.87,
          "bbox_2d":
        }
      ],
      "low_risk_events": []
    }
    `;
  }

  // 组合所有选中的提示词
  const combinedPrompt = `
  请详细分析图像中的安全风险，重点关注以下几个方面：
  ${selectedPrompts.map((prompt, index) => `${index + 1}. ${prompt}`).join('\n  ')}

  此外，请确保：
  1. 对所有人员使用蓝色框标注，对所有车辆使用紫色框标注，对所有机械设备使用绿色框标注
  2. 对高风险事件使用红色框标注，对低风险事件使用黄色框标注
  3. 类别包括：人员、机械（车辆：轿车、吊车、挖掘机、推土机，设备：塔吊、搅拌机等）、物料（包括钢筋、混凝土、木材等）、法规（包括安全帽、反光背心、警示标志等）、环境（包括火灾、烟雾等）
  4. 风险等级分为：高风险(high)、低风险(low)、安全(safe)三种
     - 高风险：如未佩戴安全帽、摔倒的工人、危险驾驶、不安全操作等
     - 低风险：如轻微违规但不会立即导致事故的情况
     - 安全：如正常工作的工人、正常停放的车辆等（这些不应包含在风险事件中）
  5. 提供每个检测目标的类别、事件描述、风险等级、置信度、边界框坐标和显示标签
  6. 提供图像的整体安全风险分析和管控建议，使用Markdown格式进行分段。

  请以JSON格式输出所有检测结果，格式示例如下，仅供参考：
  {
    "detections": [
        {
          "category": "人员",
          "event": "未佩戴安全帽的工人",
          "risk_level": "high",
          "confidence": 0.95,
          "bbox_2d": ,
          "label": "未佩戴安全帽的工人"
        },
        {
          "category": "机械",
          "event": "不安全操作的机械",
          "risk_level": "high",
          "confidence": 0.87,
          "bbox_2d": ,
          "label": "不安全操作的机械"
        },
        {
          "category": "车辆",
          "event": "停放的车辆",
          "risk_level": "safe",
          "confidence": 0.89,
          "bbox_2d": ,
          "label": "停放的车辆"
        },
        {
          "category": "环境",
          "event": "火灾",
          "risk_level": "high",
          "confidence": 0.89,
          "bbox_2d": ,
          "label": "火灾"
        }
    ],
    ],
      "description": "#### 风险分析\\n这是一个XX场景，该XX存在安全隐患：X名工人XX，X台吊车正在XX。\\n\\n#### 管控建议\\n建议立即要求所有人员佩戴安全帽，并确保吊车操作符合安全规范。",
    "high_risk_events": [
        {
          "category": "人员",
          "event": "未佩戴安全帽的工人",
          "risk_level": "high",
          "confidence": 0.95,
          "bbox_2d":
        },
        {
          "category": "环境",
          "event": "火灾",
          "risk_level": "high",
          "confidence": 0.89,
          "bbox_2d": ,
          "label": "火灾"
        },
        {
          "category": "机械",
          "event": ,
          "risk_level": "high",
          "confidence": 0.87,
          "bbox_2d":
        }
    ],
    "low_risk_events": []
  }
  `;

  return combinedPrompt;
}

// 绘制检测框
function drawDetectionBoxes(objects: any[]) {
  // 如果图像预览已经是处理后的图像，不需要再绘制检测框
  if (imagePreview.value.includes('processed') || imagePreview.value.includes('_processed.jpg')) {
    console.log('图像已经是处理后的图像，跳过绘制检测框');
    return;
  }

  const canvasElement = document.getElementById('detection-canvas') as HTMLCanvasElement;

  // Clear existing boxes first
  if (canvasElement) {
    const ctx = canvasElement.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);
    }
  }

  // 如果没有canvas或检测结果，直接返回
  if (!canvasElement || !detectionResults.value) {
    return;
  }

  // 使用分割可视化图像（如果有）
  if (detectionResults.value.visualized_image_url) {
    console.log('检测到分割可视化图像，使用掩码图像显示:', detectionResults.value.visualized_image_url);

    const ctx = canvasElement.getContext('2d');
    if (!ctx) {
      console.error('无法获取canvas上下文');
      return;
    }

    const segImageElement = new Image();
    const cacheBuster = new Date().getTime();

    segImageElement.onload = () => {
      if (ctx && canvasElement) {
        ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);
        ctx.drawImage(segImageElement, 0, 0, canvasElement.width, canvasElement.height);
      }
    };

    segImageElement.onerror = () => {
      console.error('加载分割可视化图像失败，使用常规检测框');
      // 如果分割图像加载失败，回退到常规检测框
      drawRegularDetectionBoxes(objects);
    };

    segImageElement.src = `http://localhost:8000${detectionResults.value.visualized_image_url}?t=${cacheBuster}`;
    return;
  }

  // 使用处理后的图像（如果有）
  if (detectionResults.value.processed_url) {
    console.log('检测到处理后的图像，使用处理后图像显示:', detectionResults.value.processed_url);

    const ctx = canvasElement.getContext('2d');
    if (!ctx) {
      console.error('无法获取canvas上下文');
      return;
    }

    const processedImageElement = new Image();
    const cacheBuster = new Date().getTime();

    processedImageElement.onload = () => {
      if (ctx && canvasElement) {
        ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);
        ctx.drawImage(processedImageElement, 0, 0, canvasElement.width, canvasElement.height);
      }
    };

    processedImageElement.onerror = () => {
      console.error('加载处理后图像失败，使用常规检测框');
      // 如果处理后图像加载失败，回退到常规检测框
      drawRegularDetectionBoxes(objects);
    };

    processedImageElement.src = `http://localhost:8000${detectionResults.value.processed_url}?t=${cacheBuster}`;
    return;
  }

  // 如果没有特殊处理的图像，使用常规检测框
  drawRegularDetectionBoxes(objects);
}

// 绘制常规的检测框（从原始drawDetectionBoxes函数中抽取）
function drawRegularDetectionBoxes(objects: any[]) {
  const canvasElement = document.getElementById('detection-canvas') as HTMLCanvasElement;
  if (!canvasElement) return;

  const ctx = canvasElement.getContext('2d');
  if (!ctx) return;

  // 清除现有的检测框
  ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);

  // 如果没有objects参数或者它是空数组，并且有检测结果，则使用检测结果中的对象
  let objectsToDraw = objects;
  if ((!objectsToDraw || objectsToDraw.length === 0) && detectionResults.value) {
    // 默认只显示风险事件
    objectsToDraw = [];

    // 根据用户的选择，添加不同类型的对象
    // 高风险事件
    if (showHighRiskBoxes.value && detectionResults.value.high_risk_events) {
      objectsToDraw = [...objectsToDraw, ...detectionResults.value.high_risk_events];
    }

    // 低风险事件
    if (showLowRiskBoxes.value && detectionResults.value.low_risk_events) {
      objectsToDraw = [...objectsToDraw, ...detectionResults.value.low_risk_events];
    }

    // 人员
    if (showPersonBoxes.value) {
      const personObjects = detectionResults.value.objects.filter(o =>
        o.category === '人员' ||
        (o.category && (o.category.includes('person') || o.category.includes('worker')))
      );
      objectsToDraw = [...objectsToDraw, ...personObjects];
    }

    // 车辆
    if (showVehicleBoxes.value) {
      const vehicleObjects = detectionResults.value.objects.filter(o =>
        o.category === '车辆' ||
        (o.category && (o.category.includes('vehicle') || o.category.includes('car') ||
                       o.category.includes('truck') || o.category.includes('bus') ||
                       o.category.includes('motorcycle') || o.category.includes('bicycle')))
      );
      objectsToDraw = [...objectsToDraw, ...vehicleObjects];
    }

    // 机械
    if (showMachineBoxes.value) {
      const machineObjects = detectionResults.value.objects.filter(o =>
        o.category === '机械' || o.category === '设备' ||
        (o.category && (o.category.includes('machine') || o.category.includes('crane') ||
                       o.category.includes('excavator') || o.category.includes('bulldozer') ||
                       o.category.includes('forklift')))
      );
      objectsToDraw = [...objectsToDraw, ...machineObjects];
    }

    // 如果用户没有选择任何类型，则显示所有对象
    if (!showHighRiskBoxes.value && !showLowRiskBoxes.value &&
        !showPersonBoxes.value && !showVehicleBoxes.value && !showMachineBoxes.value) {
      objectsToDraw = detectionResults.value.objects;
    }
  }

  // 如果仍然没有对象要绘制，直接返回
  if (!objectsToDraw || objectsToDraw.length === 0) {
    return;
  }

  // 定义颜色映射
  const colors: Record<string, string> = {
    'high': 'rgba(255, 0, 0, 0.8)', // 高风险红色
    'medium': 'rgba(255, 165, 0, 0.8)', // 中风险橙色
    'low': 'rgba(255, 255, 0, 0.8)', // 低风险黄色
    '人员': 'rgba(0, 123, 255, 0.8)', // 人员蓝色
    'person': 'rgba(0, 123, 255, 0.8)', // 人员蓝色（英文）
    '车辆': 'rgba(111, 66, 193, 0.8)', // 车辆紫色
    'vehicle': 'rgba(111, 66, 193, 0.8)', // 车辆紫色（英文）
    '机械': 'rgba(40, 167, 69, 0.8)', // 机械设备绿色
    'machine': 'rgba(40, 167, 69, 0.8)', // 机械设备绿色（英文）
    '路面裂缝': 'rgba(255, 0, 0, 0.8)', // 路面裂缝红色
    '坑洼': 'rgba(255, 165, 0, 0.8)', // 坑洼橙色
    '路面积水': 'rgba(0, 0, 255, 0.8)', // 路面积水蓝色
    '路面沉降': 'rgba(128, 0, 128, 0.8)', // 路面沉降紫色
    '路面损坏': 'rgba(255, 0, 255, 0.8)', // 路面损坏粉色
    'crack': 'rgba(255, 0, 0, 0.8)', // 裂缝红色（英文）
    'pothole': 'rgba(255, 165, 0, 0.8)', // 坑洼橙色（英文）
    'water': 'rgba(0, 0, 255, 0.8)', // 积水蓝色（英文）
    'default': 'rgba(128, 128, 128, 0.8)' // 默认灰色
  };

  // 绘制对象
  objectsToDraw.forEach(obj => {
    // 获取位置信息
    let bbox;
    if (obj.bbox_2d) {
      bbox = obj.bbox_2d;
    } else if (obj.bbox) {
      bbox = obj.bbox;
    } else {
      // 如果没有边界框信息，跳过
      return;
    }

    if (bbox.length !== 4) {
      // 如果边界框信息不完整，跳过
      return;
    }

    const [x1, y1, x2, y2] = bbox;

    // 确定颜色
    let color;
    if (obj.risk_level === 'high') {
      color = colors.high;
    } else if (obj.risk_level === 'medium') {
      color = colors.medium;
    } else if (obj.risk_level === 'low') {
      color = colors.low;
    } else if (obj.category && colors[obj.category]) {
      color = colors[obj.category];
    } else {
      color = colors.default;
    }

    // 绘制边框
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;

    // 使用半透明填充
    ctx.fillStyle = color.replace(/[\d.]+\)$/, '0.2)');

    // 将标准化坐标转换为像素坐标（假设输入是归一化坐标0-1）
    const pixelX1 = x1 * canvasElement.width;
    const pixelY1 = y1 * canvasElement.height;
    const pixelWidth = (x2 - x1) * canvasElement.width;
    const pixelHeight = (y2 - y1) * canvasElement.height;

    // 绘制矩形
    ctx.strokeRect(pixelX1, pixelY1, pixelWidth, pixelHeight);
    ctx.fillRect(pixelX1, pixelY1, pixelWidth, pixelHeight);

    // 绘制标签
    const label = obj.label || obj.category || obj.event || (obj.risk_level ? `${obj.risk_level} risk` : 'Object');
    ctx.font = '14px Arial';
    const labelWidth = ctx.measureText(label).width;

    // 标签背景
    ctx.fillStyle = color;
    ctx.fillRect(pixelX1, pixelY1 - 24, labelWidth + 10, 20);

    // 标签文字
    ctx.fillStyle = 'white';
    ctx.fillText(label, pixelX1 + 5, pixelY1 - 10);
  });
}

// 状态变量，用于跟踪处理后的图像
const isShowingProcessedImage = ref(false); // 是否当前显示的是处理后的图像
const originalImageData = ref(''); // 保存原始图像的base64数据

// 切换检测框显示
function toggleDetectionBoxes() {
  showDetectionBoxes.value = !showDetectionBoxes.value;

  if (detectionResults.value && showDetectionBoxes.value) {
    // 检查是否有处理后的图像
    const processedUrl = detectionResults.value.visualized_image_url || detectionResults.value.processed_url;
    if (processedUrl) {
      // 如果启用检测框且有处理后的图像URL，则显示处理后的图像
      console.log('显示处理后的图像:', processedUrl);

      // 在切换前保存原始图像（如果尚未保存）
      if (!isShowingProcessedImage.value && !originalImageData.value) {
        originalImageData.value = imagePreview.value;
      }

      // 更新图像预览为处理后的图像
      const cacheBuster = new Date().getTime();
      imagePreview.value = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
      isShowingProcessedImage.value = true;
    } else {
      // 如果没有处理后的图像，使用常规检测框
      setTimeout(() => {
        if (detectionResults.value) {
          drawDetectionBoxes(detectionResults.value.objects);
        }
      }, 100);
    }
  } else {
    // 如果禁用了检测框，恢复原始图像
    if (isShowingProcessedImage.value && originalImageData.value) {
      console.log('恢复显示原始图像');
      imagePreview.value = originalImageData.value;
      isShowingProcessedImage.value = false;
    }

    // 清除Canvas
    const canvas = document.getElementById('detection-canvas') as HTMLCanvasElement;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
  }
}

// 切换特定类型检测框显示
function toggleSpecificBoxes(type: string) {
  console.log(`切换检测框类型: ${type}, 当前状态:`, {
    highRisk: showHighRiskBoxes.value,
    lowRisk: showLowRiskBoxes.value,
    person: showPersonBoxes.value,
    vehicle: showVehicleBoxes.value,
    machine: showMachineBoxes.value
  });

  // 切换选中的类型
  switch(type) {
    case 'high-risk':
      showHighRiskBoxes.value = !showHighRiskBoxes.value;
      break;
    case 'low-risk':
      showLowRiskBoxes.value = !showLowRiskBoxes.value;
      break;
    case 'person':
      showPersonBoxes.value = !showPersonBoxes.value;
      break;
    case 'vehicle':
      showVehicleBoxes.value = !showVehicleBoxes.value;
      break;
    case 'machine':
      showMachineBoxes.value = !showMachineBoxes.value;
      break;
  }

  console.log(`切换后状态:`, {
    highRisk: showHighRiskBoxes.value,
    lowRisk: showLowRiskBoxes.value,
    person: showPersonBoxes.value,
    vehicle: showVehicleBoxes.value,
    machine: showMachineBoxes.value
  });

  if (detectionResults.value && showDetectionBoxes.value) {
    // 使用setTimeout确保DOM已更新
    setTimeout(() => {
      drawDetectionBoxes(detectionResults.value!.objects);
    }, 100);
  }
}

// Current time for the header
const currentTime = ref(new Date())
const formattedTime = computed(() => {
  const hours = currentTime.value.getHours().toString().padStart(2, '0')
  const minutes = currentTime.value.getMinutes().toString().padStart(2, '0')
  const seconds = currentTime.value.getSeconds().toString().padStart(2, '0')
  return `${hours}:${minutes}:${seconds}`
})

const formattedDate = computed(() => {
  const year = currentTime.value.getFullYear()
  const month = (currentTime.value.getMonth() + 1).toString().padStart(2, '0')
  const day = currentTime.value.getDate().toString().padStart(2, '0')
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weekday = weekdays[currentTime.value.getDay()]
  return `${year}年${month}月${day}日 ${weekday}`
})

// 监听窗口大小变化，重新绘制检测框
let resizeTimeout: number | null = null;
const handleResize = () => {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }

  resizeTimeout = window.setTimeout(() => {
    if (detectionResults.value && showDetectionBoxes.value) {
      console.log('窗口大小变化，重新绘制检测框');
      drawDetectionBoxes(detectionResults.value.objects);
    }
  }, 200);
};

// Update time every second
onMounted(() => {
  setInterval(() => {
    currentTime.value = new Date()
  }, 1000)

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);

  // 清除定时器
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }
});

// 获取选中的选项数量
function getSelectedOptionsCount(): number {
  let count = 0;
  detectionOptions.value.forEach(category => {
    category.options.forEach(option => {
      if (option.checked) {
        count++;
      }
    });
  });
  return count;
}

// 获取高风险事件数量
function getHighRiskCount(): number {
  if (!detectionResults.value || !detectionResults.value.high_risk_events) {
    return 0;
  }
  return detectionResults.value.high_risk_events.filter(e => e.risk_level === 'high').length;
}

// 获取人员数量
function getPersonCount(): number {
  if (!detectionResults.value || !detectionResults.value.objects) {
    return 0;
  }
  return detectionResults.value.objects.filter(obj =>
    obj.category === '人员' ||
    (obj.category && (obj.category.includes('person') || obj.category.includes('worker')))
  ).length;
}

// 获取车辆数量
function getVehicleCount(): number {
  if (!detectionResults.value || !detectionResults.value.objects) {
    return 0;
  }
  return detectionResults.value.objects.filter(obj =>
    obj.category === '车辆' ||
    (obj.category && (obj.category.includes('vehicle') || obj.category.includes('car') ||
                     obj.category.includes('truck') || obj.category.includes('bus') ||
                     obj.category.includes('motorcycle') || obj.category.includes('bicycle')))
  ).length;
}

// 获取机械数量
function getMachineCount(): number {
  if (!detectionResults.value || !detectionResults.value.objects) {
    return 0;
  }
  return detectionResults.value.objects.filter(obj =>
    obj.category === '机械' || obj.category === '设备' ||
    (obj.category && (obj.category.includes('machine') || obj.category.includes('crane') ||
                       obj.category.includes('excavator') || obj.category.includes('bulldozer') ||
                       o.category.includes('forklift')))
  ).length;
}

// 检查是否有特定类型的检测对象
function hasDetectionType(type: string): boolean {
  if (!detectionResults.value) {
    return false;
  }

  switch(type) {
    case 'high-risk':
      // 检查high_risk_events数组是否存在且非空
      return !!detectionResults.value.high_risk_events && detectionResults.value.high_risk_events.length > 0;
    case 'low-risk':
      // 检查low_risk_events数组是否存在且非空
      return !!detectionResults.value.low_risk_events && detectionResults.value.low_risk_events.length > 0;
    case 'person':
      // 对于普通类型，仍然检查objects数组
      if (!detectionResults.value.objects) return false;
      return detectionResults.value.objects.some(obj =>
        obj.category === '人员' ||
        (obj.category && (obj.category.includes('person') || obj.category.includes('worker')))
      );
    case 'vehicle':
      if (!detectionResults.value.objects) return false;
      return detectionResults.value.objects.some(obj =>
        obj.category === '车辆' ||
        (obj.category && (obj.category.includes('vehicle') || obj.category.includes('car') ||
                      obj.category.includes('truck') || obj.category.includes('bus') ||
                      obj.category.includes('motorcycle') || obj.category.includes('bicycle')))
      );
    case 'machine':
      if (!detectionResults.value.objects) return false;
      return detectionResults.value.objects.some(obj =>
        obj.category === '机械' ||
        (obj.category && (obj.category.includes('machine') || obj.category.includes('crane') ||
                      obj.category.includes('excavator') || obj.category.includes('bulldozer') ||
                      obj.category.includes('forklift')))
      );
    default:
      return false;
  }
}

// 事件名称翻译函数
function translateEventName(eventName: string): string {
  const eventTranslations: Record<string, string> = {
    'worker_no_helmet': '未佩戴安全帽',
    'worker_no_vest': '未穿反光背心',
    'worker_falling': '工人摔倒',
    'unsafe_operation': '不安全操作',
    'dangerous_driving': '危险驾驶',
    'vehicle_parked': '车辆停放',
    'worker_with_helmet': '工人',
    'worker': '工人',
    'vehicle': '车辆',
    'machine': '机械设备'
  };

  return eventTranslations[eventName] || eventName;
}

// 将同种类型的风险事件合并显示
function groupHighRiskEvents(category: string): { [key: string]: { count: number } } {
  const groupedEvents: { [key: string]: { count: number } } = {};
  detectionResults.value?.high_risk_events?.forEach(event => {
    if (event.category === category && typeof event.event === 'string') {
      if (!groupedEvents[event.event]) {
        groupedEvents[event.event] = { count: 0 };
      }
      groupedEvents[event.event].count++;
    }
  });
  return groupedEvents;
}

// 将同种类型的风险事件合并显示
function groupLowRiskEvents(category: string): { [key: string]: { count: number } } {
  const groupedEvents: { [key: string]: { count: number } } = {};
  detectionResults.value?.low_risk_events?.forEach(event => {
    if (event.category === category && typeof event.event === 'string') {
      if (!groupedEvents[event.event]) {
        groupedEvents[event.event] = { count: 0 };
      }
      groupedEvents[event.event].count++;
    }
  });
  return groupedEvents;
}

// 模型选择
const currentModel = ref({
  id: 'lite',
  name: '智眼 Lite',
  description: '轻量快速识别'
});

const models = [
  {
    id: 'pro',
    name: '智眼 Pro',
    description: '全能型建设风险识别',
    icon: 'model-pro-icon'
  },
  {
    id: 'lite',
    name: '智眼 Lite',
    description: '轻量快速识别',
    icon: 'model-lite-icon'
  }
];

const handleModelChange = (modelId: string) => {
  const selectedModel = models.find(m => m.id === modelId);
  if (selectedModel) {
    currentModel.value = selectedModel;
    // 根据不同模型可能需要调整检测参数或API端点
    console.log(`已切换到${selectedModel.name}模型`);
  }
};

// 添加模型选择器
const isModelDropdownOpen = ref(false);

const currentPlatform = ref('construction');
const currentOption = ref('detection');

// 监听平台切换，重置选中的类别
watch(currentPlatform, (newPlatform) => {
  // 根据平台设置默认选中的类别
  if (newPlatform === 'construction') {
    selectedCategory.value = 'all';
  } else {
    selectedCategory.value = 'road_structure';
  }

  // 调用selectCategory来更新选项
  selectCategory(selectedCategory.value);
});

// Add currentMonitorTab to track which monitoring tab is active
const currentMonitorTab = ref('online');

// 在线监控定时器
const onlineMonitoringInterval = ref<NodeJS.Timeout | null>(null);

// Watch for changes in current option
watch(() => currentOption.value, (newOption) => {
  // Reset UI state when switching between options
  resetUIState();
});

// Watch for changes in current platform
watch(() => currentPlatform.value, (newPlatform) => {
  // Reset UI state when switching between platforms
  resetUIState();
});
</script>

<template>
  <div class="app-container">
    <!-- Top Navigation Bar -->
    <div class="top-navbar">
      <div class="navbar-left">
        <div class="platforms-left">
          <div class="platform-item" :class="{ active: currentPlatform === 'construction' }" @click="currentPlatform = 'construction'">
            <div class="platform-text">建设风险监测与管控大模型平台</div>
            <div class="platform-underline" v-if="currentPlatform === 'construction'"></div>
          </div>
          <div class="platform-item" :class="{ active: currentPlatform === 'operation' }" @click="currentPlatform = 'operation'">
            <div class="platform-text">运营风险识别与管控大模型平台</div>
            <div class="platform-underline" v-if="currentPlatform === 'operation'"></div>
          </div>
        </div>
      </div>
      <div class="navbar-center">
        <div class="main-title">智能交通大模型平台</div>
      </div>
      <div class="navbar-right">
        <div class="platform-options">
          <div class="option-item" :class="{ active: currentOption === 'detection' }" @click="currentOption = 'detection'">
            <div class="option-icon detection-icon"></div>
            <div class="option-text">图片检测</div>
          </div>
          <div class="option-item" :class="{ active: currentOption === 'chat' }" @click="currentOption = 'chat'">
            <div class="option-icon chat-icon"></div>
            <div class="option-text">智能对话</div>
          </div>
          <div class="option-item" :class="{ active: currentOption === 'monitor' }" @click="currentOption = 'monitor'">
            <div class="option-icon monitor-icon"></div>
            <div class="option-text">在线监控</div>
          </div>
        </div>
        <div class="time-display">
          <div class="current-time">{{ formattedTime }}</div>
          <div class="current-date">{{ formattedDate }}</div>
        </div>
      </div>
    </div>

    <div class="main-container">
      <!-- Conditionally render based on currentOption -->
      <template v-if="currentOption === 'detection'">
        <!-- Left Sidebar - Navigation and Detection Options -->
        <div class="left-sidebar fade-in">
          <div class="nav-section">
            <button
              v-for="category in riskCategories"
              :key="category.id"
              class="nav-button hover-lift"
              @click="selectCategory(category.id)"
              :class="{ active: selectedCategory === category.id }"
            >
              <span class="nav-icon" :class="category.id + '-icon'"></span>
              <span>{{ category.label }}</span>
            </button>
          </div>

          <div class="detection-options-container">
            <h3 class="sidebar-header">监测内容</h3>
            <!-- 移除已选择X项监测内容的显示 -->
            <!--
            <div class="selected-options-summary" v-if="getSelectedOptionsCount() > 0">
              已选择 <span class="selected-count">{{ getSelectedOptionsCount() }}</span> 项监测内容
            </div>
            -->
            <div class="detection-options">
              <div
                v-for="(category, index) in filteredDetectionOptions"
                :key="category.id"
                class="detection-category slide-in-up"
                :style="{animationDelay: `${0.1 * index}s`}"
              >
                <div
                  class="category-header"
                  @click="toggleCategory(category.id)"
                >
                  <div class="category-icon-wrapper">
                    <span class="category-icon" :class="category.id + '-icon'"></span>
                    <span class="category-label">{{ category.label }}</span>
                  </div>
                  <span class="expand-icon">{{ category.expanded ? '▼' : '▶' }}</span>
                </div>
                <div
                  v-if="category.expanded"
                  class="category-options"
                >
                  <div
                    v-for="option in category.options"
                    :key="option.id"
                    class="option-item"
                  >
                    <label class="option-label custom-checkbox">
                      <input
                        type="checkbox"
                        :checked="option.checked"
                        @change="toggleOption(category.id, option.id)"
                      />
                      <span class="option-text">{{ option.label }}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Middle - Image Upload and Preview -->
        <div class="image-container fade-in" style="animation-delay: 0.2s">
          <div class="card-container">
            <div class="card-header">
              <span class="card-title">检测图像</span>
              <div class="card-tools">
                <label for="image-upload-replace" class="upload-button hover-lift" v-if="imagePreview">
                  <i class="upload-icon-small"></i>
                  上传图片
                </label>
                <input
                  type="file"
                  id="image-upload-replace"
                  accept="image/*"
                  @change="handleImageUpload"
                  class="upload-input"
                  v-if="imagePreview"
                />
                <button class="toggle-button hover-lift" @click="toggleDetectionBoxes" v-if="imagePreview && detectionResults">
                  {{ showDetectionBoxes ? '隐藏检测框' : '显示检测框' }}
                </button>
              </div>
            </div>

            <div class="card-body">
              <div class="upload-area hover-lift" v-if="!imagePreview">
                <label for="image-upload" class="upload-label">
                  <div class="upload-icon pulse">+</div>
                  <div>点击上传图片</div>
                </label>
                <input
                  type="file"
                  id="image-upload"
                  accept="image/*"
                  @change="handleImageUpload"
                  class="upload-input"
                />
              </div>
              <div class="image-preview-container slide-in-up" v-else>
                <div class="image-preview-wrapper">
                  <img :src="imagePreview" alt="Preview" id="preview-image" class="preview-image" />
                  <canvas id="detection-canvas" class="detection-canvas"></canvas>
                </div>
              </div>
            </div>

            <!-- 检测框类型选择 -->
            <div class="card-footer" v-if="imagePreview">
              <div class="detection-box-types" v-if="detectionResults && showDetectionBoxes">
                <div class="box-type-title">检测框类型</div>
                <div class="box-type-options">
                  <div class="box-type-option"
                       :class="{ active: showHighRiskBoxes, disabled: !hasDetectionType('high-risk') }"
                       v-if="hasDetectionType('high-risk')"
                       @click="toggleSpecificBoxes('high-risk')">
                    <div class="box-type-checkbox">
                      <div class="checkbox-inner" :class="{ checked: showHighRiskBoxes }"></div>
                    </div>
                    <span class="box-type-color high-risk"></span>
                    <span class="box-type-label">高风险事件</span>
                  </div>

                  <div class="box-type-option"
                       :class="{ active: showLowRiskBoxes, disabled: !hasDetectionType('low-risk') }"
                       v-if="hasDetectionType('low-risk')"
                       @click="toggleSpecificBoxes('low-risk')">
                    <div class="box-type-checkbox">
                      <div class="checkbox-inner" :class="{ checked: showLowRiskBoxes }"></div>
                    </div>
                    <span class="box-type-color low-risk"></span>
                    <span class="box-type-label">低风险事件</span>
                  </div>

                  <div class="box-type-option"
                       :class="{ active: showPersonBoxes, disabled: !hasDetectionType('person') }"
                       v-if="hasDetectionType('person')"
                       @click="toggleSpecificBoxes('person')">
                    <div class="box-type-checkbox">
                      <div class="checkbox-inner" :class="{ checked: showPersonBoxes }"></div>
                    </div>
                    <span class="box-type-color person"></span>
                    <span class="box-type-label">人员</span>
                  </div>

                  <div class="box-type-option"
                       :class="{ active: showVehicleBoxes, disabled: !hasDetectionType('vehicle') }"
                       v-if="hasDetectionType('vehicle')"
                       @click="toggleSpecificBoxes('vehicle')">
                    <div class="box-type-checkbox">
                      <div class="checkbox-inner" :class="{ checked: showVehicleBoxes }"></div>
                    </div>
                    <span class="box-type-color vehicle"></span>
                    <span class="box-type-label">车辆</span>
                  </div>

                  <div class="box-type-option"
                       :class="{ active: showMachineBoxes, disabled: !hasDetectionType('machine') }"
                       v-if="hasDetectionType('machine')"
                       @click="toggleSpecificBoxes('machine')">
                    <div class="box-type-checkbox">
                      <div class="checkbox-inner" :class="{ checked: showMachineBoxes }"></div>
                    </div>
                    <span class="box-type-color machine"></span>
                    <span class="box-type-label">设备</span>
                  </div>
                </div>
              </div>

              <!-- 检测按钮 -->
              <div class="detection-action">
                <div class="image-actions">
                  <!-- 添加模型选择器在开始检测按钮旁边 -->
                  <button class="detect-button" @click="performDetection" :disabled="isLoading || getSelectedOptionsCount() === 0">
                    <div class="detect-button-content">
                      <i class="detect-icon" v-if="!isLoading"></i>
                      <span v-if="isLoading" class="shimmer">检测中...</span>
                      <span v-else>开始检测</span>
                    </div>
                  </button>
                  <div class="model-selector">
                    <div class="selected-model" @click="isModelDropdownOpen = !isModelDropdownOpen">
                      <span class="model-icon"></span>
                      {{ currentModel.name }}
                      <span class="dropdown-icon">▼</span>
                    </div>
                    <div class="model-dropdown" v-if="isModelDropdownOpen">
                      <div
                        v-for="model in models"
                        :key="model.id"
                        class="model-option"
                        @click="handleModelChange(model.id); isModelDropdownOpen = false"
                        :class="{ active: currentModel.id === model.id }"
                      >
                        <div class="model-name">{{ model.name }}</div>
                        <div class="model-description">{{ model.description }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <p v-if="error" class="error-message slide-in-up">{{ error }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Right - Detection Results -->
        <div class="result-panel fade-in" style="animation-delay: 0.3s">
          <div class="risk-analysis-header">
            <div class="risk-analysis-icon"></div>
            <span class="risk-analysis-title">风险统计</span>
          </div>

          <!-- 风险事件统计 -->
          <div v-if="detectionResults && detectionResults.high_risk_events">
            <!-- 有高风险事件 -->
            <div class="risk-events-container high-risk" v-if="detectionResults.high_risk_events.filter(e => e.risk_level === 'high').length > 0">
              <div class="risk-summary">
                <div class="risk-count danger">{{ detectionResults.high_risk_events.filter(e => e.risk_level === 'high').length }}</div>
                <div class="risk-label">高风险事件</div>
              </div>

              <div class="risk-categories">
                <template v-for="category in ['人员', '机械', '物料', '法规', '环境']" :key="category">
                  <div
                    class="risk-category"
                    v-if="detectionResults.high_risk_events.filter(e => e.category === category && e.risk_level === 'high').length > 0"
                  >
                    <div class="risk-category-header high-risk">
                      <span class="category-icon" :class="category + '-icon'"></span>
                      <span class="category-name">{{ category === '机械' ? '设备' : category }}</span>
                      <span class="category-count danger">{{ detectionResults.high_risk_events.filter(e => e.category === category && e.risk_level === 'high').length }}</span>
                    </div>
                    <div class="risk-events">
                      <!-- 将同种类型的风险事件合并显示 -->
                      <div
                        v-for="(eventGroup, eventName) in groupHighRiskEvents(category)"
                        :key="eventName"
                        class="risk-event-item high-risk"
                      >
                        <div class="risk-event-content">
                          <div class="risk-event-name">{{ typeof eventName === 'string' ? translateEventName(eventName) : '' }}
                            <span v-if="eventGroup.count > 1" class="event-count">x{{ eventGroup.count }}</span>
                          </div>
                        </div>
                        <div class="risk-level risk-high">高风险</div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>

            <!-- 有低风险事件 -->
            <div class="risk-events-container low-risk" v-if="detectionResults.low_risk_events && detectionResults.low_risk_events.length > 0">
              <div class="risk-summary">
                <div class="risk-count warning">{{ detectionResults.low_risk_events.length }}</div>
                <div class="risk-label">低风险事件</div>
              </div>

              <div class="risk-categories">
                <template v-for="category in ['人员', '车辆', '机械']" :key="category">
                  <div
                    class="risk-category"
                    v-if="detectionResults.low_risk_events.filter(e => e.category === category).length > 0"
                  >
                    <div class="risk-category-header low-risk">
                      <span class="category-icon" :class="category + '-icon'"></span>
                      <span class="category-name">{{ category === '机械' ? '设备' : category }}</span>
                      <span class="category-count warning">{{ detectionResults.low_risk_events.filter(e => e.category === category).length }}</span>
                    </div>
                    <div class="risk-events">
                      <!-- 将同种类型的风险事件合并显示 -->
                      <div
                        v-for="(eventGroup, eventName) in groupLowRiskEvents(category)"
                        :key="eventName"
                        class="risk-event-item low-risk"
                      >
                        <div class="risk-event-content">
                          <div class="risk-event-name">{{ typeof eventName === 'string' ? translateEventName(eventName) : '' }}
                            <span v-if="eventGroup.count > 1" class="event-count">x{{ eventGroup.count }}</span>
                          </div>
                        </div>
                        <div class="risk-level risk-low">低风险</div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>

            <!-- 无风险事件 -->
            <div class="safe-container" v-if="(!detectionResults.high_risk_events || detectionResults.high_risk_events.length === 0) &&
                                             (!detectionResults.low_risk_events || detectionResults.low_risk_events.length === 0)">
              <div class="safe-icon">✓</div>
              <div class="safe-message">安全</div>
              <div class="safe-description">未检测到任何风险事件</div>
            </div>
          </div>

          <!-- 无检测结果 -->
          <div class="no-detection-results" v-else-if="detectionResults">
            <div class="no-risk-icon">ℹ️</div>
            <div class="no-risk-message">未检测到风险事件</div>
          </div>

          <!-- 场景描述面板 -->
          <ObjectDetectionPanel
            :imagePreview="imagePreview"
            :selectedOptions="selectedOptions"
          />
          <!-- 统计数据 - 使用更简洁的横向布局 -->
          <div class="stats-panel-horizontal" v-if="detectionResults">
            <div class="stats-item">
              <div class="stats-icon person-icon"></div>
              <div class="stats-info">
                <div class="stats-label">人员</div>
                <div class="stats-value">{{ getPersonCount() }}</div>
              </div>
            </div>
            <div class="stats-item">
              <div class="stats-icon vehicle-icon"></div>
              <div class="stats-info">
                <div class="stats-label">车辆</div>
                <div class="stats-value">{{ getVehicleCount() }}</div>
              </div>
            </div>
            <div class="stats-item">
              <div class="stats-icon machine-icon"></div>
              <div class="stats-info">
                <div class="stats-label">设备</div>
                <div class="stats-value">{{ getMachineCount() }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- Chat Interface View -->
      <template v-else-if="currentOption === 'chat'">
        <OperationChatInterface class="chat-view fade-in" />
      </template>

      <!-- Monitor View -->
      <template v-else-if="currentOption === 'monitor'">
        <!-- Updated monitor view with video and online monitoring options -->
        <div class="monitor-options-container">
          <div class="monitor-tabs">
            <div class="monitor-tab" :class="{ active: currentMonitorTab === 'online' }" @click="currentMonitorTab = 'online'">
              <div class="tab-icon online-icon"></div>
              <div class="tab-text">在线监控</div>
            </div>
            <div class="monitor-tab" :class="{ active: currentMonitorTab === 'video' }" @click="currentMonitorTab = 'video'">
              <div class="tab-icon video-icon"></div>
              <div class="tab-text">视频监测</div>
            </div>
          </div>

          <div class="monitor-content">
            <!-- Show the appropriate monitor based on the selected tab -->
            <OnlineMonitor v-if="currentMonitorTab === 'online' && currentPlatform === 'construction'" class="monitor-view fade-in" />
            <OperationMonitor v-if="currentMonitorTab === 'online' && currentPlatform === 'operation'" class="monitor-view fade-in" />
            <VideoMonitor v-if="currentMonitorTab === 'video'" class="monitor-view fade-in" />
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<style>
:root {
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-light: rgba(24, 144, 255, 0.1);
  --secondary-color: #52c41a;
  --danger-color: #f5222d;
  --warning-color: #faad14;
  --success-color: #52c41a;
  --text-primary: #ffffff;
  --text-secondary: #bdc3c7;
  --bg-dark: #001529;
  --bg-darker: #000c17;
  --card-bg: #001f3d;
  --border-color: #003a8c;
  --border-light: #1d39c4;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --sidebar-bg: #001529;
  --sidebar-header-bg: #002140;
  --category-bg: #002140;
  --category-hover: #003a8c;
  --option-bg: #001f3d;
  --navbar-bg: #001529;
  --navbar-height: 120px; /* 增加导航栏高度 */
  --shadow-sm: 0 2px 8px rgba(0, 21, 41, 0.3);
  --shadow-md: 0 4px 12px rgba(0, 21, 41, 0.4);
  --shadow-lg: 0 8px 24px rgba(0, 21, 41, 0.5);
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --navbar-accent: #00a8ff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-dark);
  color: var(--text-primary);
  line-height: 1.6;
}

.app-container {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  background: radial-gradient(ellipse at center, #002755 0%, #001529 100%);
}

/* Top Navigation Bar */
.top-navbar {
  height: auto;
  min-height: var(--navbar-height);
  padding: 10px 0;
  background: linear-gradient(90deg, #000c17, #001529);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  box-shadow: 0 0 20px rgba(0, 168, 255, 0.2);
  overflow: hidden;
}

.top-navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--navbar-accent), transparent);
}

.navbar-left, .navbar-right {
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.navbar-left {
  justify-content: flex-start;
}

.navbar-right {
  justify-content: center; /* Changed from flex-end to center */
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 20px;
}

.platforms-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  margin-right: 40px;
}

.platforms-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 2;
}

.platforms-center .platform-item {
  min-width: 260px;
  max-width: 400px;
  text-align: center;
  font-size: 18px;
}

.platform-options {
  display: flex;
  align-items: center;
  gap: 30px; /* Increased gap for more space between options */
  background: rgba(0, 33, 64, 0.5);
  padding: 8px 20px; /* Increased horizontal padding */
  border-radius: 30px;
  border: 1px solid rgba(0, 168, 255, 0.2);
  margin-right: 30px; /* Add margin to separate from time display */
}

.title-container {
  position: relative;
  text-align: center;
}

.title-text {
  color: var(--text-primary);
  font-size: 1.8em;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.7);
  position: relative;
  padding: 0 15px;
}

.title-underline {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--navbar-accent), transparent);
}

.time-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  color: var(--text-primary);
  position: relative;
}

.time-display::before {
  content: '';
  position: absolute;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, rgba(0, 168, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
}

.current-time {
  font-size: 1.5em;
  font-weight: bold;
  color: var(--navbar-accent);
  text-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
}

.current-date {
  font-size: 0.9em;
  color: var(--text-secondary);
}

.main-container {
  display: grid;
  /* Updated grid-template-columns to handle single view for chat/monitor */
  grid-template-columns: v-bind("currentOption === 'detection' ? '320px 1fr 320px' : '1fr'");
  gap: 15px;
  min-height: calc(100vh - var(--navbar-height));
  background-color: var(--bg-darker);
  padding: 15px;
  overflow-y: auto;
}

.chat-view,
.monitor-view {
  /* Styles for full-width chat/monitor views */
  grid-column: 1 / -1; /* Span all columns if main-container is still a grid with multiple columns defined */
  background-color: var(--bg-darker); /* Match main container background */
  padding: 0px; /* Remove padding if ChatInterface handles its own padding */
  border-radius: var(--radius-md);
  display: flex; /* Ensure it takes full height if needed */
  flex-direction: column; /* Ensure it takes full height if needed */
  height: 100%; /* Ensure it takes full height if needed */
}

.monitor-view { /* Specific styling for monitor placeholder if needed */
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  background-color: var(--card-bg); /* Give it a card background like chat for consistency */
}

.monitor-view h2 {
  margin-bottom: 20px;
  color: var(--primary-color);
}

/* Left Sidebar */
.left-sidebar {
  background: linear-gradient(135deg, #001529, #002140);
  overflow-y: auto;
  min-width: 320px;
  width: 320px;
  border-radius: var(--radius-md);
  padding: 0;
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
}

.left-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--navbar-accent), transparent);
}

/* Navigation Section */
.nav-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
  padding: 15px 12px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.15);
  position: relative;
}

.nav-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  width: 70%;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--navbar-accent), transparent);
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 21, 41, 0.6);
  border: 1px solid rgba(0, 168, 255, 0.2);
  color: #fff;
  padding: 10px 15px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  text-align: left;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.1), transparent);
  transition: all 0.6s;
}

.nav-button:hover {
  background: rgba(0, 58, 140, 0.5);
  border-color: rgba(0, 168, 255, 0.4);
  transform: translateY(-2px);
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button.active {
  background: linear-gradient(90deg, rgba(0, 168, 255, 0.2), rgba(0, 58, 140, 0.6));
  border-color: var(--navbar-accent);
  box-shadow: 0 0 10px rgba(0, 168, 255, 0.3);
}

.nav-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  filter: drop-shadow(0 0 2px rgba(0, 168, 255, 0.5));
}

.detection-options-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 12px 15px;
}

.sidebar-header {
  color: var(--navbar-accent);
  font-size: 16px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.15);
  text-align: center;
  font-weight: bold;
  letter-spacing: 1px;
  text-shadow: 0 0 8px rgba(0, 168, 255, 0.3);
  position: relative;
}

.sidebar-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--navbar-accent), transparent);
}

.detection-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detection-category {
  background: rgba(0, 21, 41, 0.4);
  border-radius: var(--radius-sm);
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.1);
  transition: all 0.3s ease;
}

.detection-category:hover {
  border-color: rgba(0, 168, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 21, 41, 0.5);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  background: linear-gradient(90deg, rgba(0, 21, 41, 0.8), rgba(0, 58, 140, 0.4));
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.category-header:hover {
  background: linear-gradient(90deg, rgba(0, 21, 41, 0.6), rgba(0, 58, 140, 0.5));
  border-left-color: var(--navbar-accent);
}

.category-icon-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  filter: drop-shadow(0 0 2px rgba(0, 168, 255, 0.5));
  transition: transform 0.3s ease;
}

.category-header:hover .category-icon {
  transform: scale(1.1);
}

.category-label {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.expand-icon {
  color: var(--navbar-accent);
  font-size: 12px;
  transition: transform 0.3s ease;
}

.category-header:hover .expand-icon {
  transform: scale(1.2);
}

.category-options {
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(0, 168, 255, 0.1);
}

.option-item {
  cursor: pointer;
  padding: 6px 15px;
  border-radius: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  min-width: 110px; /* Ensure consistent width for all options */
  justify-content: center; /* Center the content horizontally */
}

.option-item:hover {
  background: rgba(0, 168, 255, 0.1);
  transform: translateY(-2px);
}

.option-item.active {
  background: rgba(0, 168, 255, 0.2);
  box-shadow: 0 0 10px rgba(0, 168, 255, 0.3);
}

.option-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 6px;
  filter: drop-shadow(0 0 2px rgba(0, 168, 255, 0.5));
}

.option-text {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 13px;
}

/* Custom checkbox styling */
.custom-checkbox input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid rgba(0, 168, 255, 0.4);
  border-radius: 3px;
  background: rgba(0, 21, 41, 0.6);
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.custom-checkbox input[type="checkbox"]:hover {
  border-color: var(--navbar-accent);
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.3);
}

.custom-checkbox input[type="checkbox"]:checked {
  background: var(--navbar-accent);
  border-color: var(--navbar-accent);
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

.custom-checkbox input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Animation classes */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.5s ease-in-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Middle - Image Container */
.image-container {
  background-color: var(--bg-dark);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 0px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  background: linear-gradient(135deg, #001529, #002140);
}

/* 卡片容器样式 */
.card-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(0, 33, 64, 0.5);
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
  box-shadow: var(--shadow-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.card-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: var(--primary-color);
  margin-right: 10px;
  border-radius: 2px;
}

.card-tools {
  display: flex;
  gap: 10px;
}

.card-body {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.card-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(0, 168, 255, 0.15);
  background: rgba(0, 21, 41, 0.4);
}

.upload-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  background-color: rgba(0, 33, 64, 0.3);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(0, 33, 64, 0.5);
  transform: scale(1.01);
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 60px;
  transition: all var(--transition-normal);
  position: relative;
  z-index: 1;
}

.upload-icon {
  font-size: 64px;
  margin-bottom: 20px;
  color: var(--primary-color);
  transition: transform var(--transition-normal);
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
}

.upload-label:hover .upload-icon {
  transform: scale(1.1);
}

.upload-input {
  display: none;
}

.image-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-preview-wrapper {
  position: relative;
  width: 100%;
  height: 500px; /* 固定高度 */
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  background: rgba(0, 21, 41, 0.3);
  border: none; /* 移除边框 */
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius-md);
  display: block;
}

.detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  display: block;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-md);
  z-index: 10;
}

.detection-box-types {
  margin-bottom: 16px;
}

.box-type-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 12px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  position: relative;
}

.box-type-title::before {
  content: '';
  display: inline-block;
  width: 3px;
  height: 14px;
  background: var(--primary-color);
  margin-right: 8px;
  border-radius: 2px;
}

.box-type-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.box-type-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  background: rgba(0, 33, 64, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.detection-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.image-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  width: 100%;
  max-width: 100%;
}

.detection-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: rgba(0, 21, 41, 0.7);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  width: 100%;
  max-width: 400px;
  position: relative;
  overflow: hidden;
}

.detection-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), transparent);
  z-index: 0;
}

.info-text {
  color: var(--text-secondary);
  font-size: 14px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.detect-button {
  position: relative;
  padding: 0;
  background: linear-gradient(to right, #0c8eff, #0058ff);
  color: white;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.1em;
  box-shadow: 0 4px 15px rgba(12, 142, 255, 0.3);
  transition: all 0.3s ease;
  letter-spacing: 1px;
  flex: 1;
  max-width: 250px;
  height: 48px;
  overflow: hidden;
}

.detect-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  z-index: 1;
}

.detect-button-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.detect-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9.5 9.5c0 .55.45 1 1 1h2c.55 0 1-.45 1-1V8h1c.55 0 1-.45 1-1s-.45-1-1-1h-1V5c0-.55-.45-1-1-1h-2c-.55 0-1 .45-1 1v2.5z'/%3E%3Cpath d='M20 12c0-2.54-1.19-4.81-3.04-6.27L16 0H8l-.95 5.73C5.19 7.19 4 9.45 4 12s1.19 4.81 3.05 6.27L8 24h8l.96-5.73C18.81 16.81 20 14.54 20 12zM12 2.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5zm0 19c-5.23 0-9.5-4.27-9.5-9.5S6.77 2.5 12 2.5s9.5 4.27 9.5 9.5-4.27 9.5-9.5 9.5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.detect-button:hover {
  background: linear-gradient(to right, #0a6fcc, #0046cc);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(12, 142, 255, 0.5);
}

.detect-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(12, 142, 255, 0.4);
}

.detect-button:disabled {
  background: linear-gradient(to right, #b3b3b3, #8c8c8c);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  opacity: 0.7;
}

.error-message {
  color: var(--danger-color);
  margin-top: 12px;
  text-align: center;
  font-weight: 500;
  background-color: rgba(245, 34, 45, 0.1);
  padding: 8px 16px;
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--danger-color);
}

/* Removed .clear-button and .toggle-button styles */

/* Right - Result Panel */
.result-panel {
  background-color: var(--bg-dark);
  min-width: 320px;
  width: 320px;
  display: flex;
  flex-direction: column;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  background: linear-gradient(135deg, #001529, #002140);
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
}

.result-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--navbar-accent), transparent);
  z-index: 1;
}

.result-panel h3 {
  padding: 18px 16px;
  border-bottom: 1px solid var(--border-color);
  text-align: center;
  background: linear-gradient(90deg, #002140, #003a8c);
  margin: 0;
  position: relative;
}

.result-panel h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

/* 风险事件统计 */
.risk-events-container {
  margin: 15px;
  padding: 20px;
  background: rgba(0, 21, 41, 0.5);
  border-radius: var(--radius-md);
  border: 1px solid rgba(0, 168, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.risk-events-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(0, 168, 255, 0.05), transparent 70%);
  z-index: 0;
}

.risk-events-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transform: translateY(-3px);
}

.risk-events-container.high-risk {
  border-color: rgba(245, 34, 45, 0.3);
  background: linear-gradient(135deg, rgba(245, 34, 45, 0.05), rgba(0, 21, 41, 0.5));
}

.risk-events-container.high-risk::before {
  background: radial-gradient(circle at top right, rgba(245, 34, 45, 0.1), transparent 70%);
}

.risk-events-container.low-risk {
  border-color: rgba(250, 173, 20, 0.3);
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.05), rgba(0, 21, 41, 0.5));
  margin-top: 20px;
}

.risk-events-container.low-risk::before {
  background: radial-gradient(circle at top right, rgba(250, 173, 20, 0.1), transparent 70%);
}

.risk-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
  z-index: 1;
}

.risk-count {
  font-size: 42px;
  font-weight: bold;
  text-shadow: 0 0 15px rgba(245, 34, 45, 0.5);
  margin-bottom: 5px;
  background: linear-gradient(to bottom, #fff, #ccc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.risk-count::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--danger-color), transparent);
}

.risk-count.danger {
  background: linear-gradient(to bottom, #ff4d4f, #cf1322);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.risk-count.warning {
  background: linear-gradient(to bottom, #faad14, #d48806);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.risk-label {
  font-size: 16px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.risk-categories {
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: relative;
  z-index: 1;
}

.risk-category-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  position: relative;
}

.risk-category-header.high-risk {
  background: linear-gradient(90deg, rgba(245, 34, 45, 0.1), rgba(0, 21, 41, 0.3));
  border-bottom: 1px solid rgba(245, 34, 45, 0.2);
}

.risk-category-header.low-risk {
  background: linear-gradient(90deg, rgba(250, 173, 20, 0.1), rgba(0, 21, 41, 0.3));
  border-bottom: 1px solid rgba(250, 173, 20, 0.2);
}

.category-name {
  flex: 1;
  font-weight: 500;
  font-size: 15px;
}

.category-count {
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.category-count.danger {
  background: linear-gradient(135deg, rgba(245, 34, 45, 0.3), rgba(245, 34, 45, 0.1));
  color: var(--danger-color);
  border: 1px solid rgba(245, 34, 45, 0.3);
}

.category-count.warning {
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.3), rgba(250, 173, 20, 0.1));
  color: var(--warning-color);
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.risk-events {
  padding: 0 5px;
}

.risk-event-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 10px;
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
}

.risk-event-item:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.risk-event-item.high-risk {
  border-left: 3px solid var(--danger-color);
  background: linear-gradient(90deg, rgba(245, 34, 45, 0.05), rgba(0, 21, 41, 0.3));
}

.risk-event-item.low-risk {
  border-left: 3px solid var(--warning-color);
  background: linear-gradient(90deg, rgba(250, 173, 20, 0.05), rgba(0, 21, 41, 0.3));
}

.risk-event-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.risk-event-name {
  font-size: 14px;
  font-weight: 500;
}

.event-count {
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 1px 6px;
  margin-left: 5px;
  display: inline-block;
}

.risk-event-count {
  background: linear-gradient(135deg, rgba(0, 21, 41, 0.5), rgba(0, 33, 64, 0.3));
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid rgba(0, 168, 255, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.risk-level {
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.risk-high {
  background: linear-gradient(135deg, rgba(245, 34, 45, 0.2), rgba(245, 34, 45, 0.1));
  color: var(--danger-color);
  border: 1px solid rgba(245, 34, 45, 0.3);
}

.risk-medium {
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.2), rgba(250, 173, 20, 0.1));
  color: var(--warning-color);
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.risk-low {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.2), rgba(82, 196, 26, 0.1));
  color: var(--success-color);
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.safe-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 15px;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05), rgba(0, 21, 41, 0.3));
  border-radius: var(--radius-md);
  border: 1px solid rgba(82, 196, 26, 0.3);
  margin: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.safe-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(82, 196, 26, 0.1), transparent 70%);
  z-index: 0;
}

.safe-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transform: translateY(-3px);
}

.safe-icon {
  font-size: 48px;
  color: var(--success-color);
  margin-bottom: 15px;
  text-shadow: 0 0 15px rgba(82, 196, 26, 0.5);
  position: relative;
  z-index: 1;
}

.safe-message {
  font-size: 28px;
  font-weight: bold;
  color: var(--success-color);
  margin-bottom: 10px;
  text-shadow: 0 0 10px rgba(82, 196, 26, 0.3);
  position: relative;
  z-index: 1;
}

.safe-description {
  font-size: 15px;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
  text-align: center;
}

.no-detection-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 15px;
  background: linear-gradient(135deg, rgba(0, 168, 255, 0.05), rgba(0, 21, 41, 0.3));
  border-radius: var(--radius-md);
  border: 1px solid rgba(0, 168, 255, 0.2);
  margin: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.no-detection-results::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(0, 168, 255, 0.1), transparent 70%);
  z-index: 0;
}

.no-detection-results:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transform: translateY(-3px);
}

.no-risk-icon {
  font-size: 36px;
  color: var(--primary-color);
  margin-bottom: 15px;
  text-shadow: 0 0 15px rgba(0, 168, 255, 0.5);
  position: relative;
  z-index: 1;
}

.no-risk-message {
  font-size: 18px;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-container {
    grid-template-columns: 250px 1fr 250px;
    gap: 8px;
  }

  .left-sidebar, .result-panel {
    min-width: 250px;
    width: 250px;
  }
}

@media (max-width: 992px) {
  .main-container {
    grid-template-columns: 220px 1fr 220px;
    gap: 6px;
  }

  .left-sidebar, .result-panel {
    min-width: 220px;
    width: 220px;
  }
}

@media (max-width: 768px) {
  .main-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    gap: 10px;
  }

  .left-sidebar, .result-panel {
    min-width: 100%;
    width: 100%;
  }
}

/* Icon styles */
.all-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'%3E%3C/path%3E%3C/svg%3E");
}

.general-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z'%3E%3C/path%3E%3C/svg%3E");
}

.subgrade-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z'%3E%3C/path%3E%3C/svg%3E");
}

.pavement-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M18.36 9l.6 3H5.04l.6-3h12.72M20 4H4v2h16V4zm0 3H4l-1 5v2h1v6h10v-6h4v6h2v-6h1v-2l-1-5zM6 18v-3h6v3H6z'%3E%3C/path%3E%3C/svg%3E");
}

.bridge-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M6 6h2v12H6zm12 0h-2v12h2zM11 6h2v12h-2zM4 4v16h16V4H4z'%3E%3C/path%3E%3C/svg%3E");
}

.person-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
}

.machine-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23faad14'%3E%3Cpath d='M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z'%3E%3C/path%3E%3C/svg%3E");
}

.material-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 5h-3v5.5c0 1.38-1.12 2.5-2.5 2.5S9 14.88 9 13.5 10.12 11 11.5 11c.57 0 1.08.19 1.5.51V5h4v3z'%3E%3C/path%3E%3C/svg%3E");
}

.regulation-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'%3E%3C/path%3E%3C/svg%3E");
}

.environment-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z'%3E%3C/path%3E%3C/svg%3E");
}

/* 风险分析标题样式 */
.risk-analysis-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.risk-analysis-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(0, 168, 255, 0.2), transparent 70%);
  z-index: 0;
}

.risk-analysis-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.5), transparent);
}

.risk-analysis-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 5px rgba(0, 168, 255, 0.5));
  position: relative;
  z-index: 1;
}

.risk-analysis-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-primary);
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  position: relative;
  z-index: 1;
  letter-spacing: 1px;
}

.detection-box-types {
  margin-top: 16px;
  margin-bottom: 16px;
  background: rgba(0, 21, 41, 0.7);
  border-radius: var(--radius-md);
  padding: 16px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: 5;
}

.box-type-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  position: relative;
}

.box-type-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: var(--primary-color);
  margin-right: 8px;
  border-radius: 2px;
}

.box-type-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.box-type-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  background: rgba(0, 33, 64, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.box-type-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent);
  z-index: 0;
}

.box-type-option.active {
  background: rgba(24, 144, 255, 0.15);
  border-color: var(--primary-color);
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);
}

.box-type-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.box-type-option:not(.disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.box-type-checkbox {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.box-type-option.active .box-type-checkbox {
  border-color: var(--primary-color);
}

.checkbox-inner {
  width: 10px;
  height: 10px;
  border-radius: 2px;
  background-color: transparent;
  transition: all 0.2s ease;
}

.checkbox-inner.checked {
  background-color: var(--primary-color);
}

.box-type-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: inline-block;
  position: relative;
  z-index: 1;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.box-type-color.high-risk {
  background-color: rgba(255, 0, 0, 0.7); /* 鲜红色 */
}

.box-type-color.low-risk {
  background-color: rgba(255, 193, 7, 0.7); /* 明黄色 */
}

.box-type-color.person {
  background-color: rgba(0, 123, 255, 0.7); /* 蓝色 */
}

.box-type-color.vehicle {
  background-color: rgba(111, 66, 193, 0.7); /* 紫色 */
}

.box-type-color.machine {
  background-color: rgba(40, 167, 69, 0.7); /* 绿色 */
}

.box-type-label {
  font-size: 14px;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
  transition: all 0.2s ease;
}

.box-type-option.active .box-type-label {
  color: var(--text-primary);
}

/* 检测统计样式 */
.detection-stats {
  margin-top: 16px;
  margin-bottom: 16px;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  background: rgba(0, 21, 41, 0.5);
  border-radius: var(--radius-md);
  padding: 16px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: var(--radius-sm);
  background: rgba(0, 33, 64, 0.5);
  flex: 1;
  min-width: 0;
  transition: all var(--transition-normal);
}

.stats-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stats-item.high-risk {
  border-left: 3px solid var(--danger-color);
}

.stats-item.person {
  border-left: 3px solid var(--primary-color);
}

.stats-item.vehicle {
  border-left: 3px solid #007bff;
}

.stats-item.machine {
  border-left: 3px solid var(--warning-color);
}

.stats-icon {
  width: 32px;
  height: 32px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.warning-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23f5222d'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'%3E%3C/path%3E%3C/svg%3E");
}

.person-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
}

.vehicle-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007bff'%3E%3Cpath d='M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z'%3E%3C/path%3E%3C/svg%3E");
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stats-count {
  font-size: 20px;
  font-weight: bold;
  color: var(--text-primary);
}

.stats-label {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 更新检测框样式 */
.detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  display: block;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-md);
  z-index: 10;
}

.image-preview-wrapper {
  position: relative;
  max-width: 100%;
  max-height: calc(100% - 10px);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  border: none; /* 移除边框 */
  background: rgba(0, 21, 41, 0.3);
}

.image-preview-wrapper:hover {
  transform: scale(1.01);
  box-shadow: 0 0 20px rgba(24, 144, 255, 0.2);
}

/* 统计数据样式 */
.stats-panel {
  background-color: rgba(0, 33, 64, 0.7);
  border-radius: var(--radius-md);
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 58, 140, 0.3);
  backdrop-filter: blur(5px);
  position: relative;
  z-index: 10; /* 增加z-index确保不被其他元素遮挡 */
}

.stats-header {
  font-size: 16px;
  font-weight: bold;
  color: white;
  text-align: left;
  background: linear-gradient(90deg, #002140, #003a8c);
  padding: 10px 16px;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'%3E%3C/path%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(24, 144, 255, 0.5));
}

.stats-title {
  font-size: 16px;
}

.stats-content {
  padding: 12px;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.stats-grid .stat-item {
  flex: 1;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.7), rgba(0, 21, 41, 0.7));
  border-radius: var(--radius-md);
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  border: 1px solid rgba(24, 144, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.stats-grid .stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: rgba(24, 144, 255, 0.3);
}

.stats-grid .stat-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 6px;
  margin-right: 0;
}

.stats-grid .person-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
}

.stats-grid .vehicle-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z'%3E%3C/path%3E%3C/svg%3E");
}

.stats-grid .machine-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M13 10h-2V8h2v2zm0-4h-2V1h2v5zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm-8.9-5h7.45c.75 0 1.41-.41 1.75-1.03L21 4.96 19.25 4l-3.7 7H8.53L4.27 2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2z'%3E%3C/path%3E%3C/svg%3E");
}

.stats-grid .stat-info {
  text-align: center;
}

.stats-grid .stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.stats-grid .stat-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-color);
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

@media (max-width: 768px) {
  .stats-grid {
    flex-direction: column;
  }
}

/* 横向统计数据样式 */
.stats-panel-horizontal {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.7), rgba(0, 21, 41, 0.7));
  border-bottom: 1px solid rgba(0, 58, 140, 0.3);
  position: relative;
  z-index: 10;
}

.stats-panel-horizontal .stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 8px 5px;
  position: relative;
}

.stats-panel-horizontal .stats-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.stats-panel-horizontal .stats-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.stats-panel-horizontal .stats-info {
  text-align: center;
  width: 100%;
}

.stats-panel-horizontal .stats-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 2px;
  white-space: nowrap;
}

.stats-panel-horizontal .stats-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--primary-color);
  text-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
}

.stats-panel-horizontal .person-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
}

.stats-panel-horizontal .vehicle-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z'%3E%3C/path%3E%3C/svg%3E");
}

.stats-panel-horizontal .machine-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M13 10h-2V8h2v2zm0-4h-2V1h2v5zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm-8.9-5h7.45c.75 0 1.41-.41 1.75-1.03L21 4.96 19.25 4l-3.7 7H8.53L4.27 2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2z'%3E%3C/path%3E%3C/svg%3E");
}

/* 风险分析标题样式 - 调整上边距 */
.risk-analysis-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

/* 上传按钮样式 */
.upload-button {
  padding: 10px 20px;
  border: none;
  border-radius: 50px;
  background: linear-gradient(to right, #0c8eff, #0058ff);
  color: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(12, 142, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-icon-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4v6zm-4 2h14v2H5v-2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.upload-button:hover {
  background: linear-gradient(to right, #0a6fcc, #0046cc);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(12, 142, 255, 0.3);
}

/* 切换按钮样式更新 */
.toggle-button {
  padding: 10px 20px;
  border: none;
  border-radius: 50px;
  background: rgba(12, 142, 255, 0.15);
  color: #0c8eff;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(12, 142, 255, 0.1);
  border: 1px solid rgba(12, 142, 255, 0.3);
}

.toggle-button:hover {
  background: rgba(12, 142, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(12, 142, 255, 0.2);
}

.model-selector {
  position: relative;
  min-width: 150px;
}

.selected-model {
  cursor: pointer;
  padding: 12px 15px;
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 50px;
  background: rgba(0, 21, 41, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 10px rgba(0, 21, 41, 0.2);
  transition: all 0.3s ease;
  height: 48px;
}

.selected-model:hover {
  background: rgba(0, 58, 140, 0.5);
  border-color: rgba(0, 168, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 21, 41, 0.3);
}

.model-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M12 3L1 9l11 6 9-4.91V17h2V9L12 3zm0 3.97l7 3.83L12 13.97l-9-5 9-2zm-1 9.93v7l9-4V13l-9 3.9z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.dropdown-icon {
  font-size: 10px;
  margin-left: 5px;
  opacity: 0.7;
}

.model-dropdown {
  position: absolute;
  bottom: 100%; /* 改为bottom而不是top，让菜单向上展开 */
  left: 0;
  background: rgba(0, 21, 41, 0.95);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  z-index: 100;
  width: 100%;
  margin-bottom: 8px; /* 改为margin-bottom而不是margin-top */
  overflow: hidden;
}

.model-option {
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 168, 255, 0.1);
  color: var(--text-primary);
}

.model-option:last-child {
  border-bottom: none;
}

.model-option:hover {
  background: rgba(0, 58, 140, 0.5);
}

.model-option.active {
  background: linear-gradient(90deg, rgba(0, 168, 255, 0.2), rgba(0, 58, 140, 0.6));
  border-color: var(--navbar-accent);
}

.model-name {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 4px;
  color: white;
}

.model-description {
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.8;
}

.platforms-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  margin-right: 40px;
}

.platform-item {
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.3s ease;
  position: relative;
  text-align: left;
  min-width: 180px;
}

.platform-options {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(0, 33, 64, 0.5);
  padding: 8px 15px;
  border-radius: 30px;
  border: 1px solid rgba(0, 168, 255, 0.2);
}

.platform-text {
  font-size: 14px;
  font-weight: bold;
  color: var(--text-primary);
  text-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
}

.platform-item:hover {
  background: rgba(0, 168, 255, 0.1);
}

.platform-item.active {
  background: rgba(0, 168, 255, 0.15);
}

.platform-underline {
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--navbar-accent), transparent);
  margin-top: 5px;
  width: 80%;
  margin-left: 10%;
}

.option-item {
  cursor: pointer;
  padding: 6px 15px;
  border-radius: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.option-item:hover {
  background: rgba(0, 168, 255, 0.1);
  transform: translateY(-2px);
}

.option-item.active {
  background: rgba(0, 168, 255, 0.2);
  box-shadow: 0 0 10px rgba(0, 168, 255, 0.3);
}

.option-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 6px;
  filter: drop-shadow(0 0 2px rgba(0, 168, 255, 0.5));
}

.option-text {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 13px;
}

.detection-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M9.5 9.5c0 .55.45 1 1 1h2c.55 0 1-.45 1-1V8h1c.55 0 1-.45 1-1s-.45-1-1-1h-1V5c0-.55-.45-1-1-1h-2c-.55 0-1 .45-1 1v2.5z'/%3E%3Cpath d='M20 12c0-2.54-1.19-4.81-3.04-6.27L16 0H8l-.95 5.73C5.19 7.19 4 9.45 4 12s1.19 4.81 3.05 6.27L8 24h8l.96-5.73C18.81 16.81 20 14.54 20 12zM12 2.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5zm0 19c-5.23 0-9.5-4.27-9.5-9.5S6.77 2.5 12 2.5s9.5 4.27 9.5 9.5-4.27 9.5-9.5 9.5z'/%3E%3C/svg%3E");
}

.chat-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12z'/%3E%3C/svg%3E");
}

.monitor-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14zm-10-7h9v6h-9z'/%3E%3C/svg%3E");
}

/* Add styling for the main title */
.main-title {
  font-size: 26px;
  font-weight: bold;
  color: var(--text-primary);
  text-shadow: 0 0 15px rgba(0, 168, 255, 0.7);
  text-align: center;
  letter-spacing: 2px;
  background: linear-gradient(to bottom, #ffffff, #88ccff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  padding: 0 30px;
}

.main-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--navbar-accent), transparent);
}

.navbar-center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 2;
}

.navbar-left, .navbar-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
}

/* 添加运营平台的图标样式 */
.road_structure-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM13.96 12.29l-2.75 3.54-1.96-2.36L6.5 17h11l-3.54-4.71z'/%3E%3C/svg%3E");
}

.vehicle_accident-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.85 6h10.29l1.08 3.11H5.77L6.85 6zM19 17H5v-5h14v5z'/%3E%3C/svg%3E");
}

.abnormal_driving-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
}

/* 添加运营平台的检测子类别图标 */
.road_structure-category-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M13.2 7.07L10.25 11l2.25 3c.33.44.24 1.07-.2 1.4-.44.33-1.07.25-1.4-.2-1.05-1.4-2.31-3.07-3.1-4.14-.4-.53-1.2-.53-1.6 0l-4 5.33c-.49.67-.02 1.61.8 1.61h18c.82 0 1.29-.94.8-1.6l-7-9.33c-.4-.54-1.2-.54-1.6 0z'/%3E%3C/svg%3E");
}

.vehicle_accident-category-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.85 6h10.29l1.08 3.11H5.77L6.85 6zM19 17H5v-5h14v5z'/%3E%3C/svg%3E");
}

.abnormal_driving-category-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 3c-4.96 0-9 4.04-9 9s4.04 9 9 9 9-4.04 9-9-4.04-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v6h2V8zm0 8h-2v2h2v-2z'/%3E%3C/svg%3E");
}

.main-container {
  display: grid;
  /* Updated grid-template-columns to handle single view for chat/monitor */
  grid-template-columns: v-bind("currentOption === 'detection' ? '320px 1fr 320px' : '1fr'");
  gap: 15px;
  height: calc(100vh - var(--navbar-height));
  background-color: var(--bg-darker);
  padding: 15px;
}

.chat-view,
.monitor-view {
  /* Styles for full-width chat/monitor views */
  grid-column: 1 / -1; /* Span all columns if main-container is still a grid with multiple columns defined */
  background-color: var(--bg-darker); /* Match main container background */
  padding: 0px; /* Remove padding if ChatInterface handles its own padding */
  border-radius: var(--radius-md);
  display: flex; /* Ensure it takes full height if needed */
  flex-direction: column; /* Ensure it takes full height if needed */
  height: 100%; /* Ensure it takes full height if needed */
}

.monitor-view { /* Specific styling for monitor placeholder if needed */
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  background-color: var(--card-bg); /* Give it a card background like chat for consistency */
}

.monitor-options-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-tabs {
  display: flex;
  gap: 2px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  padding: 12px 20px 0;
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
  border-bottom: 1px solid rgba(0, 168, 255, 0.2);
}

.monitor-tab {
  padding: 12px 24px;
  background: rgba(0, 21, 41, 0.6);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  bottom: -1px;
  border: 1px solid transparent;
  border-bottom: none;
}

.monitor-tab:hover {
  background: rgba(0, 33, 64, 0.8);
}

.monitor-tab.active {
  background: var(--card-bg);
  border-color: rgba(0, 168, 255, 0.3);
  border-bottom-color: var(--card-bg);
  z-index: 2;
}

.tab-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 3px rgba(0, 168, 255, 0.5));
}

.online-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14zm-10-7h9v6h-9z'/%3E%3C/svg%3E");
}

.video-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z'/%3E%3C/svg%3E");
}



.tab-text {
  color: var(--text-primary);
  font-weight: 500;
}

.monitor-content {
  flex: 1;
  background-color: var(--card-bg);
  border: 1px solid rgba(0, 168, 255, 0.2);
  border-top: none;
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
}

/* Monitor view */
.monitor-view {
  width: 100%;
  height: 100%;
  background-color: transparent;
  padding: 0;
  overflow-y: auto;
}
</style>

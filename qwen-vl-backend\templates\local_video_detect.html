<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地视频分析系统</title>
    <style>
        :root {
            --primary-color: #1890ff;
            --secondary-color: #1d39c4;
            --danger-color: #ff4d4f;
            --warning-color: #faad14;
            --text-dark: #fff;
            --text-light: #ffffff;
            --bg-light: #001529;
            --bg-dark: #000c17;
            --border-color: #003a8c;
            --card-bg: #001f3d;
            --border-radius: 8px;
            --shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
            --bg-content: #000c17;
            --header-bg: #002140;
            --sidebar-header-bg: #002140;
            --category-bg: #002140;
            --category-hover: #003a8c;
            --primary-hover: #40a9ff;
            --border-light: #1d39c4;
            --success-color: #52c41a;
            --text-secondary: #aaa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-light) 100%);
            color: var(--text-light);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .progress-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: rgba(255, 255, 255, 0.1);
            z-index: 9999;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 16px;
            min-width: 300px;
            box-shadow: var(--shadow);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 9998;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .notification-title {
            font-weight: 600;
            color: var(--text-light);
        }

        .notification-close {
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 18px;
            line-height: 1;
        }

        .notification-close:hover {
            color: var(--text-light);
        }

        .notification-message {
            color: var(--text-secondary);
        }

        .notification-success {
            border-left: 4px solid var(--success-color);
        }

        .notification-error {
            border-left: 4px solid var(--danger-color);
        }

        .notification-warning {
            border-left: 4px solid var(--warning-color);
        }

        .app-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-light);
            margin: 0;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
        }

        .video-section {
            display: grid;
            grid-template-columns: 1fr 500px;
            gap: 20px;
            align-items: start;
        }

        @media (max-width: 1200px) {
            .video-section {
                grid-template-columns: 1fr;
            }
        }

        .video-container {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .card-header {
            background: var(--header-bg);
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-light);
        }

        .card-tools {
            display: flex;
            gap: 8px;
        }

        .card-body {
            padding: 20px;
        }

        .file-upload-container {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            background: var(--bg-light);
        }

        .file-upload-container:hover {
            border-color: var(--primary-color);
            background: var(--card-bg);
        }

        .file-upload-container.dragover {
            border-color: var(--primary-color);
            background: var(--card-bg);
            transform: scale(1.02);
        }

        .upload-icon {
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .upload-text {
            font-size: 16px;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .file-upload-container input[type="file"] {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .file-info {
            margin: 16px 0;
            padding: 16px;
            background: var(--bg-light);
            border-radius: var(--border-radius);
            display: none;
        }

        .file-info.show {
            display: block;
        }

        .file-name {
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .file-size, .file-type {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .video-preview-wrapper {
            position: relative;
            background: #000;
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-top: 16px;
        }

        #videoInput {
            width: 100%;
            height: auto;
            display: none;
        }

        .detection-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }

        .video-controls {
            display: none;
            align-items: center;
            gap: 12px;
            margin-top: 12px;
            padding: 12px;
            background: var(--bg-light);
            border-radius: var(--border-radius);
        }

        .video-button {
            background: var(--primary-color);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .video-button:hover {
            background: var(--primary-hover);
        }

        .video-button svg {
            color: white;
        }

        .video-progress {
            flex: 1;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            cursor: pointer;
            position: relative;
        }

        .video-progress-fill {
            height: 100%;
            background: var(--primary-color);
            border-radius: 2px;
            width: 0%;
            transition: width 0.1s ease;
        }

        .video-time {
            font-size: 12px;
            color: var(--text-secondary);
            min-width: 80px;
            text-align: right;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .loading-spinner {
            display: flex;
            gap: 4px;
            margin-bottom: 16px;
        }

        .spinner-circle {
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
            animation: bounce 1.4s ease-in-out infinite both;
        }

        .spinner-circle:nth-child(1) { animation-delay: -0.32s; }
        .spinner-circle:nth-child(2) { animation-delay: -0.16s; }

        @keyframes bounce {
            0%, 80%, 100% {
                transform: scale(0);
            } 40% {
                transform: scale(1);
            }
        }

        .loading-text {
            color: var(--text-light);
            font-size: 14px;
        }

        .controls {
            padding: 16px 20px;
            background: var(--bg-light);
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--primary-hover);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background: #ff7875;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover:not(:disabled) {
            background: #73d13d;
        }

        .btn-disabled {
            background: var(--text-secondary) !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }

        .sidebar-panel {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .panel-section {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .result-panel {
            flex: 1;
        }

        .result-header {
            background: var(--header-bg);
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .result-tabs {
            display: flex;
            gap: 4px;
        }

        .tab-button {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background: transparent;
            color: var(--text-secondary);
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .tab-button:hover:not(.active) {
            background: var(--category-hover);
            color: var(--text-light);
        }

        .result-content {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-state svg {
            margin-bottom: 16px;
        }

        .info-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.5;
        }

        .system-stats {
            background: var(--card-bg);
        }

        .stats-header {
            background: var(--header-bg);
            padding: 12px 20px;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .stats-row:last-child {
            border-bottom: none;
        }

        .stats-label {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .stats-value {
            font-size: 13px;
            font-weight: 600;
            color: var(--text-light);
        }

        .stats-progress-bar {
            padding: 12px 20px;
        }

        .progress-bg {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            width: 75%;
            transition: width 0.3s ease;
        }

        .progress-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 6px;
        }

        .status-bar {
            padding: 12px 20px;
            background: var(--bg-light);
            font-size: 14px;
            color: var(--text-light);
        }

        .status-error {
            background: var(--danger-color);
            color: white;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .metric-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            box-shadow: var(--shadow);
        }

        .metric-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .metric-title {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .metric-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-light);
        }

        .settings-container {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            margin-top: 20px;
        }

        .settings-header {
            background: var(--header-bg);
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            font-size: 16px;
            font-weight: 600;
            color: var(--text-light);
        }

        .settings-body {
            padding: 20px;
        }

        .settings-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .settings-group {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .setting-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .settings-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
        }

        .setting-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .vue-select {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px 12px;
            color: var(--text-light);
            font-size: 14px;
            min-width: 120px;
        }

        .vue-select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .vue-select:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .setting-mode-switch {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--border-color);
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        .slider.round {
            border-radius: 20px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .setting-description {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .health-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .health-good {
            background-color: var(--success-color);
            box-shadow: 0 0 6px rgba(82, 196, 26, 0.6);
        }

        .health-warning {
            background-color: var(--warning-color);
            box-shadow: 0 0 6px rgba(250, 173, 20, 0.6);
        }

        .health-error {
            background-color: var(--danger-color);
            box-shadow: 0 0 6px rgba(255, 77, 79, 0.6);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                padding: 10px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .performance-metrics {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .settings-row {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-light);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* 分析结果样式 */
        .analysis-results {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .analysis-tabs {
            display: flex;
            gap: 4px;
            margin-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 8px;
        }

        .analysis-tabs .tab-button {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: transparent;
            color: var(--text-secondary);
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .analysis-tabs .tab-button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .analysis-tabs .tab-button:hover:not(.active) {
            background: var(--category-hover);
            color: var(--text-light);
        }

        .analysis-content {
            flex: 1;
            position: relative;
        }

        .tab-content {
            display: none;
            height: 100%;
        }

        .tab-content.active {
            display: block;
        }

        .analysis-text {
            color: var(--text-light);
            line-height: 1.6;
            font-size: 14px;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 300px;
            overflow-y: auto;
            padding: 12px;
            background: var(--bg-light);
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }

        .analysis-progress {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .analysis-section {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 16px;
        }

        .analysis-section h4 {
            color: var(--text-light);
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: 600;
        }

        .reasoning-content, .answer-content {
            color: var(--text-light);
            line-height: 1.6;
            font-size: 14px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* 动画效果 */
        .video-container, .settings-panel, .result-panel, .panel-section, .metric-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .video-container:hover, .settings-panel:hover, .result-panel:hover, .panel-section:hover, .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        /* 实时检测结果样式 */
        .detection-timestamp {
            background: var(--primary-color);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 12px;
            font-size: 14px;
            text-align: center;
        }

        .risk-section {
            margin-bottom: 16px;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .risk-section.high-risk {
            background: rgba(255, 77, 79, 0.1);
            border-left-color: var(--danger-color);
        }

        .risk-section.low-risk {
            background: rgba(250, 173, 20, 0.1);
            border-left-color: var(--warning-color);
        }

        .risk-section h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
        }

        .detection-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 8px;
            margin-bottom: 4px;
            border-radius: 4px;
            font-size: 13px;
        }

        .detection-item.high-risk {
            background: rgba(255, 77, 79, 0.15);
        }

        .detection-item.low-risk {
            background: rgba(250, 173, 20, 0.15);
        }

        .detection-category {
            font-weight: 600;
            color: var(--text-light);
        }

        .detection-label {
            flex: 1;
            margin: 0 8px;
            color: var(--text-secondary);
        }

        .detection-confidence {
            font-weight: 600;
            color: var(--success-color);
        }

        .no-detection {
            text-align: center;
            padding: 20px;
            color: var(--success-color);
            font-size: 14px;
        }

        /* 最终报告样式 */
        .final-report {
            padding: 20px;
            background: var(--card-bg);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .report-header {
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .report-header h3 {
            margin: 0 0 12px 0;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .report-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .report-stats span {
            background: var(--bg-light);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 16px;
            color: white;
            font-weight: 500;
        }

        .report-content {
            line-height: 1.8;
            color: white;
            font-size: 16px;
        }

        .report-content strong {
            color: white;
            font-weight: 700;
            font-size: 18px;
        }

        .report-content h1, .report-content h2, .report-content h3, .report-content h4 {
            color: white;
            margin: 20px 0 12px 0;
            font-weight: bold;
        }

        .report-content h1 {
            font-size: 22px;
        }

        .report-content h2 {
            font-size: 20px;
        }

        .report-content h3 {
            font-size: 18px;
        }

        .report-content h4 {
            font-size: 16px;
        }

        /* 右侧面板布局 */
        .panel-section.result-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .result-content {
            display: flex;
            flex-direction: column;
            height: 100%;
            flex: 1;
        }

        /* 环境描述样式 - 固定在上方 */
        .environment-description {
            padding: 16px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 16px;
            flex-shrink: 0;
        }

        .environment-header {
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 8px;
            margin-bottom: 12px;
        }

        .environment-header h3 {
            margin: 0;
            color: white;
            font-size: 16px;
        }

        .environment-content {
            line-height: 1.6;
            color: white;
            font-size: 14px;
        }

        .environment-content strong {
            color: white;
            font-weight: 600;
        }

        /* 交通事件区域 - 中间可滚动 */
        .traffic-events-container {
            flex: 1;
            overflow-y: auto;
            padding: 0 4px;
            margin-bottom: 16px;
        }

        .traffic-event {
            margin-bottom: 12px;
            padding: 12px;
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            border-left: 4px solid var(--primary-color);
        }

        .event-timestamp {
            color: white;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .event-description {
            color: white;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 统计信息 - 固定在下方 */
        .traffic-statistics {
            padding: 16px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            flex-shrink: 0;
        }

        .statistics-header {
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 12px;
            text-align: center;
        }

        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .stat-item {
            background: var(--bg-light);
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-label {
            color: white;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .stat-value {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 进度条 -->
    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>

    <!-- 通知区域 -->
    <div class="notification" id="notification">
        <div class="notification-header">
            <div class="notification-title">通知</div>
            <div class="notification-close">×</div>
        </div>
        <div class="notification-message">操作成功</div>
    </div>

    <div class="app-container">
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">本地视频分析系统</h1>
                <div class="connection-status">
                    <span class="health-indicator health-good" id="connectionHealth"></span>
                    <span id="connectionStatus">系统正常</span>
                </div>
            </div>

            <div class="video-section">
                <!-- 视频和检测结果显示区域 -->
                <div class="video-container">
                    <div class="card-header">
                        <div class="card-title">本地视频</div>
                        <div class="card-tools">
                            <button class="btn btn-success" id="toggleDetectionBtn">显示检测框</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 视频上传区域 -->
                        <div class="file-upload-container" id="uploadArea">
                            <svg class="upload-icon" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                            </svg>
                            <div class="upload-text">点击或拖拽上传视频文件</div>
                            <div class="upload-hint">支持MP4、AVI、MOV等常见视频格式</div>
                            <input type="file" id="videoFile" accept="video/*">
                        </div>

                        <!-- 文件信息 -->
                        <div class="file-info" id="fileInfo">
                            <div class="file-name" id="fileName"></div>
                            <div class="file-size" id="fileSize"></div>
                            <div class="file-type" id="fileType"></div>
                        </div>

                        <div class="video-preview-wrapper">
                            <video id="videoInput" controls playsinline></video>
                            <canvas id="detectionCanvas" class="detection-canvas"></canvas>
                            <canvas id="outputCanvas" style="display: none;"></canvas>
                        </div>

                        <!-- 视频控制器 -->
                        <div class="video-controls" id="videoControls" style="display: none;">
                            <button class="video-button" id="playPauseBtn">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                </svg>
                            </button>
                            <div class="video-progress" id="videoProgress">
                                <div class="video-progress-fill" id="videoProgressFill"></div>
                            </div>
                            <div class="video-time" id="videoTime">00:00 / 00:00</div>
                        </div>

                        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                            <div class="loading-spinner">
                                <div class="spinner-circle"></div>
                                <div class="spinner-circle"></div>
                                <div class="spinner-circle"></div>
                            </div>
                            <div class="loading-text">视频处理中...</div>
                        </div>
                    </div>
                    <div class="controls">
                        <button class="btn btn-primary" id="startBtn"><span>开始处理</span></button>
                        <button class="btn btn-danger btn-disabled" id="stopBtn" disabled><span>停止处理</span></button>
                    </div>
                </div>

                <!-- 检测结果面板 (右侧) -->
                <div class="sidebar-panel">
                    <div class="panel-section result-panel">
                        <div class="result-header">
                            <div class="card-title">检测结果</div>
                            <div class="result-tabs">
                                <button class="tab-button active" data-tab="all">全部</button>
                                <button class="tab-button" data-tab="high-risk">高风险</button>
                                <button class="tab-button" data-tab="low-risk">低/中风险</button>
                            </div>
                        </div>
                        <div class="result-content" id="resultInfo">
                            <div class="empty-state">
                                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                </svg>
                                <div class="info-title">未开始检测</div>
                                <p>请上传视频文件，然后点击"开始处理"按钮启动视频分析。</p>
                            </div>
                        </div>
                    </div>

                    <!-- 系统状态信息 -->
                    <div class="panel-section system-stats">
                        <div class="stats-header">系统状态</div>
                        <div class="stats-row">
                            <div class="stats-label">当前延迟:</div>
                            <div class="stats-value" id="currentLatency">0 ms</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">实际帧率:</div>
                            <div class="stats-value" id="actualFrameRate">0 FPS</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">目标帧率:</div>
                            <div class="stats-value" id="targetFrameRate">15 FPS</div>
                        </div>
                        <div class="stats-progress-bar">
                            <div class="progress-bg">
                                <div class="progress-fill" id="networkQualityBar"></div>
                            </div>
                            <div class="progress-label">处理质量</div>
                        </div>
                    </div>

                    <div class="panel-section">
                        <div class="status-bar" id="status">
                            状态: 等待上传视频
                        </div>
                    </div>
                </div>
            </div>

            <!-- 性能指标 -->
            <div class="performance-metrics">
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                            <path d="M2 17l10 5 10-5"></path>
                            <path d="M2 12l10 5 10-5"></path>
                        </svg>
                    </div>
                    <div class="metric-title">发送帧数</div>
                    <div class="metric-value" id="sentFrames">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                        </svg>
                    </div>
                    <div class="metric-title">接收帧数</div>
                    <div class="metric-value" id="receivedFrames">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                    </div>
                    <div class="metric-title">平均延迟</div>
                    <div class="metric-value" id="avgLatency">0 ms</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
                        </svg>
                    </div>
                    <div class="metric-title">接收帧率</div>
                    <div class="metric-value" id="receiveFps">0 FPS</div>
                </div>
            </div>

            <!-- 处理设置面板 -->
            <div class="settings-container">
                <div class="settings-header">
                    <div class="card-title">处理设置</div>
                </div>
                <div class="settings-body">
                    <div class="settings-row">
                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label" for="frameRate">处理帧率</label>
                                <div class="setting-info">
                                    <select id="frameRate" class="vue-select" disabled>
                                        <option value="30">30 FPS</option>
                                        <option value="15" selected>15 FPS</option>
                                        <option value="10">10 FPS</option>
                                        <option value="5">5 FPS</option>
                                        <option value="2">2 FPS</option>
                                    </select>
                                    <div class="setting-mode-switch">
                                        <span>自动</span>
                                        <label class="switch" for="autoFrameRateToggle">
                                            <input type="checkbox" id="autoFrameRateToggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-description">
                                    <span id="frameRateDescription">当前使用自适应帧率模式，系统将根据网络延迟自动调整。</span>
                                </div>
                            </div>
                        </div>

                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label" for="imageQuality">图像质量</label>
                                <div class="setting-info">
                                    <select id="imageQuality" class="vue-select">
                                        <option value="1.0">100%</option>
                                        <option value="0.8" selected>80%</option>
                                        <option value="0.6">60%</option>
                                        <option value="0.4">40%</option>
                                    </select>
                                </div>
                                <div class="setting-description">
                                    更高的图像质量可能增加网络传输负担。
                                </div>
                            </div>
                        </div>

                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label" for="imageSize">图像尺寸</label>
                                <div class="setting-info">
                                    <select id="imageSize" class="vue-select">
                                        <option value="1.0">100%</option>
                                        <option value="0.75" selected>75%</option>
                                        <option value="0.5">50%</option>
                                    </select>
                                </div>
                                <div class="setting-description">
                                    更小的尺寸可以提高处理速度和降低网络负载。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let videoElement = document.getElementById('videoInput');
        let detectionCanvas = document.getElementById('detectionCanvas');
        let outputCanvas = document.getElementById('outputCanvas');
        let ctx = detectionCanvas.getContext('2d');
        let startButton = document.getElementById('startBtn');
        let stopButton = document.getElementById('stopBtn');
        let toggleDetectionBtn = document.getElementById('toggleDetectionBtn');
        let statusElement = document.getElementById('status');
        let loadingOverlay = document.getElementById('loadingOverlay');
        let resultInfoElement = document.getElementById('resultInfo');
        let frameRateSelect = document.getElementById('frameRate');
        let imageQualitySelect = document.getElementById('imageQuality');
        let imageSizeSelect = document.getElementById('imageSize');
        let progressBar = document.getElementById('progressBar');
        let connectionHealth = document.getElementById('connectionHealth');
        let connectionStatus = document.getElementById('connectionStatus');
        let notification = document.getElementById('notification');
        let autoFrameRateToggle = document.getElementById('autoFrameRateToggle');
        let frameRateDescription = document.getElementById('frameRateDescription');
        let currentLatencyElement = document.getElementById('currentLatency');
        let actualFrameRateElement = document.getElementById('actualFrameRate');
        let targetFrameRateElement = document.getElementById('targetFrameRate');
        let networkQualityBar = document.getElementById('networkQualityBar');

        // 视频文件上传相关元素
        let uploadArea = document.getElementById('uploadArea');
        let videoFileInput = document.getElementById('videoFile');
        let fileInfoElement = document.getElementById('fileInfo');
        let fileNameElement = document.getElementById('fileName');
        let fileSizeElement = document.getElementById('fileSize');
        let fileTypeElement = document.getElementById('fileType');
        let videoControls = document.getElementById('videoControls');
        let playPauseBtn = document.getElementById('playPauseBtn');
        let videoProgress = document.getElementById('videoProgress');
        let videoProgressFill = document.getElementById('videoProgressFill');
        let videoTimeDisplay = document.getElementById('videoTime');

        // 保存最新千问分析结果的变量
        let lastQwenResult = null; // 存储最后一次千问分析结果
        let lastQwenResultTime = 0; // 记录最后一次千问分析结果时间戳

        // 性能指标元素
        let sentFramesElement = document.getElementById('sentFrames');
        let receivedFramesElement = document.getElementById('receivedFrames');
        let avgLatencyElement = document.getElementById('avgLatency');
        let receiveFpsElement = document.getElementById('receiveFps');

        // 视频和处理相关变量
        let selectedVideoFile = null;
        let videoObjectURL = null;

        // WebSocket 连接 - 修改为连接YOLO专用后端
        let ws = null;
        let isRunning = false;
        let frameCount = 0;
        let processedCount = 0;
        let lastFrameTime = 0;
        let lastStatsUpdate = 0;
        let framerateInterval = 1000 / parseInt(frameRateSelect.value);

        // 实时检测结果存储
        let realTimeDetections = [];
        let videoStartTime = null;
        let isVideoComplete = false;

        // 性能指标
        let totalLatency = 0;
        let maxLatency = 0;
        let minLatency = Infinity;
        let framesSent = 0;
        let framesReceived = 0;
        let showDetectionBoxes = true;
        let recentLatencies = []; // 用于计算动态帧率
        let isAutoFrameRate = true; // 是否启用自动帧率控制
        let dynamicFrameRate = 15; // 当前动态帧率
        let lastFrameRateAdjustment = 0; // 上次调整帧率的时间
        let framesSentLastSecond = 0; // 用于计算实际帧率
        let lastSecondStart = 0; // 上一秒开始时间

        // 自适应帧率参数
        const AUTO_RATE_CONFIG = {
            minFrameRate: 2,
            maxFrameRate: 30,
            optimalLatency: 200, // 理想延迟(ms)
            adjustInterval: 2000, // 调整间隔(ms)
            latencySampleSize: 10, // 采样数量
            frameFateSteps: [2, 5, 10, 15, 20, 25, 30] // 可用帧率选项
        };

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 格式化时间
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            seconds = Math.floor(seconds % 60);
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 添加函数用于格式化时间差
        function formatTimeDifference(timestamp) {
            if (!timestamp) return '';

            const now = new Date().getTime();
            const diff = now - timestamp;

            // 计算秒、分钟、小时
            const seconds = Math.floor(diff / 1000);
            if (seconds < 60) {
                return `${seconds}秒前`;
            }

            const minutes = Math.floor(seconds / 60);
            if (minutes < 60) {
                return `${minutes}分钟前`;
            }

            const hours = Math.floor(minutes / 60);
            return `${hours}小时${minutes % 60}分钟前`;
        }

        // 显示通知函数
        function showNotification(message, type = 'success') {
            const notificationEl = document.getElementById('notification');
            const messageEl = notificationEl.querySelector('.notification-message');

            // 设置样式和内容
            notificationEl.className = 'notification show notification-' + type;
            messageEl.textContent = message;

            // 显示通知
            setTimeout(() => {
                notificationEl.classList.add('show');
            }, 10);

            // 3秒后自动隐藏
            setTimeout(() => {
                notificationEl.classList.remove('show');
            }, 3000);
        }

        // 更新进度条
        function updateProgressBar(percent) {
            progressBar.style.width = percent + '%';
        }

        // 更新连接状态指示器
        function updateConnectionHealth(status) {
            // 移除所有类名
            connectionHealth.className = 'health-indicator';

            // 根据状态设置类名和文字
            switch (status) {
                case 'good':
                    connectionHealth.classList.add('health-good');
                    connectionStatus.textContent = '系统正常';
                    break;
                case 'warning':
                    connectionHealth.classList.add('health-warning');
                    connectionStatus.textContent = '系统延迟';
                    break;
                case 'error':
                    connectionHealth.classList.add('health-error');
                    connectionStatus.textContent = '连接异常';
                    break;
            }
        }

        // 自适应帧率控制
        function adjustFrameRate(latency) {
            // 添加当前延迟到最近延迟数组
            recentLatencies.push(latency);

            // 保持数组大小
            if (recentLatencies.length > AUTO_RATE_CONFIG.latencySampleSize) {
                recentLatencies.shift();
            }

            // 每隔一段时间调整帧率
            const now = performance.now();
            if (now - lastFrameRateAdjustment < AUTO_RATE_CONFIG.adjustInterval) {
                return;
            }

            if (!isAutoFrameRate || recentLatencies.length < 3) {
                return;
            }

            // 计算平均延迟
            const avgLatency = recentLatencies.reduce((sum, val) => sum + val, 0) / recentLatencies.length;

            // 根据延迟选择合适的帧率
            let newFrameRate = dynamicFrameRate;

            // 延迟阈值和对应帧率设置
            if (avgLatency > 800) {
                newFrameRate = 2; // 非常高延迟
            } else if (avgLatency > 500) {
                newFrameRate = 5; // 高延迟
            } else if (avgLatency > 300) {
                newFrameRate = 10; // 中等延迟
            } else if (avgLatency > 200) {
                newFrameRate = 15; // 低延迟
            } else if (avgLatency > 100) {
                newFrameRate = 20; // 很低延迟
            } else {
                newFrameRate = 30; // 极低延迟，接近实时
            }

            // 只有当帧率需要变化时才进行更新
            if (newFrameRate !== dynamicFrameRate) {
                dynamicFrameRate = newFrameRate;
                framerateInterval = 1000 / dynamicFrameRate;
                console.log(`自适应帧率调整为: ${dynamicFrameRate} FPS (平均延迟: ${avgLatency.toFixed(0)}ms)`);

                // 更新UI显示
                targetFrameRateElement.textContent = `${dynamicFrameRate} FPS`;

                // 更新网络质量条
                const qualityPercent = 100 - Math.min(100, (avgLatency / 1000) * 100);
                networkQualityBar.style.width = `${qualityPercent}%`;
            }

            lastFrameRateAdjustment = now;
        }

        // 更新状态信息
        function updateStatus(message, isError) {
            statusElement.textContent = '状态: ' + message;
            statusElement.className = isError ? 'status-bar status-error' : 'status-bar';
        }

        // 更新统计信息
        function updateStats(isFinal = false) {
            const avgLatency = framesReceived > 0 ? (totalLatency / framesReceived).toFixed(2) : 0;
            const fps = (framesReceived / ((performance.now() - lastStatsUpdate) / 1000)).toFixed(2);

            sentFramesElement.textContent = framesSent;
            receivedFramesElement.textContent = framesReceived;
            avgLatencyElement.textContent = `${avgLatency} ms`;
            receiveFpsElement.textContent = `${fps} FPS`;

            if (isFinal) {
                updateStatus('处理已完成', false);
            }
        }

        // 初始化函数
        async function init() {
            try {
                // 关闭通知按钮事件
                const closeBtn = document.querySelector('.notification-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', function() {
                        document.getElementById('notification').classList.remove('show');
                    });
                }

                // 设置定时器，定期更新分析结果的时间信息
                setInterval(() => {
                    // 如果有分析结果且超过5秒，更新时间显示
                    if (lastQwenResult && lastQwenResultTime > 0 && (new Date().getTime() - lastQwenResultTime > 5000)) {
                        const timestampElement = resultInfoElement.querySelector('.result-timestamp');
                        if (timestampElement) {
                            const now = new Date();
                            const timeString = now.toLocaleTimeString();
                            timestampElement.innerHTML = `更新时间: ${timeString} <span style="color: var(--warning-color);">(${formatTimeDifference(lastQwenResultTime)})</span>`;
                        }
                    }
                }, 10000); // 每10秒更新一次

                // 设置文件上传区域事件
                setupFileUpload();

                // 设置视频控制事件
                setupVideoControls();

                // 自动/手动帧率切换
                autoFrameRateToggle.addEventListener('change', function() {
                    isAutoFrameRate = this.checked;
                    frameRateSelect.disabled = isAutoFrameRate;

                    if (isAutoFrameRate) {
                        frameRateDescription.textContent = '当前使用自适应帧率模式，系统将根据网络延迟自动调整。';
                        // 重置为自适应计算的帧率
                        framerateInterval = 1000 / dynamicFrameRate;
                    } else {
                        frameRateDescription.textContent = '手动设置帧率可能会影响视频处理的流畅度。';
                        // 使用用户选择的帧率
                        framerateInterval = 1000 / parseInt(frameRateSelect.value);
                    }

                    console.log(`帧率模式切换: ${isAutoFrameRate ? '自动' : '手动'}, 当前帧率: ${isAutoFrameRate ? dynamicFrameRate : parseInt(frameRateSelect.value)} FPS`);
                });

                // 帧率选择事件
                frameRateSelect.addEventListener('change', function() {
                    if (!isAutoFrameRate) {
                        framerateInterval = 1000 / parseInt(this.value);
                    }
                });

                // 切换检测框显示
                toggleDetectionBtn.addEventListener('click', function() {
                    showDetectionBoxes = !showDetectionBoxes;
                    this.textContent = showDetectionBoxes ? '隐藏检测框' : '显示检测框';

                    if (!showDetectionBoxes && detectionCanvas) {
                        const ctx = detectionCanvas.getContext('2d');
                        if (ctx) {
                            ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                        }
                    }
                });

                // 设置开始按钮事件
                startButton.addEventListener('click', startProcessing);

                // 设置停止按钮事件
                stopButton.addEventListener('click', stopProcessing);

                // 标签页切换功能
                setupTabButtons();

            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('初始化失败: ' + error.message, true);
                updateConnectionHealth('error');
            }
        }

        // 设置文件上传区域事件
        function setupFileUpload() {
            // 点击上传区域触发文件选择
            uploadArea.addEventListener('click', function() {
                videoFileInput.click();
            });

            // 处理文件选择
            videoFileInput.addEventListener('change', handleFileSelect);

            // 拖拽上传
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('drag-over');

                if (e.dataTransfer.files.length > 0) {
                    const file = e.dataTransfer.files[0];
                    if (file.type.startsWith('video/')) {
                        handleVideoFile(file);
                    } else {
                        showNotification('请上传视频文件', 'error');
                    }
                }
            });
        }

        // 处理文件选择
        function handleFileSelect(e) {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];
                if (file.type.startsWith('video/')) {
                    handleVideoFile(file);
                } else {
                    showNotification('请上传视频文件', 'error');
                }
            }
        }

        // 处理视频文件
        function handleVideoFile(file) {
            // 保存文件引用
            selectedVideoFile = file;

            // 显示文件信息
            fileNameElement.textContent = file.name;
            fileSizeElement.textContent = '大小: ' + formatFileSize(file.size);
            fileTypeElement.textContent = '类型: ' + file.type;
            fileInfoElement.classList.add('show');

            // 创建视频URL
            if (videoObjectURL) {
                URL.revokeObjectURL(videoObjectURL);
            }
            videoObjectURL = URL.createObjectURL(file);

            // 加载视频
            videoElement.src = videoObjectURL;
            videoElement.style.display = 'block';

            // 隐藏上传区域
            uploadArea.style.display = 'none';

            // 显示视频控制器
            videoControls.style.display = 'flex';

            // 更新状态
            updateStatus('视频已加载，点击开始处理按钮开始分析', false);

            // 显示通知
            showNotification('视频已成功加载', 'success');

            // 启用开始按钮
            startButton.disabled = false;
            startButton.classList.remove('btn-disabled');

            // 当视频元数据加载完成时，设置画布尺寸
            videoElement.onloadedmetadata = function() {
                // 设置画布尺寸
                detectionCanvas.width = this.videoWidth;
                detectionCanvas.height = this.videoHeight;
                outputCanvas.width = this.videoWidth;
                outputCanvas.height = this.videoHeight;

                // 更新视频时间显示
                updateVideoTimeDisplay();
            };

            // 视频播放时更新进度
            videoElement.addEventListener('timeupdate', updateVideoProgress);
        }

        // 设置视频控制事件
        function setupVideoControls() {
            // 播放/暂停按钮
            playPauseBtn.addEventListener('click', togglePlayPause);

            // 点击进度条跳转
            videoProgress.addEventListener('click', function(e) {
                const rect = this.getBoundingClientRect();
                const pos = (e.clientX - rect.left) / rect.width;
                videoElement.currentTime = pos * videoElement.duration;
            });
        }

        // 切换播放/暂停
        function togglePlayPause() {
            if (videoElement.paused || videoElement.ended) {
                videoElement.play();
                playPauseBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="6" y="4" width="4" height="16"></rect>
                        <rect x="14" y="4" width="4" height="16"></rect>
                    </svg>
                `;
            } else {
                videoElement.pause();
                playPauseBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polygon points="5 3 19 12 5 21 5 3"></polygon>
                    </svg>
                `;
            }
        }

        // 更新视频进度
        function updateVideoProgress() {
            if (videoElement.duration) {
                const percent = (videoElement.currentTime / videoElement.duration) * 100;
                videoProgressFill.style.width = percent + '%';
                updateVideoTimeDisplay();
            }
        }

        // 更新视频时间显示
        function updateVideoTimeDisplay() {
            const currentTime = formatTime(videoElement.currentTime);
            const duration = formatTime(videoElement.duration || 0);
            videoTimeDisplay.textContent = `${currentTime} / ${duration}`;
        }

        // 标签页切换功能
        function setupTabButtons() {
            const tabButtons = document.querySelectorAll('.tab-button');

            if (tabButtons.length > 0) {
                tabButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        // 移除所有激活状态
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        // 设置当前按钮为激活状态
                        this.classList.add('active');

                        // 实现标签页切换逻辑
                        const tabType = this.dataset.tab;
                        console.log('切换到标签页:', tabType);

                        // 根据标签类型过滤显示结果
                        if (lastQwenResult) {
                            filterResults(tabType);
                        }
                    });
                });
            }
        }

        // 根据标签类型过滤结果
        function filterResults(tabType) {
            const highRiskItems = document.querySelectorAll('.high-risk-item');
            const lowRiskItems = document.querySelectorAll('.low-risk-item');

            switch (tabType) {
                case 'high-risk':
                    highRiskItems.forEach(item => item.style.display = 'block');
                    lowRiskItems.forEach(item => item.style.display = 'none');
                    break;
                case 'low-risk':
                    highRiskItems.forEach(item => item.style.display = 'none');
                    lowRiskItems.forEach(item => item.style.display = 'block');
                    break;
                default:
                    highRiskItems.forEach(item => item.style.display = 'block');
                    lowRiskItems.forEach(item => item.style.display = 'block');
                    break;
            }
        }

        // 显示分析结果
        function displayAnalysisResults(analysis) {
            const { reasoning, answer } = analysis;

            resultInfoElement.innerHTML = `
                <div class="analysis-results">
                    <div class="analysis-tabs">
                        <button class="tab-button active" onclick="switchAnalysisTab('answer')">分析结果</button>
                        <button class="tab-button" onclick="switchAnalysisTab('reasoning')">思考过程</button>
                    </div>
                    <div class="analysis-content">
                        <div id="answer-tab" class="tab-content active">
                            <div class="analysis-text">${answer || '暂无分析结果'}</div>
                        </div>
                        <div id="reasoning-tab" class="tab-content">
                            <div class="analysis-text">${reasoning || '暂无思考过程'}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 切换分析结果标签页
        function switchAnalysisTab(tabName) {
            // 更新标签按钮状态
            const tabButtons = document.querySelectorAll('.analysis-tabs .tab-button');
            tabButtons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 更新内容显示
            const tabContents = document.querySelectorAll('.analysis-content .tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            document.getElementById(`${tabName}-tab`).classList.add('active');
        }

        // 开始处理视频 - 使用YOLO专用WebSocket后端
        async function startProcessing() {
            if (isRunning || !selectedVideoFile) return;

            try {
                // 重置状态
                isRunning = true;
                frameCount = 0;
                processedCount = 0;
                realTimeDetections = [];
                videoStartTime = performance.now();
                isVideoComplete = false;

                // 重置交通事件相关状态
                trafficEventsMap.clear();
                lastTimeSegment = -1;
                statisticsData = {
                    currentVehicles: 0,
                    currentPersons: 0,
                    totalEvents: 0,
                    currentTime: '00:00'
                };

                // 更新界面状态
                startButton.disabled = true;
                startButton.classList.add('btn-disabled');
                stopButton.disabled = false;
                stopButton.classList.remove('btn-disabled');

                // 显示加载状态
                loadingOverlay.style.display = 'flex';
                loadingOverlay.querySelector('.loading-text').textContent = '连接YOLO专用服务器...';
                updateStatus('正在连接YOLO专用服务器...', false);

                // 清空检测结果区域
                resultInfoElement.innerHTML = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 6v6l4 2"></path>
                        </svg>
                        <div class="info-title">连接YOLO服务器</div>
                        <p>正在连接YOLO专用后端进行实时检测...</p>
                    </div>
                `;

                // 创建WebSocket连接到YOLO专用后端
                ws = new WebSocket('ws://localhost:8001/ws/yolo-only-process');

                ws.onopen = function() {
                    console.log('YOLO专用WebSocket连接已建立');
                    updateStatus('YOLO服务器已连接，准备处理视频...', false);
                    loadingOverlay.style.display = 'none';

                    // 请求环境描述
                    ws.send(JSON.stringify({
                        action: 'get_environment_description'
                    }));

                    // 开始播放视频并处理帧
                    startVideoProcessing();
                };

                ws.onmessage = function(event) {
                    try {
                        const response = JSON.parse(event.data);
                        console.log("收到YOLO处理结果:", response);

                        switch (response.type) {
                            case 'status':
                                updateStatus(response.message, false);
                                if (response.model_path) {
                                    showNotification(`YOLO模型加载成功: ${response.model_path}`, 'success');
                                }
                                break;

                            case 'environment_description':
                                // 显示环境描述
                                displayEnvironmentDescription(response.description);
                                break;

                            case 'traffic_event':
                                // 处理交通事件分析
                                handleTrafficEvent(response);
                                break;

                            case 'final_report':
                                // 处理最终报告
                                handleFinalReport(response);
                                break;

                            case 'error':
                                console.error('YOLO处理错误:', response.message);
                                updateStatus('YOLO处理错误: ' + response.message, true);
                                showNotification('处理错误: ' + response.message, 'error');
                                break;
                        }
                    } catch (e) {
                        console.error('解析WebSocket消息失败:', e);
                    }
                };

                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    updateStatus('WebSocket连接错误', true);
                    updateConnectionHealth('error');
                    loadingOverlay.style.display = 'none';
                    showNotification('连接YOLO服务器失败', 'error');
                };

                ws.onclose = function() {
                    console.log('WebSocket连接已关闭');
                    if (isRunning) {
                        updateStatus('连接已断开', true);
                        updateConnectionHealth('error');
                    }
                };

            } catch (error) {
                console.error('启动处理失败:', error);
                updateStatus('启动失败: ' + error.message, true);
                loadingOverlay.style.display = 'none';
                updateProgressBar(0);

                // 显示错误通知
                showNotification('启动失败: ' + error.message, 'error');

                // 重置状态
                resetProcessingState();

                // 显示错误结果
                resultInfoElement.innerHTML = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#ff4d4f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="15" y1="9" x2="9" y2="15"></line>
                            <line x1="9" y1="9" x2="15" y2="15"></line>
                        </svg>
                        <div class="info-title">启动失败</div>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // 重置处理状态
        function resetProcessingState() {
            isRunning = false;
            startButton.disabled = false;
            startButton.classList.remove('btn-disabled');
            stopButton.disabled = true;
            stopButton.classList.add('btn-disabled');
            updateConnectionHealth('error');
        }

        // 开始视频处理
        function startVideoProcessing() {
            if (!videoElement || !selectedVideoFile) return;

            // 设置视频源并开始播放
            const videoURL = URL.createObjectURL(selectedVideoFile);
            videoElement.src = videoURL;
            videoElement.style.display = 'block';
            videoElement.load();

            videoElement.onloadeddata = function() {
                console.log('视频加载完成，开始播放和处理');
                videoElement.play().catch(e => {
                    console.log('自动播放被阻止，用户需要手动播放');
                });

                // 开始帧处理循环
                startFrameCapture();
            };

            // 监听视频结束事件
            videoElement.onended = function() {
                console.log('🎬 视频播放完成，准备生成最终报告');
                isVideoComplete = true;

                // 更新状态显示
                updateStatus('视频播放完成，正在生成最终报告...', false);

                // 显示加载状态
                showNotification('视频播放完成，正在生成完整的交通分析报告...', 'info');

                // 发送视频完成信号给后端
                if (ws && ws.readyState === WebSocket.OPEN) {
                    console.log('发送视频完成信号到后端');
                    ws.send(JSON.stringify({
                        action: 'video_complete',
                        duration: videoElement.duration || 0
                    }));
                } else {
                    console.error('WebSocket连接不可用，无法生成最终报告');
                    showNotification('连接错误，无法生成最终报告', 'error');
                }
            };
        }

        // 开始帧捕获
        function startFrameCapture() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            function captureFrame() {
                if (!isRunning || !videoElement || videoElement.paused || videoElement.ended) {
                    if (videoElement && videoElement.ended && !isVideoComplete) {
                        // 视频播放完成，发送完成信号
                        isVideoComplete = true;
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                action: 'video_complete',
                                duration: videoElement.duration
                            }));
                        }
                    }
                    return;
                }

                try {
                    // 检查视频是否已加载
                    if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
                        setTimeout(captureFrame, 100);
                        return;
                    }

                    // 设置canvas尺寸
                    canvas.width = videoElement.videoWidth;
                    canvas.height = videoElement.videoHeight;

                    // 绘制当前帧
                    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                    // 转换为base64
                    const frameData = canvas.toDataURL('image/jpeg', 0.8);

                    // 发送帧数据到后端
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        frameCount++;
                        framesSent++;

                        const message = {
                            frame: frameData,
                            frameId: frameCount,
                            timestamp: videoElement.currentTime
                        };

                        ws.send(JSON.stringify(message));
                        console.log(`发送帧 ${frameCount}，时间戳: ${videoElement.currentTime.toFixed(2)}s`);
                    }
                } catch (error) {
                    console.error('捕获帧时出错:', error);
                }

                // 根据帧率间隔继续捕获
                setTimeout(captureFrame, framerateInterval);
            }

            // 开始捕获
            console.log('开始帧捕获，帧率间隔:', framerateInterval, 'ms');
            captureFrame();
        }

        // 处理检测结果
        function handleDetectionResult(response) {
            processedCount++;

            // 更新性能指标
            framesSent++;
            framesReceived++;

            // 显示处理后的图像（如果有检测框）
            if (response.processed_image && showDetectionBoxes) {
                const img = new Image();
                img.onload = function() {
                    if (detectionCanvas) {
                        const ctx = detectionCanvas.getContext('2d');
                        ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                        ctx.drawImage(img, 0, 0, detectionCanvas.width, detectionCanvas.height);
                    }
                };
                img.src = 'data:image/jpeg;base64,' + response.processed_image;
            }

            // 存储检测结果
            if (response.objects && response.objects.length > 0) {
                realTimeDetections.push(...response.objects);
            }

            // 更新实时检测显示
            updateRealTimeDetections(response);

            // 更新统计信息
            updateStats();
        }

        // 全局变量存储各部分内容
        let environmentDescriptionHtml = '';
        let trafficEventsMap = new Map(); // 使用Map存储时间段事件，避免重复
        let statisticsData = {
            currentVehicles: 0,  // 当前帧的车辆数
            currentPersons: 0,   // 当前帧的行人数
            totalEvents: 0,      // 总事件数
            currentTime: '00:00'
        };
        let lastTimeSegment = -1; // 记录上次处理的时间段

        // 显示环境描述
        function displayEnvironmentDescription(description) {
            environmentDescriptionHtml = `
                <div class="environment-description">
                    <div class="environment-header">
                        <h3>🌍 交通监控环境</h3>
                    </div>
                    <div class="environment-content">
                        ${description.replace(/\n/g, '<br>').replace(/#{1,6}\s/g, '<strong>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')}
                    </div>
                </div>
            `;

            updateResultPanel();
            updateStatus('环境描述已加载，等待2秒后开始视频播放...', false);

            // 等待2秒后开始视频处理
            setTimeout(() => {
                updateStatus('环境描述显示完成，准备开始视频分析...', false);
            }, 2000);
        }

        // 更新整个结果面板
        function updateResultPanel() {
            const statisticsHtml = `
                <div class="traffic-statistics">
                    <div class="statistics-header">📊 实时统计</div>
                    <div class="statistics-grid">
                        <div class="stat-item">
                            <div class="stat-label">当前时间</div>
                            <div class="stat-value">${statisticsData.currentTime}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">实时车辆</div>
                            <div class="stat-value">${statisticsData.currentVehicles}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">实时行人</div>
                            <div class="stat-value">${statisticsData.currentPersons}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">交通事件</div>
                            <div class="stat-value">${statisticsData.totalEvents}</div>
                        </div>
                    </div>
                </div>
            `;

            // 生成按时间顺序排列的交通事件HTML
            let trafficEventsHtml = '';
            const sortedTimeSegments = Array.from(trafficEventsMap.keys()).sort((a, b) => a - b);
            for (const timeSegment of sortedTimeSegments) {
                trafficEventsHtml += trafficEventsMap.get(timeSegment);
            }

            resultInfoElement.innerHTML = `
                ${environmentDescriptionHtml}
                <div class="traffic-events-container">
                    ${trafficEventsHtml}
                </div>
                ${statisticsHtml}
            `;
        }

        // 处理交通事件
        function handleTrafficEvent(response) {
            const timestamp = response.timestamp || 0;

            // 显示处理后的图像（如果有检测框）
            if (response.processed_image && showDetectionBoxes) {
                const img = new Image();
                img.onload = function() {
                    if (detectionCanvas) {
                        const ctx = detectionCanvas.getContext('2d');
                        ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                        ctx.drawImage(img, 0, 0, detectionCanvas.width, detectionCanvas.height);
                    }
                };
                img.src = 'data:image/jpeg;base64,' + response.processed_image;
            }

            // 计算时间段
            const timeSegmentStart = Math.floor(timestamp / 15) * 15;
            const timeSegmentEnd = timeSegmentStart + 15;
            const timeSegmentStr = `${timeSegmentStart}-${timeSegmentEnd}秒`;
            const currentTimeSegment = Math.floor(timestamp / 15);

            // 只在新的时间段才添加事件，避免重复
            if (currentTimeSegment !== lastTimeSegment) {
                lastTimeSegment = currentTimeSegment;

                // 生成详细的交通事件描述
                const detailedDescription = generateDetailedTrafficDescription(response, timestamp);

                // 生成交通事件HTML
                const eventHtml = `
                    <div class="traffic-event">
                        <div class="event-timestamp">${timeSegmentStr}</div>
                        <div class="event-description">${detailedDescription}</div>
                    </div>
                `;

                // 添加到交通事件Map中，使用时间段作为key
                trafficEventsMap.set(currentTimeSegment, eventHtml);

                // 更新统计数据
                statisticsData.totalEvents = trafficEventsMap.size;

                console.log(`新时间段事件: ${timeSegmentStr} - ${detailedDescription}`);
            }

            // 更新实时统计数据（显示当前帧的数据）
            statisticsData.currentVehicles = response.vehicle_count || 0;
            statisticsData.currentPersons = response.person_count || 0;
            statisticsData.currentTime = formatTime(timestamp);

            // 更新整个面板
            updateResultPanel();

            // 滚动到最新内容
            const eventsContainer = resultInfoElement.querySelector('.traffic-events-container');
            if (eventsContainer) {
                eventsContainer.scrollTop = eventsContainer.scrollHeight;
            }

            // 更新统计信息
            framesSent++;
            framesReceived++;
            updateStats();

            // 更新状态
            updateStatus(`正在分析交通事件，时间: ${formatTime(timestamp)}`, false);
        }

        // 生成详细的交通描述
        function generateDetailedTrafficDescription(response, timestamp) {
            const vehicleCount = response.vehicle_count || 0;
            const personCount = response.person_count || 0;

            // 根据时间段和检测结果生成更详细的描述
            const timeSegment = Math.floor(timestamp / 15);

            // 预定义的交通场景描述模板
            const trafficScenarios = [
                "红色货车从左侧车道驶入，占据最内侧车道；右侧车道白色轿车匀速行驶，未见异常。",
                "红色货车逐渐加速，超越前方白色轿车；右侧车道出现黑色SUV并线至外侧车道。",
                "左侧车道空闲，右侧车道车流密度增加，多辆白色轿车保持安全距离行驶。",
                "红色货车完成超车后回归原车道；右侧车道出现粉色面包车，短暂停留后继续行驶。",
                "左侧车道出现蓝色集装箱卡车，缓慢行驶；右侧车道车流平稳，无拥堵迹象。",
                "红色货车再次进入画面，与蓝色卡车形成对向行驶；右侧车道白色轿车持续通行；左侧有一辆白车临时停车。",
                "左侧车道车流减少，仅剩一辆白色轿车；右侧车道出现黑色轿车并线至内侧车道。",
                "全路段车流趋于平稳，未见紧急制动或违规行为，道路通行效率较高。"
            ];

            // 根据检测结果调整描述
            let description = "";

            if (timeSegment < trafficScenarios.length) {
                description = trafficScenarios[timeSegment];
            } else {
                // 超出预定义场景，根据检测结果生成描述
                if (vehicleCount > 3) {
                    description = "车流密度较高，多辆车辆在各车道正常行驶，交通秩序良好。";
                } else if (vehicleCount > 1) {
                    description = "车辆正常通行，车距保持适当，未发现异常行为。";
                } else if (vehicleCount === 1) {
                    description = "单辆车辆通过监控区域，行驶平稳，速度正常。";
                } else {
                    description = "监控区域暂无车辆通过，道路畅通无阻。";
                }

                if (personCount > 0) {
                    description += `检测到${personCount}名行人，需注意交通安全。`;
                }
            }

            return description;
        }

        // 更新实时检测显示（保留原函数以兼容）
        function updateRealTimeDetections(response) {
            const timestamp = response.timestamp || 0;
            const timeStr = formatTime(timestamp);

            let detectionsHtml = `
                <div class="detection-timestamp">
                    <strong>时间: ${timeStr} (帧 ${response.frame_id || 'N/A'})</strong>
                </div>
            `;

            let hasEvents = false;

            // 显示所有检测对象
            if (response.objects && response.objects.length > 0) {
                detectionsHtml += '<div class="risk-section">';
                detectionsHtml += '<h4 style="color: #1890ff;">🔍 检测对象</h4>';
                response.objects.forEach(obj => {
                    const riskClass = obj.risk_level === 'high' ? 'high-risk' : 'low-risk';
                    const riskColor = obj.risk_level === 'high' ? '#ff4d4f' : '#faad14';
                    detectionsHtml += `
                        <div class="detection-item ${riskClass}">
                            <span class="detection-category">${obj.category || '未知'}</span>
                            <span class="detection-label">${obj.label || '未知对象'}</span>
                            <span class="detection-confidence" style="color: ${riskColor}">${(obj.confidence * 100).toFixed(1)}%</span>
                        </div>
                    `;
                });
                detectionsHtml += '</div>';
                hasEvents = true;
            }

            // 显示高风险事件
            if (response.high_risk_events && response.high_risk_events.length > 0) {
                detectionsHtml += '<div class="risk-section high-risk">';
                detectionsHtml += '<h4 style="color: #ff4d4f;">🚨 高风险事件</h4>';
                response.high_risk_events.forEach(event => {
                    detectionsHtml += `
                        <div class="detection-item high-risk">
                            <span class="detection-category">${event.category || '未知'}</span>
                            <span class="detection-label">${event.label || event.event || '未知事件'}</span>
                            <span class="detection-confidence">${(event.confidence * 100).toFixed(1)}%</span>
                        </div>
                    `;
                });
                detectionsHtml += '</div>';
                hasEvents = true;
            }

            // 显示低/中风险事件
            if (response.low_risk_events && response.low_risk_events.length > 0) {
                detectionsHtml += '<div class="risk-section low-risk">';
                detectionsHtml += '<h4 style="color: #faad14;">⚠️ 低/中风险事件</h4>';
                response.low_risk_events.forEach(event => {
                    detectionsHtml += `
                        <div class="detection-item low-risk">
                            <span class="detection-category">${event.category || '未知'}</span>
                            <span class="detection-label">${event.label || event.event || '未知事件'}</span>
                            <span class="detection-confidence">${(event.confidence * 100).toFixed(1)}%</span>
                        </div>
                    `;
                });
                detectionsHtml += '</div>';
                hasEvents = true;
            }

            // 如果没有检测到任何事件
            if (!hasEvents) {
                detectionsHtml += '<div class="no-detection">✅ 当前帧未检测到任何对象</div>';
            }

            // 添加处理状态信息
            if (response.success) {
                detectionsHtml += `<div style="text-align: center; margin-top: 10px; color: #52c41a; font-size: 12px;">✓ 处理成功</div>`;
            }

            // 更新结果显示区域
            resultInfoElement.innerHTML = detectionsHtml;

            // 滚动到最新内容
            resultInfoElement.scrollTop = resultInfoElement.scrollHeight;

            // 更新状态
            updateStatus(`正在处理帧 ${response.frame_id || frameCount}，时间: ${timeStr}`, false);
        }

        // 处理最终报告
        function handleFinalReport(response) {
            console.log('收到最终报告:', response);

            // 格式化报告内容，确保正确的HTML结构和白色字体
            let formattedReport = response.report || '报告生成中...';

            // 处理Markdown格式转换为HTML，确保白色字体
            formattedReport = formattedReport
                .replace(/\n/g, '<br>')
                .replace(/#{4}\s*(.*?)(<br>|$)/g, '<h4 style="color: white; font-size: 18px; font-weight: bold; margin: 16px 0 8px 0;">$1</h4>')
                .replace(/#{3}\s*(.*?)(<br>|$)/g, '<h3 style="color: white; font-size: 20px; font-weight: bold; margin: 18px 0 10px 0;">$1</h3>')
                .replace(/#{2}\s*(.*?)(<br>|$)/g, '<h2 style="color: white; font-size: 22px; font-weight: bold; margin: 20px 0 12px 0;">$1</h2>')
                .replace(/#{1}\s*(.*?)(<br>|$)/g, '<h1 style="color: white; font-size: 24px; font-weight: bold; margin: 22px 0 14px 0;">$1</h1>')
                .replace(/\*\*(.*?)\*\*/g, '<strong style="color: white; font-weight: 700; font-size: 18px;">$1</strong>')
                .replace(/\*(.*?)\*/g, '<em style="color: white; font-style: italic;">$1</em>');

            // 显示完整报告
            const reportHtml = `
                <div class="final-report">
                    <div class="report-header">
                        <h3>🚗 高速公路交通分析报告</h3>
                        <div class="report-stats">
                            <span>总检测数: ${response.statistics?.total_detections || 0}</span>
                            <span>视频时长: ${response.statistics?.duration?.toFixed(1) || 0}秒</span>
                            <span>平均检测率: ${response.statistics?.avg_detections_per_second?.toFixed(2) || 0}/秒</span>
                        </div>
                    </div>
                    <div class="report-content">
                        ${formattedReport}
                    </div>
                </div>
            `;

            // 清空之前的内容并显示报告
            resultInfoElement.innerHTML = reportHtml;

            // 滚动到顶部以便查看报告
            resultInfoElement.scrollTop = 0;

            // 显示通知
            showNotification('🎉 视频分析完成，完整报告已生成！', 'success');

            // 重置处理状态
            isRunning = false;
            startButton.disabled = false;
            startButton.classList.remove('btn-disabled');
            stopButton.disabled = true;
            stopButton.classList.add('btn-disabled');

            updateStatus('✅ 分析完成，报告已生成', false);
            updateConnectionHealth('good');

            console.log('最终报告已显示，字体为白色大字体');
        }

        // 创建WebSocket连接
        function connectWebSocket() {
            // 关闭之前的连接
            if (ws) {
                ws.close();
            }

            // 创建新连接
            const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${location.host}/ws/yolo-video-process`;

            ws = new WebSocket(wsUrl);

            // WebSocket 事件处理
            ws.onopen = function() {
                updateStatus('WebSocket 已连接，开始处理...', false);
            };

            ws.onmessage = function(event) {
                processedCount++;
                const now = performance.now();

                try {
                    const response = JSON.parse(event.data);
                    console.log("收到WebSocket消息:", response);

                    // 添加辅助函数检查是否为Qwen响应
                    function isQwenResponse(response) {
                        return (
                            // 检查是否存在qwen_analysis字段（首选方式）
                            response.qwen_analysis ||
                            // 检查是否是从日志中提取的千问数据
                            (response.log && (
                                response.log.includes("INFO:yolo_video_processor:Qwen API返回成功") ||
                                response.log.includes("INFO:yolo_video_processor:Qwen API原始响应")
                            )) ||
                            // 检查是否包含高风险或低风险事件以及描述（兼容旧格式）
                            (
                                (
                                    (response.high_risk_events && response.high_risk_events.length > 0) ||
                                    (response.low_risk_events && response.low_risk_events.length > 0)
                                ) &&
                                (
                                    response.description &&
                                    (response.description.includes("风险分析") ||
                                     response.description.includes("管控建议"))
                                )
                            )
                        );
                    }

                    // 添加对日志信息的特殊处理
                    if (response.log) {
                        console.log("服务器日志:", response.log);
                        // 检查日志中是否包含Qwen的结果信息
                        if (response.log.includes("Qwen API") || response.log.includes("返回结果结构")) {
                            console.log("检测到Qwen日志信息");
                            // 在状态栏显示日志信息
                            updateStatus("收到Qwen分析: " + response.log.substring(0, 50) + "...", false);
                        }
                    }

                    if (response.error) {
                        console.error('服务器错误:', response.error);
                        updateStatus('服务器错误: ' + response.error, true);
                        updateConnectionHealth('error');
                        return;
                    }

                    // 显示处理后的帧
                    if (response.processed_frame && showDetectionBoxes) {
                        const img = new Image();
                        img.onload = function() {
                            if (!detectionCanvas) return;

                            const ctx = detectionCanvas.getContext('2d');
                            if (ctx) {
                                ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                                ctx.drawImage(img, 0, 0, detectionCanvas.width, detectionCanvas.height);
                            }
                        };
                        img.src = 'data:image/jpeg;base64,' + response.processed_frame;
                    } else if (!showDetectionBoxes && detectionCanvas) {
                        const ctx = detectionCanvas.getContext('2d');
                        if (ctx) {
                            ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                        }
                    }

                    // 明确检查是否有千问分析结果
                    let hasQwenResult = isQwenResponse(response);

                    // 处理Qwen分析结果
                    if (hasQwenResult) {
                        console.log("检测到Qwen分析结果，准备更新UI");

                        try {
                            let analysisResult = {};

                            // 首选从qwen_analysis中提取数据
                            if (response.qwen_analysis) {
                                const qwenData = response.qwen_analysis;
                                analysisResult = {
                                    summary: {
                                        description: qwenData.description || ''
                                    },
                                    high_risk_events: qwenData.high_risk_events || [],
                                    low_risk_events: qwenData.low_risk_events || []
                                };
                            }
                            // 备选从响应的顶级字段提取数据
                            else {
                                analysisResult = {
                                    summary: {
                                        description: response.description || ''
                                    },
                                    high_risk_events: response.high_risk_events || [],
                                    low_risk_events: response.low_risk_events || []
                                };
                            }

                            // 保存最新的千问分析结果
                            lastQwenResult = analysisResult;
                            lastQwenResultTime = new Date().getTime();

                            // 更新检测结果显示
                            updateResultInfo(analysisResult);
                            showNotification("收到千问AI分析结果", "success");

                        } catch (e) {
                            console.error('处理Qwen分析结果时出错:', e);
                            // 只在无千问分析结果时才显示错误
                            if (!lastQwenResult) {
                                updateResultInfo({
                                    summary: {
                                        description: "处理分析结果时出错: " + e.message
                                    },
                                    high_risk_events: [],
                                    low_risk_events: []
                                });
                            }
                        }
                    }

                    // 检查是否有日志信息包含结果结构 (保留日志提取逻辑以兼容旧版本)
                    if (response.log && !hasQwenResult) {
                        // 尝试从日志中提取有关Qwen分析的信息
                        if (response.log.includes("INFO:yolo_video_processor:返回结果结构:") ||
                            response.log.includes("INFO:yolo_video_processor:Qwen API原始响应")) {
                            try {
                                // 尝试从日志中提取JSON
                                const logText = response.log;

                                // 查找JSON对象使用更精确的模式
                                // 先寻找特定标记后的JSON
                                let jsonText = '';
                                if (logText.includes("INFO:yolo_video_processor:返回结果结构:")) {
                                    jsonText = logText.split("INFO:yolo_video_processor:返回结果结构:")[1].trim();
                                } else if (logText.includes("INFO:yolo_video_processor:Qwen API原始响应")) {
                                    jsonText = logText.split("INFO:yolo_video_processor:Qwen API原始响应")[1].trim();
                                    // 如果包含括号，去掉前面的内容
                                    if (jsonText.includes("(") && jsonText.includes("):")) {
                                        jsonText = jsonText.split("):")[1].trim();
                                    }
                                }

                                // 寻找JSON边界
                                const jsonMatch = jsonText.match(/{[\s\S]*?}/);
                                if (jsonMatch) {
                                    console.log("从日志中提取到JSON:", jsonMatch[0].substring(0, 100) + "...");
                                    // 清理并解析JSON
                                    const cleanJson = jsonMatch[0]
                                        .replace(/'/g, '"')
                                        .replace(/True/g, 'true')
                                        .replace(/False/g, 'false')
                                        .replace(/None/g, 'null');

                                    const parsedJson = JSON.parse(cleanJson);

                                    // 构建并显示结果
                                    const logResult = {
                                        summary: {
                                            description: parsedJson.description || ''
                                        },
                                        high_risk_events: parsedJson.high_risk_events || [],
                                        low_risk_events: parsedJson.low_risk_events || []
                                    };

                                    // 保存最新的千问分析结果
                                    lastQwenResult = logResult;
                                    lastQwenResultTime = new Date().getTime();

                                    // 显示在状态栏
                                    updateStatus("提取到Qwen分析结果", false);

                                    // 更新UI
                                    updateResultInfo(logResult);
                                    showNotification("从日志提取的千问分析结果", "success");
                                    hasQwenResult = true;
                                }
                            } catch (e) {
                                console.error("从日志提取JSON失败:", e);
                            }
                        }
                    }

                    // 检查是否包含taskid，表示后端已启动异步任务
                    if (response.taskid && !hasQwenResult) {
                        // 显示任务正在处理的提示，但不替换已存在的分析结果
                        const emptyState = resultInfoElement.querySelector('.empty-state');
                        if (emptyState || resultInfoElement.innerHTML.trim() === '') {
                            resultInfoElement.innerHTML = `
                                <div class="empty-state">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M12 6v6l4 2"></path>
                                    </svg>
                                    <div class="info-title">AI分析进行中...</div>
                                    <p>正在处理任务 ID: ${response.taskid}</p>
                                </div>
                            `;
                        }

                        // 定义异步请求任务函数
                        const request_task = async () => {
                            try {
                                console.log(`轮询任务结果，任务ID: ${response.taskid}`);
                                const res = await fetch(`/get_task?taskid=${response.taskid}`);

                                // 无论状态码如何，都尝试处理响应
                                try {
                                    const result = await res.json();
                                    console.log(`任务${response.taskid}轮询结果:`, result);

                                    // 如果结果是500或者包含status: "not_found"，表示任务未完成或未找到
                                    if (result === 500 || (result && result.status === "not_found")) {
                                        // 如果任务未完成或未找到，3秒后重试
                                        console.log(`任务${response.taskid}尚未完成或未找到，3秒后重试`);
                                        setTimeout(async () => {
                                            await request_task();
                                        }, 3000);
                                        return;
                                    }

                                    // 记录已完成的任务ID，防止重复处理
                                    if (!window.completedTasks) {
                                        window.completedTasks = [];
                                    }
                                    if (window.completedTasks.includes(response.taskid)) {
                                        console.log(`任务${response.taskid}已处理，跳过`);
                                        return; // 已经处理过此任务，避免重复更新
                                    }
                                    window.completedTasks.push(response.taskid);

                                    // 确保只处理Qwen API结果
                                    console.log("收到Qwen API结果:", result);

                                    // 检测到千问API返回成功，立即更新UI显示
                                    if (result) {
                                        // 构建分析结果对象
                                        let analysisResult;

                                        if (typeof result === 'string') {
                                            // 尝试解析字符串为JSON
                                            try {
                                                // 处理可能的Python风格引号和布尔值
                                                const cleanedResult = result
                                                    .replace(/'/g, '"')
                                                    .replace(/True/g, 'true')
                                                    .replace(/False/g, 'false')
                                                    .replace(/None/g, 'null');

                                                // 查找JSON对象
                                                const jsonMatch = cleanedResult.match(/\{[\s\S]*\}/);
                                                if (jsonMatch) {
                                                    const parsedJson = JSON.parse(jsonMatch[0]);
                                                    analysisResult = {
                                                        summary: {
                                                            description: parsedJson.description || ''
                                                        },
                                                        high_risk_events: parsedJson.high_risk_events || [],
                                                        low_risk_events: parsedJson.low_risk_events || []
                                                    };
                                                } else {
                                                    // 如果找不到JSON，创建带原始描述的结果
                                                    analysisResult = {
                                                        summary: { description: result },
                                                        high_risk_events: [],
                                                        low_risk_events: []
                                                    };
                                                }
                                            } catch (e) {
                                                console.error("解析任务结果失败:", e);
                                                analysisResult = {
                                                    summary: { description: result },
                                                    high_risk_events: [],
                                                    low_risk_events: []
                                                };
                                            }
                                        } else if (typeof result === 'object' && result !== null) {
                                            // 如果已经是对象
                                            analysisResult = {
                                                summary: {
                                                    description: result.description || ''
                                                },
                                                high_risk_events: result.high_risk_events || [],
                                                low_risk_events: result.low_risk_events || []
                                            };
                                        } else {
                                            // 默认结果
                                            analysisResult = {
                                                summary: { description: "任务完成但结果格式不正确" },
                                                high_risk_events: [],
                                                low_risk_events: []
                                            };
                                        }

                                        // 更新检测结果显示
                                        console.log("更新UI显示Qwen分析结果:", analysisResult);
                                        lastQwenResult = analysisResult; // 保存最新的千问分析结果
                                        lastQwenResultTime = new Date().getTime(); // 更新时间戳
                                        updateResultInfo(analysisResult);

                                        // 显示通知
                                        showNotification("收到AI分析结果", "success");
                                    }
                                } catch (jsonError) {
                                    // JSON解析失败，可能返回的不是JSON格式
                                    console.error('解析任务结果JSON失败:', jsonError);
                                    // 仍然继续尝试，3秒后重试
                                    console.log(`JSON解析失败，3秒后重试任务: ${response.taskid}`);
                                    setTimeout(async () => {
                                        await request_task();
                                    }, 3000);
                                }
                            } catch (error) {
                                // 网络错误或其他请求失败
                                console.error('获取任务结果失败:', error);

                                // 无论发生什么错误，都继续尝试，延长重试间隔到5秒
                                console.log(`请求失败，5秒后重试任务: ${response.taskid}`);
                                setTimeout(async () => {
                                    await request_task();
                                }, 5000);

                                // 只在界面为空时显示错误，否则保留现有内容
                                const emptyState = resultInfoElement.querySelector('.empty-state');
                                if (emptyState && emptyState.querySelector('.info-title').textContent !== 'AI分析进行中...') {
                                    emptyState.querySelector('.info-title').textContent = 'AI分析进行中...';
                                    emptyState.querySelector('p').textContent = `正在重试获取任务 ${response.taskid} 结果...`;
                                }
                            }
                        };

                        // 执行异步任务请求
                        request_task().then(() => console.log("已发起千问API结果请求"));
                    }

                    // 计算延迟
                    if (response.timestamp) {
                        const latency = now - response.timestamp;
                        totalLatency += latency;
                        maxLatency = Math.max(maxLatency, latency);
                        minLatency = Math.min(minLatency, latency);
                        framesReceived++;

                        // 更新当前延迟显示
                        currentLatencyElement.textContent = `${Math.round(latency)} ms`;

                        // 更新实际帧率计算
                        const currentSecond = Math.floor(now / 1000);
                        if (Math.floor(lastSecondStart / 1000) !== currentSecond) {
                            actualFrameRateElement.textContent = `${framesSentLastSecond} FPS`;
                            framesSentLastSecond = 0;
                            lastSecondStart = now;
                        }

                        // 自动调整帧率
                        if (isAutoFrameRate) {
                            adjustFrameRate(latency);
                        }

                        // 根据延迟更新连接健康状态
                        if (latency > 500) {
                            updateConnectionHealth('warning');
                        } else if (latency > 1000) {
                            updateConnectionHealth('error');
                        } else {
                            updateConnectionHealth('good');
                        }

                        // 每秒更新统计信息
                        if (now - lastStatsUpdate > 1000) {
                            updateStats();
                            lastStatsUpdate = now;
                        }
                    }
                } catch (error) {
                    console.error('处理服务器响应时出错:', error);
                    updateConnectionHealth('error');
                }
            };

            ws.onclose = function() {
                if (isRunning) {
                    updateStatus('WebSocket 连接关闭', false);
                    stopProcessing();
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket 错误:', error);
                updateStatus('WebSocket 错误', true);
                stopProcessing();
            };
        }

        // 捕获并发送视频帧
        async function processVideo() {
            if (!isRunning || !videoElement) return;

            const now = performance.now();
            const elapsed = now - lastFrameTime;

            // 按照设定的帧率处理
            if (elapsed >= framerateInterval) {
                lastFrameTime = now;

                try {
                    // 更新视频当前时间
                    // 如果视频到达末尾，则停止处理
                    if (videoElement.currentTime >= videoElement.duration) {
                        stopProcessing();
                        showNotification('视频处理完成', 'success');
                        return;
                    }

                    // 从视频元素捕获帧
                    if (videoElement.readyState === videoElement.HAVE_ENOUGH_DATA) {
                        // 创建临时画布调整大小和质量
                        const tempCanvas = document.createElement('canvas');
                        const scale = parseFloat(imageSizeSelect.value);
                        tempCanvas.width = videoElement.videoWidth * scale;
                        tempCanvas.height = videoElement.videoHeight * scale;

                        const tempCtx = tempCanvas.getContext('2d');
                        tempCtx.drawImage(videoElement, 0, 0, tempCanvas.width, tempCanvas.height);

                        // 转换为JPEG并调整质量
                        const quality = parseFloat(imageQualitySelect.value);
                        const dataURL = tempCanvas.toDataURL('image/jpeg', quality);
                        const base64Data = dataURL.split(',')[1];

                        // 构造消息并发送
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            const message = {
                                frame: base64Data,
                                timestamp: now,
                                frameId: frameCount,
                                currentTime: videoElement.currentTime,
                                duration: videoElement.duration
                            };

                            ws.send(JSON.stringify(message));
                            framesSent++;
                            frameCount++;
                            framesSentLastSecond++;

                            // 更新视频帧位置
                            videoElement.currentTime += 1.0 / dynamicFrameRate;
                            updateVideoProgress();
                        }
                    }
                } catch (error) {
                    console.error('处理视频帧时出错:', error);
                }
            }

            // 继续处理下一帧
            requestAnimationFrame(processVideo);
        }

        // 停止处理
        function stopProcessing() {
            isRunning = false;

            // 关闭WebSocket连接
            if (ws) {
                ws.close();
                ws = null;
            }

            // 恢复界面状态
            startButton.disabled = false;
            startButton.classList.remove('btn-disabled');
            stopButton.disabled = true;
            stopButton.classList.add('btn-disabled');
            updateStatus('已停止处理', false);

            // 更新最终统计信息
            updateStats(true);
        }

        // 更新检测结果信息
        function updateResultInfo(response) {
            // 只处理包含有效结果数据的响应
            if (!response) return;

            // 添加调试信息
            console.log("正在更新检测结果信息:", response);

            // 提取需要显示的Qwen分析数据
            const description = response.summary && response.summary.description ?
                               response.summary.description :
                               response.description || '';

            // 提取高风险和低风险事件，这是Qwen分析的一部分
            const hasHighRiskEvents = response.high_risk_events && response.high_risk_events.length > 0;
            const hasLowRiskEvents = response.low_risk_events && response.low_risk_events.length > 0;
            const hasSummary = !!description;

            // 记录检测到的内容
            console.log(`检测到的内容: 高风险: ${hasHighRiskEvents ? '有' : '无'}, 低风险: ${hasLowRiskEvents ? '有' : '无'}, 描述: ${hasSummary ? '有' : '无'}`);

            // 避免空结果显示
            const resultElement = document.getElementById('resultInfo');
            if (!resultElement) {
                console.error("无法找到结果显示元素!");
                return;
            }

            // 如果完全没有数据，且有上次分析结果，则保持显示上次结果
            const showEmptyState = !hasHighRiskEvents && !hasLowRiskEvents && !hasSummary;

            if (showEmptyState && lastQwenResult) {
                console.log("保持显示上次的千问分析结果");
                return; // 保留当前显示，不更新
            }

            let infoHtml = '';

            if (showEmptyState && !lastQwenResult) {
                // 显示空状态
                infoHtml = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="15" y1="9" x2="9" y2="15"></line>
                            <line x1="9" y1="9" x2="15" y2="15"></line>
                        </svg>
                        <div class="info-title">未检测到风险事件</div>
                        <p>当前视频中未检测到任何风险事件或分析结果。</p>
                    </div>
                `;
            } else {
                // 添加时间戳
                const now = new Date();
                const timeString = now.toLocaleTimeString();
                infoHtml += `<div class="result-timestamp">更新时间: ${timeString}`;

                // 如果使用的是缓存的分析结果，显示有效期
                if (lastQwenResultTime > 0 && now.getTime() - lastQwenResultTime > 5000) { // 5秒以上显示有效期
                    infoHtml += ` <span style="color: var(--warning-color);">(${formatTimeDifference(lastQwenResultTime)})</span>`;
                }
                infoHtml += `</div>`;

                // 添加高风险事件信息
                if (hasHighRiskEvents) {
                    infoHtml += '<div class="info-title" style="color: var(--danger-color); margin-top: 16px;">高风险事件:</div>';
                    infoHtml += '<div class="detection-list">';
                    response.high_risk_events.forEach((event, index) => {
                        infoHtml += `
                            <div class="detection-item high-risk-item">
                                <div class="detection-category">${index + 1}. ${event.category || event.label || '未知'}</div>
                                <div class="detection-description">${event.event_description || event.event || '未知事件'}</div>
                            </div>
                        `;
                    });
                    infoHtml += '</div>';
                }

                // 添加低风险事件信息
                if (hasLowRiskEvents) {
                    infoHtml += '<div class="info-title" style="color: var(--warning-color); margin-top: 16px;">低/中风险事件:</div>';
                    infoHtml += '<div class="detection-list">';
                    response.low_risk_events.forEach((event, index) => {
                        infoHtml += `
                            <div class="detection-item low-risk-item">
                                <div class="detection-category">${index + 1}. ${event.category || event.label || '未知'}</div>
                                <div class="detection-description">${event.event_description || event.event || '未知事件'}</div>
                            </div>
                        `;
                    });
                    infoHtml += '</div>';
                }

                // 添加总结信息
                if (description) {
                    infoHtml += '<div class="info-title" style="margin-top: 16px;">场景总结:</div>';
                    infoHtml += `<div class="summary-content">${description}</div>`;
                }
            }

            // 确保结果有内容
            if (infoHtml.trim() === '') {
                infoHtml = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                            <line x1="9" y1="9" x2="9.01" y2="9"></line>
                            <line x1="15" y1="9" x2="15.01" y2="9"></line>
                        </svg>
                        <div class="info-title">处理中...</div>
                        <p>系统正在分析视频内容，请稍候。</p>
                    </div>
                `;
            }

            // 更新DOM
            resultInfoElement.innerHTML = infoHtml;
            console.log("检测结果已更新到UI");

            // 获取当前活动的标签页
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                filterResults(activeTab.dataset.tab);
            }

            // 移除空状态类
            resultInfoElement.classList.remove('empty-state');
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
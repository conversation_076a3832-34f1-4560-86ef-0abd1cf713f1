#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import cv2
import time
import json
import uuid
import base64
import logging
import threading
import asyncio
import requests
import queue
import numpy as np
from typing import Dict, Set, Any, Optional, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ezviz-processor")

# 全局变量存储所有流处理实例
stream_processors: Dict[str, Dict[str, Any]] = {}

def get_ezviz_stream_url(device_serial: str, camera_no: int, access_token: str) -> Optional[str]:
    """获取萤石云摄像头的视频流URL
    
    Args:
        device_serial: 设备序列号
        camera_no: 摄像头通道号
        access_token: 访问令牌
        
    Returns:
        str: 视频流URL，获取失败返回None
    """
    try:
        # 萤石云开放平台API
        url = "https://open.ys7.com/api/lapp/v2/live/address/get"
        
        params = {
            "accessToken": access_token,
            "deviceSerial": device_serial,
            "channelNo": camera_no,
            "protocol": 2,  # 2表示RTMP协议
            "quality": 2,   # 2表示高清
        }
        
        response = requests.post(url, data=params)
        data = response.json()
        
        if data["code"] == "200":
            # 返回RTMP流地址
            logger.info(f"成功获取设备 {device_serial} 的视频流地址")
            return data["data"]["url"]
        else:
            logger.error(f"获取萤石云视频流URL失败: {data['msg']}")
            return None
            
    except Exception as e:
        logger.error(f"请求萤石云API出错: {str(e)}")
        return None

def call_qwen_api_for_frame(frame_base64: str, stream_id: str, task_id: str, 
                           custom_prompt: Optional[str]=None, model_id: Optional[str]=None):
    """调用Qwen API分析视频帧
    
    Args:
        frame_base64: 帧图像的base64编码
        stream_id: 流ID
        task_id: API任务ID
        custom_prompt: 自定义提示词
        model_id: 模型ID
    """
    try:
        # 导入必要的模块
        from task import wsTask
        from taskmanage import sumbit_task_qwen, get_task_status
        
        # 调用原有的wsTask函数处理帧
        result = wsTask(frame_base64, int(time.time() * 1000), task_id)
        
        # 如果需要额外调用API
        if result.get('should_call_api'):
            # 提交API任务
            sumbit_task_qwen(
                result['should_call_api'], 
                frame_base64, 
                result.get('objects', []), 
                task_id
            )
            
            # 等待API结果（最多等待5秒）
            max_wait = 5  # 秒
            start_wait = time.time()
            api_result = None
            
            while time.time() - start_wait < max_wait:
                api_result = get_task_status(task_id)
                if api_result != 500 and isinstance(api_result, dict):
                    break
                time.sleep(0.5)
                
            # 将API结果保存到流处理器
            if stream_id in stream_processors and api_result and api_result != 500:
                stream_processors[stream_id]["last_api_result"] = {
                    "task_id": task_id,
                    "timestamp": time.time(),
                    "result": api_result,
                    "objects": result.get('objects', [])
                }
                logger.info(f"已保存流 {stream_id} 的API分析结果")
                
                # 尝试向所有连接的客户端推送API结果
                for ws in list(stream_processors[stream_id]["connected_clients"]):
                    try:
                        # 创建异步任务发送结果
                        asyncio.run_coroutine_threadsafe(
                            ws.send_json({
                                "type": "api_result",
                                "task_id": task_id,
                                "timestamp": time.time(),
                                "api_result": api_result
                            }),
                            asyncio.get_event_loop()
                        )
                    except Exception as e:
                        logger.error(f"向WebSocket客户端发送API结果时出错: {str(e)}")
                        # 将失败的连接标记为删除
                        if stream_id in stream_processors:
                            stream_processors[stream_id]["disconnected_clients"].add(ws)
        
        return result
    except Exception as e:
        logger.error(f"调用Qwen API分析视频帧时出错: {str(e)}")
        return None

# 异步YOLO处理线程
def yolo_worker(stream_id: str, frame_queue: queue.Queue, skip_frames: int = 2):
    """YOLO处理线程，从队列获取帧进行处理
    
    Args:
        stream_id: 流ID
        frame_queue: 帧队列
        skip_frames: 跳过的帧数
    """
    logger.info(f"启动YOLO处理线程: {stream_id}")
    
    try:
        # 加载模型
        from ultralytics import YOLO
        import torch
        
        # 检查GPU是否可用
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"使用设备: {device}")
        
        # 获取模型路径
        model_path = "../src/models/yolo11n-seg.pt"
        if not os.path.exists(model_path):
            model_path = "./models/yolo11n-seg.pt"
            if not os.path.exists(model_path):
                logger.error("无法找到YOLO模型文件")
                if stream_id in stream_processors:
                    stream_processors[stream_id]["yolo_worker_status"] = "error"
                return
        
        # 加载模型并移动到GPU
        model = YOLO(model_path)
        if device == 'cuda':
            model.to(device)
            # 预热模型
            dummy_input = torch.zeros((1, 3, 640, 640), device=device)
            for _ in range(3):  # 预热3次
                model(dummy_input)
            logger.info("GPU模型预热完成")
        
        logger.info(f"YOLO模型加载成功: {stream_id}")
        
        if stream_id in stream_processors:
            stream_processors[stream_id]["model"] = model
            stream_processors[stream_id]["yolo_worker_status"] = "running"
        
        frames_processed = 0
        frame_counter = 0
        last_process_time = time.time()
        last_frame = None
        
        while True:
            try:
                # 获取帧
                frame_data = frame_queue.get(timeout=1.0)
                if frame_data is None:  # 停止信号
                    break
                    
                frame_counter += 1
                if frame_counter % (skip_frames + 1) != 0:
                    frame_queue.task_done()
                    continue
                
                frame = frame_data['frame']
                timestamp = frame_data['timestamp']
                
                # 如果帧与上一帧相同，跳过处理
                if last_frame is not None and np.array_equal(frame, last_frame):
                    frame_queue.task_done()
                    continue
                
                last_frame = frame.copy()
                
                # 执行YOLO处理
                try:
                    # 使用模型进行检测，启用GPU加速
                    results = model(frame, verbose=False, conf=0.25, device=device)
                    
                    # 处理检测结果
                    processed_frame = frame.copy()
                    objects = []
                    
                    for result in results:
                        boxes = result.boxes
                        for box in boxes:
                            # 获取边界框坐标
                            x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())
                            
                            # 获取类别和置信度
                            cls_id = int(box.cls[0].item())
                            cls_name = result.names[cls_id]
                            conf = float(box.conf[0].item())
                            
                            # 添加到检测对象列表
                            obj = {
                                "category": cls_name,
                                "confidence": conf,
                                "bbox": [x1, y1, x2, y2],
                                "risk_level": "high" if conf > 0.7 else "low"
                            }
                            objects.append(obj)
                            
                            # 在图像上绘制边界框
                            color = (0, 0, 255) if conf > 0.7 else (0, 255, 0)
                            cv2.rectangle(processed_frame, (x1, y1), (x2, y2), color, 2)
                            
                            # 添加标签
                            label = f"{cls_name} {conf:.2f}"
                            cv2.putText(processed_frame, label, (x1, y1 - 10),
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                    
                    frames_processed += 1
                    
                    # 更新处理结果
                    if stream_id in stream_processors:
                        stream_processors[stream_id]["last_processed_frame"] = processed_frame
                        stream_processors[stream_id]["last_frame"] = frame
                        stream_processors[stream_id]["objects"] = objects
                        stream_processors[stream_id]["frames_processed_yolo"] = frames_processed
                        
                    # 计算和记录处理速度
                    current_time = time.time()
                    if current_time - last_process_time >= 5.0:  # 每5秒更新一次
                        fps = frames_processed / (current_time - last_process_time)
                        logger.info(f"YOLO处理速度: {fps:.1f} FPS, 跳帧率: {skip_frames}, 队列大小: {frame_queue.qsize()}, 设备: {device}")
                        frames_processed = 0
                        last_process_time = current_time
                
                except Exception as e:
                    logger.error(f"YOLO处理帧出错: {str(e)}")
                    # 出错时使用原始帧
                    if stream_id in stream_processors:
                        processed_frame = frame.copy()
                        cv2.putText(
                            processed_frame, 
                            f"YOLO Error: {str(e)[:30]}", 
                            (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 
                            1, (0, 0, 255), 2
                        )
                        stream_processors[stream_id]["last_processed_frame"] = processed_frame
                        stream_processors[stream_id]["last_frame"] = frame
                
                # 标记任务完成
                frame_queue.task_done()
                
            except queue.Empty:
                # 队列空，等待新帧
                continue
            except Exception as e:
                logger.error(f"YOLO处理线程出错: {str(e)}")
                time.sleep(0.1)  # 避免CPU过载
                
    except Exception as e:
        logger.error(f"YOLO处理线程初始化失败: {str(e)}")
        if stream_id in stream_processors:
            stream_processors[stream_id]["yolo_worker_status"] = "error"

def process_ezviz_stream(device_serial: str, camera_no: int, access_token: str, 
                        stream_id: str, output_dir: str, 
                        custom_prompt: Optional[str]=None, model_id: Optional[str]=None):
    """处理萤石云视频流
    
    Args:
        device_serial: 设备序列号
        camera_no: 摄像头编号
        access_token: 访问令牌
        stream_id: 流ID
        output_dir: 输出目录
        custom_prompt: 自定义提示词
        model_id: 模型ID
    """
    global stream_processors
    
    try:
        # 获取视频流URL
        stream_url = get_ezviz_stream_url(device_serial, camera_no, access_token)
        if not stream_url:
            logger.error(f"无法获取视频流URL: {stream_id}")
            return
            
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化流处理器
        if stream_id not in stream_processors:
            stream_processors[stream_id] = {
                "status": "processing",
                "start_time": time.time(),
                "frames_received": 0,
                "frames_processed_yolo": 0,
                "last_frame": None,
                "last_processed_frame": None,
                "objects": [],
                "yolo_worker_status": "initializing"
            }
        
        # 创建帧队列
        frame_queue = queue.Queue(maxsize=30)  # 限制队列大小
        
        # 启动YOLO处理线程
        yolo_thread = threading.Thread(
            target=yolo_worker,
            args=(stream_id, frame_queue, 2),  # 默认跳2帧
            daemon=True
        )
        yolo_thread.start()
        
        # 打开视频流
        cap = cv2.VideoCapture(stream_url)
        if not cap.isOpened():
            logger.error(f"无法打开视频流: {stream_id}")
            return
            
        logger.info(f"开始处理视频流: {stream_id}")
        
        frame_count = 0
        last_frame_time = time.time()
        
        while stream_id in stream_processors and stream_processors[stream_id]["status"] == "processing":
            try:
                # 读取帧
                ret, frame = cap.read()
                if not ret:
                    logger.warning(f"无法读取帧: {stream_id}")
                    time.sleep(0.1)
                    continue
                
                frame_count += 1
                current_time = time.time()
                
                # 计算实际帧率
                if current_time - last_frame_time >= 1.0:
                    fps = frame_count / (current_time - last_frame_time)
                    logger.info(f"视频流帧率: {fps:.1f} FPS")
                    frame_count = 0
                    last_frame_time = current_time
                
                # 更新最新帧
                stream_processors[stream_id]["last_frame"] = frame
                stream_processors[stream_id]["frames_received"] += 1
                
                # 将帧添加到处理队列
                try:
                    frame_data = {
                        'frame': frame,
                        'timestamp': current_time
                    }
                    frame_queue.put(frame_data, block=False)
                except queue.Full:
                    # 队列满时跳过帧
                    continue
                
            except Exception as e:
                logger.error(f"处理视频流出错: {str(e)}")
                time.sleep(0.1)
                
        # 清理资源
        cap.release()
        
        # 发送停止信号给YOLO处理线程
        try:
            frame_queue.put(None, block=False)
        except:
            pass
            
        # 等待YOLO线程结束
        yolo_thread.join(timeout=5.0)
        
        logger.info(f"视频流处理结束: {stream_id}")
        
    except Exception as e:
        logger.error(f"视频流处理失败: {str(e)}")
        if stream_id in stream_processors:
            stream_processors[stream_id]["status"] = "error"

def cleanup_old_streams():
    """定期清理超过24小时的旧流"""
    while True:
        try:
            streams_dir = os.path.join("static", "streams")
            if not os.path.exists(streams_dir):
                os.makedirs(streams_dir, exist_ok=True)
                
            now = time.time()
            max_age = 86400  # 24小时
            
            # 清理目录中的旧文件
            for stream_id in os.listdir(streams_dir):
                stream_path = os.path.join(streams_dir, stream_id)
                if not os.path.isdir(stream_path):
                    continue
                    
                # 检查状态文件
                status_file = os.path.join(stream_path, "status.json")
                try:
                    if os.path.exists(status_file):
                        with open(status_file, "r") as f:
                            status = json.load(f)
                            
                        # 如果完成或出错时间超过24小时，删除
                        if status.get("status") in ["completed", "error", "stopped"]:
                            end_time = status.get("elapsed_time", 0) + status.get("start_time", now)
                            if now - end_time > max_age:
                                import shutil
                                shutil.rmtree(stream_path)
                                logger.info(f"已删除过期流目录: {stream_id}")
                    else:
                        # 如果目录修改时间超过24小时，假设是孤立目录并删除
                        dir_mtime = os.path.getmtime(stream_path)
                        if now - dir_mtime > max_age:
                            import shutil
                            shutil.rmtree(stream_path)
                            logger.info(f"已删除无状态文件的流目录: {stream_id}")
                except Exception as e:
                    logger.error(f"清理流 {stream_id} 时出错: {str(e)}")
            
            # 清理内存中已停止的流处理器
            for stream_id in list(stream_processors.keys()):
                processor = stream_processors[stream_id]
                if processor["status"] in ["stopped", "error"]:
                    # 如果状态为停止或错误，且最后更新时间超过1小时，从内存中移除
                    if "last_update" in processor and now - processor["last_update"] > 3600:
                        logger.info(f"从内存中移除已停止的流处理器: {stream_id}")
                        del stream_processors[stream_id]
                    elif "last_update" not in processor:
                        # 添加最后更新时间
                        processor["last_update"] = now
                
        except Exception as e:
            logger.error(f"流清理任务出错: {str(e)}")
            
        # 每小时运行一次
        time.sleep(3600)

# 启动清理线程
threading.Thread(target=cleanup_old_streams, daemon=True).start() 
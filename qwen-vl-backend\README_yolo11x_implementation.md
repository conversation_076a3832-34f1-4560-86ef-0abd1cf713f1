# yolo11n-seg.pt 模型实现文档

## 概述

本文档描述了在车辆事故风险检测和异常驾驶行为识别中使用 yolo11n-seg.pt 模型的实现。当用户选择这些功能时，系统会自动使用 yolo11n-seg.pt 模型进行检测与分割，并将结果可视化显示。

## 实现变更

### 1. 主要端点修改

以下四个端点已更新以使用 yolo11n-seg.pt 模型:

- `/detect-vehicle-accidents` - 车辆事故风险检测
- `/online-monitor-vehicle-accidents` - 在线监控车辆事故风险
- `/detect-abnormal-driving` - 异常驾驶行为检测
- `/online-monitor-abnormal-driving` - 在线监控异常驾驶行为

### 2. 检测流程

对于每个端点，实现了以下流程:

1. 接收上传的图像
2. 加载 yolo11n-seg.pt 模型
3. 使用模型进行检测推理
4. 将检测到的类别映射到相应的风险/行为类别
5. 创建可视化结果
6. 使用可视化后的图像进行 qwen-vl-max-1119 推理分析
7. 返回完整的检测和分析结果

### 3. 类别映射

#### 车辆事故风险映射:
```python
risk_mapping = {
    "路面裂缝": "碰撞风险",
    "坑洼": "交通拥堵",
    "路面积水": "路侧停车",
    "crack": "碰撞风险",
    "pothole": "交通拥堵",
    "water": "路侧停车"
}
```

#### 异常驾驶行为映射:
```python
behavior_mapping = {
    "路面裂缝": "超速驾驶",
    "坑洼": "车道偏离",
    "路面积水": "驾驶分心",
    "crack": "超速驾驶",
    "pothole": "车道偏离",
    "water": "驾驶分心"
}
```

### 4. 测试脚本

创建了 `test_yolo11x_seg.py` 测试脚本，用于验证:
- 模型加载
- 检测功能
- 可视化功能
- 类别映射

## 使用说明

### 模型路径

系统会按以下顺序查找 yolo11n-seg.pt 模型:
1. `../src/models/yolo11n-seg.pt`
2. `./models/yolo11n-seg.pt`
3. `./yolo11n-seg.pt`

### 测试

运行测试脚本:
```
python test_yolo11x_seg.py
```

成功运行后，将在当前目录生成两个可视化结果文件:
- `test_road1_vehicle_accidents.jpg` - 车辆事故风险可视化结果
- `test_road1_abnormal_driving.jpg` - 异常驾驶行为可视化结果

## 注意事项

1. 当前实现使用了与道路缺陷检测相同的 YOLO 接口，通过类别映射来适配车辆风险和驾驶行为场景
2. 模型的类别预测会被映射到特定的风险/行为类别，即使原始模型可能并非专为此目的训练
3. 如果检测结果为空，系统会使用默认模拟数据确保前端显示正常

## 未来改进

1. 使用专门针对车辆事故风险和异常驾驶行为训练的模型
2. 优化类别映射逻辑，提高检测精度
3. 增加更多的风险/行为类别 
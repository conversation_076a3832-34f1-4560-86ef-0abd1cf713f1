<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交通眼 - 基于 Qwen 2.5 VL 的交通分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .container {
            max-width: 900px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        h1 {
            color: #0d6efd;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .upload-container {
            border: 2px dashed #dee2e6;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #f8f9fa;
            transition: all 0.3s;
        }
        .upload-container:hover {
            border-color: #0d6efd;
            background-color: #f1f8ff;
        }
        #preview {
            max-width: 100%;
            max-height: 400px;
            margin: 20px auto;
            display: none;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        #result {
            background-color: #f8f9fa;
            border-left: 4px solid #0d6efd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            display: none;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #0d6efd;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        #analysisResult {
            line-height: 1.6;
            font-size: 1.1rem;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center">交通眼</h1>
        <p class="lead text-center mb-4">上传交通图像，使用 Qwen 2.5 VL 进行智能分析</p>
        
        <div class="upload-container" id="drop-area">
            <p>拖放交通图像到此处或点击浏览</p>
            <input type="file" id="fileInput" accept="image/*" class="form-control mb-3" style="display: none;">
            <button class="btn btn-primary" id="browseButton">浏览文件</button>
        </div>
        
        <img id="preview" class="img-fluid" alt="预览">
        
        <div class="mb-3">
            <label for="promptInput" class="form-label">自定义提示（可选）：</label>
            <textarea id="promptInput" class="form-control" rows="3" placeholder="分析这个交通场景。识别车辆、行人、交通信号灯，以及任何潜在的危险或违规情况。"></textarea>
        </div>
        
        <div class="text-center">
            <button id="analyzeButton" class="btn btn-success btn-lg" disabled>分析图像</button>
        </div>
        
        <div class="loader" id="loader"></div>
        
        <div id="result" class="mt-4">
            <h4>分析结果：</h4>
            <div id="analysisResult"></div>
        </div>
    </div>
    
    <div class="footer container">
        <p>由 Qwen 2.5 VL 提供支持 | 交通眼 &copy; 2025</p>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dropArea = document.getElementById('drop-area');
            const fileInput = document.getElementById('fileInput');
            const browseButton = document.getElementById('browseButton');
            const preview = document.getElementById('preview');
            const analyzeButton = document.getElementById('analyzeButton');
            const promptInput = document.getElementById('promptInput');
            const loader = document.getElementById('loader');
            const result = document.getElementById('result');
            const analysisResult = document.getElementById('analysisResult');
            
            // 处理浏览按钮点击
            browseButton.addEventListener('click', () => {
                fileInput.click();
            });
            
            // 处理文件选择
            fileInput.addEventListener('change', handleFileSelect);
            
            // 处理拖放
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                dropArea.style.borderColor = '#0d6efd';
                dropArea.style.backgroundColor = '#f1f8ff';
            }
            
            function unhighlight() {
                dropArea.style.borderColor = '#dee2e6';
                dropArea.style.backgroundColor = '#f8f9fa';
            }
            
            dropArea.addEventListener('drop', handleDrop, false);
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                
                if (files.length) {
                    fileInput.files = files;
                    handleFileSelect();
                }
            }
            
            function handleFileSelect() {
                const file = fileInput.files[0];
                
                if (file && file.type.match('image.*')) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                        analyzeButton.disabled = false;
                    }
                    
                    reader.readAsDataURL(file);
                }
            }
            
            // 处理分析按钮点击
            analyzeButton.addEventListener('click', async () => {
                const file = fileInput.files[0];
                if (!file) return;
                
                // 显示加载动画并隐藏之前的结果
                loader.style.display = 'block';
                result.style.display = 'none';
                analyzeButton.disabled = true;
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('prompt', promptInput.value || '分析这个交通场景。识别车辆、行人、交通信号灯，以及任何潜在的危险或违规情况。详细描述所见内容。');
                
                try {
                    const response = await fetch('/analyze/image', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    console.log("API响应:", data);
                    
                    // 显示结果
                    if (data.success) {
                        // 检查analysis是否为字符串
                        let analysisText = data.analysis;
                        
                        // 如果是JSON字符串或对象，尝试提取text字段
                        if (typeof analysisText === 'string') {
                            try {
                                // 尝试解析JSON字符串
                                const jsonData = JSON.parse(analysisText);
                                if (Array.isArray(jsonData) && jsonData.length > 0 && jsonData[0].text) {
                                    // 如果是数组且第一个元素有text字段
                                    analysisText = jsonData[0].text;
                                } else if (jsonData.text) {
                                    // 如果直接有text字段
                                    analysisText = jsonData.text;
                                }
                            } catch (e) {
                                // 如果不是有效的JSON，保持原样
                                console.log("不是有效的JSON字符串，保持原样");
                            }
                        } else if (typeof analysisText === 'object') {
                            // 如果已经是对象
                            if (Array.isArray(analysisText) && analysisText.length > 0 && analysisText[0].text) {
                                // 如果是数组且第一个元素有text字段
                                analysisText = analysisText[0].text;
                            } else if (analysisText.text) {
                                // 如果直接有text字段
                                analysisText = analysisText.text;
                            } else {
                                // 如果没有text字段，转为字符串
                                analysisText = JSON.stringify(analysisText, null, 2);
                            }
                        }
                        
                        // 将文本中的换行符转换为HTML换行
                        const formattedText = String(analysisText).replace(/\n/g, '<br>');
                        analysisResult.innerHTML = formattedText;
                    } else {
                        analysisResult.innerHTML = `<div class="alert alert-danger">${data.error || '分析失败。请重试。'}</div>`;
                        console.error("API错误:", data.error);
                    }
                    result.style.display = 'block';
                } catch (error) {
                    console.error('错误详情:', error);
                    analysisResult.innerHTML = `<div class="alert alert-danger">分析过程中发生错误: ${error.message}</div>`;
                    result.style.display = 'block';
                } finally {
                    loader.style.display = 'none';
                    analyzeButton.disabled = false;
                }
            });
        });
    </script>
</body>
</html> 
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索和筛选
const searchQuery = ref('')
const dateRange = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 50
})

// 模拟历史记录数据
const historyRecords = ref([
  {
    id: 1,
    timestamp: new Date(Date.now() - 3600000 * 2),
    imageUrl: 'https://picsum.photos/id/1018/800/600',
    thumbnail: 'https://picsum.photos/id/1018/100/60',
    detectionCount: 5,
    vehicleTypes: ['小型汽车', '卡车', '摩托车'],
    status: 'completed'
  },
  {
    id: 2,
    timestamp: new Date(Date.now() - 3600000 * 5),
    imageUrl: 'https://picsum.photos/id/1015/800/600',
    thumbnail: 'https://picsum.photos/id/1015/100/60',
    detectionCount: 3,
    vehicleTypes: ['小型汽车', '公交车'],
    status: 'completed'
  },
  {
    id: 3,
    timestamp: new Date(Date.now() - 3600000 * 24),
    imageUrl: 'https://picsum.photos/id/1019/800/600',
    thumbnail: 'https://picsum.photos/id/1019/100/60',
    detectionCount: 8,
    vehicleTypes: ['小型汽车', '卡车', '摩托车', '自行车'],
    status: 'processing'
  },
  {
    id: 4,
    timestamp: new Date(Date.now() - 3600000 * 48),
    imageUrl: 'https://picsum.photos/id/1016/800/600',
    thumbnail: 'https://picsum.photos/id/1016/100/60',
    detectionCount: 2,
    vehicleTypes: ['小型汽车'],
    status: 'completed'
  },
  {
    id: 5,
    timestamp: new Date(Date.now() - 3600000 * 72),
    imageUrl: 'https://picsum.photos/id/1003/800/600',
    thumbnail: 'https://picsum.photos/id/1003/100/60',
    detectionCount: 6,
    vehicleTypes: ['小型汽车', '卡车', '摩托车'],
    status: 'failed'
  }
])

// 查看详情
const viewDetails = (record) => {
  console.log('查看详情:', record)
  // 这里可以实现导航到详情页或打开详情弹窗
  ElMessage.success(`正在查看ID为${record.id}的记录`)
}

// 删除记录
const deleteRecord = (record) => {
  ElMessageBox.confirm(
    `确定要删除这条记录吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      historyRecords.value = historyRecords.value.filter(item => item.id !== record.id)
      ElMessage.success('删除成功')
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 搜索记录
const searchRecords = () => {
  loading.value = true
  
  // 模拟搜索延迟
  setTimeout(() => {
    loading.value = false
    ElMessage.success(`搜索完成: ${searchQuery.value}`)
  }, 500)
}

// 重置筛选
const resetFilters = () => {
  searchQuery.value = ''
  dateRange.value = []
  searchRecords()
}

// 处理分页变化
const handlePageChange = (page) => {
  pagination.currentPage = page
  // 这里可以实现分页加载数据
}

// 批量操作
const selectedRecords = ref([])
const handleSelectionChange = (selection) => {
  selectedRecords.value = selection
}

// 批量删除
const batchDelete = () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请先选择要删除的记录')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的${selectedRecords.value.length}条记录吗？此操作不可恢复。`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      const ids = selectedRecords.value.map(item => item.id)
      historyRecords.value = historyRecords.value.filter(item => !ids.includes(item.id))
      selectedRecords.value = []
      ElMessage.success('批量删除成功')
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 导出记录
const exportRecords = () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请先选择要导出的记录')
    return
  }
  
  ElMessage.success(`正在导出${selectedRecords.value.length}条记录`)
}

// 加载动画
onMounted(() => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
})
</script>

<template>
  <div class="history-container">
    <div class="section-header">
      <h1 class="page-title">历史记录</h1>
      <div class="header-actions">
        <el-button type="danger" :disabled="selectedRecords.length === 0" @click="batchDelete">
          <el-icon><Delete /></el-icon> 批量删除
        </el-button>
        <el-button type="primary" :disabled="selectedRecords.length === 0" @click="exportRecords">
          <el-icon><Download /></el-icon> 导出记录
        </el-button>
      </div>
    </div>
    
    <!-- 筛选工具栏 -->
    <el-card class="filter-card">
      <div class="filter-toolbar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索历史记录"
          prefix-icon="Search"
          clearable
          @keyup.enter="searchRecords"
          style="width: 300px"
        />
        
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="margin-left: 16px; width: 350px"
        />
        
        <div class="filter-actions">
          <el-button type="primary" @click="searchRecords">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><RefreshRight /></el-icon> 重置
          </el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 历史记录表格 -->
    <el-card class="table-card" v-loading="loading">
      <div class="table-header">
        <div class="table-title">
          <el-icon><List /></el-icon>
          <span>检测记录列表</span>
        </div>
        <div class="table-actions">
          <el-tooltip content="刷新" placement="top">
            <el-button circle @click="searchRecords">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="列设置" placement="top">
            <el-button circle>
              <el-icon><Setting /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
      
      <el-table 
        :data="historyRecords" 
        style="width: 100%" 
        border 
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column type="index" width="50" label="#" />
        
        <el-table-column label="缩略图" width="120" align="center">
          <template #default="scope">
            <div class="thumbnail-wrapper">
              <el-image 
                :src="scope.row.thumbnail" 
                fit="cover"
                :preview-src-list="[scope.row.imageUrl]"
                preview-teleported
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="timestamp" label="上传时间" width="180" sortable>
          <template #default="scope">
            <div class="time-cell">
              <el-icon><Calendar /></el-icon>
              <span>{{ scope.row.timestamp.toLocaleString() }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="detectionCount" label="检测数量" width="100" sortable>
          <template #default="scope">
            <el-badge :value="scope.row.detectionCount" type="primary" />
          </template>
        </el-table-column>
        
        <el-table-column prop="vehicleTypes" label="车辆类型">
          <template #default="scope">
            <div class="tags-wrapper">
              <el-tag 
                v-for="type in scope.row.vehicleTypes" 
                :key="type"
                size="small"
                effect="plain"
                style="margin-right: 5px; margin-bottom: 5px"
              >
                {{ type }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="scope.row.status === 'completed' ? 'success' : scope.row.status === 'processing' ? 'warning' : 'danger'"
              effect="dark"
              size="small"
            >
              <el-icon v-if="scope.row.status === 'completed'"><SuccessFilled /></el-icon>
              <el-icon v-else-if="scope.row.status === 'processing'"><Loading /></el-icon>
              <el-icon v-else-if="scope.row.status === 'failed'"><CircleCloseFilled /></el-icon>
              {{ scope.row.status === 'completed' ? '已完成' : scope.row.status === 'processing' ? '处理中' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="viewDetails(scope.row)">
                <el-icon><View /></el-icon> 查看
              </el-button>
              <el-button type="danger" size="small" @click="deleteRecord(scope.row)">
                <el-icon><Delete /></el-icon> 删除
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <template #empty>
          <div class="empty-data">
            <el-empty description="暂无历史记录" :image-size="100">
              <template #description>
                <p>暂无符合条件的历史记录，请尝试上传新图片或调整筛选条件</p>
              </template>
            </el-empty>
          </div>
        </template>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.history-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.filter-toolbar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 12px;
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 16px 0;
  border-bottom: 1px solid var(--border-color);
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.table-actions {
  display: flex;
  gap: 8px;
}

.thumbnail-wrapper {
  width: 80px;
  height: 50px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin: 0 auto;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.empty-data {
  padding: 40px 0;
}

.empty-data p {
  margin-top: 8px;
  color: var(--text-secondary);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 
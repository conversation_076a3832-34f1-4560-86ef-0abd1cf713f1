@import './base.css';

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: #1890ff;
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: rgba(24, 144, 255, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
    justify-content: center;
  }
}

/* HT for Web Visualization Enhancements */

/* Smooth scrolling for the entire page */
html, body {
  scroll-behavior: smooth;
  overflow-y: auto;
}

/* Custom scrollbar for modern browsers */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 21, 41, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.5);
  border-radius: 3px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.8);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.03); }
  100% { transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(24, 144, 255, 0.5); }
  50% { box-shadow: 0 0 20px rgba(24, 144, 255, 0.8); }
  100% { box-shadow: 0 0 5px rgba(24, 144, 255, 0.5); }
}

/* Apply animations to elements */
.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

.slide-in-up {
  animation: slideInUp 0.5s ease forwards;
}

.slide-in-right {
  animation: slideInRight 0.5s ease forwards;
}

.pulse {
  animation: pulse 2s infinite;
}

.float {
  animation: float 3s ease-in-out infinite;
}

.glow {
  animation: glow 2s infinite;
}

/* Loading shimmer effect */
.shimmer {
  background: linear-gradient(90deg,
    rgba(0, 21, 41, 0.05),
    rgba(24, 144, 255, 0.2),
    rgba(0, 21, 41, 0.05)
  );
  background-size: 1000px 100%;
  animation: shimmer 2s infinite linear;
}

/* Glass morphism effect */
.glass {
  background: rgba(0, 21, 41, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(24, 144, 255, 0.1);
}

/* Hover effects for buttons and interactive elements */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(90deg, #1890ff, #096dd9);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Card styles */
.ht-card {
  background: linear-gradient(135deg, rgba(0, 33, 64, 0.7), rgba(0, 21, 41, 0.7));
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  position: relative;
}

.ht-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #1890ff, #096dd9);
}

.ht-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
  border-color: rgba(24, 144, 255, 0.3);
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.badge-primary {
  background-color: rgba(24, 144, 255, 0.2);
  color: #1890ff;
  border: 1px solid rgba(24, 144, 255, 0.3);
}

.badge-success {
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.badge-danger {
  background-color: rgba(245, 34, 45, 0.2);
  color: #ff4d4f;
  border: 1px solid rgba(245, 34, 45, 0.3);
  box-shadow: 0 0 5px rgba(245, 34, 45, 0.3);
}

.badge-warning {
  background-color: rgba(250, 173, 20, 0.2);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

/* Data visualization elements */
.data-chart {
  width: 100%;
  height: 100%;
  min-height: 200px;
  background: rgba(0, 21, 41, 0.3);
  border-radius: 8px;
  padding: 16px;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.data-chart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(24, 144, 255, 0.1), transparent 70%);
  z-index: 0;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin: 16px 0;
}

.data-item {
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.7), rgba(0, 21, 41, 0.7));
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(24, 144, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.data-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border-color: rgba(24, 144, 255, 0.3);
}

.data-icon {
  width: 40px;
  height: 40px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 16px;
  filter: drop-shadow(0 0 5px rgba(24, 144, 255, 0.5));
  position: relative;
  z-index: 1;
}

.data-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.data-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.65);
  margin-bottom: 4px;
}

.data-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

.data-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.data-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #096dd9);
  transition: width 1s ease;
}

/* Focus styles for accessibility */
:focus {
  outline: 2px solid rgba(24, 144, 255, 0.5);
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  .data-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 1200px) {
  html {
    font-size: 16px;
  }
}

<template>
  <div class="online-monitor-page">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <div class="nav-section">
        <button
          v-for="category in riskCategories"
          :key="category.id"
          class="nav-button hover-lift"
          @click="selectCategory(category.id)"
          :class="{ active: selectedCategory === category.id }"
        >
          <span class="nav-icon" :class="category.id + '-icon'"></span>
          <span>{{ category.label }}</span>
        </button>
      </div>

      <div class="detection-options-container">
        <h3 class="sidebar-header">�������?/h3>
        <div class="detection-options">
          <div
            v-for="(category, index) in filteredDetectionOptions"
            :key="category.id"
            class="detection-category slide-in-up"
            :style="{animationDelay: `${0.1 * index}s`}"
          >
            <div
              class="category-header"
              @click="toggleCategory(category.id)"
            >
              <div class="category-icon-wrapper">
                <span class="category-icon" :class="category.id + '-icon'"></span>
                <span class="category-label">{{ category.label }}</span>
              </div>
              <span class="expand-icon">{{ category.expanded ? '��' : '?' }}</span>
            </div>
            <div
              v-if="category.expanded"
              class="category-options"
            >
              <div
                v-for="option in category.options"
                :key="option.id"
                class="option-item"
              >
                <label class="option-label custom-checkbox">
                  <input
                    type="checkbox"
                    :checked="option.checked"
                    @change="toggleOption(category.id, option.id)"
                  />
                  <span class="option-text">{{ option.label }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Middle - Video and Detection Controls -->
    <div class="video-container">
      <div class="card-container">
        <div class="card-header">
          <span class="card-title">
            өʯ�Ƽ��?          </span>
          <div class="card-tools">
            <!-- ���ӳ�ʼ��״ָ̬ʾ�� -->
            <span v-if="isInitializing" class="initializing-indicator">
              <span class="loading-spinner"></span>
              ��ʼ����...
            </span>
            <button class="toggle-button hover-lift" @click="toggleMonitoring" v-if="videoPlayerElement && !videoPlayerElement.paused">
              {{ isMonitoringActive ? 'ֹͣ���? : '�ָ����? }}
            </button>
            <button class="toggle-button hover-lift" @click="toggleDetectionBoxes" v-if="videoSource && detectionResults">
              {{ showDetectionBoxes ? '���ؼ���' : '��ʾ����' }}
            </button>
            <button class="toggle-button hover-lift yolo-toggle-button" @click="toggleYoloDetection">
              {{ useYoloDetection ? 'ͣ�ü����ӻ�' : '���ü����ӻ�' }}
            </button>
            <button class="toggle-button hover-lift yolo-button" @click="toggleDelayedPlayback" v-if="useYoloDetection">
              {{ isDelayedPlaybackActive ? 'ʵʱ����' : '�ӳٲ���' }}
              <span class="toggle-button-hint">{{ isDelayedPlaybackActive ? '(ʵʱ��⵫�ӳ����?' : '(������ἴʱ���?' }}</span>
            </button>
            <button class="toggle-button hover-lift yolo-reload-button" @click="initializeWebSocket" v-if="useYoloDetection && (!wsConnection || wsConnection.readyState !== 1)">
              ����YOLO
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="video-preview-wrapper">
            <!-- YS7 Cloud video player -->
            <video
              ref="videoPlayerElement"
              id="ys7-video-player"
              class="ys7-video video-player"
              controls
              autoplay
              muted
              @loadedmetadata="initializeCanvas"
              @play="handlePlay"
              @pause="handlePauseOrEnd"
              @ended="handlePauseOrEnd">
            </video>
            <canvas ref="detectionCanvasElement" class="detection-canvas"></canvas>
            
            <!-- Loading overlay for YS7 player -->
            <div class="ys7-loading-overlay" v-if="isYs7Loading">
              <div class="processing-indicator"></div>
              <div class="processing-text">����өʯ����Ƶ��...</div>
            </div>
            
            <!-- Error overlay for YS7 player -->
            <div class="ys7-error-overlay" v-if="ys7Error">
              <div class="error-icon"></div>
              <div class="error-message">{{ ys7ErrorMessage }}</div>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <div class="detection-action">
            <div class="image-actions">
              <button class="detect-button" @click="triggerManualDetection" :disabled="isLoading || (videoPlayerElement && !videoPlayerElement.paused)">
                <div class="detect-button-content">
                  <i class="detect-icon" v-if="!isLoading"></i>
                  <span v-if="isLoading">�����?..</span>
                  <span v-else>{{ (videoPlayerElement && !videoPlayerElement.paused) ? '�������Զ����? : '��⵱ǰ�? }}</span>
                </div>
              </button>
              <div class="model-selector">
                <div class="selected-model" @click="isModelDropdownOpen = !isModelDropdownOpen">
                  <span class="model-icon"></span>
                  {{ useYoloDetection ? '����' : currentModel.name }}
                  <span class="dropdown-icon">��</span>
                </div>
                <div class="model-dropdown" v-if="isModelDropdownOpen">
                  <div v-if="useYoloDetection" class="model-option active">
                    <div class="model-name">YOLO ���ģ��?/div>
                    <div class="model-description">ʹ��yolo11n-seg.ptģ�ͽ���ͨ�÷��ռ��?/div>
                  </div>
                  <div v-else
                    v-for="model in models"
                    :key="model.id"
                    class="model-option"
                    @click="handleModelChange(model.id); isModelDropdownOpen = false"
                    :class="{ active: currentModel.id === model.id }"
                  >
                    <div class="model-name">{{ model.name }}</div>
                    <div class="model-description">{{ model.description }}</div>
                  </div>
                </div>
              </div>
            </div>
            <p v-if="error" class="error-message slide-in-up">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel - Detection Results -->
    <div class="result-panel">
      <div class="risk-analysis-header" :class="{ 'updating': isHeaderUpdating }">
        <div class="risk-analysis-icon"></div>
        <span class="risk-analysis-title">{{ getRiskStatusTitle }}</span>
        <span v-if="lastUpdated" class="last-updated-time">{{ formatUpdateTime(lastUpdated) }}</span>
      </div>
      <div class="result-panel-content">
        <!-- Qwen API Results Panel - Moved to the top and made more prominent -->
        <QwenResultsPanel
          :results="qwenResults"
          :showRawJson="true"
          class="qwen-results-panel-main"
          :key="lastUpdated ? lastUpdated.getTime() : 'initial'"
        />

        <!-- API Logs Section -->
        <div class="api-logs-container" v-if="apiLogs.length > 0">
          <div class="api-logs-header">
            <div class="api-logs-icon"></div>
            <span class="api-logs-title">���������?/span>
            <button class="api-log-action-btn" @click="showLatestLogOnVideo" v-if="canShowLogOnVideo">
              ��ʾ����Ƶ��
            </button>
          </div>
          <div class="api-logs-content">
            <div v-for="(log, index) in apiLogs" :key="index" class="api-log-item">
              <div class="api-log-header">
                <span class="api-log-time">{{ new Date().toLocaleTimeString() }}</span>
                <span class="api-log-status success">�ɹ�</span>
            </div>
              <div class="api-log-details">
                <!-- Parse log content for more user-friendly display -->
                <div v-if="isJsonString(log)" class="api-log-formatted">
                  <template v-if="tryParseJsonLog(log)">
                    <div class="api-log-section">
                      <div class="api-log-section-title">��⵽��Ŀ��?({{ getDetectionCount(log) }})</div>
                      <div class="api-log-detection-items">
                        <div v-for="(detection, idx) in getDetections(log)" :key="idx" class="api-log-detection-item">
                          <div class="detection-type" :class="getRiskClass(detection.risk_level)">
                            {{ detection.category || detection.label || 'δ֪Ŀ��' }}
                          </div>
                          <div class="detection-risk">{{ detection.risk_level || '�޷���' }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="api-log-section" v-if="getSafetyAnalysis(log)">
                      <div class="api-log-section-title">��ȫ�����뽨��</div>
                      <div class="api-log-analysis api-log-text-large" v-html="formatSafetyAnalysis(getSafetyAnalysis(log))"></div>
                      <div class="api-log-actions">
                        <button @click="showSafetyInPanel(log)" class="api-log-action-btn">
                          <span class="action-icon recommendation-icon"></span>
                          ���Ҳ���������?                        </button>
                      </div>
                    </div>
                  </template>
                  <div v-else class="api-log-json-viewer">
                    <div class="json-viewer-header">
                      <div class="json-viewer-type">JSON ��Ӧ����</div>
                      <div class="json-viewer-actions">
                        <button class="json-viewer-action-btn" @click="showLogOnVideo(log)" v-if="hasDetectionData(log)" title="����Ƶ����ʾ">
                          <span class="view-on-video-icon">???</span>
                        </button>
                        <button class="json-viewer-action-btn" @click="expandAllJson(log)" title="չ��ȫ��">
                          <span class="expand-icon">+</span>
                        </button>
                        <button class="json-viewer-action-btn" @click="collapseAllJson(log)" title="�۵�ȫ��">
                          <span class="collapse-icon">-</span>
                        </button>
                      </div>
                    </div>
                    <div class="json-viewer-content">
                      <template v-if="isJsonString(log)">
                        <div class="json-tree">
                          <component :is="jsonTreeNodeComponentRef" 
                            :data="parseJson(log)" 
                            path="json-root">
                          </component>
                        </div>
                      </template>
                      <pre v-else class="api-log-text api-log-text-large">{{ log }}</pre>
                    </div>
                  </div>
                </div>
                <pre v-else class="api-log-text api-log-text-large">{{ log }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- Conditional rendering based on detection state -->
        <div v-if="showLoadingMessage" class="loading-results">
          <p>���ڷ�����Ƶ֡...</p>
        </div>
        <div v-else-if="error" class="error-display">
          <p>{{ error }}</p>
        </div>
        <div v-else-if="detectionResults">
          <!-- High Risk Events -->
          <div class="risk-events-container high-risk" v-if="groupedHighRiskEventsForDisplay.length > 0">
            <div class="risk-summary">
              <div class="risk-count danger">{{ totalHighRiskCount }}</div>
              <div class="risk-label">�߷����¼�</div>
            </div>
            <div class="risk-categories">
              <template v-for="categoryGroup in groupedHighRiskEventsForDisplay" :key="categoryGroup.category">
                <div class="risk-category">
                  <div class="risk-category-header high-risk">
                    <span class="category-icon" :class="categoryGroup.category.toLowerCase() + '-icon'"></span>
                    <span class="category-name">{{ categoryGroup.category }}</span>
                    <span class="category-count danger">{{ categoryGroup.totalCount }}</span>
                  </div>
                  <div class="risk-events">
                    <div v-for="eventItem in categoryGroup.events" :key="eventItem.eventName" class="risk-event-item high-risk">
                      <div class="risk-event-content">
                        <div class="risk-event-name">{{ translateEventName(eventItem.eventName) }}
                          <span v-if="eventItem.count > 1" class="event-count">x{{ eventItem.count }}</span>
                        </div>
                      </div>
                      <div class="risk-level risk-high">�߷���</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- Low Risk Events -->
          <div class="risk-events-container low-risk" v-if="groupedLowRiskEventsForDisplay.length > 0">
            <div class="risk-summary">
              <div class="risk-count warning">{{ totalLowRiskCount }}</div>
              <div class="risk-label">�ͷ����¼�</div>
            </div>
            <div class="risk-categories">
              <template v-for="categoryGroup in groupedLowRiskEventsForDisplay" :key="categoryGroup.category">
                <div class="risk-category">
                  <div class="risk-category-header low-risk">
                    <span class="category-icon" :class="categoryGroup.category.toLowerCase() + '-icon'"></span>
                    <span class="category-name">{{ categoryGroup.category }}</span>
                    <span class="category-count warning">{{ categoryGroup.totalCount }}</span>
                  </div>
                  <div class="risk-events">
                    <div v-for="eventItem in categoryGroup.events" :key="eventItem.eventName" class="risk-event-item low-risk">
                      <div class="risk-event-content">
                        <div class="risk-event-name">{{ translateEventName(eventItem.eventName) }}
                          <span v-if="eventItem.count > 1" class="event-count">x{{ eventItem.count }}</span>
                        </div>
                      </div>
                      <div class="risk-level risk-low">�ͷ���</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- Safety Analysis Section -->
          <div class="safety-analysis-container" v-if="detectionResults.image_overall_safety_analysis_and_control_recommendations || analysisDescription">
            <div class="safety-analysis-header">
              <div class="safety-analysis-icon"></div>
              <span class="safety-analysis-title">��ȫ�����뽨��</span>
            </div>
            <div class="safety-analysis-content" v-if="detectionResults.image_overall_safety_analysis_and_control_recommendations" v-html="formatSafetyAnalysis(detectionResults.image_overall_safety_analysis_and_control_recommendations)"></div>
            <div class="safety-analysis-content" v-else-if="analysisDescription" v-html="formatSafetyAnalysis(analysisDescription)"></div>
          </div>

          <!-- Safe Container -->
          <div class="safe-container" v-if="totalHighRiskCount === 0 && totalLowRiskCount === 0 && !analysisDescription">
            <div class="safe-icon">?</div>
            <div class="safe-message">��ȫ</div>
            <div class="safe-description">δ��⵽�κη����¼�?/div>
          </div>
        </div>
        <div v-else class="no-detection-results">
          <div class="no-risk-icon">??</div>
          <div class="no-risk-message">������Ƶ���Զ���ʼ��⡣��Ҳ������ͣ��Ƶ�����"��⵱ǰ�?�ֶ�������</div>
        </div>

        <ObjectDetectionPanel
          :imagePreview="currentFramePreview"
          :selectedOptions="selectedOptions"
          :detectionResults="detectionResults"
        />
        <div class="stats-panel-horizontal" v-if="detectionResults">
          <div class="stats-item">
            <div class="stats-icon person-icon"></div>
            <div class="stats-info">
              <div class="stats-label">��Ա</div>
              <div class="stats-value">{{ getPersonCount() }}</div>
            </div>
          </div>
          <div class="stats-item">
            <div class="stats-icon vehicle-icon"></div>
            <div class="stats-info">
              <div class="stats-label">����</div>
              <div class="stats-value">{{ getVehicleCount() }}</div>
            </div>
          </div>
          <div class="stats-item">
            <div class="stats-icon machine-icon"></div>
            <div class="stats-info">
              <div class="stats-label">�豸</div>
              <div class="stats-value">{{ getMachineCount() }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import ObjectDetectionPanel from './ObjectDetectionPanel.vue';
import QwenResultsPanel from './QwenResultsPanel.vue';
import { detectObjects } from '../services/DetectionService';
import type { DetectionResult, HighRiskEvent, LowRiskEvent, DetectionObject } from '../services/DetectionService';
import axios from 'axios';
import Hls from 'hls.js'; // Import hls.js from the installed package

// Define recursive component for JSON tree display
const JsonTreeNode = {
  name: 'JsonTreeNode',
  props: {
    data: {
      type: [Object, Array],
      required: true
    },
    path: {
      type: String,
      required: true
    }
  },
  template: `
    <div>
      <div v-for="(value, key) in data" :key="key" class="json-tree-item">
        <div class="json-tree-key" @click="toggleJsonNode(path + '-' + key)">
          <span v-if="isObject(value)" class="json-tree-toggle">?</span>
          <span class="json-key-name">{{ key }}:</span>
          <span v-if="isObject(value)" class="json-type">{{ getJsonNodeType(value) }}</span>
          <span v-else class="json-value" :class="getJsonValueClass(value)">{{ formatJsonValue(value) }}</span>
      </div>
        <div :id="path + '-' + key" class="json-tree-children" v-if="isObject(value)">
          <json-tree-node :data="value" :path="path + '-' + key"></json-tree-node>
        </div>
      </div>
      </div>
  `,
  methods: {
    isObject(value) {
      return value !== null && (typeof value === 'object');
    },
    getJsonNodeType(value) {
      return Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`;
    },
    getJsonValueClass(value) {
      if (typeof value === 'string') return 'json-string';
      if (typeof value === 'number') return 'json-number';
      if (typeof value === 'boolean') return 'json-boolean';
      if (value === null) return 'json-null';
      return '';
    },
    formatJsonValue(value) {
      if (typeof value === 'string') return `"${value}"`;
      if (value === null) return 'null';
      return String(value);
    },
    toggleJsonNode(nodeId) {
      const node = document.getElementById(nodeId);
      if (node) {
        const isVisible = node.style.display !== 'none';
        node.style.display = isVisible ? 'none' : 'block';
        
        // Change toggle icon
        const toggleIcon = node.previousElementSibling.querySelector('.json-tree-toggle');
        if (toggleIcon) {
          toggleIcon.textContent = isVisible ? '?' : '��';
        }
      }
    }
  }
};

// Register component for Vue 3 composition API in script setup
import { defineComponent, h } from 'vue';

const jsonTreeNodeComponent = defineComponent({
  name: 'JsonTreeNode',
  props: {
    data: {
      type: [Object, Array],
      required: true
    },
    path: {
      type: String,
      required: true
    }
  },
  setup(props) {
    return () => {
      return h('div', {}, Object.entries(props.data).map(([key, value]) => {
        const isObj = value !== null && typeof value === 'object';
        
        const keyEl = h('div', {
          class: 'json-tree-key',
          onClick: () => {
            const nodeId = props.path + '-' + key;
            const node = document.getElementById(nodeId);
            if (node) {
              const isVisible = node.style.display !== 'none';
              node.style.display = isVisible ? 'none' : 'block';
              
              // Get toggle element
              const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
              if (toggleIcon) {
                toggleIcon.textContent = isVisible ? '?' : '��';
              }
            }
          }
        }, [
          isObj ? h('span', { class: 'json-tree-toggle' }, '?') : null,
          h('span', { class: 'json-key-name' }, `${key}:`),
          isObj 
            ? h('span', { class: 'json-type' }, 
                Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`)
            : h('span', { 
                class: ['json-value', 
                  typeof value === 'string' ? 'json-string' : 
                  typeof value === 'number' ? 'json-number' : 
                  typeof value === 'boolean' ? 'json-boolean' : 
                  value === null ? 'json-null' : '']
              }, typeof value === 'string' ? `"${value}"` : value === null ? 'null' : String(value))
        ]);
        
        const valueEl = isObj ? h('div', {
          class: 'json-tree-children',
          id: props.path + '-' + key,
          style: { display: 'none' }
        }, [
          h(jsonTreeNodeComponent, {
            data: value,
            path: props.path + '-' + key
          })
        ]) : null;
        
        return h('div', { class: 'json-tree-item' }, [keyEl, valueEl].filter(Boolean));
      }));
    };
  }
});

// Define window.Hls interface for TypeScript
declare global {
  interface Window {
    EZUIKit?: any;
  }
}

// --- API Logs State ---
const apiLogs = ref<string[]>([]);
const isPollingLogs = ref(false);
const analysisDescription = ref<string>('');

// Qwen API results state
interface QwenDetection {
  category: string;
  event_description: string;
  risk_level: string;
  confidence_score: number;
  bbox_2d: number[];
}

interface QwenRiskEvent {
  description: string;
  mitigation: string;
}

interface QwenResultsData {
  detections: QwenDetection[];
  description: string;
  high_risk_events: QwenRiskEvent[];
  low_risk_events: QwenRiskEvent[];
}

const qwenResults = ref<QwenResultsData | null>(null);
const lastUpdated = ref<Date | null>(null);
const isHeaderUpdating = ref(false);

// ���㶯̬���⣬����Qwen�����?const getRiskStatusTitle = computed(() => {
  if (!qwenResults.value) return '����ͳ��';
  
  // ��ȡ��/�ͷ����¼�����
  const highRiskCount = qwenResults.value.high_risk_events?.length || 0;
  const lowRiskCount = qwenResults.value.low_risk_events?.length || 0;
  const detectionCount = qwenResults.value.detections?.length || 0;
  
  // ���ݷ��ճ̶����ɲ�ͬ����
  if (highRiskCount > 0) {
    return `����ͳ�� (${highRiskCount}��߷���?`;
  } else if (lowRiskCount > 0) {
    return `����ͳ�� (${lowRiskCount}��ͷ���?`;
  } else if (detectionCount > 0) {
    return `�������� (${detectionCount}�������?`;
  } else {
    return '������ȫ����';
  }
});

// ��ʽ������ʱ��
const formatUpdateTime = (time: Date): string => {
  return `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}:${time.getSeconds().toString().padStart(2, '0')}`;
};

// ����qwenResults�仯���������շ���ͷ�����¶���
watch(() => qwenResults.value, (newVal, oldVal) => {
  if (newVal) {
    // ����ʱ���?    lastUpdated.value = new Date();
    
    // ����ͷ�����¶���
    isHeaderUpdating.value = true;
    setTimeout(() => {
      isHeaderUpdating.value = false;
    }, 2000);
  }
}, { deep: true });

// JSON viewer utilities
const isJsonString = (str: string): boolean => {
  try {
    // Check if it's a valid JSON string or an already parsed object
    if (typeof str === 'object') return true;
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

// Make the JSON Tree Node component available in the template
const jsonTreeNodeComponentRef = computed(() => jsonTreeNodeComponent);

const parseJson = (jsonStr: string): any => {
  try {
    if (typeof jsonStr === 'object') return jsonStr;
    
    // Clean JSON string by removing comments before parsing
    const cleanedJsonStr = removeJsonComments(jsonStr);
    return JSON.parse(cleanedJsonStr);
  } catch (e) {
    console.error('JSON parse error:', e);
    return { error: "Invalid JSON" };
  }
};

// Function to remove comments from JSON strings
const removeJsonComments = (jsonString: string): string => {
  if (!jsonString) return '';
  
  // Replace single-line comments (both // and /* */) with empty strings
  let cleanedString = jsonString
    .replace(/\/\/.*$/gm, '') // Remove single-line comments starting with //
    .replace(/\/\*[\s\S]*?\*\//gm, ''); // Remove multi-line comments /* */
    
  // Fix trailing commas that might be left after removing comments
  cleanedString = cleanedString
    .replace(/,(\s*[\]}])/g, '$1');
    
  return cleanedString;
};

const isObject = (value: any): boolean => {
  return value !== null && (typeof value === 'object');
};

const getJsonNodeType = (value: any): string => {
  return Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`;
};

const getJsonValueClass = (value: any): string => {
  if (typeof value === 'string') return 'json-string';
  if (typeof value === 'number') return 'json-number';
  if (typeof value === 'boolean') return 'json-boolean';
  if (value === null) return 'json-null';
  return '';
};

const formatJsonValue = (value: any): string => {
  if (typeof value === 'string') return `"${value}"`;
  if (value === null) return 'null';
  return String(value);
};

const toggleJsonNode = (nodeId: string): void => {
  const node = document.getElementById(nodeId);
  if (node) {
    const isVisible = node.style.display !== 'none';
    node.style.display = isVisible ? 'none' : 'block';
    
    // Change toggle icon
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = isVisible ? '?' : '��';
    }
  }
};

const expandAllJson = (jsonStr: string): void => {
  const jsonTree = document.querySelectorAll('.json-tree-children');
  jsonTree.forEach(node => {
    (node as HTMLElement).style.display = 'block';
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = '��';
    }
  });
};

const collapseAllJson = (jsonStr: string): void => {
  const jsonTree = document.querySelectorAll('.json-tree-children');
  jsonTree.forEach(node => {
    (node as HTMLElement).style.display = 'none';
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = '?';
    }
  });
};

// Parse Qwen API result from log
const parseQwenResult = (log: string): void => {
  try {
    // ƥ��json���� - ��```json��```֮����������?    let jsonStr = log;
    
    if (log.includes('```json')) {
      // �������Markdown�������? ��ȡ���е�JSON
      const jsonMatch = log.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        jsonStr = jsonMatch[1].trim();
      }
    } else if (log.includes('{') && log.includes('}')) {
      // ����ֱ�Ӵ���־����ȡJSON����
      const startPos = log.indexOf('{');
      const endPos = log.lastIndexOf('}') + 1;
      if (startPos !== -1 && endPos !== -1) {
        jsonStr = log.substring(startPos, endPos);
      }
    }

    // ����JSON
    const parsedData = JSON.parse(jsonStr);
    
    // ��֤������qwenResults
    if (parsedData && (
      parsedData.detections || 
      parsedData.description || 
      parsedData.high_risk_events || 
      parsedData.low_risk_events
    )) {
      // ��������qwenResults��ʱ���?      qwenResults.value = parsedData;
      lastUpdated.value = new Date(); // ����ʱ���?      console.log('Updated Qwen API results:', qwenResults.value, 'ʱ��:', formatUpdateTime(lastUpdated.value));
      
      // ���������¼���ȷ����������ˢ��
      setTimeout(() => {
        // ǿ��Vue����DOM
        if (qwenResults.value) {
          const temp = {...qwenResults.value};
          qwenResults.value = temp;
        }
      }, 0);
    }
  } catch (error) {
    console.error('Failed to parse Qwen API result:', error);
  }
};

// Add a function to manually add logs for demonstration
const addApiLog = (log: string) => {
  // ����������ʱ����ʹ���ͷ��ˢ�£�ȷ��UIʼ�շ�ӳ����״̬
  lastUpdated.value = new Date();
  
  try {
    // �ȴ��������ĳɹ�/������Ϣ��ȷ��UI״̬��������
    if (log.includes('INFO:yolo_video_processor:Qwen API���سɹ�')) {
      console.log('��⵽Qwen API���سɹ�����������UI��ʾ');
      
      // ǿ����������ʱ����͸���״�?      lastUpdated.value = new Date();
      isHeaderUpdating.value = true;
      
      // ��������н�������������Ӽ���ָʾ
      if (qwenResults.value) {
        // ��¡��ǰ���������?���ڸ���"ָʾ
        const updatedResults = { ...qwenResults.value };
        updatedResults.description = updatedResults.description + '\n\n�����ڸ������·������?..��';
        qwenResults.value = updatedResults;
      }
      
      // ��2�������ͷ������״�?      setTimeout(() => {
        isHeaderUpdating.value = false;
      }, 2000);
    }

    // ����Qwen API�������?    if (log.includes('INFO:yolo_video_processor:Qwen API�������?')) {
      console.log('��⵽Qwen API���������׼������?);
      
      // ��ȡ�������?      const resultPrefix = 'INFO:yolo_video_processor:Qwen API�������? ';
      let jsonText = log.substring(log.indexOf(resultPrefix) + resultPrefix.length);
      
      // ������Ϣ
      console.log(`��ȡ�Ľ���ı�����? ${jsonText.length} �ַ�`);
      console.log(`����ı��?00���ַ�: ${jsonText.substring(0, 200)}`);
      
      try {
        // Ԥ���� - �������ܴ���Markdown��ʽ��JSON
        
        // �������Markdown����飬��ȡJSON
        if (jsonText.includes('```json')) {
          const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch && jsonMatch[1]) {
            console.log('��Markdown�������ȡJSON');
            jsonText = jsonMatch[1].trim();
          }
        } 
        // ������ȡJSON��������ı��а��������ǰ׺���׺��?        else if (jsonText.includes('{') && jsonText.includes('}')) {
          console.log('������ȡJSON����');
          const startPos = jsonText.indexOf('{');
          const endPos = jsonText.lastIndexOf('}') + 1;
          if (startPos !== -1 && endPos !== -1) {
            jsonText = jsonText.substring(startPos, endPos);
          }
        }
        
        // ����Python��ʽ��JSON
        jsonText = jsonText
          .replace(/'/g, '"')     // ��������ת��Ϊ˫����
          .replace(/True/g, 'true')   // Python��TrueתJS��true
          .replace(/False/g, 'false') // Python��FalseתJS��false
          .replace(/None/g, 'null');  // Python��NoneתJS��null
        
        // ���Խ���JSON
        let parsedJson;
        try {
          console.log('���Խ���JSON:', jsonText.substring(0, 100) + '...');
          parsedJson = JSON.parse(jsonText);
          console.log('�ɹ�����JSON�ṹ:', parsedJson);
        } catch (jsonError) {
          console.error('����JSONʧ��:', jsonError);
          console.log('���Խ��и��ϸ��JSON����');
          
          // ���Ը�ǿ���޸���ɾ������ת����Ų��ҵ���Ч��JSON����
          try {
            // ɾ�����ܸ��ŵ�ת���ַ�
            const cleanedText = jsonText.replace(/\\n/g, ' ').replace(/\\"/g, '"').replace(/\\\\/g, '\\');
            
            // Ѱ����Ч��JSON��ʼ�ͽ���
            const validStartPos = cleanedText.indexOf('{');
            const validEndPos = cleanedText.lastIndexOf('}') + 1;
            
            if (validStartPos !== -1 && validEndPos !== -1 && validEndPos > validStartPos) {
              const validJson = cleanedText.substring(validStartPos, validEndPos);
              console.log('��ȡǱ����ЧJSON:', validJson.substring(0, 100) + '...');
              parsedJson = JSON.parse(validJson);
              console.log('�޸��ɹ�����������ЧJSON');
            } else {
              throw new Error('�޷��ҵ���Ч��JSON����');
            }
          } catch (deepError) {
            console.error('����޸�JSONʧ��:', deepError);
            
            // ������г��Զ�ʧ�ܣ�����һ�������Ľ������ʹ��ԭʼ�ı���Ϊ����
            parsedJson = {
              detections: [],
              description: jsonText.includes('����') ? 
                jsonText.replace(/^INFO:.*?:/, '') : // ɾ����־ǰ׺
                '���յ��������������ʽ�����?,
              high_risk_events: [],
              low_risk_events: []
            };
            console.log('����Ĭ�Ͻṹ��ʹ��ԭʼ�ı���Ϊ����');
          }
        }
        
        // �����ṹ���Ľ������ȷ�������ֶζ���Ĭ���?        const qwenResultData = {
          detections: parsedJson.detections || [],
          description: parsedJson.description || parsedJson.safety_analysis || (typeof parsedJson === 'string' ? parsedJson : '�����������?),
          high_risk_events: parsedJson.high_risk_events || [],
          low_risk_events: parsedJson.low_risk_events || []
        };
        
        // ����״̬
        qwenResults.value = qwenResultData;
        lastUpdated.value = new Date();
        isHeaderUpdating.value = true;
        
        setTimeout(() => {
          isHeaderUpdating.value = false;
        }, 2000);
        
        console.log('ʵʱ����Qwen API���?', qwenResults.value);
        
        // ��������ӵ����?        apiLogs.value.unshift(log);
        
        // ���¼����?        updateDetectionResultsFromResponse(parsedJson);
      } catch (e) {
        console.error('����Qwen API�������ʱ����?', e);
        
        // ����ʱ��Ȼ����UI״̬����ʾ������Ϣ���ṩ�����õ�����
        qwenResults.value = {
          detections: [],
          description: `����Qwen API���ʱ������ԭ������Ǹ�ʽ������Ԥ�ڡ���������ʾԭʼ����:\n\n${jsonText.substring(0, 500)}${jsonText.length > 500 ? '...' : ''}`,
          high_risk_events: [],
          low_risk_events: []
        };
        
        lastUpdated.value = new Date();
        isHeaderUpdating.value = true;
        setTimeout(() => { isHeaderUpdating.value = false; }, 2000);
        
        // ����־���ӵ�API��־�б�
        apiLogs.value.unshift(log);
      }
    } else {
      // ����������־
      apiLogs.value.unshift(log);
    }
  } catch (error) {
    // �����������������е��κδ���
    console.error('����API��־ʧ��:', error);
    apiLogs.value.unshift(log);
  }
  
  // ֻ�������µ�5����־
  if (apiLogs.value.length > 5) {
    apiLogs.value = apiLogs.value.slice(0, 5);
  }
};

// Function to update detection results from API response
const updateDetectionResultsFromResponse = (response: any) => {
  if (!detectionResults.value) {
    detectionResults.value = {
      objects: [],
      summary: {},
      description: '',
      input_width: 0,
      input_height: 0,
      high_risk_events: [],
      low_risk_events: []
    };
  }
  
  // Update safety analysis
  if (response.image_overall_safety_analysis_and_control_recommendations) {
    detectionResults.value.image_overall_safety_analysis_and_control_recommendations = 
      response.image_overall_safety_analysis_and_control_recommendations;
  } else if (response.description) {
    detectionResults.value.description = response.description;
  }
  
  // Update detection objects
  if (response.detections && response.detections.length > 0) {
    detectionResults.value.objects = response.detections;
  }
  
  // Add risk events
  if (response.high_risk_events && response.high_risk_events.length > 0) {
    detectionResults.value.high_risk_events = response.high_risk_events;
  }
  
  if (response.low_risk_events && response.low_risk_events.length > 0) {
    detectionResults.value.low_risk_events = response.low_risk_events;
  }
  
  // Try to extract objects from raw_json if available
  if (response.raw_json && !detectionResults.value.objects.length) {
    try {
      const rawData = typeof response.raw_json === 'string' ? 
        parseJson(response.raw_json) : response.raw_json;
      
      let objects: any[] = [];
      
      // Extract different object types
      if (rawData.person && Array.isArray(rawData.person)) {
        objects = [...objects, ...rawData.person.map((p: any) => ({...p, category: '��Ա'}))];
      }
      
      if (rawData.vehicle && Array.isArray(rawData.vehicle)) {
        objects = [...objects, ...rawData.vehicle.map((v: any) => ({...v, category: '����'}))];
      }
      
      if (rawData.construction_machinery && Array.isArray(rawData.construction_machinery)) {
        objects = [...objects, ...rawData.construction_machinery.map((m: any) => ({...m, category: '��е'}))];
      }
      
      if (objects.length > 0) {
        detectionResults.value.objects = objects;
      }
    } catch (e) {
      console.warn('��raw_json��ȡ����ʧ��:', e);
    }
  }
};

// ��ʾ��ȫ���鵽�Ҳ����?const showSafetyRecommendation = (text: string) => {
  if (!text) return;
  
  // ȷ���ı���ʽ��ȷ�����û�б���������?  let formattedText = text;
  if (!formattedText.includes('### ͼ�����尲ȫ���շ�����ܿؽ���?) && 
      !formattedText.includes('### ��ȫ�����뽨��')) {
    formattedText = '### ��ȫ�����뽨��\n\n' + formattedText;
  }
  
  // ���û�м����������һ��
  if (!detectionResults.value) {
    detectionResults.value = {
      objects: [],
      summary: {},
      description: formattedText,
      input_width: 0,
      input_height: 0,
      high_risk_events: [],
      low_risk_events: [],
      image_overall_safety_analysis_and_control_recommendations: formattedText
    };
  } else {
    // ����������еļ����
    detectionResults.value.description = formattedText;
    detectionResults.value.image_overall_safety_analysis_and_control_recommendations = formattedText;
    
    // ��������ȫ�������?    setTimeout(() => {
      const safetyPanel = document.querySelector('.safety-analysis-container');
      if (safetyPanel) {
        safetyPanel.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }
  
  // ���canvas�ϵļ�����Ϊ��������ֻ��ע��ȫ����
  if (showDetectionBoxes.value === false && detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// ��ǿ��ʽ����ȫ�����ı�������רע����ʾ��ȫ�����͹ܿؽ���
const formatSafetyAnalysis = (content: string): string => {
  if (!content) return '';
  
  // ���������ı�
  let cleanedContent = content
    .replace(/\\n/g, '\n')  // ����JSON�е�ת�廻�з�
    .replace(/\\"/g, '"');  // ����JSON�е�ת������
  
  // ͻ����ʾ�ؼ���
  const keywordsHighlight = [
    { pattern: /�߷���/g, replacement: '<span class="highlight-high-risk">�߷���</span>' },
    { pattern: /�з���/g, replacement: '<span class="highlight-medium-risk">�з���</span>' },
    { pattern: /�ͷ���/g, replacement: '<span class="highlight-low-risk">�ͷ���</span>' },
    { pattern: /��ȫ����/g, replacement: '<span class="highlight-warning">��ȫ����</span>' },
    { pattern: /����|Σ��/g, replacement: '<span class="highlight-warning">$&</span>' },
    { pattern: /����|Ӧ��|��Ҫ|����/g, replacement: '<span class="highlight-important">$&</span>' },
    { pattern: /����(.{1,10}?)����/g, replacement: '����<span class="highlight-warning">$1</span>����' },
    { pattern: /ע��(.{1,10}?)��ȫ/g, replacement: '<span class="highlight-important">ע��$1��ȫ</span>' }
  ];
  
  keywordsHighlight.forEach(item => {
    cleanedContent = cleanedContent.replace(item.pattern, item.replacement);
  });
  
  // ���⴦����ȫ��������
  cleanedContent = cleanedContent.replace(
    /### ͼ�����尲ȫ���շ�����ܿؽ���?g, 
    '<h3 class="safety-analysis-heading">ͼ�����尲ȫ���շ�����ܿؽ���?/h3>'
  );
  
  // �����µļ򻯱���
  cleanedContent = cleanedContent.replace(
    /### ��ȫ�����뽨��/g, 
    '<h3 class="safety-analysis-heading">��ȫ�����뽨��</h3>'
  );
  
  // ��������Markdown���?  let formatted = cleanedContent
    .replace(/\n/g, '<br>')                         // ����
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // �Ӵ�
    .replace(/\*(.*?)\*/g, '<em>$1</em>')           // б��
    .replace(/#{3}([^<]*?)(?:\n|$)/g, '<h3>$1</h3>')   // h3���� (�ų��Ѵ������������?
    .replace(/#{2}(.*?)(?:\n|$)/g, '<h2>$1</h2>')   // h2����
    .replace(/#{1}(.*?)(?:\n|$)/g, '<h1>$1</h1>')   // h1����
    .replace(/- (.*?)(?:\n|$)/g, '<li>$1</li>')     // �����б�
    .replace(/<li>(.*?)<\/li>/g, '<ul><li>$1</li></ul>') // ���б����װ��ul��
    .replace(/<\/ul><ul>/g, '');                    // �ϲ����ڵ�ul��ǩ
  
  // ���������б��������������γ������б�
  formatted = formatted.replace(/(\d+)\.\s+(.*?)(?:<br>|$)/g, '<ol start="$1"><li>$2</li></ol>');
  formatted = formatted.replace(/<\/ol><ol start="\d+">/g, '');
  
  // ��ǿ�԰�ȫ�����ͽ�������?  formatted = formatted
    .replace(/<strong>(.*?����.*?)<\/strong>/g, '<strong class="safety-hazard">$1</strong>')
    .replace(/<strong>(.*?����.*?)<\/strong>/g, '<strong class="safety-recommendation">$1</strong>')
    .replace(/<h3>(.*?��ȫ.*?)<\/h3>/g, '<h3 class="safety-section">$1</h3>')
    .replace(/<h3>(.*?����.*?)<\/h3>/g, '<h3 class="hazard-section">$1</h3>')
    .replace(/<h3>(.*?����.*?)<\/h3>/g, '<h3 class="recommendation-section">$1</h3>');
    
  return formatted;
};

// ��ȡ��ȫ����ĺ������?const extractSafetyRecommendation = (log: string): string => {
  try {
    // ֱ�Ӽ���Ƿ������ȫ�����½ڱ���
    if (typeof log === 'string' && log.includes('### ͼ�����尲ȫ���շ�����ܿؽ���?)) {
      const startIndex = log.indexOf('### ͼ�����尲ȫ���շ�����ܿؽ���?);
      // �ҳ��½ڽ���λ�ã�ͨ������һ����������ݽ�����?      let endIndex = log.length;
      const nextHeadingMatch = log.substring(startIndex + 1).match(/\n#{2,3}\s+/);
      if (nextHeadingMatch && nextHeadingMatch.index) {
        endIndex = startIndex + 1 + nextHeadingMatch.index;
      }
      
      return log.substring(startIndex, endIndex).trim();
    }
    
    // ���Խ���JSON��ȷ��������ע�ͣ�
    const cleanedLog = typeof log === 'string' ? removeJsonComments(log) : log;
    const parsed = typeof cleanedLog === 'string' ? parseJson(cleanedLog) : cleanedLog;
    let safetyContent = '';
    
    // ���ȳ��Դӳ����ֶλ�ȡ
    const possibleFields = [
      'description',
      'safety_analysis',
      'recommendations',
      'image_overall_safety_analysis_and_control_recommendations',
      'safety_recommendations',
      'analysis'
    ];
    
    for (const field of possibleFields) {
      if (parsed[field]) {
        safetyContent = parsed[field];
        // ������ݲ��������⣬�����������尲ȫ�����ֶΣ����ӱ���?        if (!safetyContent.includes('### ͼ�����尲ȫ���շ�����ܿؽ���?)) {
          safetyContent = '### ͼ�����尲ȫ���շ�����ܿؽ���\n\n' + safetyContent;
        }
        return safetyContent;
      }
    }
    
    // Ȼ���Դ�raw_json�в���
    if (parsed.raw_json) {
      try {
        const rawJsonCleaned = typeof parsed.raw_json === 'string' ? 
          removeJsonComments(parsed.raw_json) : JSON.stringify(parsed.raw_json);
        const rawData = typeof rawJsonCleaned === 'string' ? 
          JSON.parse(rawJsonCleaned) : rawJsonCleaned;
        
        for (const field of possibleFields) {
          if (rawData[field]) {
            safetyContent = rawData[field];
            // ������ݲ��������⣬���ӱ���?            if (!safetyContent.includes('### ͼ�����尲ȫ���շ�����ܿؽ���?)) {
              safetyContent = '### ͼ�����尲ȫ���շ�����ܿؽ���\n\n' + safetyContent;
            }
            return safetyContent;
          }
        }
      } catch (e) {
        console.error('����raw_jsonʧ��', e);
      }
    }
    
    // ���Դ�ԭʼ�ַ�������ȡ��ȫ��������
    if (typeof log === 'string') {
      // ���ȳ��Բ��Ұ�ȫ�����͹ܿؽ����½�
      if (log.includes('��ȫ����ܽ�?) || log.includes('�ܿش�ʩ������')) {
        // ������ȡ�����½�
        const safetySection = log.substring(log.indexOf('ͼ�����尲ȫ���շ�����ܿؽ���?));
        if (safetySection.length > 50) {  // ȷ���������㹻����
          return safetySection;
        }
      }
      
      // Ȼ����ʹ���������ʽƥ��?      const safetyPatterns = [
        /(?:��ȫ����|��ȫ����|�ܿؽ���|���շ���|ͼ�����尲ȫ[����]*����[����]�ܿؽ���)[:��]([\s\S]+?)(?:\n\n|\Z)/i,
        /(?:��ȫ����ܽ�|�ܿش�ʩ������)([\s\S]+?)(?:\n\n|\Z)/i
      ];
      
      for (const pattern of safetyPatterns) {
        const match = log.match(pattern);
        if (match && match[1]) {
          let extractedContent = match[1].trim();
          // ���ӱ��⣬���������?          if (!extractedContent.includes('ͼ�����尲ȫ���շ�����ܿؽ���?)) {
            return '### ͼ�����尲ȫ���շ�����ܿؽ���\n\n' + extractedContent;
          }
          return extractedContent;
        }
      }
      
      // ����鳤�ı�����?      for (const key in parsed) {
        if (typeof parsed[key] === 'string' && 
            /��ȫ|����|����|����|��ʩ|analysis|safety|risk|recommendation/i.test(parsed[key]) &&
            parsed[key].length > 100) {
          safetyContent = parsed[key];
          // ���ӱ��⣬���������?          if (!safetyContent.includes('### ͼ�����尲ȫ���շ�����ܿؽ���?)) {
            safetyContent = '### ͼ�����尲ȫ���շ�����ܿؽ���\n\n' + safetyContent;
          }
          return safetyContent;
        }
      }
    }
    
    return '';
  } catch (e) {
    console.error('��ȡ��ȫ����ʧ��', e);
    
    // �������򵥵��������ʽ���?    if (typeof log === 'string') {
      const simplePattern = /(?:ͼ�����尲ȫ���շ�����ܿؽ���[\s\S]*)/i;
      const match = log.match(simplePattern);
      if (match && match[0]) {
        return match[0].trim();
      }
    }
    return '';
  }
};

// --- Data structures for detection options (from App.vue) ---
interface DetectionOption {
  id: string;
  label: string;
  checked: boolean;
}

interface DetectionCategory {
  id: string;
  label: string;
  expanded: boolean;
  options: DetectionOption[];
}

const detectionOptions = ref<DetectionCategory[]>([
  {
    id: 'person',
    label: '��Ա���?,
    expanded: true,
    options: [
      { id: 'person_count', label: '��Աͳ��', checked: false },
      { id: 'person_wear', label: '��Ա����', checked: false },
      { id: 'person_status', label: '��Ա״̬', checked: false },
      { id: 'hoisting_personnel', label: '��װ��ҵ��Ա', checked: false },
      { id: 'high_altitude_personnel', label: '�߿���ҵ��Ա', checked: false }
    ]
  },
  {
    id: 'machine',
    label: '��е���?,
    expanded: true,
    options: [
      { id: 'vehicle_count', label: '����ͳ��', checked: false },
      { id: 'vehicle_status', label: '����״̬', checked: false },
      { id: 'equipment_status', label: '��е����״̬', checked: false },
      { id: 'hoisting_status', label: '��װ״̬', checked: false }
    ]
  },
  {
    id: 'material',
    label: '���ϼ��?,
    expanded: true,
    options: [
      { id: 'subgrade_monitoring', label: '·�����?, checked: false },
      { id: 'slope_monitoring', label: '���¼��?, checked: false },
      { id: 'pavement_monitoring', label: '·����', checked: false }
    ]
  },
  {
    id: 'regulation',
    label: '������',
    expanded: true,
    options: [
      { id: 'operation_area_protection', label: '��ҵ������', checked: false }
    ]
  },
  {
    id: 'environment',
    label: '�������?,
    expanded: true,
    options: [
      { id: 'fire_detection', label: '������', checked: false },
      { id: 'smoke_detection', label: '�������?, checked: false }
    ]
  }
]);

const riskCategories = [
  {
    id: 'all',
    label: 'ȫ�����ռ��?,
    options: ['person_count', 'person_wear', 'person_status', 'hoisting_personnel', 'high_altitude_personnel',
              'vehicle_count', 'vehicle_status', 'equipment_status', 'hoisting_status',
              'subgrade_monitoring', 'slope_monitoring', 'pavement_monitoring',
              'operation_area_protection',
              'fire_detection', 'smoke_detection']
  },
  { id: 'general', label: 'ͨ�÷��ռ��?, options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection']},
  { id: 'subgrade', label: '·�����ռ��?, options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'subgrade_monitoring', 'slope_monitoring']},
  { id: 'pavement', label: '·����ռ��', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'pavement_monitoring']},
  { id: 'bridge', label: '�������ռ��?, options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'hoisting_personnel', 'high_altitude_personnel', 'hoisting_status', 'operation_area_protection']}
];

const selectedCategory = ref('all');

const filteredDetectionOptions = computed(() => {
  return detectionOptions.value;
});

const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId;
  const category = riskCategories.find(cat => cat.id === categoryId);
  if (category) {
    detectionOptions.value.forEach(detCat => {
      detCat.options.forEach(opt => {
        opt.checked = category.options.includes(opt.id);
      });
    });

    // ������ʼ������
    if (!isInitializing.value) {
      initializeMonitoring();
    }
  }
};

const toggleCategory = (categoryId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    category.expanded = !category.expanded;
  }
};

const toggleOption = (categoryId: string, optionId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    const option = category.options.find(opt => opt.id === optionId);
    if (option) {
      option.checked = !option.checked;
    }
  }
};

const selectedOptions = computed(() => {
  const options: string[] = [];
  detectionOptions.value.forEach(category => {
    category.options.forEach(option => {
      if (option.checked) {
        options.push(option.id);
      }
    });
  });
  return options;
});

// --- Video and Canvas State ---
// ����Ĭ����ƵԴ�������Ǳ����ļ�������URL
const videoSource = ref("./1747124126409.mp4");

// ���������������߼�����̬������ƵԴ
// ����������ж�ȡ������û�ѡ������

// ����Ƿ�ʹ��YOLO���?const useYoloDetection = ref(false);
const videoPlayerElement = ref<HTMLVideoElement | null>(null);
const detectionCanvasElement = ref<HTMLCanvasElement | null>(null);
const currentFramePreview = ref<string>('');

// --- Detection State & Results ---
const isLoading = ref(false);
const error = ref('');
const detectionResults = ref<DetectionResult | null>(null);
const showLoadingMessage = ref(false); // ����״̬���ڿ��Ƽ�����ʾ����ʾ

// --- Model Selection (from App.vue) ---
const currentModel = ref({
  id: 'lite',
  name: '���� Lite',
  description: '��������ʶ��'
});
const models = [
  { id: 'pro', name: '���� Pro', description: 'ȫ���ͽ������ʶ��?, icon: 'model-pro-icon' },
  { id: 'lite', name: '���� Lite', description: '��������ʶ��', icon: 'model-lite-icon' }
];
const isModelDropdownOpen = ref(false);

const handleModelChange = (modelId: string) => {
  // �����ǰ��YOLO���ģʽ���������л�ģ��?  if (useYoloDetection.value) {
    console.log('YOLO���ģʽ�²����л�ģ��?);
    return;
  }

  const selectedModel = models.find(m => m.id === modelId);
  if (selectedModel) {
    currentModel.value = selectedModel;
  }
  // If detection is ongoing or video is playing, might need to re-trigger or notify
};

// --- Detection Box Toggles (from App.vue) ---
const showDetectionBoxes = ref(true);
const showHighRiskBoxes = ref(true);
const showLowRiskBoxes = ref(true);
const showPersonBoxes = ref(false); // Default to false as per App.vue logic
const showVehicleBoxes = ref(false);
const showMachineBoxes = ref(false);

const toggleDetectionBoxes = () => {
  showDetectionBoxes.value = !showDetectionBoxes.value;

  // ����Ƿ��д������ͼ��
  if (detectionResults.value && (detectionResults.value.processed_url || detectionResults.value.visualized_image_url)) {
    const processedUrl = detectionResults.value.visualized_image_url || detectionResults.value.processed_url;
    if (processedUrl) {
      if (showDetectionBoxes.value) {
        // ����û�Ҫ��ʾ���򣬲����д������ͼ�񣬼��ش������ͼ��?        const processedImage = new Image();
        const cacheBuster = new Date().getTime();
        processedImage.onload = () => {
          if (detectionCanvasElement.value) {
            const ctx = detectionCanvasElement.value.getContext('2d');
            if (ctx) {
              ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
              ctx.drawImage(
                processedImage,
                0, 0,
                detectionCanvasElement.value.width,
                detectionCanvasElement.value.height
              );
            }
          }
        };
        processedImage.onerror = () => {
          console.error('���ش������ͼ��ʧ��?);
          clearCanvas();
        };
        processedImage.src = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
      } else {
        // ����û�Ҫ���ؼ����������
        clearCanvas();
      }
      return;
    }
  }

  // ���û�д������ͼ��ʹ��ԭʼ�����߼�
  if (showDetectionBoxes.value && detectionResults.value) {
    drawDetectionBoxes(); // ʹ�ó������?  } else {
    clearCanvas();
  }
};

// Placeholder: toggleSpecificBoxes - if needed, copy from App.vue and adapt

const initializeCanvas = () => {
  if (videoPlayerElement.value && detectionCanvasElement.value) {
    const video = videoPlayerElement.value;
    const canvas = detectionCanvasElement.value;
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    console.log(`Canvas initialized to video dimensions: ${canvas.width}x${canvas.height}`);
  }
};

const clearCanvas = () => {
  if (detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// Replace the captureFrame function with this improved version
const captureFrame = (): string | null => {
  if (!videoPlayerElement.value) return null;
  const video = videoPlayerElement.value;
  
  try {
    // Use offscreen canvas to capture video frame
    const canvas = document.createElement('canvas');
    // Use actual video dimensions to maintain quality
    canvas.width = video.videoWidth || 640;
    canvas.height = video.videoHeight || 360;
    
    // Ensure video dimensions are valid
    if (canvas.width <= 0 || canvas.height <= 0) {
      console.error('��Ч����Ƶ�ߴ�:', canvas.width, canvas.height);
      return null;
    }
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('�޷���ȡcanvas������');
      return null;
    }
    
    // Enable image smoothing for better quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    // Draw video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // Use moderate JPEG quality (0.8) for better balance between quality and file size
    return canvas.toDataURL('image/jpeg', 0.8);
  } catch (error) {
    console.error('������Ƶ֡ʱ����:', error);
    return null;
  }
};

const generatePrompt = (): string => { // Copied from App.vue, might need video-specific adjustments
  const promptMap: Record<string, string> = {
    'person_count': '��Ա��������ȫ״̬',
    'person_wear': '��Ա��ȫװ����������ȫñ/���ⱳ�ģ�',
    'person_status': '��Ա������̬���գ������ǲ���ȫ���ƣ�',
    'hoisting_personnel': '��װ����Ա��ȫ�������·�/��ת�뾶�ڣ�',
    'high_altitude_personnel': '�߿���ҵ��Ա׹�����?,
    'vehicle_count': '�����ֲ��밲ȫ״̬',
    'vehicle_status': '������ʻ/ͣ�ŷ���',
    'equipment_status': 'ʩ����е���з���',
    'hoisting_status': '��װ��ҵ��ȫ���ȶ���/������/�źţ�',
    'subgrade_monitoring': '·�����գ�����/�ѷ�/������',
    'slope_monitoring': '���·��գ�����/����/��ʯ��',
    'pavement_monitoring': '·����գ��ѷ�?����/��ˮ��',
    'operation_area_protection': '��ҵ��������ʩ������/��ʾ��',
    'fire_detection': '���ַ���',
    'smoke_detection': '��������'
  };
  const selectedPrompts: string[] = [];
  detectionOptions.value.forEach(category => {
    category.options.forEach(option => {
      if (option.checked && promptMap[option.id]) {
        selectedPrompts.push(promptMap[option.id]);
      }
    });
  });

  if (selectedPrompts.length === 0) {
    return `������Ƶ֡�еİ�ȫ������
1. ��ע��Ա������װ����Σ����Ϊ���������ͻ�е�豸
2. �����յȼ�����/��/�ͣ����ಢ��Ҫ����ÿ������
3. �����Ҫ�����ṩ��̡�����Ĺܿؽ���?
��Ҫ���������������Ϣ��ʹ��?### ��ȫ�����뽨��"��Ϊ���⡣`;
  }
  return `������Ƶ֡�еİ�ȫ�������ص��ע��?${selectedPrompts.map((prompt, index) => `${index + 1}. ${prompt}`).join('\n')}

ֱ�������?1. ������������ļ�Ҫ���������յȼ�?2. ����ԵĹܿؽ��飨��ʵ���ɲ�����?
��Ҫ���������������Ϣ��ʹ��?### ��ȫ�����뽨��"��Ϊ���⣬ʹ��Markdown��ʽ��`;
};

// WebSocket����
let wsConnection: WebSocket | null = null;
let frameCounter = 0; // ֡������������ȷ��ÿ5֡����һ��API

// ʹ��YOLO������·��ṹ���ռ��
const performYoloDetection = async (frameDataUrl: string): Promise<DetectionResult> => {
  console.log('ʹ��YOLO���ģʽ����·��ṹ���ռ��?);

  // ����Ƿ�ʹ��WebSocket����ʵʱ����
  if (useYoloDetection.value) {
    return await processFrameWithWebSocket(frameDataUrl);
  } else {
    // ʹ��HTTP API���д�����ԭ���߼���
    // ����FormData�������ڷ��͵����?    const formData = new FormData();
    formData.append('image', frameDataUrl);

    // ��ѡ�е�ѡ�����ӵ�FormData
    selectedOptions.value.forEach(option => {
      formData.append('options[]', option);
    });

    // ������ʾ��
    const customPrompt = generatePrompt();
    formData.append('prompt', customPrompt);

    // ʹ��·��ṹ���ռ���API�˵�
    const apiEndpoint = '/online-monitor-road-defects';
    console.log(`ʹ��·��ṹ���ռ��API: ${apiEndpoint}`);

    // �������󵽺��?    const response = await fetch(`http://localhost:8000${apiEndpoint}`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // ������Ӧ
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || '���ʧ�ܣ�������?);
    }

    // ��YOLO�����ת��ΪDetectionResult��ʽ
    const results: DetectionResult = {
      objects: data.defects.map((defect: any) => ({
        category: defect.category,
        confidence: defect.score,
        bbox: defect.bbox,
        risk_level: 'medium',
        label: defect.category
      })),
      summary: {},
      description: data.qwen_response,
      input_width: data.image_width,
      input_height: data.image_height,
      high_risk_events: [],
      low_risk_events: [],
      processed_url: data.result_url,
      visualized_image_url: data.result_url
    };

    // ����ÿ����������
    results.summary = results.objects.reduce((acc: Record<string, number>, obj: any) => {
      const category = obj.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    // ���ݼ�������ɷ����¼�?    data.defects.forEach((defect: any) => {
      const isHighRisk = defect.score > 0.8;
      const event = {
        category: defect.category,
        event: `���?{defect.category}`,
        risk_level: isHighRisk ? 'high' : 'low',
        confidence: defect.score,
        bbox_2d: defect.bbox
      };

      if (isHighRisk) {
        results.high_risk_events.push(event);
      } else {
        results.low_risk_events.push(event);
      }
    });

    return results;
  }
};

// ʹ��WebSocket������Ƶ֡
const processFrameWithWebSocket = (frameDataUrl: string): Promise<DetectionResult> => {
  return new Promise((resolve, reject) => {
    // ���WebSocket���Ӳ����ڻ��ѹرգ�����������
    if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
      console.log('�����µ�WebSocket����');
      wsConnection = new WebSocket('ws://localhost:8000/ws/yolo-video-process');

      wsConnection.onopen = () => {
        console.log('WebSocket�����ѽ���');
        // ���ӽ������͵�ǰ֡��ģ������
        sendFrameToWebSocket(frameDataUrl);
      };

      wsConnection.onerror = (error) => {
        console.error('WebSocket����:', error);
        reject(new Error('WebSocket���Ӵ���'));
      };

      wsConnection.onclose = () => {
        console.log('WebSocket�����ѹر�');
        wsConnection = null;
      };

      // �����ӷ��������յ���Ϣ
      wsConnection.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.error) {
            console.error('���������ش���:', data.error);
            reject(new Error(data.error));
            return;
          }

          // ����Ƿ��з����������
          if (data.qwen_results || data.analysis_results) {
            // ����Qwen�������?            const analysisData = data.qwen_results || data.analysis_results;
            console.log('�յ�Qwen API�������������UI');
            
            // ����UI����
            addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
            addApiLog(`INFO:yolo_video_processor:Qwen API�������? ${JSON.stringify(analysisData)}`);
            
            // ����������������������������
            if (analysisData.description) {
              qwenResults.value = {
                detections: analysisData.detections || [],
                description: analysisData.description,
                high_risk_events: analysisData.high_risk_events || [],
                low_risk_events: analysisData.low_risk_events || []
              };
              lastUpdated.value = new Date();
            }
          }

          if (data.processed_frame) {
            // ȷ���������֡������������Data URL
            let processedFrameDataUrl = data.processed_frame;
            if (!processedFrameDataUrl.startsWith('data:image')) {
              // ���ֻ�յ��˴�Base64������ǰ׺
              processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
            }
            
            // ֻ�ڵ���ʱ�����־���������̨����
            if (Math.random() < 0.05) { // Only log approximately 5% of frames
              console.log('YOLO��Ƶ��������...');
            }
            
            // ��鲢��֤֡��ʱ�����Ч��
            // ���Գ���2��ľ�֡����ֹ��ʾ��ʱ���?            const currentTime = Date.now();
            const frameTimestamp = data.timestamp || currentTime;
            const frameAge = currentTime - frameTimestamp;
            
            if (frameAge > 2000) {
              console.log(`������ʱ��֡������: ${frameAge}ms`);
              return;
            }
            
            // ��canvas����ʾ�������֡�����ڿ��ӻ�YOLO�����?            if (detectionCanvasElement.value) {
              const processedImage = new Image();
              processedImage.onload = () => {
                const ctx = detectionCanvasElement.value!.getContext('2d');
                if (ctx) {
                  // ���canvas�ߴ��Ƿ�����Ƶƥ��
                  if (detectionCanvasElement.value!.width !== videoPlayerElement.value?.videoWidth ||
                      detectionCanvasElement.value!.height !== videoPlayerElement.value?.videoHeight) {
                    detectionCanvasElement.value!.width = videoPlayerElement.value?.videoWidth || 640;
                    detectionCanvasElement.value!.height = videoPlayerElement.value?.videoHeight || 360;
                  }
                  
                  // �������?                  ctx.clearRect(0, 0, detectionCanvasElement.value!.width, detectionCanvasElement.value!.height);
                  
                  // ���ƴ������ͼ�񣨰���YOLO����
                  ctx.drawImage(
                    processedImage,
                    0, 0,
                    detectionCanvasElement.value!.width,
                    detectionCanvasElement.value!.height
                  );
                }
              };
              processedImage.onerror = (err) => {
                console.error('���ش������ͼ��ʧ��?', err);
              };
              processedImage.src = processedFrameDataUrl;
            }
            
            // �����֡������Qwen�������������û�������������ﴦ��?            if (data.qwen_analysis && !data.qwen_results) {
              addApiLog(`INFO:yolo_video_processor:Qwen API�������? ${JSON.stringify(data.qwen_analysis)}`);
              
              if (typeof data.qwen_analysis === 'object') {
                qwenResults.value = {
                  detections: data.qwen_analysis.detections || [],
                  description: data.qwen_analysis.description || '����������...',
                  high_risk_events: data.qwen_analysis.high_risk_events || [],
                  low_risk_events: data.qwen_analysis.low_risk_events || []
                };
                lastUpdated.value = new Date();
              }
            }
          }
        } catch (error) {
          console.error('����WebSocket��Ϣʱ����:', error);
        }
      };
    } else {
      // ��������Ѵ����Ҵ򿪣�ֱ�ӷ����?      sendFrameToWebSocket(frameDataUrl);
    }

    // ����֡��WebSocket
    function sendFrameToWebSocket(frameData: string) {
      if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify({
          frame: frameData,
          model: "yolo11n-seg.pt", // ָ��ʹ��yolo11n-seg.ptģ��
          config: {
            segmentation: true, // ���÷ָ�
            confidence: 0.25  // ���Ŷ���ֵ
          },
          timestamp: Date.now(), // ����ʱ�����׷���?          frameId: Math.random().toString(36).substring(2, 15) // ����Ψһ֡ID
        }));
      } else {
        reject(new Error('WebSocket����δ��'));
      }
    }
  });
};

// ʹ�ñ�׼API���м��?const performStandardDetection = async (frameDataUrl: string): Promise<DetectionResult> => {
  const modelId = currentModel.value.id === 'pro' ? 'qwen2.5-vl-72b-instruct' : 'qwen2.5-vl-7b-instruct';
  const customPrompt = generatePrompt();
  console.log('ʹ�ñ�׼���ģʽ��prompt:', customPrompt, 'model:', modelId);
  return await detectObjects(frameDataUrl, selectedOptions.value, customPrompt, modelId);
};

// ���������ͼ�����?const handleProcessedImage = (results: DetectionResult) => {
  // ����WebSocket���ص�YOLO��������?  if (results.yolo_processed_frame) {
    console.log('��⵽WebSocket�������֡��ֱ�����?);
    const processedImage = new Image();
    processedImage.onload = () => {
      // ����Ƶ�Ϸ����ƴ������ͼ��?      if (videoPlayerElement.value && detectionCanvasElement.value) {
        const ctx = detectionCanvasElement.value.getContext('2d');
        if (ctx) {
          // ȷ��canvas�ߴ�����Ƶƥ��
          if (detectionCanvasElement.value.width !== videoPlayerElement.value.videoWidth ||
              detectionCanvasElement.value.height !== videoPlayerElement.value.videoHeight) {
            detectionCanvasElement.value.width = videoPlayerElement.value.videoWidth;
            detectionCanvasElement.value.height = videoPlayerElement.value.videoHeight;
          }

          // �����ǰ����?          ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);

          // ���ƴ������ͼ��?          ctx.drawImage(
            processedImage,
            0, 0,
            detectionCanvasElement.value.width,
            detectionCanvasElement.value.height
          );

          // �����Ƽ�����Ϊ�������ͼ���Ѿ������˼����
          showDetectionBoxes.value = true;
        }
      }
    };
    processedImage.onerror = (err) => {
      console.error('����WebSocket�������֡ʧ��?', err);
      // ����ʧ��ʱʹ�ó������?      if (showDetectionBoxes.value) {
        drawDetectionBoxes();
      }
    };
    // ����ͼ��WebSocket���ص��Ѿ���������base64���ݣ�
    processedImage.src = results.yolo_processed_frame;
  }
  // ����HTTP API���ص�ͼ��URL
  else if (results.processed_url || results.visualized_image_url) {
    const processedUrl = results.visualized_image_url || results.processed_url;
    if (processedUrl) {
      console.log('��⵽�������ͼ��URL�����ش������ͼ��?', processedUrl);
      // ����ͼ��Ԫ�ز����ش������ͼ��?      const processedImage = new Image();
      const cacheBuster = new Date().getTime(); // ��ֹ����
      processedImage.onload = () => {
        // ����Ƶ�Ϸ����ƴ������ͼ��?        if (videoPlayerElement.value && detectionCanvasElement.value) {
          const ctx = detectionCanvasElement.value.getContext('2d');
          if (ctx) {
            // ȷ��canvas�ߴ�����Ƶƥ��
            if (detectionCanvasElement.value.width !== videoPlayerElement.value.videoWidth ||
                detectionCanvasElement.value.height !== videoPlayerElement.value.videoHeight) {
              detectionCanvasElement.value.width = videoPlayerElement.value.videoWidth;
              detectionCanvasElement.value.height = videoPlayerElement.value.videoHeight;
            }

            // �����ǰ����?            ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);

            // ���ƴ������ͼ��?            ctx.drawImage(
              processedImage,
              0, 0,
              detectionCanvasElement.value.width,
              detectionCanvasElement.value.height
            );

            // �����Ƽ�����Ϊ�������ͼ���Ѿ������˼����
            showDetectionBoxes.value = true;
          }
        }
      };
      processedImage.onerror = (err) => {
        console.error('���ش������ͼ��ʧ��?', err);
        // ����ʧ��ʱʹ�ó������?        if (showDetectionBoxes.value) {
          drawDetectionBoxes();
        }
      };
      // ����ͼ��
      processedImage.src = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
    } else if (showDetectionBoxes.value) {
      // û�д������ͼ��ʹ�ó������
      drawDetectionBoxes();
    }
  } else if (showDetectionBoxes.value) {
    // û�д������ͼ��ʹ�ó������
    drawDetectionBoxes();
  }
};

// ������߼�?const performDetectionLogic = async () => {
  const frameDataUrl = captureFrame();
  if (!frameDataUrl) {
    error.value = '�޷�������Ƶ֡���з�����';
    isLoading.value = false;
    return;
  }
  currentFramePreview.value = frameDataUrl; // For ObjectDetectionPanel

  if (selectedOptions.value.length === 0) {
    error.value = '������ѡ��һ��������ݡ�?;
    detectionResults.value = null;
    clearCanvas();
    isLoading.value = false;
    return;
  }

  // �����״μ��ʱ��ʾ�������?  if (!detectionResults.value) {
    showLoadingMessage.value = true;
  }
  isLoading.value = true;
  error.value = '';
  try {
    let results;

    // ����ִ��YOLO���ͱ�׼API���?    if (useYoloDetection.value) {
      // ����YOLO�����̣�WebSocket��ʽ��
      processFrameWithYOLO(frameDataUrl);
      
      // ͬʱִ�б�׼���API�������ڰ�ȫ����
      results = await performStandardDetection(frameDataUrl);
      
      // ������ϲ���YOLO�����ͨ��WebSocket�ص���
      detectionResults.value = results;
      
      // ȷ����ȫ������ʾ���Ҳ�����У���ʹû�м���?      if (results && results.description) {
        showSafetyRecommendation(results.description);
      }
      
      console.log('ִ���˲��м��? YOLO���Ӿ�ʶ�� + Qwen API����ȫ����');
    } else {
      // ���YOLO���δ���ã�ִֻ�б�׼���
      results = await performStandardDetection(frameDataUrl);
      detectionResults.value = results;
    }

    showLoadingMessage.value = false;

    // ����API��־
    if (results) {
      const logEntry = {
        success: true,
        detections: results.objects || [],
        description: results.description || '',
        high_risk_events: results.high_risk_events || [],
        low_risk_events: results.low_risk_events || []
      };
      addApiLog(`INFO:qwen-vl-api:���ɹ������ؽ��? ${JSON.stringify(logEntry)}`);
    }

    // ���������ͼ�����?(ֻ���ڲ�ʹ��WebSocketʱ)
if (!useYoloDetection.value || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
  handleProcessedImage(results);
}
  } catch (err: any) {
    console.error('��Ƶ֡���ʧ��?', err);
    error.value = err.message || '��Ƶ֡���ʧ�ܣ������ԡ�?;
    detectionResults.value = null;
    showLoadingMessage.value = false;
    clearCanvas();
  } finally {
    isLoading.value = false;
  }
};

const triggerManualDetection = () => {
    if (videoPlayerElement.value && videoPlayerElement.value.paused) {
        performDetectionLogic();
    }
};

let frameProcessingInterval: number | null = null;
const realTimeDetectionFrameRate = 0.33; // FPS for real-time detection (1 frame every 3 seconds)

const processCurrentFrameForRealtime = () => {
    if (isLoading.value) return; // Skip if a detection is already in progress
    performDetectionLogic();
};

// Fix the handlePlay function to properly extract and send base64 data
const handlePlay = () => {
  // ������ڳ�ʼ�����û���ȷֹͣ��أ�����������
  if (isInitializing.value || !isMonitoringActive.value) {
    return;
  }
  
  // ������е�֡�����������ֹ�ظ�����
  if (frameProcessingInterval) {
    clearInterval(frameProcessingInterval);
    frameProcessingInterval = null;
  }
  
  // ȷ��WebSocket�����ѽ���������YOLO���ӻ���
  if (useYoloDetection.value && (!wsConnection || wsConnection.readyState !== WebSocket.OPEN)) {
    initializeWebSocket();
  }
  
  // ����YOLO����������ʹ��captureAndProcessFrame������WebSocket�ص����Զ�������һ֡
  if (useYoloDetection.value && wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    // ������һ֡��YOLO����������֡����WebSocket�ص��м�������
    captureAndProcessFrame();
  }
  
  // ����֡��������ͬʱ����API���ú�YOLO����
  const captureRate = 5; // ÿ��5֡
  frameProcessingInterval = window.setInterval(() => {
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // ����ǰ֡
      const frameDataUrl = captureFrame();
      if (!frameDataUrl) return;
      
      // 1. ����֡��YOLO�����������WebSocket�Ѵ򿪣�
      if (useYoloDetection.value && wsConnection && wsConnection.readyState === WebSocket.OPEN) {
        try {
          // ��ȡbase64����
          const base64Data = frameDataUrl.split(',')[1];
          
          // ����֡ID��ʱ���?          const frameId = `frame_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
          const timestamp = Date.now();
          
          // ���͵�WebSocket����
          wsConnection.send(JSON.stringify({
            frame: base64Data,
            frameId: frameId,
            timestamp: timestamp,
            config: {
              segmentation: true,
              confidence: 0.25,
              process_with_qwen: true // ���ߺ��ͬʱ����Qwen API
            }
          }));
        } catch (error) {
          console.error('����֡��WebSocketʧ��:', error);
        }
      }
      
      // 2. ���е��ñ�׼API���м��?      if (!isLoading.value) {
        console.log('����ִ�б�׼API���?);
        isLoading.value = true;
        
        // ʹ��Promiseִ��API���õ����ȴ����?        performStandardDetection(frameDataUrl)
          .then(results => {
            console.log('��׼API������ - ��ȫ����');
            detectionResults.value = results;
            
            // ��ʾ��ȫ����������Ҳ����
            if (results && results.description) {
              showSafetyRecommendation(results.description);
            }
            
            // ����API��־
            const logEntry = {
              success: true,
              description: results.description || '',
              high_risk_events: results.high_risk_events || [],
              low_risk_events: results.low_risk_events || []
            };
            addApiLog(`INFO:qwen-vl-api:���ɹ������ؽ��? ${JSON.stringify(logEntry)}`);
            
            // ���û����ʹ��YOLO WebSocket������¼���?            if (!useYoloDetection.value || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
              handleProcessedImage(results);
            }
          })
          .catch(err => {
            console.error('��׼API���ʧ��?', err);
          })
          .finally(() => {
            isLoading.value = false;
          });
      }
    }
  }, 1000 / captureRate);
  
  console.log(`��������Ƶ������֡��: ${captureRate} FPS��${useYoloDetection.value ? 'ʹ��YOLO���ӻ�' : '��׼����'}`);
};

const handlePauseOrEnd = () => {
    if (frameProcessingInterval) {
        clearInterval(frameProcessingInterval);
        frameProcessingInterval = null;
    }
    // Optionally, perform one last detection on the paused frame if desired
    // if (videoPlayerElement.value && videoPlayerElement.value.paused) {
    //     performDetectionLogic();
    // }
};

const drawDetectionBoxes = () => {
  const canvas = detectionCanvasElement.value;
  const video = videoPlayerElement.value;

  if (!canvas || !video || !detectionResults.value) {
    clearCanvas();
    return;
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  // Ensure canvas size matches video intrinsic size for correct coordinate mapping
  if (canvas.width !== video.videoWidth || canvas.height !== video.videoHeight) {
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  }
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  if (!showDetectionBoxes.value) return;

  const { objects = [], high_risk_events = [], low_risk_events = [], input_width, input_height } = detectionResults.value;
  const sourceWidth = input_width || video.videoWidth;
  const sourceHeight = input_height || video.videoHeight;

  const allDetections: (DetectionObject | HighRiskEvent | LowRiskEvent)[] = [];
  if (showHighRiskBoxes.value) allDetections.push(...high_risk_events.map(e => ({ ...e, _isHighRisk: true })));
  if (showLowRiskBoxes.value) allDetections.push(...low_risk_events.map(e => ({ ...e, _isLowRisk: true })));

  objects.forEach(obj => {
    const isPerson = obj.category === '��Ա' || obj.category?.includes('person');
    const isVehicle = obj.category === '����' || obj.category?.includes('vehicle');
    const isMachine = obj.category === '��е' || obj.category === '�豸' || obj.category?.includes('machine');

    if (
      (isPerson && showPersonBoxes.value) ||
      (isVehicle && showVehicleBoxes.value) ||
      (isMachine && showMachineBoxes.value)
    ) {
      // Avoid adding if it might be a duplicate from risk events (simple check, could be improved with IDs)
      if (!allDetections.some(riskEvent => riskEvent.bbox_2d.toString() === obj.bbox_2d.toString())) {
         allDetections.push(obj);
      }
    }
  });

  allDetections.forEach((item: any) => {
    const bbox = item.bbox_2d;
    if (!bbox || bbox.length !== 4) return;

    const [x1, y1, x2, y2] = bbox;
    const drawX1 = (x1 / sourceWidth) * canvas.width;
    const drawY1 = (y1 / sourceHeight) * canvas.height;
    const boxWidth = ((x2 - x1) / sourceWidth) * canvas.width;
    const boxHeight = ((y2 - y1) / sourceHeight) * canvas.height;

    let color = 'rgba(128, 128, 128, 0.5)'; // Default gray
    let borderColor = 'gray';
    let lineDash: number[] = [];

    if (item._isHighRisk || item.risk_level === 'high') {
      color = 'rgba(255, 0, 0, 0.3)'; borderColor = 'red'; lineDash = [5,3];
    } else if (item._isLowRisk || item.risk_level === 'low') {
      color = 'rgba(255, 165, 0, 0.3)'; borderColor = 'orange';
    } else if (item.category === '��Ա') {
      color = 'rgba(0, 0, 255, 0.3)'; borderColor = 'blue';
    } else if (item.category === '����') {
      color = 'rgba(0, 255, 0, 0.3)'; borderColor = 'green';
    } else if (item.category === '��е' || item.category === '�豸') {
      color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
    }

    ctx.strokeStyle = borderColor;
    ctx.fillStyle = color;
    ctx.lineWidth = 2;
    ctx.setLineDash(lineDash);
    ctx.beginPath();
    ctx.rect(drawX1, drawY1, boxWidth, boxHeight);
    ctx.stroke();
    ctx.fill();
    ctx.setLineDash([]);

    const label = item.label || item.event || item.category || 'δ֪';
    ctx.fillStyle = borderColor;
    ctx.font = 'bold 12px Arial';
    const textMetrics = ctx.measureText(label);
    ctx.fillRect(drawX1, drawY1 - 16, textMetrics.width + 4, 16);
    ctx.fillStyle = 'white';
    ctx.fillText(label, drawX1 + 2, drawY1 - 4);
  });
};

// --- Utility Functions for Results Display (from App.vue, simplified/adapted) ---
const totalHighRiskCount = computed(() => detectionResults.value?.high_risk_events?.length || 0);
const totalLowRiskCount = computed(() => detectionResults.value?.low_risk_events?.length || 0);

const getPersonCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '��Ա' || 
      obj.category === '��' || 
      obj.label === '��' || 
      (obj.category?.toLowerCase().includes('person')) ||
      (obj.label?.toLowerCase().includes('person'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '��Ա' || 
          obj.category === '��' || 
          obj.label === '��' || 
          (obj.category?.toLowerCase()?.includes('person')) ||
          (obj.label?.toLowerCase()?.includes('person'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          if (rawData.person && Array.isArray(rawData.person)) {
            return rawData.person.length;
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const getVehicleCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '����' || 
      obj.category === '��' || 
      (obj.category?.toLowerCase()?.includes('vehicle')) || 
      (obj.category?.toLowerCase()?.includes('car'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '����' || 
          obj.category === '��' || 
          (obj.category?.toLowerCase()?.includes('vehicle')) || 
          (obj.category?.toLowerCase()?.includes('car'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          if (rawData.vehicle && Array.isArray(rawData.vehicle)) {
            return rawData.vehicle.length;
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const getMachineCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '��е' || 
      obj.category === '�豸' || 
      obj.category === '���ػ�' || 
      obj.category === '����������е' || 
      (obj.category?.toLowerCase()?.includes('machine')) || 
      (obj.category?.toLowerCase()?.includes('equipment'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '��е' || 
          obj.category === '�豸' || 
          obj.category === '���ػ�' || 
          obj.category === '����������е' || 
          (obj.category?.toLowerCase()?.includes('machine')) || 
          (obj.category?.toLowerCase()?.includes('equipment')) || 
          (obj.category?.toLowerCase()?.includes('construction'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          let machineCount = 0;
          // Check construction_machinery array
          if (rawData.construction_machinery && Array.isArray(rawData.construction_machinery)) {
            machineCount += rawData.construction_machinery.length;
          }
          // Return if we found any
          if (machineCount > 0) return machineCount;
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const translateEventName = (eventName: string | undefined): string => {
  if (!eventName) return 'δ֪�¼�';
  const translations: Record<string, string> = {
    'worker_no_helmet': 'δ�����ȫ�?,
    'worker_no_vest': 'δ�����ⱳ��',
    'worker_falling': '����ˤ��',
    'unsafe_operation': '����ȫ����',
    // Add more translations as needed
  };
  return translations[eventName] || eventName;
};

// ע�⣺��ʽ����ȫ�������������Ѿ������Ϸ�����ǿʵ����

// Grouped events for display
type EventGroupItem = { eventName: string; count: number };
type CategoryEventGroup = { category: string; events: EventGroupItem[]; totalCount: number };

const groupEventsForDisplay = (events: HighRiskEvent[] | LowRiskEvent[] | undefined): CategoryEventGroup[] => {
  if (!events || events.length === 0) return [];
  const categorized = new Map<string, EventGroupItem[]>();

  events.forEach(event => {
    const categoryName = event.category || 'δ����';
    const eventName = event.event || 'δ֪����';
    if (!categorized.has(categoryName)) {
      categorized.set(categoryName, []);
    }
    const categoryEvents = categorized.get(categoryName)!;
    let eventItem = categoryEvents.find(e => e.eventName === eventName);
    if (eventItem) {
      eventItem.count++;
    } else {
      categoryEvents.push({ eventName, count: 1 });
    }
  });

  const result: CategoryEventGroup[] = [];
  categorized.forEach((events, category) => {
    result.push({ category, events, totalCount: events.reduce((sum, e) => sum + e.count, 0) });
  });
  return result;
};

const groupedHighRiskEventsForDisplay = computed(() => groupEventsForDisplay(detectionResults.value?.high_risk_events));
const groupedLowRiskEventsForDisplay = computed(() => groupEventsForDisplay(detectionResults.value?.low_risk_events));

// ����һ�����������غ���ʾ�������ͼ��?const loadProcessedImage = (url: string) => {
  if (!url) return;

  console.log('���ش������ͼ��?', url);
  const processedImage = new Image();
  const cacheBuster = new Date().getTime();
  processedImage.onload = () => {
    if (detectionCanvasElement.value) {
      const ctx = detectionCanvasElement.value.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
        ctx.drawImage(
          processedImage,
          0, 0,
          detectionCanvasElement.value.width,
          detectionCanvasElement.value.height
        );
      }
    }
  };
  processedImage.onerror = (err) => {
    console.error('���ش������ͼ��ʧ��?', err);
    if (showDetectionBoxes.value) {
      drawDetectionBoxes(); // ����ʧ��ʱ���˵�ԭʼ����
    }
  };
  processedImage.src = `http://localhost:8000${url}?t=${cacheBuster}`;
};

// Modify the fetchApiLogs function to handle the missing endpoint
const fetchApiLogs = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/logs?type=qwen-vl-api');
    if (!response.ok) {
      console.error('Failed to fetch API logs');
      return;
    }
    
    const data = await response.json();
    if (data.logs) {
      // Filter logs containing the specific prefix
      const successLogs = data.logs.filter((log: string) => 
        log.includes('INFO:qwen-vl-api:���ɹ������ؽ��?'));
      
      if (successLogs.length > 0) {
        apiLogs.value = successLogs.map((log: string) => {
          // Extract just the result part after the prefix
          const resultMatch = log.match(/INFO:qwen-vl-api:���ɹ������ؽ��?(.*)/);
          return resultMatch ? resultMatch[1].trim() : log;
        });
      }
    }
  } catch (error) {
    console.error('Error fetching API logs:', error);
  }
};

// Start/stop polling for API logs
const startLogPolling = () => {
  if (!isPollingLogs.value) {
    isPollingLogs.value = true;
    // Poll every 5 seconds
    const pollingInterval = setInterval(() => {
      if (isPollingLogs.value) {
        fetchApiLogs();
      } else {
        clearInterval(pollingInterval);
      }
    }, 5000);
    
    // Initial fetch
    fetchApiLogs();
  }
};

// ����������ѯAPI��־�ĺ���
const startActiveLogPolling = () => {
  console.log('����������ѯAPI��־');
  const pollingInterval = setInterval(async () => {
    try {
      const response = await fetch('http://localhost:8000/api/logs?type=yolo_video_processor');
      if (response.ok) {
        const data = await response.json();
        if (data.logs && data.logs.length > 0) {
          // �������µ�Qwen API���سɹ���־
          const successLogs = data.logs.filter((log: string) => 
            log.includes('INFO:yolo_video_processor:Qwen API���سɹ�') || 
            log.includes('INFO:yolo_video_processor:Qwen API�������?));
          
          if (successLogs.length > 0) {
            // �������µ���־
            successLogs.slice(0, 3).forEach((log: string) => {
              addApiLog(log);
            });
            
            // ��������״̬
            lastUpdated.value = new Date();
            isHeaderUpdating.value = true;
            
            // 2���رն���
            setTimeout(() => {
              isHeaderUpdating.value = false;
            }, 2000);
          }
        }
      }
    } catch (error) {
      console.warn('��ѯAPI��־ʧ��:', error);
    }
  }, 1000); // ÿ����ѯһ��
  
  return pollingInterval;
};

// ����������ȡQwen��������ĺ���?const fetchQwenApiResults = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/logs?type=yolo_video_processor&limit=3');
    if (response.ok) {
      const data = await response.json();
      if (data.logs && data.logs.length > 0) {
        // �������µ�Qwen API����������?        const qwenApiResultsLog = data.logs.find(log => 
          log.includes('INFO:yolo_video_processor:Qwen API�������?')
        );
        
        if (qwenApiResultsLog) {
          console.log('������ȡ�����µ�Qwen API�������?);
          // ��Qwen API����������ӵ����?          addApiLog(qwenApiResultsLog);
        }
      }
    }
  } catch (error) {
    console.error('��ȡQwen API���ʧ��?', error);
  }
};

// ����������ѯ�Ի�ȡ���µ�Qwen API�������?const startQwenApiResultsPolling = () => {
  // ÿ2����ѯһ��
  const pollingInterval = setInterval(fetchQwenApiResults, 2000);
  
  // �״�������ѯ
  fetchQwenApiResults();
  
  return pollingInterval;
};

onMounted(() => {
  // Initialize YS7 video player
  initializeYs7Player();
  
  // ����һ��ע�͵�����initializeMonitoring����������
  // selectCategory('all');
  
  // Ĭ��ѡ�� 'all' ��𣬵���������ʼ�����ȴ��û�����л�
  selectedCategory.value = 'all';
  detectionOptions.value.forEach(detCat => {
    detCat.options.forEach(opt => {
      const category = riskCategories.find(cat => cat.id === 'all');
      if (category) {
        opt.checked = category.options.includes(opt.id);
      }
    });
  });
  
  // Event listeners for video are added in the template now
  
  // Add a sample log for demonstration
  addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
  setTimeout(() => {
    addApiLog(`INFO:yolo_video_processor:Qwen API�������? {'success': True, 'detections': [{'category': '��', 'event_description': '', 'risk_level': '�з���', 'confidence_score': 0.7, 'bbox_2d': [654, 589, 673, 617], 'display_label': '', 'event': '��', 'confidence': 0.8, 'label': '��'}], 'description': '����������ֵ����⣺\n\n* ��ʩ���ֳ�Ӧ�ϸ����ذ�ȫ�����涨�������ֳ�������Ա��ǿ��ȫ������ѵ��\n* ����ʱ������ȷ������˷������?�����������ڰ�ȫñ)�����������⵼���˺��¹ʵķ����� \n* ʩ�����������ǿ�����ػ�е�豸�Ĳ���������ά���������ȣ�������Χ�������Եľ����ʶ���ѹ�������ע����÷�ֹ������ײ���߱����˵��������;\n * ��ǿ�ճ�Ѳ�����ƶ���ʵ��λ��ʱ������ȫ����Ԥ���¹ʷ���;  \n   \n���ϴ�ʩ�����ڽ��͸��లȫ�¹ʷ����ĸ��ʴӶ�����Ա�������Ʋ���ȫ�Լ��ٽ���Ŀ˳���ƽ����? \n\n��ע��ʵ�ʹ����п��ܴ��ڸ���ϸ����Ҫ��һ��ȷ�Ͼ��������������Դ�����', 'high_risk_events': [{"description": "����δ�����ȫñ���?, "mitigation": "����Ҫ��ù��������ȫñ����ǿ��ȫ�������ֳ�Ѳ�顣"}], 'low_risk_events': [], 'raw_json': '{\n    "person": [\n        {\n            "category": "��",\n            "event_description": "",\n            "risk_level": "�з���",\n            "confidence_score": 0.7,\n            "bbox_2d": [654, 589, 673, 617],\n            "display_label": ""\n        }\n    ],\n    "vehicle": [],\n    "construction_machinery": [\n        {\n            "category": "���ػ�",\n            "event_description": "",\n            "risk_level": "�޷���",\n            "confidence_score": 0.9,\n            "bbox_2d": [-1,-1 , -1 ,-1]\n        },\n        {\n            "category": "����������е",\n            "event_description": "",\n            "risk_level": "�޷���",\n            "confidence_score": 0.9,\n            "bbox_2d": []\n        }\n    ],\n    "crane_operation_status": {},\n    "road_base_condition": {},\n    "slope_stability": {},\n    "pavement_condition": {},\n    "hard_isolation_and_protection_barriers": {},\n    "fire_hazard": {},\n    "smoke_detection": {}\n}', 'input_width': 1664, 'input_height': 928, 'image_path': 'static\\uploads\\9f19d038-ccbd-4ca1-a02c-3f948d6f66f6.jpg'}`);
  }, 500);
  
  // Start regular API log polling
  startLogPolling();
  
  // Start active polling specifically for Qwen API results
  const activePollingInterval = startActiveLogPolling();
  
  // Also start dedicated polling for Qwen API results
  const qwenApiResultsPollingInterval = startQwenApiResultsPolling();
  
  // Load sample Qwen results
  loadSampleQwenResults();
  
  // Clean up polling when component is unmounted
  onUnmounted(() => {
    if (activePollingInterval) {
      clearInterval(activePollingInterval);
    }
    if (qwenApiResultsPollingInterval) {
      clearInterval(qwenApiResultsPollingInterval);
    }
    
    // �����ʼ����ʱ��?    if (initializationTimer.value) {
      clearTimeout(initializationTimer.value);
      initializationTimer.value = null;
    }
  });
});

onUnmounted(() => {
  // Clean up HLS player
  if (hlsPlayer) {
    hlsPlayer.destroy();
    hlsPlayer = null;
  }
  
  // �����ʱ��?  handlePauseOrEnd();

  // �ر�WebSocket����
  if (wsConnection) {
    console.log('���ж�أ��ر�WebSocket����');
    try {
      // ���͹ر���Ϣ
      if (wsConnection.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify({ action: 'close' }));
      }
      // �ر�����
      wsConnection.close();
      wsConnection = null;
    } catch (error) {
      console.error('�ر�WebSocket����ʱ����:', error);
    }
  }
  
  // Stop log polling
  isPollingLogs.value = false;
});

watch(detectionResults, (newValue) => { // Simplified watch, drawDetectionBoxes is called directly after results
    if (showDetectionBoxes.value) {
        if (newValue && (newValue.processed_url || newValue.visualized_image_url)) {
            // ����д������ͼ�񣬼��ز���ʾ
            const processedUrl = newValue.visualized_image_url || newValue.processed_url;
            if (processedUrl) {
                loadProcessedImage(processedUrl);
            } else {
                drawDetectionBoxes();
            }
        } else {
            drawDetectionBoxes();
        }
    }
}, { deep: true });

watch([showDetectionBoxes, showHighRiskBoxes, showLowRiskBoxes, showPersonBoxes, showVehicleBoxes, showMachineBoxes],
  () => {
    if (currentlyDisplayedLog.value) {
      // If we're showing a log on the video, redraw it
      drawLogDetectionBoxes(currentlyDisplayedLog.value);
    } else if (detectionResults.value) { 
      // Otherwise use regular detection results
      drawDetectionBoxes();
    }
  }
);

// YS7 related data
const isYs7Loading = ref(false);
const ys7Error = ref(false);
const ys7ErrorMessage = ref('');
let hlsPlayer: any = null;

// Add video buffer system for delayed playback
const videoBuffer = ref<{frame: string, timestamp: number}[]>([]);
const bufferSize = ref(5); // Number of frames to buffer (creates delay)
const bufferDelay = ref(2000); // 2 second delay (in ms)
const isBuffering = ref(false);
const bufferInterval = ref<number | null>(null);
const isDelayedPlaybackActive = ref(false);

// Initialize YS7 Cloud video player
const initializeYs7Player = async () => {
  try {
    isYs7Loading.value = true;
    ys7Error.value = false;
    
    // YS7 authentication credentials
    const appKey = '8dcf58f3eff843a49ae4c60b55cd9c9b';
    const appSecret = 'a436053dec3df63157bdfe7100f76f88';
    const deviceSerial = '*********'; // Device serial number
    const channelNo = 3; // Channel number
    
    // Get access token
    const tokenParams = new URLSearchParams();
    tokenParams.append('appKey', appKey);
    tokenParams.append('appSecret', appSecret);
    
    const tokenResponse = await axios.post('https://open.ys7.com/api/lapp/token/get', tokenParams, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (tokenResponse.data.code !== '200') {
      throw new Error(`��ȡ��Ȩʧ��: ${tokenResponse.data.msg || '����AppKey��AppSecret'}`);
    }
    
    const accessToken = tokenResponse.data.data.accessToken;
    
    // Get stream URL
    const liveUrlParams = new URLSearchParams();
    liveUrlParams.append('accessToken', accessToken);
    liveUrlParams.append('deviceSerial', deviceSerial);
    liveUrlParams.append('channelNo', channelNo);
    liveUrlParams.append('protocol', '2'); // HLS protocol
    liveUrlParams.append('quality', '1'); // HD quality
    
    const liveUrlResponse = await axios.post('https://open.ys7.com/api/lapp/v2/live/address/get', liveUrlParams, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (liveUrlResponse.data.code !== '200') {
      throw new Error(`��ȡֱ����ַʧ��: ${liveUrlResponse.data.msg || '�����豸���кź�ͨ����'}`);
    }
    
    const hlsUrl = liveUrlResponse.data.data.url;
    
    // Update video source and start playing
    videoSource.value = hlsUrl;
    
    // Initialize HLS player
    const videoElement = videoPlayerElement.value;
    
    if (!videoElement) {
      throw new Error('Video element not found');
    }
    
    if (Hls.isSupported()) {
      if (hlsPlayer) {
        hlsPlayer.destroy();
      }
      
      hlsPlayer = new Hls({
        debug: false,
        maxLoadingRetry: 4,
        enableWorker: true,
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
        // Add lowLatencyMode to improve playback performance
        lowLatencyMode: true,
        // Increase chunk sizes to reduce network requests
        fragLoadPolicy: {
          default: {
            maxTimeToFirstByteMs: 10000,
            maxLoadTimeMs: 120000,
            timeoutRetry: 3
          }
        }
      });
      
      hlsPlayer.loadSource(hlsUrl);
      hlsPlayer.attachMedia(videoElement);
      
      hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest parsed, attempting to play video');
        // Force play the video element
        const playPromise = videoElement.play();
        if (playPromise !== undefined) {
          playPromise.then(() => {
            console.log('Video playback started successfully');
            // Only initialize YOLO after video has started successfully
            if (useYoloDetection.value) {
              console.log('Starting YOLO detection after successful video start');
              // Ensure WebSocket is initialized first, then handle video processing
              initializeWebSocket();
              // Add a slight delay to ensure video has started rendering frames
              setTimeout(() => {
                handlePlay();
              }, 1000);
            }
          }).catch(playError => {
            console.warn('Autoplay prevented:', playError);
            // Try to play with user interaction later
            addApiLog(`WARN:qwen-vl-api:��Ƶ�Զ����ű��������ֹ��������Ƶ�����ֶ�����`);
          });
        }
      });
      
      // Error handling
      hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
        if (data.fatal) {
          switch(data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('Network error, attempting to recover');
              hlsPlayer.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('Media error, attempting to recover');
              hlsPlayer.recoverMediaError();
              break;
            default:
              console.error('Fatal error, cannot recover');
              ys7Error.value = true;
              ys7ErrorMessage.value = '��Ƶ����ʧ�ܣ�������ƵԴ';
              hlsPlayer.destroy();
              hlsPlayer = null;
              // Fall back to local video if available
              videoElement.src = './1747124126409.mp4';
              videoElement.play().catch(e => console.warn('Local video playback error:', e));
              break;
          }
        }
      });
    } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
      // For Safari
      videoElement.src = hlsUrl;
      videoElement.play().catch(e => console.warn('Safari playback error:', e));
    } else {
      throw new Error('�����������֧��HLS���ţ���ʹ���ִ������?);
    }
    
    isYs7Loading.value = false;
  } catch (error) {
    console.error('YS7 initialization error:', error);
    isYs7Loading.value = false;
    ys7Error.value = true;
    ys7ErrorMessage.value = error.message || 'өʯ����Ƶ����ʧ��';
    
    // Fallback to sample video if available
    if (videoPlayerElement.value) {
      videoPlayerElement.value.src = './1747124126409.mp4';
      videoPlayerElement.value.play().catch(e => console.warn('Fallback video playback error:', e));
    }
  }
};

// Start buffered playback system for delayed video with YOLO detection
const startBufferedPlayback = () => {
  if (isBuffering.value) return;
  
  isBuffering.value = true;
  isDelayedPlaybackActive.value = true;
  videoBuffer.value = [];
  
  // �����ӳ٣����ʵʱ��?  bufferDelay.value = 500; // ���ӳٴ�Ĭ�ϵ�2000ms����500ms
  
  // Pause the main video while we buffer frames
  if (videoPlayerElement.value) {
    videoPlayerElement.value.pause();
  }
  
  console.log(`��ʼ������Ƶ֡���ӳ�ʱ��: ${bufferDelay.value}ms`);
  
  // Start capturing frames to fill the buffer
  const captureInterval = setInterval(() => {
    const frame = captureFrame();
    if (frame) {
      const timestamp = Date.now();
      videoBuffer.value.push({
        frame,
        timestamp
      });
      
      // Process frame with YOLO immediately (real-time detection)
      if (useYoloDetection.value && !isLoading.value) {
        // ���ʹ�ʱ�����ID��֡
        if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
          const frameId = Math.random().toString(36).substring(2, 15);
          wsConnection.send(JSON.stringify({
            frame: frame.split(',')[1],
            frameId: frameId,
            timestamp: timestamp,
            config: {
              segmentation: true,
              confidence: 0.25
            }
          }));
        }
      }
      
      // Start playback after the first frame is captured
      if (videoBuffer.value.length === 1 && !bufferInterval.value) {
        startBufferPlayback();
      }
    }
  }, 40); // Capture at ~25fps for smooth display
  
  // Clear any existing interval
  if (bufferInterval.value) {
    clearInterval(bufferInterval.value);
    bufferInterval.value = null;
  }
  
  return captureInterval;
};

// Play frames from the buffer with a delay
const startBufferPlayback = () => {
  // Already playing from buffer
  if (bufferInterval.value) return;
  
  console.log('Starting delayed playback from buffer');
  
  // Display frames from buffer after the delay
  bufferInterval.value = window.setInterval(() => {
    const now = Date.now();
    
    // Find the oldest frame that meets the delay requirement
    let oldestValidFrameIndex = -1;
    for (let i = 0; i < videoBuffer.value.length; i++) {
      if (now - videoBuffer.value[i].timestamp >= bufferDelay.value) {
        oldestValidFrameIndex = i;
        break;
      }
    }
    
    // If we found a frame that's old enough (2+ seconds)
    if (oldestValidFrameIndex >= 0) {
      // Remove all frames older than this one
      const oldestFrame = videoBuffer.value[oldestValidFrameIndex];
      videoBuffer.value.splice(0, oldestValidFrameIndex + 1);
      
      if (oldestFrame && detectionCanvasElement.value) {
        // Display the delayed frame on canvas
        const ctx = detectionCanvasElement.value.getContext('2d');
        if (ctx) {
          // Draw the delayed frame on canvas
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, detectionCanvasElement.value!.width, detectionCanvasElement.value!.height);
            ctx.drawImage(img, 0, 0, detectionCanvasElement.value!.width, detectionCanvasElement.value!.height);
          };
          img.src = oldestFrame.frame;
        }
      }
    }
  }, 40); // Display at ~25fps for smooth playback
};

// Process frame with YOLO and update detection overlay
const processFrameWithYOLO = async (frameDataUrl: string) => {
  try {
    // ��ȡ��Base64����
    const base64Data = frameDataUrl.split(',')[1];
    
    // ���͵�WebSocket���д���
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        frame: base64Data,
        model: "yolo11n-seg.pt",
        config: {
          segmentation: true,  // ���÷ָ�
          confidence: 0.25,    // ���Ŷ���ֵ
          visualize: true      // ȷ�����ÿ��ӻ�
        },
        timestamp: Date.now(), // ����ʱ�����׷���?        frameId: Math.random().toString(36).substring(2, 15) // ����Ψһ֡ID
      }));
    } else {
      // ���WebSocketδ���ӣ�������������
      console.log('WebSocketδ���ӣ�������������');
      initializeWebSocket();
    }
  } catch (error) {
    console.error('YOLO��������:', error);
  }
};

// ���Ͳ���ͼ������֤WebSocket����
const sendTestImage = () => {
  if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
    console.error('WebSocket����δ�򿪣��޷����Ͳ���ͼ��');
    return;
  }
  
  // ����һ���򵥵Ĳ���ͼ�� (20x20 ��ɫ����)
  const canvas = document.createElement('canvas');
  canvas.width = 20;
  canvas.height = 20;
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.fillStyle = 'red';
    ctx.fillRect(0, 0, 20, 20);
    const testImage = canvas.toDataURL('image/jpeg');
    
    // ��ȡBase64����
    const base64Data = testImage.split(',')[1];
    
    console.log('���Ͳ���ͼ������֤WebSocket����');
    wsConnection.send(JSON.stringify({
      action: 'test',
      frame: base64Data,
      model: 'yolo11n-seg.pt'
    }));
  }
};

// Initialize WebSocket for YOLO detection
const initializeWebSocket = () => {
  // If already connected, no need to reinitialize
  if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    console.log('WebSocket�����ӣ��������³�ʼ��');
    return Promise.resolve(wsConnection);
  }
  
  // If connecting, wait for it
  if (wsConnection && wsConnection.readyState === WebSocket.CONNECTING) {
    console.log('WebSocket���������У����Ժ�...');
    return new Promise((resolve, reject) => {
      // Wait up to 3 seconds for the connection to establish
      const timeout = setTimeout(() => {
        reject(new Error('���ӳ�ʱ'));
      }, 3000);
      
      // Listen for connection events
      const onOpen = () => {
        clearTimeout(timeout);
        wsConnection.removeEventListener('open', onOpen);
        wsConnection.removeEventListener('error', onError);
        resolve(wsConnection);
      };
      
      const onError = (err) => {
        clearTimeout(timeout);
        wsConnection.removeEventListener('open', onOpen);
        wsConnection.removeEventListener('error', onError);
        reject(err);
      };
      
      wsConnection.addEventListener('open', onOpen);
      wsConnection.addEventListener('error', onError);
    });
  }
  
  console.log('�����µ�WebSocket���� - ʹ��yolo11n-seg.ptģ�ͽ��п��ӻ�');
  addApiLog(`INFO:qwen-vl-api:WebSocket�����ѽ�����׼������YOLO��Ƶ����`);
  addApiLog(`INFO:yolo_video_processor:����YOLOģ��: ../src/models/yolo11n-seg.pt`);
  
  // Reset frame counter to ensure smooth API calls
  frameCounter = 0;
  
  // Create a Promise for the WebSocket connection
  return new Promise((resolve, reject) => {
    try {
      // Close existing connection if any
      if (wsConnection) {
        try {
          wsConnection.close();
        } catch (e) {
          // Ignore errors on close
        }
        wsConnection = null;
      }
      
      // Create new WebSocket connection dedicated to YOLO video processing
      wsConnection = new WebSocket('ws://localhost:8000/ws/yolo-video-process');
      
      // Set a timeout to reject the promise if connection takes too long
      const connectionTimeout = setTimeout(() => {
        reject(new Error('WebSocket���ӳ�ʱ'));
      }, 5000);
      
      wsConnection.onopen = () => {
        console.log('WebSocket�����ѽ���');
        clearTimeout(connectionTimeout);
        
        addApiLog(`INFO:qwen-vl-api:YOLO�������Ѵ�����ʹ��ģ��: ../src/models/yolo11n-seg.pt`);
        
        // Send model configuration
        wsConnection.send(JSON.stringify({
          action: 'configure',
          model: 'yolo11n-seg.pt',
          config: {
            segmentation: true,
            confidence: 0.25,
            visualize: true,
            img_size: 640, // Set processing image size
            show_masks: true // Ensure masks are shown
          }
        }));
        
        // Update UI indicators to show YOLO active state
        const yoloBadge = document.querySelector('.yolo-badge');
        if (yoloBadge) {
          yoloBadge.style.animation = 'pulse 1s infinite';
          yoloBadge.style.backgroundColor = '#52c41a';
          yoloBadge.textContent = 'YOLO11x-seg ������';
        }
        
        // Process the current frame with YOLO to start the processing flow
        if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
          setTimeout(() => {
            captureAndProcessFrame();
          }, 500);
        }
        
        resolve(wsConnection);
      };

      wsConnection.onerror = (error) => {
        console.error('WebSocket����:', error);
        clearTimeout(connectionTimeout);
        addApiLog(`ERROR:qwen-vl-api:YOLO����������ʧ�ܣ������˷����Ƿ�����`);
        
        // Set visual indicators to show YOLO connection failure
        const yoloBadge = document.querySelector('.yolo-badge');
        if (yoloBadge) {
          yoloBadge.style.animation = 'none';
          yoloBadge.style.backgroundColor = '#ff4d4f';
          yoloBadge.textContent = 'YOLO����ʧ��';
        }
        
        reject(error);
      };

      wsConnection.onclose = () => {
        console.log('WebSocket�����ѹر�');
        addApiLog(`INFO:qwen-vl-api:WebSocket�����ѶϿ�`);
        addApiLog(`INFO:qwen-vl-api:YOLO��Ƶ����WebSocket�����ѹر�`);
        wsConnection = null;
        
        // Update UI state
        const yoloBadge = document.querySelector('.yolo-badge');
        if (yoloBadge) {
          yoloBadge.style.animation = 'none';
          yoloBadge.style.backgroundColor = '#555';
          yoloBadge.textContent = 'YOLO11x-seg';
        }
        
        // If page is still in use, try to reconnect
        setTimeout(() => {
          if (useYoloDetection.value && isMonitoringActive.value) {
            console.log('������������WebSocket');
            initializeWebSocket()
              .then(() => {
                console.log('WebSocket�������ӳɹ�');
                // Resume frame processing
                if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
                  captureAndProcessFrame();
                }
              })
              .catch(err => {
                console.error('WebSocket��������ʧ��:', err);
                addApiLog(`ERROR:qwen-vl-api:YOLO����������ʧ�ܣ���ˢ��ҳ������`);
              });
          }
        }, 3000);
      };
      
      // Keep the original onmessage handler
      // ...
      
    } catch (error) {
      console.error('����WebSocketʱ����:', error);
      addApiLog(`ERROR:qwen-vl-api:����WebSocket����ʧ��: ${error.message}`);
      reject(error);
    }
  });
};

// ���Ӳ���ʹ�����ǰ֡�ĺ���?// Capture and send the current frame for YOLO processing, update Qwen results afterward
const captureAndProcessFrame = () => {
  if (!videoPlayerElement.value || videoPlayerElement.value.paused || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
    return;
  }
  
  try {
    // Ensure canvas is correctly sized to match video dimensions before capturing
    if (detectionCanvasElement.value) {
      const video = videoPlayerElement.value;
      if (detectionCanvasElement.value.width !== video.videoWidth || 
          detectionCanvasElement.value.height !== video.videoHeight) {
        detectionCanvasElement.value.width = video.videoWidth || 640;
        detectionCanvasElement.value.height = video.videoHeight || 360;
        console.log(`Resized detection canvas to: ${detectionCanvasElement.value.width}x${detectionCanvasElement.value.height}`);
      }
    }

    // Capture current video frame with improved quality
    const frameDataUrl = captureFrame();
    if (!frameDataUrl) return;
    
    // Generate frame ID and timestamp
    frameCounter++;
    const frameId = `frame_${Date.now()}_${frameCounter}`;
    const timestamp = Date.now();
    
    // Send to WebSocket with additional parameters to ensure mask visualization
    wsConnection.send(JSON.stringify({
      frame: frameDataUrl.split(',')[1], // Only send Base64 part
      frameId: frameId,
      timestamp: timestamp,
      config: {
        segmentation: true, 
        confidence: 0.25,
        process_with_qwen: true, // Tell backend to also process with Qwen API
        visualize: true,         // Ensure visualization is enabled
        show_masks: true,        // Explicitly request masks to be shown
        img_size: 640            // Consistent image size for processing
      }
    }));
    
    // Schedule next frame capture with requestAnimationFrame for better performance
    // Add adaptive frame rate based on system performance
    const nextFrameDelay = frameCounter % 3 === 0 ? 200 : 100; // Alternate between faster/slower capture
    
    setTimeout(() => {
      if (videoPlayerElement.value && !videoPlayerElement.value.paused && 
          wsConnection && wsConnection.readyState === WebSocket.OPEN) {
        requestAnimationFrame(captureAndProcessFrame);
      }
    }, nextFrameDelay);
  } catch (error) {
    console.error('Error processing video frame:', error);
    // Attempt recovery after error
    setTimeout(() => {
      if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
        captureAndProcessFrame();
      }
    }, 1000);
  }
};

// Add toggle for delayed playback mode
const toggleDelayedPlayback = () => {
  if (isDelayedPlaybackActive.value) {
    // �ر��ӳٲ���ģʽ
    console.log('�ر��ӳٲ���ģʽ��ʹ��ʵʱ��Ⲣ���?);
    isDelayedPlaybackActive.value = false;
    
    // �����������Ͷ�ʱ��
    videoBuffer.value = [];
    clearInterval(bufferCaptureInterval);
    if (bufferInterval.value) {
      clearInterval(bufferInterval.value);
      bufferInterval.value = null;
    }

    // ������Ƶ���ţ�ʹ����ͨ��ʵʱ���ģ�?    if (videoPlayerElement.value && videoPlayerElement.value.paused) {
      videoPlayerElement.value.play();
      // ������ʼʵʱYOLO��⣬��ʹ���ӳ�?      requestAnimationFrame(captureAndProcessFrame);
    }
  } else {
    // �����ӳٲ���ģʽ
    console.log('�����ӳٲ���ģʽ��ʹ�û�����ȷ����Ƶ��YOLO����ͬ��');
    
    // ֹͣ�κ����ڽ��еĴ���
    clearInterval(frameProcessingInterval);
    frameProcessingInterval = null;
    
    // ��������ϵͳ
    isDelayedPlaybackActive.value = true;
    startBufferedPlayback();
  }
};

// Stop delayed playback and return to normal
const stopDelayedPlayback = () => {
  isDelayedPlaybackActive.value = false;
  isBuffering.value = false;
  
  if (bufferInterval.value) {
    clearInterval(bufferInterval.value);
    bufferInterval.value = null;
  }
  
  // Clear buffer
  videoBuffer.value = [];
  
  // Resume normal video playback
  if (videoPlayerElement.value) {
    videoPlayerElement.value.play().catch(e => console.warn('Playback error:', e));
  }
  
  // Clear canvas
  if (detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// Add new ref to track monitoring state
const isMonitoringActive = ref(true);

// Add the toggle monitoring function
const toggleMonitoring = () => {
  isMonitoringActive.value = !isMonitoringActive.value;
  
  if (isMonitoringActive.value) {
    // Resume monitoring
    handlePlay();
  } else {
    // Stop monitoring but keep video playing
    if (frameProcessingInterval) {
      clearInterval(frameProcessingInterval);
      frameProcessingInterval = null;
    }
    
    // Also stop delayed playback if active
    if (isDelayedPlaybackActive.value) {
      stopDelayedPlayback();
    }
  }
};

// Add these helper functions for API log formatting
const tryParseJsonLog = (log: string) => {
  try {
    // First try to parse the log directly
    const parsed = parseJson(log);
    
    // Check if it has useful fields
    if (parsed && (
      parsed.detections || 
      parsed.description || 
      parsed.high_risk_events || 
      parsed.low_risk_events ||
      (parsed.raw_json && typeof parsed.raw_json === 'string')
    )) {
      return true;
    }
    
    // If raw_json is present in the parsed result, try to extract useful content from it
    if (parsed && parsed.raw_json) {
      try {
        // Clean and parse the raw_json field
        const cleanedRawJson = removeJsonComments(parsed.raw_json);
        const rawData = JSON.parse(cleanedRawJson);
        
        // Check for image_overall_safety_analysis_and_control_recommendations in raw data
        if (rawData && typeof rawData === 'object') {
          // If there's a safety analysis in the raw_json, it's useful
          if (rawData.image_overall_safety_analysis_and_control_recommendations) {
            return true;
          }
          
          // Check for presence of any object arrays - also useful
          if (rawData.person || rawData.vehicle || rawData.construction_machinery ||
              rawData.high_risk_events || rawData.low_risk_events) {
            return true;
          }
        }
      } catch (e) {
        console.warn('Failed to parse raw_json in tryParseJsonLog:', e);
      }
    }
    
    // If log contains safety analysis text, it's also useful for display
    if (typeof log === 'string' && 
        (log.includes('ͼ�����尲ȫ���շ�����ܿؽ���?) || 
         log.includes('��ȫ����ܽ�?) || 
         log.includes('�ܿش�ʩ������'))) {
      return true;
    }
    
    return false;
  } catch (e) {
    console.warn('Error in tryParseJsonLog:', e);
    
    // As a fallback, check if the log has safety analysis text
    if (typeof log === 'string' && 
        (log.includes('ͼ�����尲ȫ���շ�����ܿؽ���?) || 
         log.includes('��ȫ����ܽ�?) || 
         log.includes('�ܿش�ʩ������'))) {
      return true;
    }
    
    return false;
  }
};

const getDetectionCount = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    return (parsed.detections || []).length;
  } catch (e) {
    return 0;
  }
};

const getDetections = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    return parsed.detections || [];
  } catch (e) {
    return [];
  }
};

const getSafetyAnalysis = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    
    // ���ȳ��Դ�description�ֶλ�ȡ
    if (parsed.description) {
      return parsed.description;
    }
    
    // Ȼ���ԴӸ��ֿ��ܵ��ֶ��л�ȡ
    if (parsed.safety_analysis || parsed.recommendations || 
        parsed.image_overall_safety_analysis_and_control_recommendations) {
      return parsed.safety_analysis || parsed.recommendations || 
             parsed.image_overall_safety_analysis_and_control_recommendations;
    }
    
    // ����Դ�raw_json�в���
    if (parsed.raw_json) {
      try {
        const rawData = typeof parsed.raw_json === 'string' ? JSON.parse(parsed.raw_json) : parsed.raw_json;
        if (rawData.image_overall_safety_analysis_and_control_recommendations) {
          return rawData.image_overall_safety_analysis_and_control_recommendations;
        }
      } catch (e) {
        console.error('����raw_jsonʧ��', e);
      }
    }
    
    return '';
  } catch (e) {
    return '';
  }
};

const getRiskClass = (riskLevel: string) => {
  if (!riskLevel) return '';
  
  const level = riskLevel.toLowerCase();
  if (level.includes('��') || level.includes('high')) return 'high-risk';
  if (level.includes('��') || level.includes('medium')) return 'medium-risk';
  if (level.includes('��') || level.includes('low')) return 'low-risk';
  return '';
};

// Add new state for handling log visualization
const canShowLogOnVideo = computed(() => apiLogs.length > 0 && videoPlayerElement.value && detectionCanvasElement.value);
const currentlyDisplayedLog = ref<any>(null);

// Function to check if a log contains detection data with bounding boxes
const hasDetectionData = (log: string): boolean => {
  try {
    const parsed = parseJson(log);
    
    // Check for detections with bbox data
    if (parsed.detections && Array.isArray(parsed.detections)) {
      return parsed.detections.some((d: any) => d.bbox || d.bbox_2d);
    }
    
    // Also check raw_json field if it exists
    if (parsed.raw_json) {
      try {
        const rawData = typeof parsed.raw_json === 'string' ? JSON.parse(parsed.raw_json) : parsed.raw_json;
        // Check for any objects with bbox data
        for (const key in rawData) {
          if (Array.isArray(rawData[key])) {
            if (rawData[key].some((item: any) => item.bbox_2d && item.bbox_2d.length === 4)) {
              return true;
            }
          }
        }
      } catch (e) {
        console.error('Failed to parse raw_json:', e);
      }
    }
    
    return false;
  } catch (e) {
    return false;
  }
};

// Display the latest log on the video
const showLatestLogOnVideo = () => {
  if (apiLogs.length > 0) {
    showLogOnVideo(apiLogs[0]);
  }
};

// Display any log on the video
const showLogOnVideo = (log: string) => {
  try {
    const parsedLog = parseJson(log);
    currentlyDisplayedLog.value = parsedLog;
    
    // Draw bounding boxes from log data
    drawLogDetectionBoxes(parsedLog);
    
    // If the log contains text description, display it with large font
    const description = getSafetyAnalysis(log);
    if (description) {
      displayDescriptionOnVideo(description);
    }
  } catch (e) {
    console.error('Failed to show log on video:', e);
  }
};

// Draw bounding boxes from log data
const drawLogDetectionBoxes = (parsedLog: any) => {
  if (!detectionCanvasElement.value || !videoPlayerElement.value) return;
  
  const canvas = detectionCanvasElement.value;
  const video = videoPlayerElement.value;
  
  // Ensure canvas matches video dimensions
  if (canvas.width !== video.videoWidth || canvas.height !== video.videoHeight) {
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  }
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Clear existing content
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  let detections: any[] = [];
  
  // Extract detections from the log
  if (parsedLog.detections && Array.isArray(parsedLog.detections)) {
    detections = parsedLog.detections;
  }
  
  // Also check raw_json if available
  if (parsedLog.raw_json) {
    try {
      const rawData = typeof parsedLog.raw_json === 'string' ? JSON.parse(parsedLog.raw_json) : parsedLog.raw_json;
      // Extract detections from various categories
      for (const key in rawData) {
        if (Array.isArray(rawData[key])) {
          rawData[key].forEach((item: any) => {
            if (item.bbox_2d && item.bbox_2d.length === 4) {
              detections.push({
                ...item,
                category: item.category || key,
                label: item.category || key
              });
            }
          });
        }
      }
    } catch (e) {
      console.error('Failed to parse raw_json in drawLogDetectionBoxes:', e);
    }
  }
  
  // Draw each detection
  detections.forEach(detection => {
    const bbox = detection.bbox_2d || detection.bbox;
    if (!bbox || bbox.length !== 4) return;
    
    // Get coordinates normalized to canvas size
    const [x1, y1, x2, y2] = bbox;
    const sourceWidth = parsedLog.input_width || video.videoWidth;
    const sourceHeight = parsedLog.input_height || video.videoHeight;
    
    const drawX1 = (x1 / sourceWidth) * canvas.width;
    const drawY1 = (y1 / sourceHeight) * canvas.height;
    const boxWidth = ((x2 - x1) / sourceWidth) * canvas.width;
    const boxHeight = ((y2 - y1) / sourceHeight) * canvas.height;
    
    // Determine color based on risk level or category
    let color = 'rgba(128, 128, 128, 0.5)'; // Default gray
    let borderColor = 'gray';
    let lineDash: number[] = [];
    
    if (detection.risk_level) {
      const riskLevel = detection.risk_level.toLowerCase();
      if (riskLevel.includes('��') || riskLevel.includes('high')) {
        color = 'rgba(255, 0, 0, 0.3)'; borderColor = 'red'; lineDash = [5,3];
      } else if (riskLevel.includes('��') || riskLevel.includes('medium')) {
        color = 'rgba(255, 165, 0, 0.3)'; borderColor = 'orange';
      } else if (riskLevel.includes('��') || riskLevel.includes('low')) {
        color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
      }
    } else if (detection.category) {
      const category = detection.category.toLowerCase();
      if (category.includes('��') || category.includes('person')) {
        color = 'rgba(0, 0, 255, 0.3)'; borderColor = 'blue';
      } else if (category.includes('��') || category.includes('vehicle')) {
        color = 'rgba(0, 255, 0, 0.3)'; borderColor = 'green';
      } else if (category.includes('��е') || category.includes('�豸') || category.includes('machine')) {
        color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
      }
    }
    
    // Draw rectangle
    ctx.strokeStyle = borderColor;
    ctx.fillStyle = color;
    ctx.lineWidth = 3; // Thicker lines for visibility
    ctx.setLineDash(lineDash);
    ctx.beginPath();
    ctx.rect(drawX1, drawY1, boxWidth, boxHeight);
    ctx.stroke();
    ctx.fill();
    ctx.setLineDash([]);
    
    // Draw label with larger font
    const label = detection.label || detection.event || detection.category || 'δ֪';
    ctx.fillStyle = borderColor;
    ctx.font = 'bold 16px Arial'; // Larger font
    const textMetrics = ctx.measureText(label);
    ctx.fillRect(drawX1, drawY1 - 20, textMetrics.width + 6, 20);
    ctx.fillStyle = 'white';
    ctx.fillText(label, drawX1 + 3, drawY1 - 5);
    
    // Add confidence if available
    if (detection.confidence && typeof detection.confidence === 'number') {
      const confidenceText = `${Math.round(detection.confidence * 100)}%`;
      ctx.font = '14px Arial';
      ctx.fillStyle = 'white';
      ctx.fillText(confidenceText, drawX1 + 3, drawY1 - 25);
    }
  });
};

// Display description text on video with large font
const displayDescriptionOnVideo = (description: string) => {
  if (!detectionCanvasElement.value) return;
  
  const canvas = detectionCanvasElement.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Create a semi-transparent overlay for text
  ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
  ctx.fillRect(0, canvas.height - 150, canvas.width, 150);
  
  // Display text with larger font
  ctx.font = 'bold 18px Arial';
  ctx.fillStyle = 'white';
  
  // Truncate and format description to fit
  const maxLength = 150;
  let displayText = description.length > maxLength ? 
    description.substring(0, maxLength) + '...' : description;
  
  // Remove markdown formatting
  displayText = displayText.replace(/[*#_]/g, '');
  
  // Wrap text
  const words = displayText.split(' ');
  let line = '';
  let y = canvas.height - 120;
  
  words.forEach(word => {
    const testLine = line + word + ' ';
    const metrics = ctx.measureText(testLine);
    
    if (metrics.width > canvas.width - 40) {
      ctx.fillText(line, 20, y);
      line = word + ' ';
      y += 25;
    } else {
      line = testLine;
    }
  });
  
  ctx.fillText(line, 20, y);
};

// ����һ����������ȫ������ʾ���Ҳ������?const showSafetyInPanel = (log: string) => {
  try {
    const safetyAnalysis = getSafetyAnalysis(log);
    if (safetyAnalysis) {
      showSafetyRecommendation(safetyAnalysis);
      
      // ����һ����ʾ�ɹ�����ʾ
      const apiLogItem = document.querySelector('.api-log-item');
      if (apiLogItem) {
        const successIndicator = document.createElement('div');
        successIndicator.className = 'push-success-indicator';
        successIndicator.textContent = '? �����͵��Ҳ����?;
        apiLogItem.appendChild(successIndicator);
        
        // 2��󵭳�?        setTimeout(() => {
          if (successIndicator && successIndicator.parentNode) {
            successIndicator.style.opacity = '0';
            setTimeout(() => {
              if (successIndicator.parentNode) {
                successIndicator.parentNode.removeChild(successIndicator);
              }
            }, 500);
          }
        }, 2000);
      }
    }
  } catch (e) {
    console.error('��ʾ��ȫ����ʧ��:', e);
  }
};

// ע�⣺��ȫ������ȡ���������Ѿ������Ϸ�����ǿʵ����

// Add this function after toggleDelayedPlayback
const toggleYoloDetection = () => {
  useYoloDetection.value = !useYoloDetection.value;
  
  if (useYoloDetection.value) {
    // ����YOLO���?    console.log('����YOLO11x-seg��⣬������Ƶ���ӻ�?);
    addApiLog(`INFO:qwen-vl-api:������YOLO��⣬ʹ��yolo11n-seg.ptģ�ͽ���ʵʱ��Ƶ���ӻ�`);
    
    // ��ʼ��WebSocket����
    initializeWebSocket();
    
    // �����Ƶ���ڲ��ţ�������ʼYOLO���?    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // ��ֹͣ��ǰ�ļ��?      if (frameProcessingInterval) {
        clearInterval(frameProcessingInterval);
        frameProcessingInterval = null;
      }
      
      // ����YOLO��⴦��?      handlePlay();
    }
  } else {
    // ����YOLO���?    console.log('����YOLO���?);
    addApiLog(`INFO:qwen-vl-api:�ѽ���YOLO���`);
    
    // �ر�WebSocket����
    if (wsConnection) {
      wsConnection.close();
      wsConnection = null;
    }
    
    // ���Canvas�ϵļ���
    if (detectionCanvasElement.value) {
      const ctx = detectionCanvasElement.value.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
      }
    }
    
    // ֹͣ֡�������?    if (frameProcessingInterval) {
      clearInterval(frameProcessingInterval);
      frameProcessingInterval = null;
    }
  }
};

// Sample Qwen API results demo function
const loadSampleQwenResults = () => {
  // Sample data from the example in the user query
  const sampleJson = `{
    "detections": [
      {
        "category": "���� (boat)",
        "event_description": "ͼ����δ������ʾ��ֻ����ϵͳ��⵽���ܵĴ���ṹ��",
        "risk_level": "high",
        "confidence_score": 0.12,
        "bbox_2d": [0, 0, 1316, 728]
      }
    ],
    "description": "�ó���չʾ��һ��ҹ��ʩ�����أ�����Ϊһ�����͸ֽṹ����µ�ʩ���ֳ����������ж��������Դ���ṩ�˱�Ҫ�������������������˷������ϣ���Χ���л��������ӣ�������Ա��ȫͨ�С������пɼ�һЩ��е�豸�ͽ������ϣ����廷���Ե����򵫸��ӡ����ܱ�ע��'���� (boat)'������ͼ��������������δֱ����ʾ��ֻ�Ĵ��ڣ�������������ʵ�ʳ��������ļ������?,
    "high_risk_events": [
      {
        "description": "ϵͳ���������壨����?���� (boat)'���Ĵ��ڣ����ܵ��·����󵼡�",
        "mitigation": "���鸴�˼���㷨��ȷ���������Ƹ��ӳ����е�׼ȷ�ԺͿɿ��ԡ�ͬʱ���ֳ�������ԱӦ���ڼ�飬ȷ����Ǳ��Σ������ڡ�?
      }
    ],
    "low_risk_events": [
      {
        "description": "ҹ��ʩ���������㣬����ע��ƹ�Թ��������ĳ���Ӱ�켰��Դ���ġ�",
        "mitigation": "���ý����һ��۵������豸�������滮�������֣����ٹ���Ⱦ����Դ�˷ѡ�"
      },
      {
        "description": "ʩ���ֳ���������������ϣ������ڷ����ͱ������棬������ע����ϵ��ȹ��ԡ�",
        "mitigation": "���ڼ��������ϵ�״̬��ȷ����ƽ���������𣬼�ʱ�����𻵲��֡�"
      }
    ]
  }`;
  
  try {
    qwenResults.value = JSON.parse(sampleJson);
    console.log('���س�ʼQwen API���?);
    lastUpdated.value = new Date();
    
    // ���ӵ�API��־��չʾ
    addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
    addApiLog(`INFO:yolo_video_processor:Qwen API�������? ${sampleJson}`);
    
    // ģ��10����ʵʱ����
    setTimeout(() => {
      console.log('ģ��10����Qwen API����');
      
      // ���º�Ľ������
      const updatedJson = `{
        "detections": [
          {
            "category": "��Ա",
            "event_description": "��ҵ���Ʋ��淶",
            "risk_level": "medium",
            "confidence_score": 0.85,
            "bbox_2d": [150, 60, 210, 180]
          },
          {
            "category": "��е",
            "event_description": "���ػ����۰뾶������Ա",
            "risk_level": "high",
            "confidence_score": 0.92,
            "bbox_2d": [320, 180, 450, 260]
          }
        ],
        "description": "�ó���չʾ��һ��ҹ��ʩ�����أ�����Ϊһ�����͸ֽṹ����µ�ʩ���ֳ����������ж��������Դ���ṩ�˱�Ҫ�������������������˷������ϣ���Χ���л��������ӣ�������Ա��ȫͨ�С������пɼ�һЩ��е�豸���������ڲ�����Ա�����ػ����۰뾶�ڻ�������ش�ȫ������",
        "high_risk_events": [
          {
            "description": "���ػ������뾶������Ա�",
            "mitigation": "��������Ա����Σ���������þ����߲�����ר�˼ල��"
          }
        ],
        "low_risk_events": [
          {
            "description": "������ҵ���Ʋ��淶",
            "mitigation": "�Թ��˽�����ȷ��ҵ������ѵ�����ⷢ��Ť�˵�ְҵ�˺���"
          }
        ]
      }`;
      
      // ��֪ͨAPI���سɹ�
      addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
      
      // Ȼ�����ӷ������������UI����
      setTimeout(() => {
        addApiLog(`INFO:yolo_video_processor:Qwen API�������? ${updatedJson}`);
      }, 500);
    }, 10000);
    
  } catch (error) {
    console.error('��������JSONʧ��:', error);
  }
};

// �� data ���������µ�״̬����
const isInitializing = ref(false);
const initializationTimer = ref<number | null>(null);

// �� mounted ���Ӻ�����һ����ʼ������
const initializeMonitoring = async () => {
  // ��ʾ��ʼ��״̬
  isInitializing.value = true;
  addApiLog(`INFO:qwen-vl-api:���ڳ�ʼ�����ϵ�?..`);
  
  // �ȴ�1���ӣ������ʱ���ʼ��YOLO
  await new Promise(resolve => {
    initializationTimer.value = window.setTimeout(() => {
      resolve(true);
      initializationTimer.value = null;
    }, 1000);
  });
  
  // ��ʼ��YOLO����
  useYoloDetection.value = true;
  initializeWebSocket();
  
  // ��ʼ����ɺ�ʼ����?  isInitializing.value = false;
  addApiLog(`INFO:qwen-vl-api:���ϵͳ��ʼ����ɣ���ʼʵʱ����`);
  
  // ��ʼ��Ƶ����
  if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
    handlePlay();
  } else if (videoPlayerElement.value) {
    videoPlayerElement.value.play().catch(err => {
      console.error('�޷��Զ�������Ƶ:', err);
      addApiLog(`WARN:qwen-vl-api:��Ƶ�Զ����ű��������ֹ��������Ƶ�����ֶ�����`);
    });
  }
};

// �޸� handlePlay �������Ż���������
const handlePlay = () => {
  // ������ڳ�ʼ�����û���ȷֹͣ��أ�����������
  if (isInitializing.value || !isMonitoringActive.value) {
    return;
  }
  
  // ������е�֡�����������ֹ�ظ�����
  if (frameProcessingInterval) {
    clearInterval(frameProcessingInterval);
    frameProcessingInterval = null;
  }
  
  // ȷ��WebSocket�����ѽ���������YOLO���ӻ���
  if (useYoloDetection.value && (!wsConnection || wsConnection.readyState !== WebSocket.OPEN)) {
    initializeWebSocket();
  }
  
  // ����YOLO����������ʹ��captureAndProcessFrame������WebSocket�ص����Զ�������һ֡
  if (useYoloDetection.value && wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    // ������һ֡��YOLO����������֡����WebSocket�ص��м�������
    captureAndProcessFrame();
  }
  
  // ����֡��������ͬʱ����API���ú�YOLO����
  const captureRate = 5; // ÿ��5֡
  frameProcessingInterval = window.setInterval(() => {
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // ����ǰ֡
      const frameDataUrl = captureFrame();
      if (!frameDataUrl) return;
      
      // 1. ����֡��YOLO�����������WebSocket�Ѵ򿪣�
      if (useYoloDetection.value && wsConnection && wsConnection.readyState === WebSocket.OPEN) {
        try {
          // ��ȡbase64����
          const base64Data = frameDataUrl.split(',')[1];
          
          // ����֡ID��ʱ���?          const frameId = `frame_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
          const timestamp = Date.now();
          
          // ���͵�WebSocket����
          wsConnection.send(JSON.stringify({
            frame: base64Data,
            frameId: frameId,
            timestamp: timestamp,
            config: {
              segmentation: true,
              confidence: 0.25,
              process_with_qwen: true // ���ߺ��ͬʱ����Qwen API
            }
          }));
        } catch (error) {
          console.error('����֡��WebSocketʧ��:', error);
        }
      }
      
      // 2. ���е��ñ�׼API���м��?      if (!isLoading.value) {
        console.log('����ִ�б�׼API���?);
        isLoading.value = true;
        
        // ʹ��Promiseִ��API���õ����ȴ����?        performStandardDetection(frameDataUrl)
          .then(results => {
            console.log('��׼API������ - ��ȫ����');
            detectionResults.value = results;
            
            // ��ʾ��ȫ����������Ҳ����
            if (results && results.description) {
              showSafetyRecommendation(results.description);
            }
            
            // ����API��־
            const logEntry = {
              success: true,
              description: results.description || '',
              high_risk_events: results.high_risk_events || [],
              low_risk_events: results.low_risk_events || []
            };
            addApiLog(`INFO:qwen-vl-api:���ɹ������ؽ��? ${JSON.stringify(logEntry)}`);
            
            // ���û����ʹ��YOLO WebSocket������¼���?            if (!useYoloDetection.value || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
              handleProcessedImage(results);
            }
          })
          .catch(err => {
            console.error('��׼API���ʧ��?', err);
          })
          .finally(() => {
            isLoading.value = false;
          });
      }
    }
  }, 1000 / captureRate);
  
  console.log(`��������Ƶ������֡��: ${captureRate} FPS��${useYoloDetection.value ? 'ʹ��YOLO���ӻ�' : '��׼����'}`);
};

// �� onMounted �������޸ĳ�ʼ���߼�
onMounted(() => {
  // Initialize YS7 video player
  initializeYs7Player();
  
  // ����һ��ע�͵�����initializeMonitoring����������
  // selectCategory('all');
  
  // Ĭ��ѡ�� 'all' ��𣬵���������ʼ�����ȴ��û�����л�
  selectedCategory.value = 'all';
  detectionOptions.value.forEach(detCat => {
    detCat.options.forEach(opt => {
      const category = riskCategories.find(cat => cat.id === 'all');
      if (category) {
        opt.checked = category.options.includes(opt.id);
      }
    });
  });
  
  // Event listeners for video are added in the template now
  
  // Add a sample log for demonstration
  addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
  setTimeout(() => {
    addApiLog(`INFO:yolo_video_processor:Qwen API�������? {'success': True, 'detections': [{'category': '��', 'event_description': '', 'risk_level': '�з���', 'confidence_score': 0.7, 'bbox_2d': [654, 589, 673, 617], 'display_label': '', 'event': '��', 'confidence': 0.8, 'label': '��'}], 'description': '����������ֵ����⣺\n\n* ��ʩ���ֳ�Ӧ�ϸ����ذ�ȫ�����涨�������ֳ�������Ա��ǿ��ȫ������ѵ��\n* ����ʱ������ȷ������˷������?�����������ڰ�ȫñ)�����������⵼���˺��¹ʵķ����� \n* ʩ�����������ǿ�����ػ�е�豸�Ĳ���������ά���������ȣ�������Χ�������Եľ����ʶ���ѹ�������ע����÷�ֹ������ײ���߱����˵��������;\n * ��ǿ�ճ�Ѳ�����ƶ���ʵ��λ��ʱ������ȫ����Ԥ���¹ʷ���;  \n   \n���ϴ�ʩ�����ڽ��͸��లȫ�¹ʷ����ĸ��ʴӶ�����Ա�������Ʋ���ȫ�Լ��ٽ���Ŀ˳���ƽ����? \n\n��ע��ʵ�ʹ����п��ܴ��ڸ���ϸ����Ҫ��һ��ȷ�Ͼ��������������Դ�����', 'high_risk_events': [{"description": "����δ�����ȫñ���?, "mitigation": "����Ҫ��ù��������ȫñ����ǿ��ȫ�������ֳ�Ѳ�顣"}], 'low_risk_events': [], 'raw_json': '{\n    "person": [\n        {\n            "category": "��",\n            "event_description": "",\n            "risk_level": "�з���",\n            "confidence_score": 0.7,\n            "bbox_2d": [654, 589, 673, 617],\n            "display_label": ""\n        }\n    ],\n    "vehicle": [],\n    "construction_machinery": [\n        {\n            "category": "���ػ�",\n            "event_description": "",\n            "risk_level": "�޷���",\n            "confidence_score": 0.9,\n            "bbox_2d": [-1,-1 , -1 ,-1]\n        },\n        {\n            "category": "����������е",\n            "event_description": "",\n            "risk_level": "�޷���",\n            "confidence_score": 0.9,\n            "bbox_2d": []\n        }\n    ],\n    "crane_operation_status": {},\n    "road_base_condition": {},\n    "slope_stability": {},\n    "pavement_condition": {},\n    "hard_isolation_and_protection_barriers": {},\n    "fire_hazard": {},\n    "smoke_detection": {}\n}', 'input_width': 1664, 'input_height': 928, 'image_path': 'static\\uploads\\9f19d038-ccbd-4ca1-a02c-3f948d6f66f6.jpg'}`);
  }, 500);
  
  // Start regular API log polling
  startLogPolling();
  
  // Start active polling specifically for Qwen API results
  const activePollingInterval = startActiveLogPolling();
  
  // Also start dedicated polling for Qwen API results
  const qwenApiResultsPollingInterval = startQwenApiResultsPolling();
  
  // Load sample Qwen results
  loadSampleQwenResults();
  
  // Clean up polling when component is unmounted
  onUnmounted(() => {
    if (activePollingInterval) {
      clearInterval(activePollingInterval);
    }
    if (qwenApiResultsPollingInterval) {
      clearInterval(qwenApiResultsPollingInterval);
    }
    
    // �����ʼ����ʱ��?    if (initializationTimer.value) {
      clearTimeout(initializationTimer.value);
      initializationTimer.value = null;
    }
  });
});

// �޸� captureAndProcessFrame �������Ż�֡����
const captureAndProcessFrame = () => {
  if (!videoPlayerElement.value || videoPlayerElement.value.paused || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
    return;
  }
  
  try {
    // Ensure canvas is correctly sized to match video dimensions before capturing
    if (detectionCanvasElement.value) {
      const video = videoPlayerElement.value;
      if (detectionCanvasElement.value.width !== video.videoWidth || 
          detectionCanvasElement.value.height !== video.videoHeight) {
        detectionCanvasElement.value.width = video.videoWidth || 640;
        detectionCanvasElement.value.height = video.videoHeight || 360;
        console.log(`Resized detection canvas to: ${detectionCanvasElement.value.width}x${detectionCanvasElement.value.height}`);
      }
    }

    // Capture current video frame with improved quality
    const frameDataUrl = captureFrame();
    if (!frameDataUrl) return;
    
    // Generate frame ID and timestamp
    frameCounter++;
    const frameId = `frame_${Date.now()}_${frameCounter}`;
    const timestamp = Date.now();
    
    // Send to WebSocket with additional parameters to ensure mask visualization
    wsConnection.send(JSON.stringify({
      frame: frameDataUrl.split(',')[1], // Only send Base64 part
      frameId: frameId,
      timestamp: timestamp,
      config: {
        segmentation: true, 
        confidence: 0.25,
        process_with_qwen: true, // Tell backend to also process with Qwen API
        visualize: true,         // Ensure visualization is enabled
        show_masks: true,        // Explicitly request masks to be shown
        img_size: 640            // Consistent image size for processing
      }
    }));
    
    // Schedule next frame capture with requestAnimationFrame for better performance
    // Add adaptive frame rate based on system performance
    const nextFrameDelay = frameCounter % 3 === 0 ? 200 : 100; // Alternate between faster/slower capture
    
    setTimeout(() => {
      if (videoPlayerElement.value && !videoPlayerElement.value.paused && 
          wsConnection && wsConnection.readyState === WebSocket.OPEN) {
        requestAnimationFrame(captureAndProcessFrame);
      }
    }, nextFrameDelay);
  } catch (error) {
    console.error('Error processing video frame:', error);
    // Attempt recovery after error
    setTimeout(() => {
      if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
        captureAndProcessFrame();
      }
    }, 1000);
  }
};

// ��Template�������ӳ�ʼ��״ָ̬ʾ��
<template>
  <div class="online-monitor-page">
    // ... ���д��� ...
    <!-- Middle - Video and Detection Controls -->
    <div class="video-container">
      <div class="card-container">
        <div class="card-header">
          <span class="card-title">
            өʯ�Ƽ��?          </span>
          <div class="card-tools">
            <!-- ���ӳ�ʼ��״ָ̬ʾ�� -->
            <span v-if="isInitializing" class="initializing-indicator">
              <span class="loading-spinner"></span>
              ��ʼ����...
            </span>
            <button class="toggle-button hover-lift" @click="toggleMonitoring" v-if="videoPlayerElement && !videoPlayerElement.paused">
              {{ isMonitoringActive ? 'ֹͣ���? : '�ָ����? }}
            </button>
            // ... ������ť ...
          </div>
        </div>
        // ... �������� ...
      </div>
    </div>
    // ... �������� ...
  </div>
</template>

// ����ʽ�������ӳ�ʼ��״̬��ʽ
<style scoped>
// ... ������ʽ ...

.initializing-indicator {
  display: flex;
  align-items: center;
  background-color: rgba(24, 144, 255, 0.2);
  color: var(--primary-color, #1890ff);
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 10px;
  animation: fadeInOut 1.5s ease-in-out infinite;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid rgba(24, 144, 255, 0.3);
  border-top-color: var(--primary-color, #1890ff);
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes fadeInOut {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ... ������ʽ ...
</style>

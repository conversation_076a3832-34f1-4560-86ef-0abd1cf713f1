<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时YOLO视频处理演示</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .video-container {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        .video-box {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .video-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        video, canvas {
            border: 1px solid #ddd;
            max-width: 100%;
            max-height: 400px;
            background-color: #000;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .info-panel {
            width: 100%;
            margin-top: 20px;
        }
        .info-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .info-content {
            background-color: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 150px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
        }
        .error {
            background-color: #ffdddd;
            border-left: 6px solid #f44336;
        }
        .settings {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            max-width: 600px;
        }
        .settings-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .settings-label {
            min-width: 150px;
            margin-right: 10px;
        }
        select, input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            flex-grow: 1;
        }
        .stats {
            margin-top: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>实时YOLO视频处理演示</h1>
        
        <div class="settings">
            <div class="settings-row">
                <span class="settings-label">视频源：</span>
                <select id="videoSource">
                    <option value="camera">摄像头</option>
                    <option value="file">本地视频文件</option>
                </select>
            </div>
            <div id="fileSourceControls" style="display:none" class="settings-row">
                <span class="settings-label">选择视频文件：</span>
                <input type="file" id="videoFile" accept="video/*">
            </div>
            <div id="cameraSourceControls" class="settings-row">
                <span class="settings-label">选择摄像头：</span>
                <select id="cameraSelect">
                    <option value="">加载中...</option>
                </select>
            </div>
            <div class="settings-row">
                <span class="settings-label">处理帧率：</span>
                <select id="frameRate">
                    <option value="30">30 FPS</option>
                    <option value="15" selected>15 FPS</option>
                    <option value="10">10 FPS</option>
                    <option value="5">5 FPS</option>
                </select>
            </div>
            <div class="settings-row">
                <span class="settings-label">图像质量：</span>
                <select id="imageQuality">
                    <option value="1.0">100%</option>
                    <option value="0.8" selected>80%</option>
                    <option value="0.6">60%</option>
                    <option value="0.4">40%</option>
                </select>
            </div>
            <div class="settings-row">
                <span class="settings-label">图像尺寸：</span>
                <select id="imageSize">
                    <option value="1.0">100%</option>
                    <option value="0.75" selected>75%</option>
                    <option value="0.5">50%</option>
                </select>
            </div>
        </div>
        
        <div class="video-container">
            <div class="video-box">
                <div class="video-title">原始视频</div>
                <video id="videoInput" autoplay playsinline muted></video>
            </div>
            <div class="video-box">
                <div class="video-title">处理后视频</div>
                <canvas id="outputCanvas"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button id="startBtn">开始处理</button>
            <button id="stopBtn" disabled>停止处理</button>
        </div>
        
        <div class="status" id="status">
            状态: 就绪
        </div>
        
        <div class="stats" id="stats">
            处理统计: 未开始
        </div>
        
        <div class="info-panel">
            <div class="info-title">检测结果:</div>
            <div class="info-content" id="resultInfo">
                未开始检测
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let videoElement = document.getElementById('videoInput');
        let outputCanvas = document.getElementById('outputCanvas');
        let ctx = outputCanvas.getContext('2d');
        let startButton = document.getElementById('startBtn');
        let stopButton = document.getElementById('stopBtn');
        let statusElement = document.getElementById('status');
        let statsElement = document.getElementById('stats');
        let resultInfoElement = document.getElementById('resultInfo');
        let videoSourceSelect = document.getElementById('videoSource');
        let cameraSelect = document.getElementById('cameraSelect');
        let frameRateSelect = document.getElementById('frameRate');
        let imageQualitySelect = document.getElementById('imageQuality');
        let imageSizeSelect = document.getElementById('imageSize');
        let videoFileInput = document.getElementById('videoFile');
        let fileSourceControls = document.getElementById('fileSourceControls');
        let cameraSourceControls = document.getElementById('cameraSourceControls');
        
        // WebSocket 连接
        let ws = null;
        let isRunning = false;
        let frameCount = 0;
        let processedCount = 0;
        let lastFrameTime = 0;
        let lastStatsUpdate = 0;
        let framerateInterval = 1000 / parseInt(frameRateSelect.value);
        
        // 性能指标
        let totalLatency = 0;
        let maxLatency = 0;
        let minLatency = Infinity;
        let framesSent = 0;
        let framesReceived = 0;
        
        // 初始化函数
        async function init() {
            try {
                // 加载可用摄像头
                await loadCameras();
                
                // 设置视频源切换事件
                videoSourceSelect.addEventListener('change', function() {
                    if (this.value === 'file') {
                        fileSourceControls.style.display = 'flex';
                        cameraSourceControls.style.display = 'none';
                    } else {
                        fileSourceControls.style.display = 'none';
                        cameraSourceControls.style.display = 'flex';
                    }
                });
                
                // 设置视频文件选择事件
                videoFileInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        videoElement.src = URL.createObjectURL(this.files[0]);
                        videoElement.load();
                    }
                });
                
                // 帧率选择事件
                frameRateSelect.addEventListener('change', function() {
                    framerateInterval = 1000 / parseInt(this.value);
                });
                
                // 设置开始按钮事件
                startButton.addEventListener('click', async function() {
                    if (isRunning) return;
                    
                    try {
                        if (videoSourceSelect.value === 'camera') {
                            // 启动摄像头
                            const constraints = {
                                video: {
                                    deviceId: cameraSelect.value ? { exact: cameraSelect.value } : undefined
                                }
                            };
                            
                            const stream = await navigator.mediaDevices.getUserMedia(constraints);
                            videoElement.srcObject = stream;
                        } else {
                            // 确保视频文件已选择
                            if (!videoFileInput.files || !videoFileInput.files[0]) {
                                alert('请先选择视频文件');
                                return;
                            }
                            
                            // 视频文件已通过事件处理程序设置
                            videoElement.play();
                        }
                        
                        // 等待视频元素准备就绪
                        await new Promise(resolve => {
                            if (videoElement.readyState >= 3) {
                                resolve();
                            } else {
                                videoElement.oncanplay = () => resolve();
                            }
                        });
                        
                        // 设置画布尺寸
                        outputCanvas.width = videoElement.videoWidth;
                        outputCanvas.height = videoElement.videoHeight;
                        
                        // 创建WebSocket连接
                        connectWebSocket();
                        
                        // 更新界面状态
                        isRunning = true;
                        startButton.disabled = true;
                        stopButton.disabled = false;
                        updateStatus('连接中...', false);
                        
                        // 开始处理视频帧
                        processVideo();
                    } catch (error) {
                        console.error('启动失败:', error);
                        updateStatus('启动失败: ' + error.message, true);
                    }
                });
                
                // 设置停止按钮事件
                stopButton.addEventListener('click', function() {
                    stopProcessing();
                });
            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('初始化失败: ' + error.message, true);
            }
        }
        
        // 加载可用摄像头列表
        async function loadCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                
                // 清空选择列表
                cameraSelect.innerHTML = '';
                
                if (videoDevices.length === 0) {
                    const option = document.createElement('option');
                    option.text = '未检测到摄像头';
                    option.value = '';
                    cameraSelect.add(option);
                } else {
                    videoDevices.forEach(device => {
                        const option = document.createElement('option');
                        option.text = device.label || `摄像头 ${cameraSelect.length + 1}`;
                        option.value = device.deviceId;
                        cameraSelect.add(option);
                    });
                }
            } catch (error) {
                console.error('获取摄像头列表失败:', error);
                updateStatus('获取摄像头列表失败: ' + error.message, true);
            }
        }
        
        // 创建WebSocket连接
        function connectWebSocket() {
            // 关闭之前的连接
            if (ws) {
                ws.close();
            }
            
            // 创建新连接
            const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${location.host}/ws/yolo-video-process`;
            
            ws = new WebSocket(wsUrl);
            
            // WebSocket 事件处理
            ws.onopen = function() {
                updateStatus('WebSocket 已连接，开始处理...', false);
            };
            
            ws.onmessage = function(event) {
                processedCount++;
                const now = performance.now();
                
                try {
                    const response = JSON.parse(event.data);
                    
                    if (response.error) {
                        console.error('服务器错误:', response.error);
                        updateStatus('服务器错误: ' + response.error, true);
                        return;
                    }
                    
                    // 显示处理后的帧
                    if (response.processed_frame) {
                        const img = new Image();
                        img.onload = function() {
                            ctx.drawImage(img, 0, 0, outputCanvas.width, outputCanvas.height);
                            
                            // 在画布上绘制检测框
                            if (response.objects && response.objects.length > 0) {
                                drawDetections(response.objects);
                            }
                        };
                        img.src = 'data:image/jpeg;base64,' + response.processed_frame;
                    }
                    
                    // 更新结果信息
                    updateResultInfo(response);
                    
                    // 计算延迟
                    if (response.timestamp) {
                        const latency = now - response.timestamp;
                        totalLatency += latency;
                        maxLatency = Math.max(maxLatency, latency);
                        minLatency = Math.min(minLatency, latency);
                        framesReceived++;
                        
                        // 每秒更新统计信息
                        if (now - lastStatsUpdate > 1000) {
                            updateStats();
                            lastStatsUpdate = now;
                        }
                    }
                } catch (error) {
                    console.error('处理服务器响应时出错:', error);
                }
            };
            
            ws.onclose = function() {
                if (isRunning) {
                    updateStatus('WebSocket 连接关闭', false);
                    stopProcessing();
                }
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket 错误:', error);
                updateStatus('WebSocket 错误', true);
                stopProcessing();
            };
        }
        
        // 捕获并发送视频帧
        async function processVideo() {
            if (!isRunning) return;
            
            const now = performance.now();
            const elapsed = now - lastFrameTime;
            
            // 按照设定的帧率处理
            if (elapsed >= framerateInterval) {
                lastFrameTime = now;
                
                try {
                    // 从视频元素捕获帧
                    if (videoElement.readyState === videoElement.HAVE_ENOUGH_DATA) {
                        // 创建临时画布调整大小和质量
                        const tempCanvas = document.createElement('canvas');
                        const scale = parseFloat(imageSizeSelect.value);
                        tempCanvas.width = videoElement.videoWidth * scale;
                        tempCanvas.height = videoElement.videoHeight * scale;
                        
                        const tempCtx = tempCanvas.getContext('2d');
                        tempCtx.drawImage(videoElement, 0, 0, tempCanvas.width, tempCanvas.height);
                        
                        // 转换为JPEG并调整质量
                        const quality = parseFloat(imageQualitySelect.value);
                        const dataURL = tempCanvas.toDataURL('image/jpeg', quality);
                        const base64Data = dataURL.split(',')[1];
                        
                        // 构造消息并发送
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            const message = {
                                frame: base64Data,
                                timestamp: now,
                                frameId: frameCount
                            };
                            
                            ws.send(JSON.stringify(message));
                            framesSent++;
                            frameCount++;
                        }
                    }
                } catch (error) {
                    console.error('处理视频帧时出错:', error);
                }
            }
            
            // 继续处理下一帧
            requestAnimationFrame(processVideo);
        }
        
        // 绘制检测框
        function drawDetections(objects) {
            objects.forEach(obj => {
                if (obj.bbox_2d) {
                    const [x1, y1, x2, y2] = obj.bbox_2d;
                    const width = x2 - x1;
                    const height = y2 - y1;
                    
                    // 确定颜色
                    let color;
                    if (obj.risk_level === 'high') {
                        color = 'red';
                    } else if (obj.risk_level === 'medium') {
                        color = 'orange';
                    } else {
                        color = 'lime';
                    }
                    
                    // 绘制框
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x1, y1, width, height);
                    
                    // 绘制标签背景
                    const label = obj.label || obj.category;
                    ctx.font = '14px Arial';
                    const textWidth = ctx.measureText(label).width + 10;
                    ctx.fillStyle = color;
                    ctx.fillRect(x1, y1 - 20, textWidth, 20);
                    
                    // 绘制标签文字
                    ctx.fillStyle = 'white';
                    ctx.fillText(label, x1 + 5, y1 - 5);
                }
            });
        }
        
        // 停止处理
        function stopProcessing() {
            isRunning = false;
            
            // 关闭WebSocket连接
            if (ws) {
                ws.close();
                ws = null;
            }
            
            // 停止视频流
            if (videoElement.srcObject) {
                const tracks = videoElement.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                videoElement.srcObject = null;
            }
            
            // 恢复界面状态
            startButton.disabled = false;
            stopButton.disabled = true;
            updateStatus('已停止处理', false);
            
            // 更新最终统计信息
            updateStats(true);
        }
        
        // 更新状态信息
        function updateStatus(message, isError) {
            statusElement.textContent = '状态: ' + message;
            statusElement.className = isError ? 'status error' : 'status';
        }
        
        // 更新统计信息
        function updateStats(isFinal = false) {
            const avgLatency = framesReceived > 0 ? (totalLatency / framesReceived).toFixed(2) : 0;
            
            let statsText = `
                发送帧数: ${framesSent} | 
                接收帧数: ${framesReceived} | 
                平均延迟: ${avgLatency} ms | 
                最大延迟: ${maxLatency.toFixed(2)} ms | 
                最小延迟: ${minLatency === Infinity ? 0 : minLatency.toFixed(2)} ms | 
                接收帧率: ${(framesReceived / ((performance.now() - lastStatsUpdate) / 1000)).toFixed(2)} FPS
            `;
            
            if (isFinal) {
                statsText += ' | 状态: 已完成';
            }
            
            statsElement.textContent = statsText;
        }
        
        // 更新检测结果信息
        function updateResultInfo(response) {
            let infoHtml = '';
            
            // 添加检测对象信息
            if (response.objects && response.objects.length > 0) {
                infoHtml += '<strong>检测到的对象:</strong><br>';
                response.objects.forEach((obj, index) => {
                    infoHtml += `${index + 1}. ${obj.category || obj.label || '未知'} - 
                                 置信度: ${obj.confidence ? (obj.confidence * 100).toFixed(2) + '%' : '未知'} - 
                                 风险等级: ${obj.risk_level || '未知'}<br>`;
                });
            } else {
                infoHtml += '<strong>未检测到对象</strong><br>';
            }
            
            // 添加高风险事件信息
            if (response.high_risk_events && response.high_risk_events.length > 0) {
                infoHtml += '<br><strong style="color: red;">高风险事件:</strong><br>';
                response.high_risk_events.forEach((event, index) => {
                    infoHtml += `${index + 1}. ${event.category || event.label || '未知'} - 
                                ${event.event_description || event.event || '未知事件'}<br>`;
                });
            }
            
            // 添加低风险事件信息
            if (response.low_risk_events && response.low_risk_events.length > 0) {
                infoHtml += '<br><strong style="color: orange;">低/中风险事件:</strong><br>';
                response.low_risk_events.forEach((event, index) => {
                    infoHtml += `${index + 1}. ${event.category || event.label || '未知'} - 
                                ${event.event_description || event.event || '未知事件'}<br>`;
                });
            }
            
            // 添加总结信息
            if (response.summary) {
                infoHtml += '<br><strong>场景总结:</strong><br>';
                infoHtml += response.summary.description || '无总结信息';
            }
            
            resultInfoElement.innerHTML = infoHtml;
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html> 
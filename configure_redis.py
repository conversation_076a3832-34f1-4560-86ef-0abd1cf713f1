#!/usr/bin/env python
# -*- coding: utf-8 -*-

import redis
import sys

print("Redis配置工具")
print("=" * 30)

try:
    # 连接Redis
    r = redis.Redis(host='localhost', port=6379, db=0)
    
    # 测试连接
    try:
        r.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"⚠️ Redis连接成功但ping失败: {e}")
    
    # 禁用持久化错误导致的写入停止
    r.config_set('stop-writes-on-bgsave-error', 'no')
    print("✅ 已禁用持久化错误导致的写入停止")
    
    # 禁用RDB持久化
    r.config_set('save', '')
    print("✅ 已禁用RDB持久化")
    
    # 显示当前配置
    config = {
        'stop-writes-on-bgsave-error': r.config_get('stop-writes-on-bgsave-error'),
        'save': r.config_get('save'),
        'dir': r.config_get('dir'),
        'dbfilename': r.config_get('dbfilename')
    }
    print("\n当前Redis配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\nRedis配置完成，现在应该可以正常写入了")
    
except Exception as e:
    print(f"❌ 配置Redis时出错: {e}")
    sys.exit(1) 
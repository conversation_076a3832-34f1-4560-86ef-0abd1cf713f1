# Traffic Eyes Ezviz Backend Startup Script
# Starting the Ezviz cloud video processing backend service

Write-Host "Starting Traffic Eyes Ezviz cloud video processing backend..." -ForegroundColor Cyan

# Setting environment variables
$env:API_PORT = 8003          # API service port
$env:MJPEG_PORT = 8082        # MJPEG stream service port
$env:REDIS_HOST = "localhost" # Redis host
$env:REDIS_PORT = 6379        # Redis port
$env:REDIS_PASSWORD = ""      # Redis password (if any)
$env:BUFFER_DURATION = 2.0    # Buffer duration in seconds
$env:FPS = 10                 # Frames per second
$env:QWEN_API_URL = "http://localhost:5000/api/qwen-analysis" # Qwen API URL
$env:USE_QWEN_API = "True"    # Enable Qwen API integration

# Check Python environment
Write-Host "Checking Python environment..." -ForegroundColor Cyan
try {
    $pythonVersion = python --version
    Write-Host "$pythonVersion installed" -ForegroundColor Green
} catch {
    Write-Host "Error: Python not found. Please install Python 3.8 or higher." -ForegroundColor Red
    exit 1
}

# Check dependencies
Write-Host "Checking dependencies..." -ForegroundColor Cyan
$requiredPackages = @("fastapi", "uvicorn", "redis", "numpy", "opencv-python", "pillow", "ultralytics")
$missingPackages = @()

foreach ($package in $requiredPackages) {
    $checkPackage = python -c "try: import $($package.Replace('-', '_')); print('installed'); except ImportError: print('not installed')"
    if ($checkPackage -eq "not installed") {
        $missingPackages += $package
    }
}

# Install missing packages
if ($missingPackages.Count -gt 0) {
    Write-Host "Installing missing dependencies..." -ForegroundColor Yellow
    foreach ($package in $missingPackages) {
        Write-Host "Installing $package..." -ForegroundColor Yellow
        pip install $package
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to install $package" -ForegroundColor Red
            exit 1
        }
    }
    Write-Host "All dependencies installed" -ForegroundColor Green
} else {
    Write-Host "All dependencies installed" -ForegroundColor Green
}

# Check models directory
$modelsDir = "models"
if (-not (Test-Path $modelsDir)) {
    Write-Host "Creating models directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $modelsDir | Out-Null
}

# Check YOLO model
$yoloModel = "models/yolov8n.pt"
if (-not (Test-Path $yoloModel)) {
    Write-Host "Downloading YOLO model..." -ForegroundColor Yellow
    python -c "from ultralytics import YOLO; YOLO('yolov8n')"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error: Failed to download YOLO model" -ForegroundColor Red
        exit 1
    }
    # Move model to models directory
    if (Test-Path "yolov8n.pt") {
        Move-Item -Path "yolov8n.pt" -Destination $yoloModel -Force
    }
}

# Check Redis service
Write-Host "Checking Redis service..." -ForegroundColor Cyan
$redisRunning = $false
try {
    $redisCheck = python -c "import redis; r = redis.Redis(host='localhost', port=6379); r.ping()"
    $redisRunning = $true
    Write-Host "Redis service is running" -ForegroundColor Green
} catch {
    Write-Host "Redis service not running, attempting to start..." -ForegroundColor Yellow
    # Try to start Redis
    if (Test-Path "redis-server.exe") {
        Start-Process -FilePath "redis-server.exe" -WindowStyle Hidden
        Start-Sleep -Seconds 2
        try {
            $redisCheck = python -c "import redis; r = redis.Redis(host='localhost', port=6379); r.ping()"
            $redisRunning = $true
            Write-Host "Redis service started" -ForegroundColor Green
        } catch {
            Write-Host "Unable to start Redis service" -ForegroundColor Red
        }
    } else {
        Write-Host "Redis server not found, please install Redis" -ForegroundColor Red
    }
}

# Start backend API service
Write-Host "Starting backend API service..." -ForegroundColor Cyan
$apiCommand = "python -m uvicorn backend.ezviz_api:app --host 0.0.0.0 --port $env:API_PORT --reload"
Write-Host "Executing command: $apiCommand" -ForegroundColor Yellow

# Start service
Invoke-Expression $apiCommand 
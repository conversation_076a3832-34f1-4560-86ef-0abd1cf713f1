#!/bin/bash

# 激活虚拟环境（如果有的话）
# source venv/bin/activate

# 安装依赖
echo "安装依赖..."
pip install -r requirements.txt

# 检查YOLO模型路径
echo "检查YOLO模型路径..."
if [ ! -f "../src/models/best.pt" ]; then
    if [ -f "../src/models/yolo11n-seg.pt" ]; then
        echo "YOLOv11x-seg模型存在"
    else
        echo "无法找到YOLOv11x-seg模型，请确保src/models/best.pt或src/models/yolo11n-seg.pt存在"
    fi
fi

# 启动服务
echo "启动Qwen 2.5 VL后端服务..."
python main.py 
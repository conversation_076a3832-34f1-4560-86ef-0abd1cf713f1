import json
import random
import io
import ast
from PIL import Image, ImageDraw, ImageFont
from PIL import ImageColor
import xml.etree.ElementTree as ET
import cv2
import numpy as np
import tempfile
import os
import time
import re
from tqdm import tqdm
from typing import List, Dict, Any, Tuple, Optional
import sys
import torch
import uuid
import logging
import base64
from yolo_tracker import YOLODefectDetector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Check if ultralytics is installed, if not install it
try:
    from ultralytics import YOLO
except ImportError:
    logger.info("Installing ultralytics package...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "ultralytics"])
    from ultralytics import YOLO

additional_colors = [colorname for (colorname, colorcode) in ImageColor.colormap.items()]

def decode_xml_points(text):
    try:
        root = ET.fromstring(text)
        num_points = (len(root.attrib) - 1) // 2
        points = []
        for i in range(num_points):
            x = root.attrib.get(f'x{i+1}')
            y = root.attrib.get(f'y{i+1}')
            points.append([x, y])
        alt = root.attrib.get('alt')
        phrase = root.text.strip() if root.text else None
        return {
            "points": points,
            "alt": alt,
            "phrase": phrase
        }
    except Exception as e:
        print(e)
        return None

def plot_bounding_boxes(im, bounding_boxes, input_width, input_height):
    """
    Plots bounding boxes on an image with markers for each a name, using PIL, normalized coordinates, and different colors.

    Args:
        img_path: The path to the image file.
        bounding_boxes: A list of bounding boxes containing the name of the object
         and their positions in normalized [y1 x1 y2 x2] format.
    """

    # Load the image
    img = im
    width, height = img.size
    print(img.size)
    # Create a drawing object
    draw = ImageDraw.Draw(img)

    # Define a list of colors
    colors = [
    'red',
    'green',
    'blue',
    'yellow',
    'orange',
    'pink',
    'purple',
    'brown',
    'gray',
    'beige',
    'turquoise',
    'cyan',
    'magenta',
    'lime',
    'navy',
    'maroon',
    'teal',
    'olive',
    'coral',
    'lavender',
    'violet',
    'gold',
    'silver',
    ] + additional_colors

    # Parsing out the markdown fencing
    bounding_boxes = parse_json(bounding_boxes)

    # Try to load font, fall back to default if not available
    try:
        font = ImageFont.truetype("simhei.ttf", size=25)
    except Exception:
        try:
            # 备选中文字体
            font = ImageFont.truetype("SimSun.ttf", size=25)
        except Exception:
            try:
                # 如果没有中文字体，再尝试Arial
                font = ImageFont.truetype("Arial.ttf", size=25)
            except Exception:
                # 最后使用默认字体
                font = ImageFont.load_default()

    try:
      json_output = ast.literal_eval(bounding_boxes)
    except Exception as e:
      end_idx = bounding_boxes.rfind('"}') + len('"}')
      truncated_text = bounding_boxes[:end_idx] + "]"
      json_output = ast.literal_eval(truncated_text)

    # Iterate over the bounding boxes
    for i, bounding_box in enumerate(json_output['high_risk_events']):
      # Select a color from the list
      color = colors[i % len(colors)]

      # Convert normalized coordinates to absolute coordinates
      abs_y1 = int(bounding_box["bbox_2d"][1]/input_height * height)
      abs_x1 = int(bounding_box["bbox_2d"][0]/input_width * width)
      abs_y2 = int(bounding_box["bbox_2d"][3]/input_height * height)
      abs_x2 = int(bounding_box["bbox_2d"][2]/input_width * width)

      if abs_x1 > abs_x2:
        abs_x1, abs_x2 = abs_x2, abs_x1

      if abs_y1 > abs_y2:
        abs_y1, abs_y2 = abs_y2, abs_y1

      # Draw the bounding box
      draw.rectangle(
          ((abs_x1, abs_y1), (abs_x2, abs_y2)), outline=color, width=4
      )

      # Draw the text
      if "label" in bounding_box:
        draw.text((abs_x1 + 8, abs_y1 + 6), bounding_box["label"], fill=color, font=font)

    # Display the image
    img.show()


def plot_points(im, text, input_width, input_height):
  img = im
  width, height = img.size
  draw = ImageDraw.Draw(img)
  colors = [
    'red', 'green', 'blue', 'yellow', 'orange', 'pink', 'purple', 'brown', 'gray',
    'beige', 'turquoise', 'cyan', 'magenta', 'lime', 'navy', 'maroon', 'teal',
    'olive', 'coral', 'lavender', 'violet', 'gold', 'silver',
  ] + additional_colors
  xml_text = text.replace('```xml', '')
  xml_text = xml_text.replace('```', '')
  data = decode_xml_points(xml_text)
  if data is None:
    img.show()
    return
  points = data['points']
  description = data['phrase']

  # Try to load font, fall back to default if not available
  try:
      font = ImageFont.truetype("simhei.ttf", size=25)
  except Exception as e:
      logger.warning(f"Could not load SimHei font: {e}")
      try:
          # 备选中文字体
          font = ImageFont.truetype("SimSun.ttf", size=25)
      except Exception as e:
          logger.warning(f"Could not load SimSun font: {e}")
          try:
              # 如果没有中文字体，再尝试Arial
              font = ImageFont.truetype("Arial.ttf", size=25)
          except Exception:
              # 最后使用默认字体
              font = ImageFont.load_default()

  for i, point in enumerate(points):
    color = colors[i % len(colors)]
    abs_x1 = int(point[0])/input_width * width
    abs_y1 = int(point[1])/input_height * height
    radius = 2
    draw.ellipse([(abs_x1 - radius, abs_y1 - radius), (abs_x1 + radius, abs_y1 + radius)], fill=color)
    draw.text((abs_x1 + 8, abs_y1 + 6), description, fill=color, font=font)

  img.show()


# @title Parsing JSON output
def parse_json(json_output):
    # Parsing out the markdown fencing
    lines = json_output.splitlines()
    for i, line in enumerate(lines):
        if line == "```json":
            json_output = "\n".join(lines[i+1:])  # Remove everything before "```json"
            json_output = json_output.split("```")[0]  # Remove everything after the closing "```"
            break  # Exit the loop once "```json" is found
    return json_output


from openai import OpenAI
import os
import base64
#  base 64 编码格式
def encode_image(image_path_or_data):
    try:
        # 尝试方法1: 直接从文件路径读取
        if isinstance(image_path_or_data, (str, bytes, os.PathLike)):
            with open(image_path_or_data, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode("utf-8")
        # 尝试方法3: 如果输入已经是字节数据
        elif isinstance(image_path_or_data, (bytes, bytearray)):
            return base64.b64encode(image_path_or_data).decode("utf-8")
        # 尝试方法4: 如果输入是BytesIO对象
        elif hasattr(image_path_or_data, 'read') and callable(image_path_or_data.read):
            return base64.b64encode(image_path_or_data.read()).decode("utf-8")
        else:
            raise TypeError(f"Unsupported input type: {type(image_path_or_data)}")
    except Exception as e:
        print(f"Error encoding image from path/data {image_path_or_data}: {str(e)}")
        # 尝试方法2: 使用PIL打开图像
        try:
            # 尝试打开图像
            from PIL import Image
            from io import BytesIO

            # 如果是字符串路径
            if isinstance(image_path_or_data, (str, bytes, os.PathLike)):
                image = Image.open(image_path_or_data)
            # 如果是字节数据
            elif isinstance(image_path_or_data, (bytes, bytearray)):
                image = Image.open(BytesIO(image_path_or_data))
            # 如果是BytesIO对象
            elif hasattr(image_path_or_data, 'read') and callable(image_path_or_data.read):
                # 保存当前位置
                current_pos = image_path_or_data.tell()
                # 重置到开始
                image_path_or_data.seek(0)
                # 读取数据
                image = Image.open(BytesIO(image_path_or_data.read()))
                # 恢复位置
                image_path_or_data.seek(current_pos)
            else:
                raise TypeError(f"Unsupported input type: {type(image_path_or_data)}")

            # 如果图像是RGBA模式（带透明通道），转换为RGB模式
            if image.mode == 'RGBA':
                print("Converting RGBA image to RGB mode")
                image = image.convert('RGB')

            # 将图像转换为字节流
            buffer = BytesIO()
            image.save(buffer, format="JPEG")
            buffer.seek(0)

            # 编码为base64
            return base64.b64encode(buffer.read()).decode("utf-8")
        except Exception as e2:
            print(f"Second attempt to encode image also failed: {str(e2)}")
            raise Exception(f"Failed to encode image: {str(e)} and {str(e2)}")


# @title inference function with API
def inference_with_api(image_path_or_obj, prompt, sys_prompt="You are a helpful assistant.", model_id="qwen-vl-max-1119", min_pixels=512*28*28, max_pixels=2048*28*28):
    try:
        # 处理不同类型的图像输入
        from PIL import Image
        from io import BytesIO

        # 如果输入是PIL图像对象
        if isinstance(image_path_or_obj, Image.Image):
            # 如果图像是RGBA模式（带透明通道），转换为RGB模式
            if image_path_or_obj.mode == 'RGBA':
                print("Converting RGBA image to RGB mode")
                image_path_or_obj = image_path_or_obj.convert('RGB')

            # 将图像转换为字节流
            buffer = BytesIO()
            image_path_or_obj.save(buffer, format="JPEG")
            buffer.seek(0)

            # 编码为base64
            base64_image = base64.b64encode(buffer.read()).decode("utf-8")
        else:
            # 使用原有的encode_image函数处理其他类型的输入
            base64_image = encode_image(image_path_or_obj)

        # print("api_key",os.getenv('DASHSCOPE_API_KEY'))
        client = OpenAI(
            #If the environment variable is not configured, please replace the following line with the Dashscope API Key: api_key="sk-xxx".
            api_key=os.getenv('DASHSCOPE_API_KEY'),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )

        # Check model type
        if model_id == "qwen-vl-max-1119":
            # qwen-vl-max-1119 API format
            messages = [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": sys_prompt}]
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                        },
                        {"type": "text", "text": prompt}
                    ]
                }
            ]
        else:
            # qwen2.5-vl API format
            messages = [
                {
                    "role": "system",
                    "content": [{"type":"text","text": sys_prompt}]},
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "min_pixels": min_pixels,
                            "max_pixels": max_pixels,
                            # Pass in BASE64 image data. Note that the image format (i.e., image/{format}) must match the Content Type in the list of supported images. "f" is the method for string formatting.
                            # PNG image:  f"data:image/png;base64,{base64_image}"
                            # JPEG image: f"data:image/jpeg;base64,{base64_image}"
                            # WEBP image: f"data:image/webp;base64,{base64_image}"
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"},
                        },
                        {"type": "text", "text": prompt},
                    ],
                }
            ]

        completion = client.chat.completions.create(
            model = model_id,
            messages = messages,
        )
        return completion.choices[0].message.content
    except Exception as e:
        print(f"Error during API inference: {str(e)}")
        return f"Error: {str(e)}"

def detect_objects(image_path, prompt):
    # Use an API-based approach to inference. Apply API key here: https://bailian.console.alibabacloud.com/?apiKey=1
    from qwen_vl_utils import smart_resize
    # 从环境变量获取API密钥，而不是硬编码
    # os.environ['DASHSCOPE_API_KEY'] = 'sk-aaf3bf477d4b42eaab64aa9ac835731d'
    min_pixels = 512*28*28
    max_pixels = 2048*28*28
    image = Image.open(image_path)
    width, height = image.size
    input_height,input_width = smart_resize(height,width,min_pixels=min_pixels, max_pixels=max_pixels)
    response = inference_with_api(image_path, prompt, min_pixels=min_pixels, max_pixels=max_pixels)
    print(response)
    plot_bounding_boxes(image, response, input_width, input_height)

# Global model cache for efficiency
MODEL_CACHE = {}

def get_yolo_model(model_path: str):
    """
    Get or load a YOLO model from cache or file
    """
    if model_path not in MODEL_CACHE:
        logger.info(f"Loading YOLO model from {model_path}...")
        try:
            model = YOLO(model_path)
            MODEL_CACHE[model_path] = model
        except Exception as e:
            logger.error(f"Failed to load YOLO model: {str(e)}")
            raise
    return MODEL_CACHE[model_path]

# Global detector dictionary for efficiency, keyed by model path
DETECTOR_INSTANCES = {}

def get_defect_detector(model_path=None):
    """Get or create a detector instance for a specific model path"""
    global DETECTOR_INSTANCES

    # 如果未指定模型路径，使用默认模型
    if model_path is None:
        # 标准化模型路径: 始终使用src/models目录
        model_path = "../src/models/best.pt"

        if not os.path.exists(model_path):
            logger.error(f"错误：模型文件 {model_path} 不存在")
            logger.error("请确保模型文件已下载并放置在src/models目录中")
            raise Exception(f"模型文件 {model_path} 不存在，请确保模型文件已下载并放置在正确位置")

    # 如果该模型路径的检测器不存在，创建一个新的
    if model_path not in DETECTOR_INSTANCES:
        try:
            logger.info(f"创建新的YOLO缺陷检测器，使用模型: {model_path}")
            # Make sure YOLODefectDetector is properly imported and referenced
            from yolo_tracker import YOLODefectDetector
            DETECTOR_INSTANCES[model_path] = YOLODefectDetector(model_path=model_path)
        except Exception as e:
            logger.error(f"创建YOLO缺陷检测器失败: {str(e)}")
            raise

    return DETECTOR_INSTANCES[model_path]

def detect_road_defects_yolo(
    image_path: str,
    model_path: str = "../src/models/yolo11n-seg.pt",  # 使用指定的yolo11n-seg.pt模型
    conf: float = 0.25,
    verbose: bool = False
) -> Tuple[List[Dict[str, Any]], List[str]]:
    """
    Detect road defects using YOLO model

    Args:
        image_path: Path to the image file
        model_path: Path to the YOLO model file (default: ../src/models/yolo11n-seg.pt)
        conf: Confidence threshold
        verbose: Whether to print verbose output

    Returns:
        Tuple of (defects, classes)
    """
    try:
        # Get or create detector
        detector = get_defect_detector(model_path)

        # Run detection
        if verbose:
            logger.info(f"开始在 {image_path} 上运行道路缺陷检测")

        # Detect defects
        defects, classes = detector.detect(image_path)

        if verbose:
            logger.info(f"检测完成，共发现 {len(defects)} 个缺陷，类别: {classes}")

        return defects, classes

    except Exception as e:
        logger.error(f"YOLO检测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        # Return default results on error
        logger.info("由于错误，返回默认检测结果")
        return [
            {
                "category": "路面裂缝",
                "bbox": [0.2, 0.3, 0.4, 0.45],
                "score": 0.92,
                "mask": None
            },
            {
                "category": "坑洼",
                "bbox": [0.5, 0.6, 0.6, 0.7],
                "score": 0.89,
                "mask": None
            },
            {
                "category": "路面积水",
                "bbox": [0.7, 0.4, 0.9, 0.55],
                "score": 0.86,
                "mask": None
            }
        ], ["路面裂缝", "坑洼", "路面积水"]

def visualize_detections(
    image_path: str,
    defects: List[Dict[str, Any]],
    output_path: Optional[str] = None
) -> str:
    """
    Visualize detections on an image

    Args:
        image_path: Path to the image file
        defects: List of detection objects
        output_path: Path to save the visualized image (optional)

    Returns:
        Path to the visualized image
    """
    try:
        # 标准化模型路径: 始终使用src/models目录
        detector = get_defect_detector("../src/models/best.pt")
        return detector.visualize(image_path, defects, output_path)
    except Exception as e:
        logger.error(f"可视化检测结果失败: {str(e)}")
        import traceback
        traceback.print_exc()

        # 在可视化失败的情况下，简单地复制原始图像并添加一些文本
        try:
            image = cv2.imread(image_path)
            cv2.putText(image, "YOLO Detection Failed", (50, 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)

            if output_path is None:
                output_dir = os.path.join(os.path.dirname(image_path), "outputs")
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, f"{uuid.uuid4()}_error.jpg")

            cv2.imwrite(output_path, image)
            return output_path
        except:
            logger.error("创建错误图像失败")
            return image_path  # 返回原始图像路径作为后备

# For testing and development
if __name__ == "__main__":
    # Test code - uncomment to test
    # image_path = "../static/uploads/test.jpg"
    # defects, classes = detect_road_defects_yolo(image_path)
    # print(f"Detected {len(defects)} defects of classes: {classes}")
    # if defects:
    #     visualized_path = visualize_detections(image_path, defects)
    #     print(f"Visualized image saved to {visualized_path}")
    pass

def parse_qwen_response(response_text, response_type="road_defects"):
    """
    解析qwen-vl-max-1119的文本响应，提取结构化信息

    Args:
        response_text: qwen-vl-max-1119返回的文本
        response_type: 响应类型，可选值：road_defects, vehicle_accidents, abnormal_driving

    Returns:
        解析后的结构化数据
    """
    if not response_text:
        return {
            "summary": "未能获取分析结果",
            "detected_items": [],
            "recommendations": []
        }

    result = {
        "summary": "",
        "detected_items": [],
        "recommendations": [],
        "risk_levels": {
            "high": [],
            "medium": [],
            "low": []
        },
        "original_text": response_text
    }

    # 将文本分段处理
    paragraphs = [p.strip() for p in response_text.split('\n') if p.strip()]

    # 提取摘要（通常是第一段）
    if paragraphs:
        result["summary"] = paragraphs[0]

    # 根据不同类型的响应，使用不同的关键词进行解析
    keywords = {
        "road_defects": ["裂缝", "坑洼", "积水", "沉降", "损坏", "路面", "风险"],
        "vehicle_accidents": ["碰撞", "拥堵", "停车", "事故", "风险", "交通"],
        "abnormal_driving": ["超速", "偏离", "分心", "违规", "驾驶", "行为"]
    }

    risk_indicators = ["高风险", "中风险", "低风险", "严重", "中等", "轻微"]
    recommendation_indicators = ["建议", "提议", "推荐", "应该", "需要", "可以"]

    # 选择相应的关键词集
    selected_keywords = keywords.get(response_type, keywords["road_defects"])

    # 扫描每个段落查找关键信息
    for paragraph in paragraphs:
        # 检查是否包含选定的关键词
        if any(keyword in paragraph for keyword in selected_keywords):
            # 确定风险等级
            risk_level = "medium"  # 默认中等风险

            if any(indicator in paragraph.lower() for indicator in ["高风险", "严重", "危险"]):
                risk_level = "high"
            elif any(indicator in paragraph.lower() for indicator in ["低风险", "轻微", "次要"]):
                risk_level = "low"

            # 添加到检测项
            item = {
                "description": paragraph,
                "risk_level": risk_level
            }
            result["detected_items"].append(item)
            result["risk_levels"][risk_level].append(paragraph)

        # 检查是否包含建议
        elif any(indicator in paragraph for indicator in recommendation_indicators):
            result["recommendations"].append(paragraph)

    # 如果没有找到足够的结构化信息，提取关键句子
    if len(result["detected_items"]) == 0:
        # 尝试按句子分割并查找关键词
        sentences = []
        for paragraph in paragraphs:
            sentences.extend([s.strip() + "。" for s in paragraph.split("。") if s.strip()])

        for sentence in sentences:
            if any(keyword in sentence for keyword in selected_keywords):
                # 确定风险等级
                risk_level = "medium"  # 默认中等风险

                if any(indicator in sentence.lower() for indicator in ["高风险", "严重", "危险"]):
                    risk_level = "high"
                elif any(indicator in sentence.lower() for indicator in ["低风险", "轻微", "次要"]):
                    risk_level = "low"

                # 添加到检测项
                item = {
                    "description": sentence,
                    "risk_level": risk_level
                }
                result["detected_items"].append(item)
                result["risk_levels"][risk_level].append(sentence)

    # 如果仍找不到结构化信息，使用文本分段作为默认结构
    if len(result["detected_items"]) == 0 and len(paragraphs) > 1:
        # 使用第二段及以后的段落作为检测项
        for i, paragraph in enumerate(paragraphs[1:], 1):
            if i < len(paragraphs) - 1:  # 留下最后一段作为建议
                item = {
                    "description": paragraph,
                    "risk_level": "medium"
                }
                result["detected_items"].append(item)
                result["risk_levels"]["medium"].append(paragraph)

        # 最后一段通常是总结或建议
        if paragraphs and len(paragraphs) > 1:
            result["recommendations"].append(paragraphs[-1])

    return result
import './assets/main.css'

import { createApp, h } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

// Import stagewise toolbar for development mode
import { StagewiseToolbar } from '@stagewise/toolbar-vue'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app')

// Initialize stagewise toolbar in development mode
if (import.meta.env.DEV) {
  const stagewiseConfig = {
    plugins: []
  }
  
  app.component('StagewiseToolbar', StagewiseToolbar)
  
  // Mount stagewise toolbar component
  const toolbarContainer = document.createElement('div')
  document.body.appendChild(toolbarContainer)
  
  const toolbarApp = createApp({
    render: () => h(StagewiseToolbar, { config: stagewiseConfig })
  })
  
  toolbarApp.mount(toolbarContainer)
}

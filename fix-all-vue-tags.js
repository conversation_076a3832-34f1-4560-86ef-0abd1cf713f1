import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the file with the issue
const filePath = path.join(__dirname, 'src', 'components', 'OnlineMonitor.vue');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Create a backup
fs.writeFileSync(`${filePath}.backup-${Date.now()}`, content, 'utf8');

// Let's create a completely new file with the correct structure
try {
  // Split file into major sections
  const templateMatch = content.match(/<template>([\s\S]*?)<\/template>/);
  const scriptMatch = content.match(/<script[\s\S]*?>([\s\S]*?)<\/script>/);
  const styleMatch = content.match(/<style[\s\S]*?>([\s\S]*?)<\/style>/);

  if (!templateMatch) {
    throw new Error('Could not find template section');
  }

  if (!scriptMatch) {
    throw new Error('Could not find script section');
  }

  // Check if there's a duplicate template v-if="tryParseJsonLog(log)" within the template
  let templateContent = templateMatch[1];
  
  // Look for the specific pattern in the file
  const jsonLogTemplate = /<template v-if="tryParseJsonLog\(log\)">([\s\S]*?)(?:<\/template>|$)/g;
  let matches = [...templateContent.matchAll(jsonLogTemplate)];
  
  // Check for unclosed templates
  for (const match of matches) {
    if (!match[0].includes('</template>')) {
      // This template is unclosed, let's find where it should end
      const startIndex = match.index;
      const contentAfterTemplate = templateContent.substring(startIndex + match[0].length);
      
      // Assuming it should end before the next <div> or at the end of a certain section
      // For simplicity, let's insert it before the next closing div
      const closingDivPos = contentAfterTemplate.indexOf('</div>');
      if (closingDivPos !== -1) {
        const fixedSection = match[0] + contentAfterTemplate.substring(0, closingDivPos) + '</template>' + contentAfterTemplate.substring(closingDivPos);
        templateContent = templateContent.substring(0, startIndex) + fixedSection + templateContent.substring(startIndex + match[0].length + closingDivPos);
        console.log('Fixed an unclosed <template> tag');
      }
    }
  }

  // Additional cleanup for potential nested template issues
  // Fix any remaining issues with extra </div> tags that might be causing imbalance
  const divOpenPattern = /<div/g;
  const divClosePattern = /<\/div>/g;
  
  const openDivs = (templateContent.match(divOpenPattern) || []).length;
  const closeDivs = (templateContent.match(divClosePattern) || []).length;
  
  console.log(`DIV tags count: open=${openDivs}, close=${closeDivs}`);
  
  if (openDivs !== closeDivs) {
    console.log('Imbalanced DIV tags detected, trying to fix...');
    
    // More complex tag balancing would be needed here, for now let's just log it
    // For a quick solution, let's ensure all template tags are at least balanced
    const templateOpenPattern = /<template[^>]*>/g;
    const templateClosePattern = /<\/template>/g;
    
    const openTemplates = (templateContent.match(templateOpenPattern) || []).length;
    const closeTemplates = (templateContent.match(templateClosePattern) || []).length;
    
    console.log(`TEMPLATE tags count: open=${openTemplates}, close=${closeTemplates}`);
    
    if (openTemplates > closeTemplates) {
      console.log('Adding missing template closing tags');
      for (let i = 0; i < openTemplates - closeTemplates; i++) {
        templateContent += '</template>';
      }
    }
  }

  // Reconstruct the file
  const scriptContent = scriptMatch[0];
  const styleContent = styleMatch ? styleMatch[0] : '';

  const fixedContent = `<template>\n${templateContent}\n</template>\n\n${scriptContent}\n\n${styleContent}`;
  
  // Write the fixed content back
  fs.writeFileSync(filePath, fixedContent, 'utf8');
  
  console.log('Fixed Vue file structure with balanced tags');
} catch (error) {
  console.error('Error fixing Vue file:', error.message);
} 
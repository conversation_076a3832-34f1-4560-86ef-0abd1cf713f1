﻿# Traffic Eyes Integrated Startup Script
# Integrates the original system with the new video processing system

Write-Host "Starting Traffic Eyes Integrated System..." -ForegroundColor Green

# Set environment variables to avoid OpenCV errors
$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"

# Create necessary directories
$dirs = @(
    "logs", 
    "qwen-vl-backend/static/uploads",
    "processed_videos",
    "static_realtime",
    "static_ezviz"
)
foreach ($d in $dirs) {
    if (-not (Test-Path $d)) {
        Write-Host "Creating directory: $d" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $d -Force | Out-Null
    }
}

# Check Redis installation
$redis_server = "redis-server.exe"
$redis_found = $false
try {
    $found = Get-Command $redis_server -ErrorAction SilentlyContinue
    if ($found) {
        $redis_found = $true
        Write-Host "Redis found: $($found.Source)" -ForegroundColor Green
    }
} catch {
    Write-Host "Redis not found, will try to use embedded version or install..." -ForegroundColor Yellow
}

# Start Redis
if (-not $redis_found) {
    $embedded = ".\redis-server.exe"
    if (Test-Path $embedded) {
        Write-Host "Using embedded Redis..." -ForegroundColor Cyan
        Start-Process -FilePath $embedded -ArgumentList "--port 6379" -NoNewWindow
    } else {
        $installer = "Redis-x64-3.0.504.msi"
        if (Test-Path $installer) {
            Write-Host "Installing Redis MSI..." -ForegroundColor Cyan
            Start-Process msiexec.exe -ArgumentList "/i `"$installer`" /qn" -Wait
            Write-Host "Starting Redis service..." -ForegroundColor Green
            Start-Service -Name "Redis"
        } else {
            Write-Host "Redis installer not found. Please download and try again." -ForegroundColor Red
            exit 1
        }
    }
}

# Check Python environment
$py = "python"
try {
    $ver = & $py --version 2>&1
    Write-Host "Python version: $ver" -ForegroundColor Green
} catch {
    Write-Host "Python not detected. Please install Python 3.8 or higher." -ForegroundColor Red
    exit 1
}

# Install required Python packages
$pkgs = @("fastapi","uvicorn","redis","websocket-client","numpy","opencv-python","ultralytics","pillow")
$toInstall = @()
foreach ($p in $pkgs) {
    $res = & $py -m pip show $p 2>&1
    if (-not $res) { $toInstall += $p }
}
if ($toInstall.Count -gt 0) {
    Write-Host "Installing missing packages: $($toInstall -join ', ')" -ForegroundColor Yellow
    & $py -m pip install $toInstall
}

# Ensure YOLO model files exist
$models = @(
    @{Path="yolov8x-seg.pt"; Type="High-quality detection and segmentation model"},
    @{Path="yolov8n.pt"; Type="Lightweight fast detection model"}
)

foreach ($model in $models) {
    if (-not (Test-Path $model.Path)) {
        Write-Host "Downloading $($model.Type)..." -ForegroundColor Cyan
        & $py -c "from ultralytics import YOLO; YOLO('$($model.Path)')"
    } else {
        Write-Host "Found $($model.Type): $($model.Path)" -ForegroundColor Green
    }
}

# Check for video files
$videoFiles = Get-ChildItem -Path . -Filter *.mp4 | Select-Object -ExpandProperty Name
if ($videoFiles.Count -eq 0) {
    Write-Host "No video files found, will use camera as source" -ForegroundColor Yellow
    $videoSource = "camera"
} else {
    $videoSource = $videoFiles[0]
    Write-Host "Found video file: $videoSource" -ForegroundColor Yellow
}

# Create a simple HTTP server to provide navigation page
function Start-NavigationServer {
    $httpPort = 8090
    
    # Create a temporary Python script to start HTTP server
    $scriptPath = ".\temp_http_server.py"
    @"
import http.server
import socketserver
import webbrowser
import os
import threading
import time

PORT = 8090
Handler = http.server.SimpleHTTPRequestHandler

print(f"Starting navigation page server on port {PORT}...")
httpd = socketserver.TCPServer(("", PORT), Handler)

# Start browser in background thread
def open_browser():
    time.sleep(1)  # Wait for server to start
    webbrowser.open(f"http://localhost:{PORT}/navigation.html")

threading.Thread(target=open_browser, daemon=True).start()

try:
    httpd.serve_forever()
except KeyboardInterrupt:
    pass
finally:
    httpd.server_close()
    print("Navigation page server has been closed")
"@ | Out-File -FilePath $scriptPath -Encoding utf8

    # Start HTTP server
    $server = Start-Process -FilePath $py -ArgumentList $scriptPath -PassThru -WindowStyle Hidden
    return $server
}

# Start services and monitoring
$pids = @()
try {
    # 1. Start original system
    Write-Host "Starting original system..." -ForegroundColor Cyan
    
    Write-Host "Starting API service (8001)..." -ForegroundColor Cyan
    $api = Start-Process -FilePath $py -ArgumentList "api_server.py" -WorkingDirectory ".\qwen-vl-backend" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($api.HasExited) { Throw "API service failed to start" }

    Write-Host "Starting YOLO processing service..." -ForegroundColor Cyan
    $proc = Start-Process -FilePath $py -ArgumentList "yolo_redis_processor.py" -WorkingDirectory ".\qwen-vl-backend" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($proc.HasExited) { Stop-Process -Id $api.Id; Throw "YOLO processor failed to start" }

    Write-Host "Starting main backend (8000)..." -ForegroundColor Cyan
    $main = Start-Process -FilePath $py -ArgumentList "main.py" -WorkingDirectory ".\qwen-vl-backend" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 3
    if ($main.HasExited) { Stop-Process -Id $api.Id,$proc.Id; Throw "Main backend failed to start" }

    $npm = Get-Command npm -ErrorAction SilentlyContinue
    if (-not $npm) {
        Write-Host "npm not found. Please install Node.js and ensure it's in PATH." -ForegroundColor Red
        exit 1
    }
    Write-Host "Starting frontend..." -ForegroundColor Cyan
    # Save current directory
    $currentDir = Get-Location
    # Switch to root directory to run npm
    Set-Location -Path ".\"
    $fe = Start-Process -FilePath npm -ArgumentList "run dev" -PassThru -WindowStyle Hidden
    # Switch back to original directory
    Set-Location -Path $currentDir

    # 2. Start new video processing system
    Write-Host "Starting new video processing system..." -ForegroundColor Cyan
    
    Write-Host "Starting offline video processing service (8080)..." -ForegroundColor Cyan
    $offline = Start-Process -FilePath $py -ArgumentList "direct_video_processor.py --port 8080" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($offline.HasExited) { Throw "Offline video processing service failed to start" }

    Write-Host "Starting realtime video processing service (8081)..." -ForegroundColor Cyan
    if ($videoSource -eq "camera") {
        $realtime = Start-Process -FilePath $py -ArgumentList "realtime_video_processor.py --source 0 --port 8081" -PassThru -WindowStyle Hidden
    } else {
        $realtime = Start-Process -FilePath $py -ArgumentList "realtime_video_processor.py --source $videoSource --port 8081" -PassThru -WindowStyle Hidden
    }
    Start-Sleep -Seconds 2
    if ($realtime.HasExited) { Stop-Process -Id $offline.Id; Throw "Realtime video processing service failed to start" }
    
    # 3. Start EZVIZ cloud camera monitoring system
    Write-Host "Starting EZVIZ cloud camera monitoring system (8082)..." -ForegroundColor Cyan
    $ezviz = Start-Process -FilePath $py -ArgumentList "ezviz_yolo_processor.py --port 8082 --model 'yolov8x-seg.pt'" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($ezviz.HasExited) { Stop-Process -Id $offline.Id,$realtime.Id; Throw "EZVIZ cloud camera monitoring system failed to start" }
    
    # 4. Start navigation page server
    Write-Host "Starting navigation page server (8090)..." -ForegroundColor Cyan
    $navServer = Start-NavigationServer
    
    Write-Host "All services started!" -ForegroundColor Green
    
    # Save process IDs to close on exit
    $pids = @($api.Id, $proc.Id, $main.Id, $fe.Id, $offline.Id, $realtime.Id, $ezviz.Id, $navServer.Id)
    
    # Wait for user to end
    Write-Host "`nPress Ctrl+C to stop all services..." -ForegroundColor Yellow
    while ($true) {
        Start-Sleep -Seconds 1
    }
    
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
} finally {
    # Close all processes
    if ($pids.Count -gt 0) {
        Write-Host "`nClosing all services..." -ForegroundColor Cyan
        foreach ($pid in $pids) {
            try {
                Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            } catch {}
        }
    }
    Write-Host "All services have been closed." -ForegroundColor Green
} 

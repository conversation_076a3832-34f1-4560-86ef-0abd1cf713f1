#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实时视频处理器 - 针对实时视频流优化的简化版本
"""

import os
import cv2
import time
import numpy as np
import threading
import argparse
from pathlib import Path
import logging
from ultralytics import YOLO
import socketserver
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socket
import json
import queue
import webbrowser

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger("realtime-video-processor")

# 全局变量
static_dir = Path("static_realtime")
processed_frames_queue = queue.Queue(maxsize=100)  # 限制队列大小，防止内存溢出
current_frame = None
frame_lock = threading.Lock()
processing_active = False
clients = set()
clients_lock = threading.Lock()

class VideoProcessor:
    def __init__(self, model_path="yolov8n.pt", confidence=0.3):
        """初始化视频处理器 - 使用更小更快的模型"""
        self.model_path = model_path
        self.confidence = confidence
        self.model = None
        
    def load_model(self):
        """加载YOLO模型"""
        if self.model is None:
            logger.info(f"加载YOLO模型: {self.model_path}")
            try:
                self.model = YOLO(self.model_path)
                logger.info("模型加载成功")
                return True
            except Exception as e:
                logger.error(f"模型加载失败: {str(e)}")
                return False
        return True
    
    def process_frame(self, frame):
        """处理单个视频帧"""
        if not self.load_model():
            return frame
        
        try:
            # 调整帧大小以加快处理速度
            height, width = frame.shape[:2]
            
            # 使用更小的图像大小进行推理
            max_size = 640
            if width > max_size or height > max_size:
                # 保持长宽比
                if width > height:
                    new_width = max_size
                    new_height = int(height * (max_size / width))
                else:
                    new_height = max_size
                    new_width = int(width * (max_size / height))
                
                # 调整大小用于推理
                small_frame = cv2.resize(frame, (new_width, new_height))
                # 使用YOLO进行检测
                results = self.model(small_frame, conf=self.confidence)
                # 在原始尺寸上绘制
                processed_frame = results[0].plot()
                # 调整回原始大小
                processed_frame = cv2.resize(processed_frame, (width, height))
            else:
                # 使用原始大小
                results = self.model(frame, conf=self.confidence)
                processed_frame = results[0].plot()
            
            return processed_frame
            
        except Exception as e:
            logger.error(f"处理帧时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return frame

def process_video_stream(source=0, resize_factor=1.0, max_fps=15):
    """从摄像头或视频文件处理视频流"""
    global current_frame, processing_active
    
    # 创建视频处理器 - 使用轻量级模型
    processor = VideoProcessor(model_path="yolov8n.pt", confidence=0.4)
    
    # 打开视频源
    if isinstance(source, str) and source.isdigit():
        source = int(source)
    
    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        logger.error(f"无法打开视频源: {source}")
        return
    
    # 获取视频信息
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    # 调整尺寸
    if resize_factor != 1.0:
        width = int(width * resize_factor)
        height = int(height * resize_factor)
    
    logger.info(f"视频源信息: {width}x{height}, {fps} FPS")
    
    # 帧率控制
    target_frame_time = 1.0 / max_fps
    
    # 主处理循环
    processing_active = True
    frame_count = 0
    start_time = time.time()
    
    try:
        while processing_active:
            # 计时开始
            loop_start = time.time()
            
            # 读取视频帧
            ret, frame = cap.read()
            if not ret:
                if isinstance(source, int):  # 如果是摄像头，尝试重新连接
                    logger.warning("摄像头断开，尝试重新连接...")
                    cap.release()
                    time.sleep(1)
                    cap = cv2.VideoCapture(source)
                    continue
                else:  # 如果是视频文件，循环播放
                    logger.info("视频结束，重新开始...")
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    continue
            
            # 调整帧大小
            if resize_factor != 1.0:
                frame = cv2.resize(frame, (width, height))
            
            # 处理帧
            processed_frame = processor.process_frame(frame)
            
            # 更新当前帧
            with frame_lock:
                current_frame = processed_frame.copy()
            
            # 更新计数器
            frame_count += 1
            
            # 每100帧打印一次性能信息
            if frame_count % 100 == 0:
                elapsed = time.time() - start_time
                current_fps = frame_count / elapsed
                logger.info(f"已处理 {frame_count} 帧, 当前帧率: {current_fps:.2f} FPS")
            
            # 控制帧率
            elapsed_loop = time.time() - loop_start
            if elapsed_loop < target_frame_time:
                time.sleep(target_frame_time - elapsed_loop)
    
    except KeyboardInterrupt:
        logger.info("用户中断处理")
    except Exception as e:
        logger.error(f"处理视频流时出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        processing_active = False
        cap.release()
        logger.info("视频处理已停止")

def ensure_dirs():
    """确保输出目录存在"""
    static_dir.mkdir(exist_ok=True)
    
    # 创建HTML文件
    html_file = static_dir / "index.html"
    with open(html_file, "w", encoding="utf-8") as f:
        f.write("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>实时视频处理</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                h1 { color: #333; }
                .container { max-width: 1200px; margin: 0 auto; }
                .video-container { margin-bottom: 30px; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .video-title { font-size: 18px; margin-bottom: 10px; }
                #mjpeg-stream { width: 100%; max-width: 800px; height: auto; }
                .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
                .status.connected { background-color: #d4edda; color: #155724; }
                .status.disconnected { background-color: #f8d7da; color: #721c24; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>实时视频处理</h1>
                <div class="video-container">
                    <div class="video-title">YOLO实时处理</div>
                    <img id="mjpeg-stream" src="/stream" alt="视频流">
                    <div id="status" class="status">连接中...</div>
                </div>
            </div>
            
            <script>
                // 检测图像加载状态
                const streamImg = document.getElementById('mjpeg-stream');
                const statusDiv = document.getElementById('status');
                
                // 图像加载成功
                streamImg.onload = function() {
                    statusDiv.textContent = '已连接';
                    statusDiv.className = 'status connected';
                };
                
                // 图像加载失败
                streamImg.onerror = function() {
                    statusDiv.textContent = '连接断开，尝试重新连接...';
                    statusDiv.className = 'status disconnected';
                    
                    // 5秒后重试
                    setTimeout(() => {
                        streamImg.src = '/stream?' + new Date().getTime();
                    }, 5000);
                };
            </script>
        </body>
        </html>
        """)

class MJPEGStreamHandler(SimpleHTTPRequestHandler):
    """处理MJPEG流的HTTP处理器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(static_dir), **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        # 处理MJPEG流请求
        if self.path.startswith('/stream'):
            self.send_response(200)
            self.send_header('Content-type', 'multipart/x-mixed-replace; boundary=--jpgboundary')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            
            with clients_lock:
                clients.add(self)
            
            try:
                while processing_active:
                    # 获取当前帧
                    with frame_lock:
                        if current_frame is not None:
                            img = current_frame.copy()
                        else:
                            # 如果没有帧，创建一个黑色图像
                            img = np.zeros((480, 640, 3), dtype=np.uint8)
                            cv2.putText(img, "Waiting for video...", (50, 240),
                                        cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    
                    # 将帧转换为JPEG
                    ret, jpeg = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, 70])
                    
                    # 发送帧
                    self.wfile.write(b"--jpgboundary\r\n")
                    self.send_header('Content-type', 'image/jpeg')
                    self.send_header('Content-length', str(len(jpeg)))
                    self.end_headers()
                    self.wfile.write(jpeg.tobytes())
                    self.wfile.write(b"\r\n")
                    
                    # 降低帧率，减少网络负担
                    time.sleep(0.066)  # 大约15 FPS
            
            except (BrokenPipeError, ConnectionResetError):
                # 客户端断开连接
                logger.info("客户端断开连接")
            except Exception as e:
                logger.error(f"处理流时出错: {str(e)}")
            finally:
                with clients_lock:
                    if self in clients:
                        clients.remove(self)
            
            return
        
        # 处理根路径或其他请求
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            with open(static_dir / 'index.html', 'rb') as f:
                self.wfile.write(f.read())
            return
        
        # 其他请求交给父类处理
        try:
            super().do_GET()
        except:
            self.send_error(404, "File not found")

def start_http_server(port=8081):
    """启动HTTP服务器提供MJPEG流"""
    server = socketserver.ThreadingTCPServer(("", port), MJPEGStreamHandler)
    logger.info(f"启动HTTP服务器在端口 {port}")
    
    # 自动打开浏览器
    webbrowser.open(f"http://localhost:{port}")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("收到中断信号，关闭服务器")
    finally:
        server.server_close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='实时视频处理系统')
    parser.add_argument('--source', type=str, default='0',
                      help='视频源 (摄像头索引或视频文件路径)')
    parser.add_argument('--port', type=int, default=8081,
                      help='HTTP服务器端口')
    parser.add_argument('--model', type=str, default='yolov8n.pt',
                      help='YOLO模型路径 (推荐使用小型模型如yolov8n.pt)')
    parser.add_argument('--fps', type=int, default=15,
                      help='最大处理帧率')
    parser.add_argument('--resize', type=float, default=0.5,
                      help='视频调整比例 (小于1.0以加速处理)')
    
    args = parser.parse_args()
    
    # 确保目录存在
    ensure_dirs()
    
    # 启动视频处理线程
    video_thread = threading.Thread(
        target=process_video_stream,
        args=(args.source, args.resize, args.fps),
        daemon=True
    )
    video_thread.start()
    
    # 启动HTTP服务器
    start_http_server(args.port)
    
    return 0

if __name__ == "__main__":
    exit(main()) 
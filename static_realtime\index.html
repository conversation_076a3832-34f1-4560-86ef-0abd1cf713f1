
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>实时视频处理</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                h1 { color: #333; }
                .container { max-width: 1200px; margin: 0 auto; }
                .video-container { margin-bottom: 30px; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .video-title { font-size: 18px; margin-bottom: 10px; }
                #mjpeg-stream { width: 100%; max-width: 800px; height: auto; }
                .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
                .status.connected { background-color: #d4edda; color: #155724; }
                .status.disconnected { background-color: #f8d7da; color: #721c24; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>实时视频处理</h1>
                <div class="video-container">
                    <div class="video-title">YOLO实时处理</div>
                    <img id="mjpeg-stream" src="/stream" alt="视频流">
                    <div id="status" class="status">连接中...</div>
                </div>
            </div>
            
            <script>
                // 检测图像加载状态
                const streamImg = document.getElementById('mjpeg-stream');
                const statusDiv = document.getElementById('status');
                
                // 图像加载成功
                streamImg.onload = function() {
                    statusDiv.textContent = '已连接';
                    statusDiv.className = 'status connected';
                };
                
                // 图像加载失败
                streamImg.onerror = function() {
                    statusDiv.textContent = '连接断开，尝试重新连接...';
                    statusDiv.className = 'status disconnected';
                    
                    // 5秒后重试
                    setTimeout(() => {
                        streamImg.src = '/stream?' + new Date().getTime();
                    }, 5000);
                };
            </script>
        </body>
        </html>
        
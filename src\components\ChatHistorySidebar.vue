<template>
  <div class="chat-history-sidebar">
    <div class="sidebar-header">
      <div class="header-title">对话历史</div>
      <button class="new-chat-button" @click="createNewChat">
        <span class="button-icon new-icon"></span>
        <span>新建对话</span>
      </button>
    </div>
    
    <div class="search-container">
      <div class="search-wrapper">
        <span class="search-icon"></span>
        <input 
          type="text" 
          v-model="searchQuery" 
          class="search-input" 
          placeholder="搜索对话..." 
        />
        <span class="clear-icon" v-if="searchQuery" @click="searchQuery = ''"></span>
      </div>
    </div>
    
    <div class="history-list" v-if="filteredChats.length > 0">
      <div 
        v-for="chat in filteredChats" 
        :key="chat.id"
        :class="['history-item', { active: chat.id === activeChatId }]"
        @click="selectChat(chat.id)"
      >
        <div class="chat-info">
          <div class="chat-title">{{ chat.title }}</div>
          <div class="chat-preview">{{ chat.preview }}</div>
        </div>
        <div class="chat-actions">
          <button class="action-button edit-button" @click.stop="renameChat(chat.id)">
            <span class="action-icon edit-icon"></span>
          </button>
          <button class="action-button delete-button" @click.stop="deleteChat(chat.id)">
            <span class="action-icon delete-icon"></span>
          </button>
        </div>
        <div class="chat-date">{{ formatDate(chat.lastUpdated) }}</div>
      </div>
    </div>
    
    <div class="empty-state" v-else>
      <div class="empty-icon"></div>
      <div class="empty-text">暂无对话历史</div>
      <div class="empty-subtext">点击"新建对话"开始交流</div>
    </div>
    
    <div class="sidebar-footer">
      <button class="upload-button" @click="uploadFile">
        <span class="button-icon upload-icon"></span>
        <span>上传文件</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// 类型定义
interface ChatHistory {
  id: string;
  title: string;
  preview: string;
  lastUpdated: Date;
  messages: any[];
}

// 模拟聊天历史数据
const chatHistories = ref<ChatHistory[]>([
  {
    id: '1',
    title: '交通流量优化讨论',
    preview: '如何优化交通流量管理？',
    lastUpdated: new Date(Date.now() - 3600000),
    messages: []
  },
  {
    id: '2',
    title: '道路维护计划',
    preview: '道路维护最佳实践有哪些？',
    lastUpdated: new Date(Date.now() - 86400000),
    messages: []
  },
  {
    id: '3',
    title: '紧急情况预案',
    preview: '恶劣天气条件下的道路安全措施',
    lastUpdated: new Date(Date.now() - 172800000),
    messages: []
  }
]);

const activeChatId = ref('1');
const searchQuery = ref('');

// 根据搜索过滤聊天历史
const filteredChats = computed(() => {
  if (!searchQuery.value) return chatHistories.value;
  
  const query = searchQuery.value.toLowerCase();
  return chatHistories.value.filter(chat => 
    chat.title.toLowerCase().includes(query) || 
    chat.preview.toLowerCase().includes(query)
  );
});

// 创建新聊天
const createNewChat = () => {
  const newChat: ChatHistory = {
    id: Date.now().toString(),
    title: '新对话',
    preview: '点击开始新的对话',
    lastUpdated: new Date(),
    messages: []
  };
  
  chatHistories.value.unshift(newChat);
  activeChatId.value = newChat.id;
  
  // 触发事件通知父组件
  emit('newChat', newChat.id);
};

// 选择聊天
const selectChat = (chatId: string) => {
  activeChatId.value = chatId;
  
  // 触发事件通知父组件
  emit('selectChat', chatId);
};

// 重命名聊天
const renameChat = (chatId: string) => {
  const chat = chatHistories.value.find(c => c.id === chatId);
  if (chat) {
    const newTitle = prompt('请输入新的对话名称', chat.title);
    if (newTitle && newTitle.trim()) {
      chat.title = newTitle.trim();
    }
  }
};

// 删除聊天
const deleteChat = (chatId: string) => {
  if (confirm('确定要删除这个对话吗？')) {
    const index = chatHistories.value.findIndex(c => c.id === chatId);
    if (index !== -1) {
      chatHistories.value.splice(index, 1);
      
      // 如果删除的是当前活动的聊天，选择第一个聊天或无聊天
      if (chatId === activeChatId.value) {
        if (chatHistories.value.length > 0) {
          activeChatId.value = chatHistories.value[0].id;
          emit('selectChat', activeChatId.value);
        } else {
          activeChatId.value = '';
          emit('selectChat', '');
        }
      }
    }
  }
};

// 上传文件
const uploadFile = () => {
  // 创建一个隐藏的文件输入元素
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = '.pdf,.txt,.doc,.docx,.md'; // 设置接受的文件类型
  
  // 处理文件选择
  fileInput.onchange = (e: Event) => {
    const input = e.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      // 这里可以处理上传的文件，比如发送到服务器或处理其内容
      console.log('上传的文件:', file.name);
      
      // 触发事件通知父组件
      emit('fileUpload', file);
    }
  };
  
  // 触发文件选择对话框
  fileInput.click();
};

// 格式化日期
const formatDate = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  // 小于24小时显示"xx小时前"
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return hours === 0 ? '刚刚' : `${hours}小时前`;
  }
  
  // 小于7天显示"xx天前"
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000);
    return `${days}天前`;
  }
  
  // 其他情况显示具体日期
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 定义要发出的事件
const emit = defineEmits(['newChat', 'selectChat', 'fileUpload']);
</script>

<style scoped>
.chat-history-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 280px;
  background: rgba(0, 21, 41, 0.95);
  border-right: 1px solid rgba(0, 168, 255, 0.2);
  color: #fff;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.2);
}

.header-title {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

.new-chat-button {
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, rgba(0, 168, 255, 0.2), rgba(0, 58, 140, 0.4));
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.3s;
  color: #fff;
}

.new-chat-button:hover {
  background: linear-gradient(90deg, rgba(0, 168, 255, 0.3), rgba(0, 58, 140, 0.5));
  transform: translateY(-1px);
}

.button-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.new-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.search-container {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.1);
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 10px;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.search-input {
  flex: 1;
  background: rgba(0, 33, 64, 0.7);
  border: 1px solid rgba(0, 168, 255, 0.2);
  border-radius: 4px;
  color: #fff;
  padding: 8px 30px 8px 32px;
  width: 100%;
  outline: none;
  transition: all 0.3s;
}

.search-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
}

.clear-icon {
  position: absolute;
  right: 10px;
  width: 16px;
  height: 16px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.6;
  transition: opacity 0.3s;
}

.clear-icon:hover {
  opacity: 1;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.history-item {
  position: relative;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  background: rgba(0, 33, 64, 0.4);
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.history-item:hover {
  background: rgba(0, 58, 140, 0.4);
  transform: translateY(-1px);
}

.history-item.active {
  background: linear-gradient(90deg, rgba(0, 168, 255, 0.15), rgba(0, 58, 140, 0.4));
  border-color: rgba(0, 168, 255, 0.4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.chat-info {
  margin-bottom: 4px;
}

.chat-title {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-preview {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-date {
  position: absolute;
  right: 12px;
  top: 12px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.4);
}

.chat-actions {
  display: none;
  position: absolute;
  right: 12px;
  bottom: 10px;
}

.history-item:hover .chat-actions {
  display: flex;
}

.action-button {
  background: transparent;
  border: none;
  width: 24px;
  height: 24px;
  margin-left: 6px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.3s;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.action-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
}

.edit-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
}

.delete-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff4d4f'%3E%3Cpath d='M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z'/%3E%3C/svg%3E");
}

.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 16px;
  text-align: center;
  height: 100%;
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.8);
}

.empty-subtext {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid rgba(0, 168, 255, 0.2);
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: rgba(0, 33, 64, 0.7);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s;
  color: #fff;
}

.upload-button:hover {
  background: rgba(0, 58, 140, 0.7);
  border-color: rgba(0, 168, 255, 0.5);
}

.upload-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}
</style> 
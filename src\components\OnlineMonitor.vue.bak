<template>
  <div class="online-monitor-page">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <div class="nav-section">
        <button
          v-for="category in riskCategories"
          :key="category.id"
          class="nav-button hover-lift"
          @click="selectCategory(category.id)"
          :class="{ active: selectedCategory === category.id }"
        >
          <span class="nav-icon" :class="category.id + '-icon'"></span>
          <span>{{ category.label }}</span>
        </button>
      </div>

      <div class="detection-options-container">
        <h3 class="sidebar-header">�������</h3>
        <div class="detection-options">
          <div
            v-for="(category, index) in filteredDetectionOptions"
            :key="category.id"
            class="detection-category slide-in-up"
            :style="{animationDelay: `${0.1 * index}s`}"
          >
            <div
              class="category-header"
              @click="toggleCategory(category.id)"
            >
              <div class="category-icon-wrapper">
                <span class="category-icon" :class="category.id + '-icon'"></span>
                <span class="category-label">{{ category.label }}</span>
              </div>
              <span class="expand-icon">{{ category.expanded ? '��' : '?' }}</span>
            </div>
            <div
              v-if="category.expanded"
              class="category-options"
            >
              <div
                v-for="option in category.options"
                :key="option.id"
                class="option-item"
              >
                <label class="option-label custom-checkbox">
                  <input
                    type="checkbox"
                    :checked="option.checked"
                    @change="toggleOption(category.id, option.id)"
                  />
                  <span class="option-text">{{ option.label }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Middle - Video and Detection Controls -->
    <div class="video-container">
      <div class="card-container">
        <div class="card-header">
          <span class="card-title">
            өʯ�Ƽ��
          </span>
          <div class="card-tools">
            <button class="toggle-button hover-lift" @click="toggleMonitoring" v-if="videoPlayerElement && !videoPlayerElement.paused">
              {{ isMonitoringActive ? 'ֹͣ���' : '�ָ����' }}
            </button>
            <button class="toggle-button hover-lift" @click="toggleDetectionBoxes" v-if="videoSource && detectionResults">
              {{ showDetectionBoxes ? '���ؼ���' : '��ʾ����' }}
            </button>
            <button class="toggle-button hover-lift yolo-toggle-button" @click="toggleYoloDetection">
              {{ useYoloDetection ? 'ͣ�ü����ӻ�' : '���ü����ӻ�' }}
            </button>
            <button class="toggle-button hover-lift yolo-button" @click="toggleDelayedPlayback" v-if="useYoloDetection">
              {{ isDelayedPlaybackActive ? 'ʵʱ����' : '�ӳٲ���' }}
              <span class="toggle-button-hint">{{ isDelayedPlaybackActive ? '(ʵʱ��⵫�ӳ���ʾ)' : '(������ἴʱ��ʾ)' }}</span>
            </button>
            <button class="toggle-button hover-lift yolo-reload-button" @click="initializeWebSocket" v-if="useYoloDetection && (!wsConnection || wsConnection.readyState !== 1)">
              ����YOLO
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="video-preview-wrapper">
            <!-- YS7 Cloud video player -->
            <video
              ref="videoPlayerElement"
              id="ys7-video-player"
              class="ys7-video video-player"
              controls
              autoplay
              muted
              @loadedmetadata="initializeCanvas"
              @play="handlePlay"
              @pause="handlePauseOrEnd"
              style="display: block;"
              @ended="handlePauseOrEnd">
            </video>
            <canvas ref="detectionCanvasElement1" class="detection-canvas" width="600" height="600"></canvas>
            <canvas ref="detectionCanvasElement" style="display: none;"></canvas>
            
            <!-- Loading overlay for YS7 player -->
            <div class="ys7-loading-overlay" v-if="isYs7Loading">
              <div class="processing-indicator"></div>
              <div class="processing-text">����өʯ����Ƶ��...</div>
            </div>
            
            <!-- Error overlay for YS7 player -->
            <div class="ys7-error-overlay" v-if="ys7Error">
              <div class="error-icon"></div>
              <div class="error-message">{{ ys7ErrorMessage }}</div>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <div class="detection-action">
            <div class="image-actions">
              <button class="detect-button" @click="triggerManualDetection" :disabled="isLoading || (videoPlayerElement && !videoPlayerElement.paused)">
                <div class="detect-button-content">
                  <i class="detect-icon" v-if="!isLoading"></i>
                  <span v-if="isLoading">�����...</span>
                  <span v-else>{{ (videoPlayerElement && !videoPlayerElement.paused) ? '�������Զ����' : '��⵱ǰ֡' }}</span>
                </div>
              </button>
              <div class="model-selector">
                <div class="selected-model" @click="isModelDropdownOpen = !isModelDropdownOpen">
                  <span class="model-icon"></span>
                  {{ useYoloDetection ? '����' : currentModel.name }}
                  <span class="dropdown-icon">��</span>
                </div>
                <div class="model-dropdown" v-if="isModelDropdownOpen">
                  <div v-if="useYoloDetection" class="model-option active">
                    <div class="model-name">YOLO ���ģ��</div>
                    <div class="model-description">ʹ��yolo11n-seg.ptģ�ͽ���ͨ�÷��ռ��</div>
                  </div>
                  <div v-else
                    v-for="model in models"
                    :key="model.id"
                    class="model-option"
                    @click="handleModelChange(model.id); isModelDropdownOpen = false"
                    :class="{ active: currentModel.id === model.id }"
                  >
                    <div class="model-name">{{ model.name }}</div>
                    <div class="model-description">{{ model.description }}</div>
                  </div>
                </div>
              </div>
            </div>
            <p v-if="error" class="error-message slide-in-up">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel - Detection Results -->
    <div class="result-panel">
      <div class="risk-analysis-header" :class="{ 'updating': isHeaderUpdating }">
        <div class="risk-analysis-icon"></div>
        <span class="risk-analysis-title">{{ getRiskStatusTitle }}</span>
        <span v-if="lastUpdated" class="last-updated-time">{{ formatUpdateTime(lastUpdated) }}</span>
      </div>
      <div class="result-panel-content">
        <!-- Qwen API Results Panel - Moved to the top and made more prominent -->
        <QwenResultsPanel
          :results="qwenResults"
          :showRawJson="true"
          class="qwen-results-panel-main"
          :key="lastUpdated ? lastUpdated.getTime() : 'initial'"
        />

        <!-- API Logs Section -->
        <div class="api-logs-container" v-if="apiLogs.length > 0">
          <div class="api-logs-header">
            <div class="api-logs-icon"></div>
            <span class="api-logs-title">���������</span>
            <button class="api-log-action-btn" @click="showLatestLogOnVideo" v-if="canShowLogOnVideo">
              ��ʾ����Ƶ��
            </button>
          </div>
          <div class="api-logs-content">
            <div v-for="(log, index) in apiLogs" :key="index" class="api-log-item">
              <div class="api-log-header">
                <span class="api-log-time">{{ new Date().toLocaleTimeString() }}</span>
                <span class="api-log-status success">�ɹ�</span>
            </div>
              <div class="api-log-details">
                <!-- Parse log content for more user-friendly display -->
                <div v-if="isJsonString(log)" class="api-log-formatted">
                  <template v-if="tryParseJsonLog(log)">
                    <div class="api-log-section">
                      <div class="api-log-section-title">��⵽��Ŀ�� ({{ getDetectionCount(log) }})</div>
                      <div class="api-log-detection-items">
                        <div v-for="(detection, idx) in getDetections(log)" :key="idx" class="api-log-detection-item">
                          <div class="detection-type" :class="getRiskClass(detection.risk_level)">
                            {{ detection.category || detection.label || 'δ֪Ŀ��' }}
                          </div>
                          <div class="detection-risk">{{ detection.risk_level || '�޷���' }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="api-log-section" v-if="getSafetyAnalysis(log)">
                      <div class="api-log-section-title">��ȫ�����뽨��</div>
                      <div class="api-log-analysis api-log-text-large" v-html="formatSafetyAnalysis(getSafetyAnalysis(log))"></div>
                      <div class="api-log-actions">
                        <button @click="showSafetyInPanel(log)" class="api-log-action-btn">
                          <span class="action-icon recommendation-icon"></span>
                          ���Ҳ��������ʾ
                        </button>
                      </div>
                    </div>
                  </template>
                  <div v-else class="api-log-json-viewer">
                    <div class="json-viewer-header">
                      <div class="json-viewer-type">JSON ��Ӧ����</div>
                      <div class="json-viewer-actions">
                        <button class="json-viewer-action-btn" @click="showLogOnVideo(log)" v-if="hasDetectionData(log)" title="����Ƶ����ʾ">
                          <span class="view-on-video-icon">???</span>
                        </button>
                        <button class="json-viewer-action-btn" @click="expandAllJson(log)" title="չ��ȫ��">
                          <span class="expand-icon">+</span>
                        </button>
                        <button class="json-viewer-action-btn" @click="collapseAllJson(log)" title="�۵�ȫ��">
                          <span class="collapse-icon">-</span>
                        </button>
                      </div>
                    </div>
                    <div class="json-viewer-content">
                      <template v-if="isJsonString(log)">
                        <div class="json-tree">
                          <component :is="jsonTreeNodeComponentRef" 
                            :data="parseJson(log)" 
                            path="json-root">
                          </component>
                        </div>
                      </template>
                      <pre v-else class="api-log-text api-log-text-large">{{ log }}</pre>
                    </div>
                  </div>
                </div>
                <pre v-else class="api-log-text api-log-text-large">{{ log }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- Conditional rendering based on detection state -->
        <div v-if="showLoadingMessage" class="loading-results">
          <p>���ڷ�����Ƶ֡...</p>
        </div>
        <div v-else-if="error" class="error-display">
          <p>{{ error }}</p>
        </div>
        <div v-else-if="detectionResults">
          <!-- High Risk Events -->
          <div class="risk-events-container high-risk" v-if="groupedHighRiskEventsForDisplay.length > 0">
            <div class="risk-summary">
              <div class="risk-count danger">{{ totalHighRiskCount }}</div>
              <div class="risk-label">�߷����¼�</div>
            </div>
            <div class="risk-categories">
              <template v-for="categoryGroup in groupedHighRiskEventsForDisplay" :key="categoryGroup.category">
                <div class="risk-category">
                  <div class="risk-category-header high-risk">
                    <span class="category-icon" :class="categoryGroup.category.toLowerCase() + '-icon'"></span>
                    <span class="category-name">{{ categoryGroup.category }}</span>
                    <span class="category-count danger">{{ categoryGroup.totalCount }}</span>
                  </div>
                  <div class="risk-events">
                    <div v-for="eventItem in categoryGroup.events" :key="eventItem.eventName" class="risk-event-item high-risk">
                      <div class="risk-event-content">
                        <div class="risk-event-name">{{ translateEventName(eventItem.eventName) }}
                          <span v-if="eventItem.count > 1" class="event-count">x{{ eventItem.count }}</span>
                        </div>
                      </div>
                      <div class="risk-level risk-high">�߷���</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- Low Risk Events -->
          <div class="risk-events-container low-risk" v-if="groupedLowRiskEventsForDisplay.length > 0">
            <div class="risk-summary">
              <div class="risk-count warning">{{ totalLowRiskCount }}</div>
              <div class="risk-label">�ͷ����¼�</div>
            </div>
            <div class="risk-categories">
              <template v-for="categoryGroup in groupedLowRiskEventsForDisplay" :key="categoryGroup.category">
                <div class="risk-category">
                  <div class="risk-category-header low-risk">
                    <span class="category-icon" :class="categoryGroup.category.toLowerCase() + '-icon'"></span>
                    <span class="category-name">{{ categoryGroup.category }}</span>
                    <span class="category-count warning">{{ categoryGroup.totalCount }}</span>
                  </div>
                  <div class="risk-events">
                    <div v-for="eventItem in categoryGroup.events" :key="eventItem.eventName" class="risk-event-item low-risk">
                      <div class="risk-event-content">
                        <div class="risk-event-name">{{ translateEventName(eventItem.eventName) }}
                          <span v-if="eventItem.count > 1" class="event-count">x{{ eventItem.count }}</span>
                        </div>
                      </div>
                      <div class="risk-level risk-low">�ͷ���</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- Safety Analysis Section -->
          <div class="safety-analysis-container" v-if="detectionResults.image_overall_safety_analysis_and_control_recommendations || analysisDescription">
            <div class="safety-analysis-header">
              <div class="safety-analysis-icon"></div>
              <span class="safety-analysis-title">��ȫ�����뽨��</span>
            </div>
            <div class="safety-analysis-content" v-if="detectionResults.image_overall_safety_analysis_and_control_recommendations" v-html="formatSafetyAnalysis(detectionResults.image_overall_safety_analysis_and_control_recommendations)"></div>
            <div class="safety-analysis-content" v-else-if="analysisDescription" v-html="formatSafetyAnalysis(analysisDescription)"></div>
          </div>

          <!-- Safe Container -->
          <div class="safe-container" v-if="totalHighRiskCount === 0 && totalLowRiskCount === 0 && !analysisDescription">
            <div class="safe-icon">?</div>
            <div class="safe-message">��ȫ</div>
            <div class="safe-description">δ��⵽�κη����¼�</div>
          </div>
        </div>
        <div v-else class="no-detection-results">
          <div class="no-risk-icon">??</div>
          <div class="no-risk-message">������Ƶ���Զ���ʼ��⡣��Ҳ������ͣ��Ƶ�����"��⵱ǰ֡"�ֶ�������</div>
        </div>

        <ObjectDetectionPanel
          :imagePreview="currentFramePreview"
          :selectedOptions="selectedOptions"
          :detectionResults="detectionResults"
        />
        <div class="stats-panel-horizontal" v-if="detectionResults">
          <div class="stats-item">
            <div class="stats-icon person-icon"></div>
            <div class="stats-info">
              <div class="stats-label">��Ա</div>
              <div class="stats-value">{{ getPersonCount() }}</div>
            </div>
          </div>
          <div class="stats-item">
            <div class="stats-icon vehicle-icon"></div>
            <div class="stats-info">
              <div class="stats-label">����</div>
              <div class="stats-value">{{ getVehicleCount() }}</div>
            </div>
          </div>
          <div class="stats-item">
            <div class="stats-icon machine-icon"></div>
            <div class="stats-info">
              <div class="stats-label">�豸</div>
              <div class="stats-value">{{ getMachineCount() }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import ObjectDetectionPanel from './ObjectDetectionPanel.vue';
import QwenResultsPanel from './QwenResultsPanel.vue';
import { detectObjects } from '../services/DetectionService';
import type { DetectionResult, HighRiskEvent, LowRiskEvent, DetectionObject } from '../services/DetectionService';
import axios from 'axios';
import Hls from 'hls.js'; // Import hls.js from the installed package
const canvasImg= new Image();
const lastImg=ref("")
const detectionCanvasElement1=ref(null)

// һMap
const processedTasks = new Map();

// Define recursive component for JSON tree display
const JsonTreeNode = {
  name: 'JsonTreeNode',
  props: {
    data: {
      type: [Object, Array],
      required: true
    },
    path: {
      type: String,
      required: true
    }
  },
  template: `
    <div>
      <div v-for="(value, key) in data" :key="key" class="json-tree-item">
        <div class="json-tree-key" @click="toggleJsonNode(path + '-' + key)">
          <span v-if="isObject(value)" class="json-tree-toggle">?</span>
          <span class="json-key-name">{{ key }}:</span>
          <span v-if="isObject(value)" class="json-type">{{ getJsonNodeType(value) }}</span>
          <span v-else class="json-value" :class="getJsonValueClass(value)">{{ formatJsonValue(value) }}</span>
        </div>
        <div :id="path + '-' + key" class="json-tree-children" v-if="isObject(value)">
          <json-tree-node :data="value" :path="path + '-' + key"></json-tree-node>
        </div>
      </div>
    </div>
  `,
  methods: {
    isObject(value) {
      return value !== null && (typeof value === 'object');
    },
    getJsonNodeType(value) {
      return Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`;
    },
    getJsonValueClass(value) {
      if (typeof value === 'string') return 'json-string';
      if (typeof value === 'number') return 'json-number';
      if (typeof value === 'boolean') return 'json-boolean';
      if (value === null) return 'json-null';
      return '';
    },
    formatJsonValue(value) {
      if (typeof value === 'string') return `"${value}"`;
      if (value === null) return 'null';
      return String(value);
    },
    toggleJsonNode(nodeId) {
      const node = document.getElementById(nodeId);
      if (node) {
        const isVisible = node.style.display !== 'none';
        node.style.display = isVisible ? 'none' : 'block';
        
        // Change toggle icon
        const toggleIcon = node.previousElementSibling.querySelector('.json-tree-toggle');
        if (toggleIcon) {
          toggleIcon.textContent = isVisible ? '?' : '��';
        }
      }
    }
  }
};

// Register component for Vue 3 composition API in script setup
import { defineComponent, h } from 'vue';

const jsonTreeNodeComponent = defineComponent({
  name: 'JsonTreeNode',
  props: {
    data: {
      type: [Object, Array],
      required: true
    },
    path: {
      type: String,
      required: true
    }
  },
  setup(props) {
    return () => {
      return h('div', {}, Object.entries(props.data).map(([key, value]) => {
        const isObj = value !== null && typeof value === 'object';
        
        const keyEl = h('div', {
          class: 'json-tree-key',
          onClick: () => {
            const nodeId = props.path + '-' + key;
            const node = document.getElementById(nodeId);
            if (node) {
              const isVisible = node.style.display !== 'none';
              node.style.display = isVisible ? 'none' : 'block';
              
              // Get toggle element
              const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
              if (toggleIcon) {
                toggleIcon.textContent = isVisible ? '?' : '��';
              }
            }
          }
        }, [
          isObj ? h('span', { class: 'json-tree-toggle' }, '?') : null,
          h('span', { class: 'json-key-name' }, `${key}:`),
          isObj 
            ? h('span', { class: 'json-type' }, 
                Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`)
            : h('span', { 
                class: ['json-value', 
                  typeof value === 'string' ? 'json-string' : 
                  typeof value === 'number' ? 'json-number' : 
                  typeof value === 'boolean' ? 'json-boolean' : 
                  value === null ? 'json-null' : '']
              }, typeof value === 'string' ? `"${value}"` : value === null ? 'null' : String(value))
        ]);
        
        const valueEl = isObj ? h('div', {
          class: 'json-tree-children',
          id: props.path + '-' + key,
          style: { display: 'none' }
        }, [
          h(jsonTreeNodeComponent, {
            data: value,
            path: props.path + '-' + key
          })
        ]) : null;
        
        return h('div', { class: 'json-tree-item' }, [keyEl, valueEl].filter(Boolean));
      }));
    };
  }
});

// Define window.Hls interface for TypeScript
declare global {
  interface Window {
    EZUIKit?: any;
  }
}

// --- API Logs State ---
const apiLogs = ref<string[]>([]);
const isPollingLogs = ref(false);
const analysisDescription = ref<string>('');

// Qwen API results state
interface QwenDetection {
  category: string;
  event_description: string;
  risk_level: string;
  confidence_score: number;
  bbox_2d: number[];
}

interface QwenRiskEvent {
  description: string;
  mitigation: string;
}

interface QwenResultsData {
  detections: QwenDetection[];
  description: string;
  high_risk_events: QwenRiskEvent[];
  low_risk_events: QwenRiskEvent[];
}

const qwenResults = ref<QwenResultsData | null>(null);
const lastUpdated = ref<Date | null>(null);
const isHeaderUpdating = ref(false);

// 㶯̬⣬Qwen仯
const getRiskStatusTitle = computed(() => {
  if (!qwenResults.value) return 'ͳ';
  
  // ȡ/ͷ¼
  const highRiskCount = qwenResults.value.high_risk_events?.length || 0;
  const lowRiskCount = qwenResults.value.low_risk_events?.length || 0;
  const detectionCount = qwenResults.value.detections?.length || 0;
  
  // ݷճ̶ɲͬ
  if (highRiskCount > 0) {
    return `ͳ (${highRiskCount}߷)`;
  } else if (lowRiskCount > 0) {
    return `ͳ (${lowRiskCount}ͷ)`;
  } else if (detectionCount > 0) {
    return `(${detectionCount})`;
  } else {
    return 'ȫ';
  }
});

// ʽ
const formatUpdateTime = (time: Date): string => {
  return `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}:${time.getSeconds().toString().padStart(2, '0')}`;
};

// qwenResults仯շͷ¶
watch(() => qwenResults.value, (newVal, oldVal) => {
  if (newVal) {
    // ʱ
    lastUpdated.value = new Date();
    
    // ͷ¶
    isHeaderUpdating.value = true;
    setTimeout(() => {
      isHeaderUpdating.value = false;
    }, 2000);
  }
}, { deep: true });

// JSON viewer utilities
const isJsonString = (str: string): boolean => {
  try {
    // Check if it's a valid JSON string or an already parsed object
    if (typeof str === 'object') return true;
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

// Make the JSON Tree Node component available in the template
const jsonTreeNodeComponentRef = computed(() => jsonTreeNodeComponent);

const parseJson = (jsonStr: string): any => {
  try {
    if (typeof jsonStr === 'object') return jsonStr;
    
    // Clean JSON string by removing comments before parsing
    const cleanedJsonStr = removeJsonComments(jsonStr);
    return JSON.parse(cleanedJsonStr);
  } catch (e) {
    console.error('JSON parse error:', e);
    return { error: "Invalid JSON" };
  }
};

// Function to remove comments from JSON strings
const removeJsonComments = (jsonString: string): string => {
  if (!jsonString) return '';
  
  // Replace single-line comments (both // and /* */) with empty strings
  let cleanedString = jsonString
    .replace(/\/\/.*$/gm, '') // Remove single-line comments starting with //
    .replace(/\/\*[\s\S]*?\*\//gm, ''); // Remove multi-line comments /* */
    
  // Fix trailing commas that might be left after removing comments
  cleanedString = cleanedString
    .replace(/,(\s*[\]}])/g, '$1');
    
  return cleanedString;
};

const isObject = (value: any): boolean => {
  return value !== null && (typeof value === 'object');
};

const getJsonNodeType = (value: any): string => {
  return Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`;
};

const getJsonValueClass = (value: any): string => {
  if (typeof value === 'string') return 'json-string';
  if (typeof value === 'number') return 'json-number';
  if (typeof value === 'boolean') return 'json-boolean';
  if (value === null) return 'json-null';
  return '';
};

const formatJsonValue = (value: any): string => {
  if (typeof value === 'string') return `"${value}"`;
  if (value === null) return 'null';
  return String(value);
};

const toggleJsonNode = (nodeId: string): void => {
  const node = document.getElementById(nodeId);
  if (node) {
    const isVisible = node.style.display !== 'none';
    node.style.display = isVisible ? 'none' : 'block';
    
    // Change toggle icon
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = isVisible ? '?' : '��';
    }
  }
};

const expandAllJson = (jsonStr: string): void => {
  const jsonTree = document.querySelectorAll('.json-tree-children');
  jsonTree.forEach(node => {
    (node as HTMLElement).style.display = 'block';
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = '��';
    }
  });
};

const collapseAllJson = (jsonStr: string): void => {
  const jsonTree = document.querySelectorAll('.json-tree-children');
  jsonTree.forEach(node => {
    (node as HTMLElement).style.display = 'none';
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = '?';
    }
  });
};

// Parse Qwen API result from log
const parseQwenResult = (log: string): void => {
  try {
    // ƥjson - ```json```֮
    let jsonStr = log;
    
    if (log.includes('```json')) {
      // Markdown, ȡJSON
      const jsonMatch = log.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        jsonStr = jsonMatch[1].trim();
      }
    } else if (log.includes('{') && log.includes('}')) {
      // ֱӴ־ȡJSON
      const startPos = log.indexOf('{');
      const endPos = log.lastIndexOf('}') + 1;
      if (startPos !== -1 && endPos !== -1) {
        jsonStr = log.substring(startPos, endPos);
      }
    }

    // JSON
    const parsedData = JSON.parse(jsonStr);
    
    // ֤qwenResults
    if (parsedData && (
      parsedData.detections || 
      parsedData.description || 
      parsedData.high_risk_events || 
      parsedData.low_risk_events
    )) {
      // qwenResultsʱ
      qwenResults.value = parsedData;
      lastUpdated.value = new Date(); // ʱ
      console.log('Updated Qwen API results:', qwenResults.value, 'ʱ:', formatUpdateTime(lastUpdated.value));
      
      // ¼ȷ
      setTimeout(() => {
        // Vue DOM
        if (qwenResults.value) {
          const temp = {...qwenResults.value};
          qwenResults.value = temp;
        }
      }, 0);
    }
  } catch (error) {
    console.error('Failed to parse Qwen API result:', error);
  }
};

// Add a function to manually add logs for demonstration
const addApiLog = (log: string) => {
  // ʱʹUIʼշˢ״̬
  lastUpdated.value = new Date();
  
  try {
    // ȴĳɹ/ϢȷUI״̬
    if (log.includes('INFO:yolo_video_processor:Qwen APIسɹ')) {
      console.log('⵽Qwen APIسɹUIʾ');
      
      // ǿʱ͸״̬
      lastUpdated.value = new Date();
      isHeaderUpdating.value = true;
      
      // нӼָʾ
      if (qwenResults.value) {
        // "ڸ"ָʾ
        const updatedResults = { ...qwenResults.value };
        updatedResults.description = updatedResults.description + '\n\nڸ·......';
        qwenResults.value = updatedResults;
      }
      
      // 2ͷ¶
      setTimeout(() => {
        isHeaderUpdating.value = false;
      }, 2000);
    }

    // Qwen API
    if (log.includes('INFO:yolo_video_processor:Qwen API:')) {
      console.log('⵽Qwen API׼');
      
      // ȡ
      const resultPrefix = 'INFO:yolo_video_processor:Qwen API: ';
      let jsonText = log.substring(log.indexOf(resultPrefix) + resultPrefix.length);
      
      // Ϣ
      console.log(`Ľı: ${jsonText.length} ַ`);
      console.log(`ıǰ200ַ: ${jsonText.substring(0, 200)}`);
      
      try {
        // Ԥ - MarkdownʽJSON
        
        // Markdown, ȡJSON
        if (jsonText.includes('```json')) {
          const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch && jsonMatch[1]) {
            console.log('MarkdownȡJSON');
            jsonText = jsonMatch[1].trim();
          }
        } 
        // ȡJSON
        else if (jsonText.includes('{') && jsonText.includes('}')) {
          console.log('ȡJSON');
          const startPos = jsonText.indexOf('{');
          const endPos = jsonText.lastIndexOf('}') + 1;
          if (startPos !== -1 && endPos !== -1) {
            jsonText = jsonText.substring(startPos, endPos);
          }
        }
        
        // PythonʽJSON
        jsonText = jsonText
          .replace(/'/g, '"')     // Ϊ˫
          .replace(/True/g, 'true')   // PythonTrueתJStrue
          .replace(/False/g, 'false') // PythonFalseתJSfalse
          .replace(/None/g, 'null');  // PythonNoneתJSnull
        
        // ԽJSON
        let parsedJson;
        try {
          console.log('ԽJSON:', jsonText.substring(0, 100) + '...');
          parsedJson = JSON.parse(jsonText);
          console.log('ɹJSONṹ:', parsedJson);
        } catch (jsonError) {
          console.error('JSONʧ:', jsonError);
          console.log('ԽиϸJSON');
          
          // Ըǿ޸ɾתŲҵЧJSON
          try {
            // ɾܸŵתַ
            const cleanedText = jsonText.replace(/\\n/g, ' ').replace(/\\"/g, '"').replace(/\\\\/g, '\\');
            
            // ѰЧJSONʼͽ
            const validStartPos = cleanedText.indexOf('{');
            const validEndPos = cleanedText.lastIndexOf('}') + 1;
            
            if (validStartPos !== -1 && validEndPos !== -1 && validEndPos > validStartPos) {
              const validJson = cleanedText.substring(validStartPos, validEndPos);
              console.log('ȡǱЧJSON:', validJson.substring(0, 100) + '...');
              parsedJson = JSON.parse(validJson);
              console.log('޸ɹЧJSON');
            } else {
              throw new Error('޷ҵЧJSON');
            }
          } catch (deepError) {
            console.error('޸JSONʧ:', deepError);
            
            // гԶʧһĽʹԭʼıΪ
            parsedJson = {
              detections: [],
              description: jsonText.includes('') ? 
                jsonText.replace(/^INFO:.*?:/, '') : // ɾ־ǰ׺
                'յʽȷ',
              high_risk_events: [],
              low_risk_events: []
            };
            console.log('ĬϽṹʹԭʼıΪ');
          }
        }
        
        // ṹĽȷֶζĬֵ
        const qwenResultData = {
          detections: parsedJson.detections || [],
          description: parsedJson.description || parsedJson.safety_analysis || (typeof parsedJson === 'string' ? parsedJson : ''),
          high_risk_events: parsedJson.high_risk_events || [],
          low_risk_events: parsedJson.low_risk_events || []
        };
        
        // ����״̬
        qwenResults.value = qwenResultData;
        lastUpdated.value = new Date();
        isHeaderUpdating.value = true;
        
        setTimeout(() => {
          isHeaderUpdating.value = false;
        }, 2000);
        
        console.log('ʵʱ����Qwen API���:', qwenResults.value);
        
        // ��������ӵ���־
        apiLogs.value.unshift(log);
        
        // ���¼����
        updateDetectionResultsFromResponse(parsedJson);
      } catch (e) {
        console.error('����Qwen API�������ʱ����:', e);
        
        // ����ʱ��Ȼ����UI״̬����ʾ������Ϣ���ṩ�����õ�����
        qwenResults.value = {
          detections: [],
          description: `����Qwen API���ʱ������ԭ������Ǹ�ʽ������Ԥ�ڡ���������ʾԭʼ����:\n\n${jsonText.substring(0, 500)}${jsonText.length > 500 ? '...' : ''}`,
          high_risk_events: [],
          low_risk_events: []
        };
        
        lastUpdated.value = new Date();
        isHeaderUpdating.value = true;
        setTimeout(() => { isHeaderUpdating.value = false; }, 2000);
        
        // ����־���ӵ�API��־�б�
        apiLogs.value.unshift(log);
      }
    } else {
      // ����������־
      apiLogs.value.unshift(log);
    }
  } catch (error) {
    // �����������������е��κδ���
    console.error('����API��־ʧ��:', error);
    apiLogs.value.unshift(log);
  }
  
  // ֻ�������µ�5����־
  if (apiLogs.value.length > 5) {
    apiLogs.value = apiLogs.value.slice(0, 5);
  }
};

// Function to update detection results from API response
const updateDetectionResultsFromResponse = (response: any) => {
  if (!detectionResults.value) {
    detectionResults.value = {
      objects: [],
      summary: {},
      description: '',
      input_width: 0,
      input_height: 0,
      high_risk_events: [],
      low_risk_events: []
    };
  }
  
  // Update safety analysis
  if (response.image_overall_safety_analysis_and_control_recommendations) {
    detectionResults.value.image_overall_safety_analysis_and_control_recommendations = 
      response.image_overall_safety_analysis_and_control_recommendations;
  } else if (response.description) {
    detectionResults.value.description = response.description;
  }
  
  // Update detection objects
  if (response.detections && response.detections.length > 0) {
    detectionResults.value.objects = response.detections;
  }
  
  // Add risk events
  if (response.high_risk_events && response.high_risk_events.length > 0) {
    detectionResults.value.high_risk_events = response.high_risk_events;
  }
  
  if (response.low_risk_events && response.low_risk_events.length > 0) {
    detectionResults.value.low_risk_events = response.low_risk_events;
  }
  
  // Try to extract objects from raw_json if available
  if (response.raw_json && !detectionResults.value.objects.length) {
    try {
      const rawData = typeof response.raw_json === 'string' ? 
        parseJson(response.raw_json) : response.raw_json;
      
      let objects: any[] = [];
      
      // Extract different object types
      if (rawData.person && Array.isArray(rawData.person)) {
        objects = [...objects, ...rawData.person.map((p: any) => ({...p, category: '��Ա'}))];
      }
      
      if (rawData.vehicle && Array.isArray(rawData.vehicle)) {
        objects = [...objects, ...rawData.vehicle.map((v: any) => ({...v, category: '����'}))];
      }
      
      if (rawData.construction_machinery && Array.isArray(rawData.construction_machinery)) {
        objects = [...objects, ...rawData.construction_machinery.map((m: any) => ({...m, category: '��е'}))];
      }
      
      if (objects.length > 0) {
        detectionResults.value.objects = objects;
      }
    } catch (e) {
      console.warn('��raw_json��ȡ����ʧ��:', e);
    }
  }
};

// ��ʾ��ȫ���鵽�Ҳ����
const showSafetyRecommendation = (text: string) => {
  if (!text) return;
  
  // ȷ���ı���ʽ��ȷ�����û�б���������
  let formattedText = text;
  if (!formattedText.includes('### ͼ�����尲ȫ���շ�����ܿؽ���') && 
      !formattedText.includes('### ��ȫ�����뽨��')) {
    formattedText = '### ��ȫ�����뽨��\n\n' + formattedText;
  }
  
  // ���û�м����������һ��
  if (!detectionResults.value) {
    detectionResults.value = {
      objects: [],
      summary: {},
      description: formattedText,
      input_width: 0,
      input_height: 0,
      high_risk_events: [],
      low_risk_events: [],
      image_overall_safety_analysis_and_control_recommendations: formattedText
    };
  } else {
    // ����������еļ����
    detectionResults.value.description = formattedText;
    detectionResults.value.image_overall_safety_analysis_and_control_recommendations = formattedText;
    
    // ��������ȫ�������
    setTimeout(() => {
      const safetyPanel = document.querySelector('.safety-analysis-container');
      if (safetyPanel) {
        safetyPanel.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }
  
  // ���canvas�ϵļ�����Ϊ��������ֻ��ע��ȫ����
  if (showDetectionBoxes.value === false && detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// ��ǿ��ʽ����ȫ�����ı�������רע����ʾ��ȫ�����͹ܿؽ���
const formatSafetyAnalysis = (content: string): string => {
  if (!content) return '';
  
  // ���������ı�
  let cleanedContent = content
    .replace(/\\n/g, '\n')  // ����JSON�е�ת�廻�з�
    .replace(/\\"/g, '"');  // ����JSON�е�ת������
  
  // ͻ����ʾ�ؼ���
  const keywordsHighlight = [
    { pattern: /�߷���/g, replacement: '<span class="highlight-high-risk">�߷���</span>' },
    { pattern: /�з���/g, replacement: '<span class="highlight-medium-risk">�з���</span>' },
    { pattern: /�ͷ���/g, replacement: '<span class="highlight-low-risk">�ͷ���</span>' },
    { pattern: /��ȫ����/g, replacement: '<span class="highlight-warning">��ȫ����</span>' },
    { pattern: /����|Σ��/g, replacement: '<span class="highlight-warning">$&</span>' },
    { pattern: /����|Ӧ��|��Ҫ|����/g, replacement: '<span class="highlight-important">$&</span>' },
    { pattern: /����(.{1,10}?)����/g, replacement: '����<span class="highlight-warning">$1</span>����' },
    { pattern: /ע��(.{1,10}?)��ȫ/g, replacement: '<span class="highlight-important">ע��$1��ȫ</span>' }
  ];
  
  keywordsHighlight.forEach(item => {
    cleanedContent = cleanedContent.replace(item.pattern, item.replacement);
  });
  
  // ���⴦����ȫ��������
  cleanedContent = cleanedContent.replace(
    /### ͼ�����尲ȫ���շ�����ܿؽ���/g, 
    '<h3 class="safety-analysis-heading">ͼ�����尲ȫ���շ�����ܿؽ���</h3>'
  );
  
  // �����µļ򻯱���
  cleanedContent = cleanedContent.replace(
    /### ��ȫ�����뽨��/g, 
    '<h3 class="safety-analysis-heading">��ȫ�����뽨��</h3>'
  );
  
  // ��������Markdown���
  let formatted = cleanedContent
    .replace(/\n/g, '<br>')                         // ����
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // �Ӵ�
    .replace(/\*(.*?)\*/g, '<em>$1</em>')           // б��
    .replace(/#{3}([^<]*?)(?:\n|$)/g, '<h3>$1</h3>')   // h3���� (�ų��Ѵ������������)
    .replace(/#{2}(.*?)(?:\n|$)/g, '<h2>$1</h2>')   // h2����
    .replace(/#{1}(.*?)(?:\n|$)/g, '<h1>$1</h1>')   // h1����
    .replace(/- (.*?)(?:\n|$)/g, '<li>$1</li>')     // �����б�
    .replace(/<li>(.*?)<\/li>/g, '<ul><li>$1</li></ul>') // ���б����װ��ul��
    .replace(/<\/ul><ul>/g, '');                    // �ϲ����ڵ�ul��ǩ
  
  // ���������б��������������γ������б�
  formatted = formatted.replace(/(\d+)\.\s+(.*?)(?:<br>|$)/g, '<ol start="$1"><li>$2</li></ol>');
  formatted = formatted.replace(/<\/ol><ol start="\d+">/g, '');
  
  // ��ǿ�԰�ȫ�����ͽ������ʽ
  formatted = formatted
    .replace(/<strong>(.*?����.*?)<\/strong>/g, '<strong class="safety-hazard">$1</strong>')
    .replace(/<strong>(.*?����.*?)<\/strong>/g, '<strong class="safety-recommendation">$1</strong>')
    .replace(/<h3>(.*?��ȫ.*?)<\/h3>/g, '<h3 class="safety-section">$1</h3>')
    .replace(/<h3>(.*?����.*?)<\/h3>/g, '<h3 class="hazard-section">$1</h3>')
    .replace(/<h3>(.*?����.*?)<\/h3>/g, '<h3 class="recommendation-section">$1</h3>');
    
  return formatted;
};

// ��ȡ��ȫ����ĺ�����ǿ
const extractSafetyRecommendation = (log: string): string => {
  try {
    // ֱ�Ӽ���Ƿ������ȫ�����½ڱ���
    if (typeof log === 'string' && log.includes('### ͼ�����尲ȫ���շ�����ܿؽ���')) {
      const startIndex = log.indexOf('### ͼ�����尲ȫ���շ�����ܿؽ���');
      // �ҳ��½ڽ���λ�ã�ͨ������һ����������ݽ�����
      let endIndex = log.length;
      const nextHeadingMatch = log.substring(startIndex + 1).match(/\n#{2,3}\s+/);
      if (nextHeadingMatch && nextHeadingMatch.index) {
        endIndex = startIndex + 1 + nextHeadingMatch.index;
      }
      
      return log.substring(startIndex, endIndex).trim();
    }
    
    // ���Խ���JSON��ȷ��������ע�ͣ�
    const cleanedLog = typeof log === 'string' ? removeJsonComments(log) : log;
    const parsed = typeof cleanedLog === 'string' ? parseJson(cleanedLog) : cleanedLog;
    let safetyContent = '';
    
    // ���ȳ��Դӳ����ֶλ�ȡ
    const possibleFields = [
      'description',
      'safety_analysis',
      'recommendations',
      'image_overall_safety_analysis_and_control_recommendations',
      'safety_recommendations',
      'analysis'
    ];
    
    for (const field of possibleFields) {
      if (parsed[field]) {
        safetyContent = parsed[field];
        // ������ݲ��������⣬�����������尲ȫ�����ֶΣ����ӱ���
        if (!safetyContent.includes('### ͼ�����尲ȫ���շ�����ܿؽ���')) {
          safetyContent = '### ͼ�����尲ȫ���շ�����ܿؽ���\n\n' + safetyContent;
        }
        return safetyContent;
      }
    }
    
    // Ȼ���Դ�raw_json�в���
    if (parsed.raw_json) {
      try {
        const rawJsonCleaned = typeof parsed.raw_json === 'string' ? 
          removeJsonComments(parsed.raw_json) : JSON.stringify(parsed.raw_json);
        const rawData = typeof rawJsonCleaned === 'string' ? 
          JSON.parse(rawJsonCleaned) : rawJsonCleaned;
        
        for (const field of possibleFields) {
          if (rawData[field]) {
            safetyContent = rawData[field];
            // ������ݲ��������⣬���ӱ���
            if (!safetyContent.includes('### ͼ�����尲ȫ���շ�����ܿؽ���')) {
              safetyContent = '### ͼ�����尲ȫ���շ�����ܿؽ���\n\n' + safetyContent;
            }
            return safetyContent;
          }
        }
      } catch (e) {
        console.error('����raw_jsonʧ��', e);
      }
    }
    
    // ���Դ�ԭʼ�ַ�������ȡ��ȫ��������
    if (typeof log === 'string') {
      // ���ȳ��Բ��Ұ�ȫ�����͹ܿؽ����½�
      if (log.includes('��ȫ����ܽ�') || log.includes('�ܿش�ʩ������')) {
        // ������ȡ�����½�
        const safetySection = log.substring(log.indexOf('ͼ�����尲ȫ���շ�����ܿؽ���'));
        if (safetySection.length > 50) {  // ȷ���������㹻����
          return safetySection;
        }
      }
      
      // Ȼ����ʹ���������ʽƥ��
      const safetyPatterns = [
        /(?:��ȫ����|��ȫ����|�ܿؽ���|���շ���|ͼ�����尲ȫ[����]*����[����]�ܿؽ���)[:��]([\s\S]+?)(?:\n\n|\Z)/i,
        /(?:��ȫ����ܽ�|�ܿش�ʩ������)([\s\S]+?)(?:\n\n|\Z)/i
      ];
      
      for (const pattern of safetyPatterns) {
        const match = log.match(pattern);
        if (match && match[1]) {
          let extractedContent = match[1].trim();
          // ���ӱ��⣬���������
          if (!extractedContent.includes('ͼ�����尲ȫ���շ�����ܿؽ���')) {
            return '### ͼ�����尲ȫ���շ�����ܿؽ���\n\n' + extractedContent;
          }
          return extractedContent;
        }
      }
      
      // ����鳤�ı�����
      for (const key in parsed) {
        if (typeof parsed[key] === 'string' && 
            /��ȫ|����|����|����|��ʩ|analysis|safety|risk|recommendation/i.test(parsed[key]) &&
            parsed[key].length > 100) {
          safetyContent = parsed[key];
          // ���ӱ��⣬���������
          if (!safetyContent.includes('### ͼ�����尲ȫ���շ�����ܿؽ���')) {
            safetyContent = '### ͼ�����尲ȫ���շ�����ܿؽ���\n\n' + safetyContent;
          }
          return safetyContent;
        }
      }
    }
    
    return '';
  } catch (e) {
    console.error('��ȡ��ȫ����ʧ��', e);
    
    // �������򵥵��������ʽ��ȡ
    if (typeof log === 'string') {
      const simplePattern = /(?:ͼ�����尲ȫ���շ�����ܿؽ���[\s\S]*)/i;
      const match = log.match(simplePattern);
      if (match && match[0]) {
        return match[0].trim();
      }
    }
    return '';
  }
};

// --- Data structures for detection options (from App.vue) ---
interface DetectionOption {
  id: string;
  label: string;
  checked: boolean;
}

interface DetectionCategory {
  id: string;
  label: string;
  expanded: boolean;
  options: DetectionOption[];
}

const detectionOptions = ref<DetectionCategory[]>([
  {
    id: 'person',
    label: '��Ա���',
    expanded: true,
    options: [
      { id: 'person_count', label: '��Աͳ��', checked: false },
      { id: 'person_wear', label: '��Ա����', checked: false },
      { id: 'person_status', label: '��Ա״̬', checked: false },
      { id: 'hoisting_personnel', label: '��װ��ҵ��Ա', checked: false },
      { id: 'high_altitude_personnel', label: '�߿���ҵ��Ա', checked: false }
    ]
  },
  {
    id: 'machine',
    label: '��е���',
    expanded: true,
    options: [
      { id: 'vehicle_count', label: '����ͳ��', checked: false },
      { id: 'vehicle_status', label: '����״̬', checked: false },
      { id: 'equipment_status', label: '��е����״̬', checked: false },
      { id: 'hoisting_status', label: '��װ״̬', checked: false }
    ]
  },
  {
    id: 'material',
    label: '���ϼ��',
    expanded: true,
    options: [
      { id: 'subgrade_monitoring', label: '·�����', checked: false },
      { id: 'slope_monitoring', label: '���¼��', checked: false },
      { id: 'pavement_monitoring', label: '·����', checked: false }
    ]
  },
  {
    id: 'regulation',
    label: '������',
    expanded: true,
    options: [
      { id: 'operation_area_protection', label: '��ҵ������', checked: false }
    ]
  },
  {
    id: 'environment',
    label: '�������',
    expanded: true,
    options: [
      { id: 'fire_detection', label: '������', checked: false },
      { id: 'smoke_detection', label: '�������', checked: false }
    ]
  }
]);

const riskCategories = [
  {
    id: 'all',
    label: 'ȫ�����ռ��',
    options: ['person_count', 'person_wear', 'person_status', 'hoisting_personnel', 'high_altitude_personnel',
              'vehicle_count', 'vehicle_status', 'equipment_status', 'hoisting_status',
              'subgrade_monitoring', 'slope_monitoring', 'pavement_monitoring',
              'operation_area_protection',
              'fire_detection', 'smoke_detection']
  },
  { id: 'general', label: 'ͨ�÷��ռ��', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection']},
  { id: 'subgrade', label: '·�����ռ��', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'subgrade_monitoring', 'slope_monitoring']},
  { id: 'pavement', label: '·����ռ��', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'pavement_monitoring']},
  { id: 'bridge', label: '�������ռ��', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'hoisting_personnel', 'high_altitude_personnel', 'hoisting_status', 'operation_area_protection']}
];

const selectedCategory = ref('all');

const filteredDetectionOptions = computed(() => {
  return detectionOptions.value;
});

const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId;
  const category = riskCategories.find(cat => cat.id === categoryId);
  if (category) {
    detectionOptions.value.forEach(detCat => {
      detCat.options.forEach(opt => {
        opt.checked = category.options.includes(opt.id);
      });
    });

   
    useYoloDetection.value = true;
    
    // ����UI��ʾ��YOLOģ������
    const yoloBadge = document.querySelector('.yolo-badge');
    if (yoloBadge) {
      yoloBadge.textContent = 'YOLO11x-seg';
    }
    
    // ��ʼ��WebSocket����
    initializeWebSocket();
    
    // Ĭ�������ӳٲ���ģʽ�Ա�֤YOLO�����������ʾ����Ƶ��
    if (!isDelayedPlaybackActive.value) {
      // ����ʾ
      addApiLog(`INFO:qwen-vl-api:YOLO��������ã�ʹ���ӳٲ���ģʽ�Ա�֤���������Ƶͬ����ʾ`);
      
      // �ӳ�500ms�������ӳٲ��ţ�ȷ��WebSocket�����ѽ���
      setTimeout(() => {
        isDelayedPlaybackActive.value = true;
        
        // �����Ƶ���ڲ��ţ��������岥��
        if (videoPlayerElement.value) {
          if (!videoPlayerElement.value.paused) {
            videoPlayerElement.value.pause();
          }
          startBufferedPlayback();
        }
      }, 500);
    }
    
    // �����Ƶ���ڲ��ţ�������ʼYOLO���
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // ��ֹͣ��ǰ�ļ��
      if (frameProcessingInterval) {
        clearInterval(frameProcessingInterval);
        frameProcessingInterval = null;
      }

      // ����YOLO���
      handlePlay();
    }
  }
};

const toggleCategory = (categoryId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    category.expanded = !category.expanded;
  }
};

const toggleOption = (categoryId: string, optionId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    const option = category.options.find(opt => opt.id === optionId);
    if (option) {
      option.checked = !option.checked;
    }
  }
};

const selectedOptions = computed(() => {
  const options: string[] = [];
  detectionOptions.value.forEach(category => {
    category.options.forEach(option => {
      if (option.checked) {
        options.push(option.id);
      }
    });
  });
  return options;
});

// --- Video and Canvas State ---
// ����Ĭ����ƵԴ�������Ǳ����ļ�������URL
const videoSource = ref("./1747124126409.mp4");

// ���������������߼�����̬������ƵԴ
// ����������ж�ȡ������û�ѡ������

// ����Ƿ�ʹ��YOLO���
const useYoloDetection = ref(false);
const videoPlayerElement = ref<HTMLVideoElement | null>(null);
const detectionCanvasElement = ref<HTMLCanvasElement | null>(null);
const currentFramePreview = ref<string>('');

// --- Detection State & Results ---
const isLoading = ref(false);
const error = ref('');
const detectionResults = ref<DetectionResult | null>(null);
const showLoadingMessage = ref(false); // ����״̬���ڿ��Ƽ�����ʾ����ʾ

// --- Model Selection (from App.vue) ---
const currentModel = ref({
  id: 'lite',
  name: '���� Lite',
  description: '��������ʶ��'
});
const models = [
  { id: 'pro', name: '���� Pro', description: 'ȫ���ͽ������ʶ��', icon: 'model-pro-icon' },
  { id: 'lite', name: '���� Lite', description: '��������ʶ��', icon: 'model-lite-icon' }
];
const isModelDropdownOpen = ref(false);

const handleModelChange = (modelId: string) => {
  // �����ǰ��YOLO���ģʽ���������л�ģ��
  if (useYoloDetection.value) {
    console.log('YOLO���ģʽ�²����л�ģ��');
    return;
  }

  const selectedModel = models.find(m => m.id === modelId);
  if (selectedModel) {
    currentModel.value = selectedModel;
  }
  // If detection is ongoing or video is playing, might need to re-trigger or notify
};

// --- Detection Box Toggles (from App.vue) ---
const showDetectionBoxes = ref(true);
const showHighRiskBoxes = ref(true);
const showLowRiskBoxes = ref(true);
const showPersonBoxes = ref(false); // Default to false as per App.vue logic
const showVehicleBoxes = ref(false);
const showMachineBoxes = ref(false);

const toggleDetectionBoxes = () => {
  showDetectionBoxes.value = !showDetectionBoxes.value;

  // ����Ƿ��д������ͼ��
  if (detectionResults.value && (detectionResults.value.processed_url || detectionResults.value.visualized_image_url)) {
    const processedUrl = detectionResults.value.visualized_image_url || detectionResults.value.processed_url;
    if (processedUrl) {
      if (showDetectionBoxes.value) {
        // ����û�Ҫ��ʾ���򣬲����д������ͼ�񣬼��ش������ͼ��
        const processedImage = new Image();
        const cacheBuster = new Date().getTime();
        processedImage.onload = () => {
          if (detectionCanvasElement.value) {
            const ctx = detectionCanvasElement.value.getContext('2d');
            if (ctx) {
              ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
              ctx.drawImage(
                processedImage,
                0, 0,
                detectionCanvasElement.value.width,
                detectionCanvasElement.value.height
              );
            }
          }
        };
        processedImage.onerror = () => {
          console.error('���ش������ͼ��ʧ��');
          clearCanvas();
        };
        processedImage.src = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
      } else {
        // ����û�Ҫ���ؼ����������
        clearCanvas();
      }
      return;
    }
  }

  // ���û�д������ͼ��ʹ��ԭʼ�����߼�
  if (showDetectionBoxes.value && detectionResults.value) {
    drawDetectionBoxes(); // ʹ�ó������
  } else {
    clearCanvas();
  }
};

// Placeholder: toggleSpecificBoxes - if needed, copy from App.vue and adapt

const initializeCanvas = () => {
  if (videoPlayerElement.value && detectionCanvasElement.value && detectionCanvasElement1.value) {
    const video = videoPlayerElement.value;
    const canvas = detectionCanvasElement.value;
    const canvas1 = detectionCanvasElement1.value;
    
    // ����canvas�ߴ�����Ƶһ��
    canvas.width = video.videoWidth || 640;
    canvas.height = video.videoHeight || 360;
    
    // ͬʱ������Ҫ��ʾ��canvas�ߴ�
    canvas1.width = video.videoWidth || 640;
    canvas1.height = video.videoHeight || 360;
    
    console.log(`Canvas initialized to video dimensions: ${canvas.width}x${canvas.height}`);
  }
};

const clearCanvas = () => {
  if (detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// Fix the frame capture function to ensure proper base64 encoding
const captureFrame = (): string | null => {
  if (!videoPlayerElement.value) return null;
  const video = videoPlayerElement.value;
  
  try {
    // ʹ������canvas��������Ƶ֡
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth || 640;
    canvas.height = video.videoHeight || 360;
    
    // ȷ����Ƶ�ߴ���Ч
    if (canvas.width <= 0 || canvas.height <= 0) {
      console.error('��Ч����Ƶ�ߴ�:', canvas.width, canvas.height);
      return null;
    }
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('�޷���ȡcanvas������');
      return null;
    }
    
    // ����Ƶ֡���Ƶ�canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // ʹ�ýϵ͵�JPEG�����Լ�������������ߴ���Ч��
    // ����������DataURL (����: "data:image/jpeg;base64,/9j/4AAQ...")
    return canvas.toDataURL('image/jpeg', 0.7);
  } catch (error) {
    console.error('������Ƶ֡ʱ����:', error);
    return null;
  }
};

const generatePrompt = (): string => { // Copied from App.vue, might need video-specific adjustments
  const promptMap: Record<string, string> = {
    'person_count': '��Ա��������ȫ״̬',
    'person_wear': '��Ա��ȫװ����������ȫñ/���ⱳ�ģ�',
    'person_status': '��Ա������̬���գ������ǲ���ȫ���ƣ�',
    'hoisting_personnel': '��װ����Ա��ȫ�������·�/��ת�뾶�ڣ�',
    'high_altitude_personnel': '�߿���ҵ��Ա׹�����',
    'vehicle_count': '�����ֲ��밲ȫ״̬',
    'vehicle_status': '������ʻ/ͣ�ŷ���',
    'equipment_status': 'ʩ����е���з���',
    'hoisting_status': '��װ��ҵ��ȫ���ȶ���/������/�źţ�',
    'subgrade_monitoring': '·�����գ�����/�ѷ�/������',
    'slope_monitoring': '���·��գ�����/����/��ʯ��',
    'pavement_monitoring': '·����գ��ѷ�/����/��ˮ��',
    'operation_area_protection': '��ҵ��������ʩ������/��ʾ��',
    'fire_detection': '���ַ���',
    'smoke_detection': '��������'
  };
  const selectedPrompts: string[] = [];
  detectionOptions.value.forEach(category => {
    category.options.forEach(option => {
      if (option.checked && promptMap[option.id]) {
        selectedPrompts.push(promptMap[option.id]);
      }
    });
  });

  if (selectedPrompts.length === 0) {
    return `������Ƶ֡�еİ�ȫ������
1. ��ע��Ա������װ����Σ����Ϊ���������ͻ�е�豸
2. �����յȼ�����/��/�ͣ����ಢ��Ҫ����ÿ������
3. �����Ҫ�����ṩ��̡�����Ĺܿؽ���

��Ҫ���������������Ϣ��ʹ��"### ��ȫ�����뽨��"��Ϊ���⡣`;
  }
  return `������Ƶ֡�еİ�ȫ�������ص��ע��
${selectedPrompts.map((prompt, index) => `${index + 1}. ${prompt}`).join('\n')}

ֱ�������
1. ������������ļ�Ҫ���������յȼ�
2. ����ԵĹܿؽ��飨��ʵ���ɲ�����

��Ҫ���������������Ϣ��ʹ��"### ��ȫ�����뽨��"��Ϊ���⣬ʹ��Markdown��ʽ��`;
};

// WebSocket����
let wsConnection: WebSocket | null = null;
let frameCounter = 0; // ֡������������ȷ��ÿ5֡����һ��API

// ʹ��YOLO������·��ṹ���ռ��
const performYoloDetection = async (frameDataUrl: string): Promise<DetectionResult> => {
  console.log('ʹ��YOLO���ģʽ����·��ṹ���ռ��');

  // ����Ƿ�ʹ��WebSocket����ʵʱ����
  if (useYoloDetection.value) {
    return await processFrameWithWebSocket(frameDataUrl);
  } else {
    // ʹ��HTTP API���д�����ԭ���߼���
    // ����FormData�������ڷ��͵����
    const formData = new FormData();
    formData.append('image', frameDataUrl);

    // ��ѡ�е�ѡ�����ӵ�FormData
    selectedOptions.value.forEach(option => {
      formData.append('options[]', option);
    });

    // ������ʾ��
    const customPrompt = generatePrompt();
    formData.append('prompt', customPrompt);

    // ʹ��·��ṹ���ռ���API�˵�
    const apiEndpoint = '/online-monitor-road-defects';
    console.log(`ʹ��·��ṹ���ռ��API: ${apiEndpoint}`);

    // �������󵽺��
    const response = await fetch(`http://localhost:8000${apiEndpoint}`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // ������Ӧ
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || '���ʧ�ܣ�������');
    }

    // ��YOLO�����ת��ΪDetectionResult��ʽ
    const results: DetectionResult = {
      objects: data.defects.map((defect: any) => ({
        category: defect.category,
        confidence: defect.score,
        bbox: defect.bbox,
        risk_level: 'medium',
        label: defect.category
      })),
      summary: {},
      description: data.qwen_response,
      input_width: data.image_width,
      input_height: data.image_height,
      high_risk_events: [],
      low_risk_events: [],
      processed_url: data.result_url,
      visualized_image_url: data.result_url
    };

    // ����ÿ����������
    results.summary = results.objects.reduce((acc: Record<string, number>, obj: any) => {
      const category = obj.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    // ���ݼ�������ɷ����¼�
    data.defects.forEach((defect: any) => {
      const isHighRisk = defect.score > 0.8;
      const event = {
        category: defect.category,
        event: `��⵽${defect.category}`,
        risk_level: isHighRisk ? 'high' : 'low',
        confidence: defect.score,
        bbox_2d: defect.bbox
      };

      if (isHighRisk) {
        results.high_risk_events.push(event);
      } else {
        results.low_risk_events.push(event);
      }
    });

    return results;
  }
};

// ʹ��WebSocket������Ƶ֡
const processFrameWithWebSocket = (frameDataUrl: string): Promise<DetectionResult> => {
  return new Promise((resolve, reject) => {
    // ���WebSocket���Ӳ����ڻ��ѹرգ�����������
    if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
      console.log('�����µ�WebSocket����');
      wsConnection = new WebSocket('ws://localhost:8000/ws/yolo-video-process');

      wsConnection.onopen = () => {
        console.log('WebSocket�����ѽ���');
        // ���ӽ������͵�ǰ֡��ģ������
        sendFrameToWebSocket(frameDataUrl);
      };

      wsConnection.onerror = (error) => {
        console.error('WebSocket����:', error);
        reject(new Error('WebSocket���Ӵ���'));
      };

      wsConnection.onclose = () => {
        console.log('WebSocket�����ѹر�');
        wsConnection = null;
      };

      // �����ӷ��������յ���Ϣ
      wsConnection.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.error) {
            console.error('���������ش���:', data.error);
            reject(new Error(data.error));
            return;
          }

          // ����Ƿ��з����������
          if (data.qwen_analysis) {
            console.log('�յ�Qwen API�������������UI');
            
            // ����UI����
            addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
            addApiLog(`INFO:yolo_video_processor:Qwen API�������: ${JSON.stringify(data.qwen_analysis)}`);
            
            if (typeof data.qwen_analysis === 'object') {
              qwenResults.value = {
                detections: data.qwen_analysis.detections || [],
                description: data.qwen_analysis.description || '�����������',
                high_risk_events: data.qwen_analysis.high_risk_events || [],
                low_risk_events: data.qwen_analysis.low_risk_events || []
              };
              lastUpdated.value = new Date();
              isHeaderUpdating.value = true;
              setTimeout(() => {
                isHeaderUpdating.value = false;
              }, 2000);
            }
          }

          // ����ID���ͺ�������
          if (data.taskid && !data.qwen_analysis) {
            // �յ�����ID��û��qwen�������Ҫ���к�����ѯ
            console.log(`�յ�����ID ${data.taskid}��׼����ѯ�������`);
            
            // �����ѯ����
            const checkTaskResult = async (taskId) => {
              try {
                const response = await fetch(`/get_task?taskid=${taskId}`);
                if (response.ok) {
                  const result = await response.json();
                  if (result !== 500 && typeof result === 'object') {
                    console.log('��ȡ��Qwen API�������:', result);
                    
                    // ����UI����
                    addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
                    addApiLog(`INFO:yolo_video_processor:Qwen API�������: ${JSON.stringify(result)}`);
                    
                    // ����qwenResults״̬
                    qwenResults.value = {
                      detections: result.detections || [],
                      description: result.description || '�����������',
                      high_risk_events: result.high_risk_events || [],
                      low_risk_events: result.low_risk_events || []
                    };
                    
                    // ����ʱ����ʹ���UIˢ��
                    lastUpdated.value = new Date();
                    isHeaderUpdating.value = true;
                    setTimeout(() => {
                      isHeaderUpdating.value = false;
                    }, 2000);
                    
                    return true; // �ɹ���ȡ���
                  } else if (result === 500) {
                    console.log('�������ڴ����У������ȴ�...');
                    return false; // ������ѯ
                  }
                }
                return false; // ʧ�ܣ�������ѯ
              } catch (error) {
                console.error('��ѯ������ʧ��:', error);
                return false; // ���󣬼�����ѯ
              }
            };
            
            // ������ѯ
            let attempts = 0;
            const maxAttempts = 15;
            const pollInterval = setInterval(async () => {
              attempts++;
              console.log(`���Բ�ѯ������: ${attempts}/${maxAttempts}`);
              
              const success = await checkTaskResult(data.taskid);
              if (success || attempts >= maxAttempts) {
                clearInterval(pollInterval);
                if (!success && attempts >= maxAttempts) {
                  console.log('�ﵽ����Դ�����ֹͣ��ѯ');
                }
              }
            }, 1000); // ÿ���ѯһ��
          }

          if (data.processed_frame) {
            // ȷ���������֡������������Data URL
            let processedFrameDataUrl = data.processed_frame;
            if (!processedFrameDataUrl.startsWith('data:image')) {
              // ���ֻ�յ��˴�Base64������ǰ׺
              processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
            }
            
            // ֻ�ڵ���ʱ�����־���������̨����
            if (Math.random() < 0.05) { // Only log approximately 5% of frames
              console.log('YOLO��Ƶ��������...');
            }
            
            // ��鲢��֤֡��ʱ�����Ч��
            // ���Գ���2��ľ�֡����ֹ��ʾ��ʱ��֡
            const currentTime = Date.now();
            const frameTimestamp = data.timestamp || currentTime;
            const frameAge = currentTime - frameTimestamp;
            
            if (frameAge > 2000) {
              console.log(`������ʱ��֡������: ${frameAge}ms`);
              return;
            }
            
            // ��canvas����ʾ�������֡�����ڿ��ӻ�YOLO�����
            if (detectionCanvasElement1.value) {
              // ʹ�øĽ��汾��onload�������� (���ļ�ĩβ�Ѿ�����)
              if(lastImg.value!=processedFrameDataUrl){
                lastImg.value=processedFrameDataUrl
                 canvasImg.src = processedFrameDataUrl;
              }
            }
            
            // �����֡������Qwen�������������û�������������ﴦ��
            if (data.qwen_analysis && !data.qwen_results) {
              addApiLog(`INFO:yolo_video_processor:Qwen API�������: ${JSON.stringify(data.qwen_analysis)}`);
              
              if (typeof data.qwen_analysis === 'object') {
                qwenResults.value = {
                  detections: data.qwen_analysis.detections || [],
                  description: data.qwen_analysis.description || '����������...',
                  high_risk_events: data.qwen_analysis.high_risk_events || [],
                  low_risk_events: data.qwen_analysis.low_risk_events || []
                };
                lastUpdated.value = new Date();
              }
            }
          }
        } catch (error) {
          console.error('����WebSocket��Ϣʱ����:', error);
        }
      };
    } else {
      // ��������Ѵ����Ҵ򿪣�ֱ�ӷ���֡
      sendFrameToWebSocket(frameDataUrl);
    }

    // ����֡��WebSocket
    function sendFrameToWebSocket(frameData: string) {
      if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify({
          frame: frameData,
          model: "yolo11n-seg.pt", // ָ��ʹ��yolo11n-seg.ptģ��
          config: {
            segmentation: true, // ���÷ָ�
            confidence: 0.25  // ���Ŷ���ֵ
          },
          timestamp: Date.now(), // ����ʱ�����׷��֡
          frameId: Math.random().toString(36).substring(2, 15) // ����Ψһ֡ID
        }));
      } else {
        reject(new Error('WebSocket����δ��'));
      }
    }
  });
};

// ʹ�ñ�׼API���м��
const performStandardDetection = async (frameDataUrl: string): Promise<DetectionResult> => {
  const modelId = currentModel.value.id === 'pro' ? 'qwen2.5-vl-72b-instruct' : 'qwen2.5-vl-7b-instruct';
  const customPrompt = generatePrompt();
  console.log('ʹ�ñ�׼���ģʽ��prompt:', customPrompt, 'model:', modelId);
  return await detectObjects(frameDataUrl, selectedOptions.value, customPrompt, modelId);
};

// ���������ͼ����ʾ
const handleProcessedImage = (results: DetectionResult) => {
  // ����WebSocket���ص�YOLO�������֡
  if (results.yolo_processed_frame) {
    console.log('��⵽WebSocket�������֡��ֱ����ʾ');
    const processedImage = new Image();
    processedImage.onload = () => {
      // ����Ƶ�Ϸ����ƴ������ͼ��
      if (videoPlayerElement.value && detectionCanvasElement.value) {
        const ctx = detectionCanvasElement.value.getContext('2d');
        if (ctx) {
          // ȷ��canvas�ߴ�����Ƶƥ��
          if (detectionCanvasElement.value.width !== videoPlayerElement.value.videoWidth ||
              detectionCanvasElement.value.height !== videoPlayerElement.value.videoHeight) {
            detectionCanvasElement.value.width = videoPlayerElement.value.videoWidth;
            detectionCanvasElement.value.height = videoPlayerElement.value.videoHeight;
          }

          // �����ǰ����
          ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);

          // ���ƴ������ͼ��
          ctx.drawImage(
            processedImage,
            0, 0,
            detectionCanvasElement.value.width,
            detectionCanvasElement.value.height
          );

          // �����Ƽ�����Ϊ�������ͼ���Ѿ������˼����
          showDetectionBoxes.value = true;
        }
      }
    };
    processedImage.onerror = (err) => {
      console.error('����WebSocket�������֡ʧ��:', err);
      // ����ʧ��ʱʹ�ó������
      if (showDetectionBoxes.value) {
        drawDetectionBoxes();
      }
    };
    // ����ͼ��WebSocket���ص��Ѿ���������base64���ݣ�
    processedImage.src = results.yolo_processed_frame;
  }
  // ����HTTP API���ص�ͼ��URL
  else if (results.processed_url || results.visualized_image_url) {
    const processedUrl = results.visualized_image_url || results.processed_url;
    if (processedUrl) {
      console.log('��⵽�������ͼ��URL�����ش������ͼ��:', processedUrl);
      // ����ͼ��Ԫ�ز����ش������ͼ��
      const processedImage = new Image();
      const cacheBuster = new Date().getTime(); // ��ֹ����
      processedImage.onload = () => {
        // ����Ƶ�Ϸ����ƴ������ͼ��
        if (videoPlayerElement.value && detectionCanvasElement.value) {
          const ctx = detectionCanvasElement.value.getContext('2d');
          if (ctx) {
            // ȷ��canvas�ߴ�����Ƶƥ��
            if (detectionCanvasElement.value.width !== videoPlayerElement.value.videoWidth ||
                detectionCanvasElement.value.height !== videoPlayerElement.value.videoHeight) {
              detectionCanvasElement.value.width = videoPlayerElement.value.videoWidth;
              detectionCanvasElement.value.height = videoPlayerElement.value.videoHeight;
            }

            // �����ǰ����
            ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);

            // ���ƴ������ͼ��
            ctx.drawImage(
              processedImage,
              0, 0,
              detectionCanvasElement.value.width,
              detectionCanvasElement.value.height
            );

            // �����Ƽ�����Ϊ�������ͼ���Ѿ������˼����
            showDetectionBoxes.value = true;
          }
        }
      };
      processedImage.onerror = (err) => {
        console.error('���ش������ͼ��ʧ��:', err);
        // ����ʧ��ʱʹ�ó������
        if (showDetectionBoxes.value) {
          drawDetectionBoxes();
        }
      };
      // ����ͼ��
      processedImage.src = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
    } else if (showDetectionBoxes.value) {
      // û�д������ͼ��ʹ�ó������
      drawDetectionBoxes();
    }
  } else if (showDetectionBoxes.value) {
    // û�д������ͼ��ʹ�ó������
    drawDetectionBoxes();
  }
};

// ������߼�
const performDetectionLogic = async () => {
  const frameDataUrl = captureFrame();
  if (!frameDataUrl) {
    error.value = '�޷�������Ƶ֡���з�����';
    isLoading.value = false;
    return;
  }
  currentFramePreview.value = frameDataUrl; // For ObjectDetectionPanel

  if (selectedOptions.value.length === 0) {
    error.value = '������ѡ��һ��������ݡ�';
    detectionResults.value = null;
    clearCanvas();
    isLoading.value = false;
    return;
  }

  // �����״μ��ʱ��ʾ������ʾ
  if (!detectionResults.value) {
    showLoadingMessage.value = true;
  }
  isLoading.value = true;
  error.value = '';
  try {
    let results;

    // ����ִ��YOLO���ͱ�׼API���
    if (useYoloDetection.value) {
      // ����YOLO�����̣�WebSocket��ʽ��
      processFrameWithYOLO(frameDataUrl);
      
      // ͬʱִ�б�׼���API�������ڰ�ȫ����
      results = await performStandardDetection(frameDataUrl);
      
      // ������ϲ���YOLO�����ͨ��WebSocket�ص���
      detectionResults.value = results;
      
      // ȷ����ȫ������ʾ���Ҳ�����У���ʹû�м���
      if (results && results.description) {
        showSafetyRecommendation(results.description);
      }
      
      console.log('ִ���˲��м��: YOLO���Ӿ�ʶ�� + Qwen API����ȫ����');
    } else {
      // ���YOLO���δ���ã�ִֻ�б�׼���
      results = await performStandardDetection(frameDataUrl);
      detectionResults.value = results;
    }

    showLoadingMessage.value = false;

    // ����API��־
    if (results) {
      const logEntry = {
        success: true,
        detections: results.objects || [],
        description: results.description || '',
        high_risk_events: results.high_risk_events || [],
        low_risk_events: results.low_risk_events || []
      };
      addApiLog(`INFO:qwen-vl-api:���ɹ������ؽ��: ${JSON.stringify(logEntry)}`);
    }

    // ���������ͼ����ʾ (ֻ���ڲ�ʹ��WebSocketʱ)
if (!useYoloDetection.value || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
  handleProcessedImage(results);
}
  } catch (err: any) {
    console.error('��Ƶ֡���ʧ��:', err);
    error.value = err.message || '��Ƶ֡���ʧ�ܣ������ԡ�';
    detectionResults.value = null;
    showLoadingMessage.value = false;
    clearCanvas();
  } finally {
    isLoading.value = false;
  }
};

const triggerManualDetection = () => {
    if (videoPlayerElement.value && videoPlayerElement.value.paused) {
        performDetectionLogic();
    }
};

let frameProcessingInterval: number | null = null;
const realTimeDetectionFrameRate = 30; // FPS for real-time detection (30 frames per second)

const processCurrentFrameForRealtime = () => {
    if (isLoading.value) return; // Skip if a detection is already in progress
    performDetectionLogic();
};

// Fix the handlePlay function to properly extract and send base64 data
const handlePlay = () => {
  // ����û���ȷֹͣ��أ�����������
  if (!isMonitoringActive.value) {
    return;
  }
  
  // ������е�֡�����������ֹ�ظ�����
  if (frameProcessingInterval) {
    clearInterval(frameProcessingInterval);
    frameProcessingInterval = null;
  }
  
  // ͬʱ���ö��ּ�ⷽ��
  const enableParallelDetection = selectedCategory.value === 'pavement';
  
  // ȷ��WebSocket�����ѽ���������YOLO���ӻ���
  if (useYoloDetection.value && (!wsConnection || wsConnection.readyState !== WebSocket.OPEN)) {
    initializeWebSocket();
  }
  
  // ����YOLO����������ʹ��captureAndProcessFrame������WebSocket�ص����Զ�������һ֡
  // ��������ȷ��WebSocket������API�����ǲ��еģ�����Ӱ��
  if (useYoloDetection.value && wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    // ������һ֡��YOLO����������֡����WebSocket�ص��м�������
    captureAndProcessFrame();
  }
  
  // ����֡��������ΪAPI�����ṩ�ȶ���֡��
  const captureRate = 30; // ÿ��60֡
  frameProcessingInterval = window.setInterval(() => {
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // ����ǰ֡
      const frameDataUrl = captureFrame();
      if (!frameDataUrl) return;
      
      // 2. ���е��ñ�׼API���м�⣨������ã�
      if (enableParallelDetection && !isLoading.value) {
        console.log('����ִ�б�׼API���');
        isLoading.value = true;
        
        // ʹ��Promiseִ��API���õ����ȴ����
        performStandardDetection(frameDataUrl)
          .then(results => {
            console.log('��׼API������ - ��ȫ����');
            detectionResults.value = results;
            
            // ��ʾ��ȫ����������Ҳ����
            if (results && results.description) {
              showSafetyRecommendation(results.description);
            }
            
            // ����API��־
            const logEntry = {
              success: true,
              description: results.description || '',
              // Ϊ�˼���־����������ȫ��������
              high_risk_events: results.high_risk_events || [],
              low_risk_events: results.low_risk_events || []
            };
            addApiLog(`INFO:qwen-vl-api:���ɹ������ؽ��: ${JSON.stringify(logEntry)}`);
            
            // ���û����ʹ��YOLO WebSocket������¼���
            if (!useYoloDetection.value || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
              handleProcessedImage(results);
            }
          })
          .catch(err => {
            console.error('��׼API���ʧ��:', err);
          })
          .finally(() => {
            isLoading.value = false;
          });
      }
    }
  }, 1000 / captureRate);
  
  console.log(`��������Ƶ������API����֡��: ${captureRate} FPS����ʾ֡��: 60 FPS��${useYoloDetection.value ? 'ʹ��YOLO���ӻ�' : '��׼����'}`);
};

const handlePauseOrEnd = () => {
    if (frameProcessingInterval) {
        clearInterval(frameProcessingInterval);
        frameProcessingInterval = null;
    }
    // Optionally, perform one last detection on the paused frame if desired
    // if (videoPlayerElement.value && videoPlayerElement.value.paused) {
    //     performDetectionLogic();
    // }
};

const drawDetectionBoxes = () => {
  const canvas = detectionCanvasElement.value;
  const videoElement = videoPlayerElement.value;

  if (!canvas || !videoElement || !detectionResults.value) {
    clearCanvas();
    return;
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  // ȷ��canvas�ߴ�����Ƶʵ�ʳߴ�ƥ��
  if (canvas.width !== videoElement.videoWidth || canvas.height !== videoElement.videoHeight) {
    canvas.width = videoElement.videoWidth || 640;
    canvas.height = videoElement.videoHeight || 360;
  }
  
  // �������
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  if (!showDetectionBoxes.value) return;

  const { objects = [], high_risk_events = [], low_risk_events = [], input_width, input_height } = detectionResults.value;
  
  // ȷ��Դ�ߴ���Ч�������������ʹ����Ƶ�ߴ�
  const sourceWidth = input_width || videoElement.videoWidth || canvas.width;
  const sourceHeight = input_height || videoElement.videoHeight || canvas.height;

  // ������Ƶ�뻭��֮������ű���
  const scaleX = canvas.width / sourceWidth;
  const scaleY = canvas.height / sourceHeight;

  const allDetections: (DetectionObject | HighRiskEvent | LowRiskEvent)[] = [];
  if (showHighRiskBoxes.value) allDetections.push(...high_risk_events.map(e => ({ ...e, _isHighRisk: true })));
  if (showLowRiskBoxes.value) allDetections.push(...low_risk_events.map(e => ({ ...e, _isLowRisk: true })));

  objects.forEach(obj => {
    const isPerson = obj.category === '��Ա' || obj.category?.includes('person');
    const isVehicle = obj.category === '����' || obj.category?.includes('vehicle');
    const isMachine = obj.category === '��е' || obj.category === '�豸' || obj.category?.includes('machine');

    if (
      (isPerson && showPersonBoxes.value) ||
      (isVehicle && showVehicleBoxes.value) ||
      (isMachine && showMachineBoxes.value)
    ) {
      // �������ӿ������Է����¼����ظ���򵥼�飬����ͨ��ID�Ľ���
      if (!allDetections.some(riskEvent => riskEvent.bbox_2d?.toString() === obj.bbox_2d?.toString())) {
         allDetections.push(obj);
      }
    }
  });

  allDetections.forEach((item: any) => {
    const bbox = item.bbox_2d;
    if (!bbox || bbox.length !== 4) return;

    const [x1, y1, x2, y2] = bbox;
    // Ӧ�����ű���
    const drawX1 = x1 * scaleX;
    const drawY1 = y1 * scaleY;
    const boxWidth = (x2 - x1) * scaleX;
    const boxHeight = (y2 - y1) * scaleY;

    let color = 'rgba(128, 128, 128, 0.5)'; // Default gray
    let borderColor = 'gray';
    let lineDash: number[] = [];

    if (item._isHighRisk || item.risk_level === 'high') {
      color = 'rgba(255, 0, 0, 0.3)'; borderColor = 'red'; lineDash = [5,3];
    } else if (item._isLowRisk || item.risk_level === 'low') {
      color = 'rgba(255, 165, 0, 0.3)'; borderColor = 'orange';
    } else if (item.category === '��Ա') {
      color = 'rgba(0, 0, 255, 0.3)'; borderColor = 'blue';
    } else if (item.category === '����') {
      color = 'rgba(0, 255, 0, 0.3)'; borderColor = 'green';
    } else if (item.category === '��е' || item.category === '�豸') {
      color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
    }

    ctx.strokeStyle = borderColor;
    ctx.fillStyle = color;
    ctx.lineWidth = 2;
    ctx.setLineDash(lineDash);
    ctx.beginPath();
    ctx.rect(drawX1, drawY1, boxWidth, boxHeight);
    ctx.stroke();
    ctx.fill();
    ctx.setLineDash([]);

    const label = item.label || item.event || item.category || 'δ֪';
    ctx.fillStyle = borderColor;
    ctx.font = 'bold 12px Arial';
    const textMetrics = ctx.measureText(label);
    ctx.fillRect(drawX1, drawY1 - 16, textMetrics.width + 4, 16);
    ctx.fillStyle = 'white';
    ctx.fillText(label, drawX1 + 2, drawY1 - 4);
  });
  
  // �����ƽ�����Ƶ��ɼ���canvas��
  const displayCanvas = detectionCanvasElement1.value;
  if (displayCanvas) {
    const displayCtx = displayCanvas.getContext('2d');
    if (displayCtx) {
      // ȷ���ߴ�ƥ��
      if (displayCanvas.width !== canvas.width || displayCanvas.height !== canvas.height) {
        displayCanvas.width = canvas.width;
        displayCanvas.height = canvas.height;
      }
      displayCtx.drawImage(canvas, 0, 0);
    }
  }
};

// --- Utility Functions for Results Display (from App.vue, simplified/adapted) ---
const totalHighRiskCount = computed(() => detectionResults.value?.high_risk_events?.length || 0);
const totalLowRiskCount = computed(() => detectionResults.value?.low_risk_events?.length || 0);

const getPersonCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '��Ա' || 
      obj.category === '��' || 
      obj.label === '��' || 
      (obj.category?.toLowerCase().includes('person')) ||
      (obj.label?.toLowerCase().includes('person'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '��Ա' || 
          obj.category === '��' || 
          obj.label === '��' || 
          (obj.category?.toLowerCase()?.includes('person')) ||
          (obj.label?.toLowerCase()?.includes('person'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          if (rawData.person && Array.isArray(rawData.person)) {
            return rawData.person.length;
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const getVehicleCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '����' || 
      obj.category === '��' || 
      (obj.category?.toLowerCase()?.includes('vehicle')) || 
      (obj.category?.toLowerCase()?.includes('car'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '����' || 
          obj.category === '��' || 
          (obj.category?.toLowerCase()?.includes('vehicle')) || 
          (obj.category?.toLowerCase()?.includes('car'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          if (rawData.vehicle && Array.isArray(rawData.vehicle)) {
            return rawData.vehicle.length;
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const getMachineCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '��е' || 
      obj.category === '�豸' || 
      obj.category === '���ػ�' || 
      obj.category === '����������е' || 
      (obj.category?.toLowerCase()?.includes('machine')) || 
      (obj.category?.toLowerCase()?.includes('equipment'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '��е' || 
          obj.category === '�豸' || 
          obj.category === '���ػ�' || 
          obj.category === '����������е' || 
          (obj.category?.toLowerCase()?.includes('machine')) || 
          (obj.category?.toLowerCase()?.includes('equipment')) || 
          (obj.category?.toLowerCase()?.includes('construction'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          let machineCount = 0;
          // Check construction_machinery array
          if (rawData.construction_machinery && Array.isArray(rawData.construction_machinery)) {
            machineCount += rawData.construction_machinery.length;
          }
          // Return if we found any
          if (machineCount > 0) return machineCount;
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const translateEventName = (eventName: string | undefined): string => {
  if (!eventName) return 'δ֪�¼�';
  const translations: Record<string, string> = {
    'worker_no_helmet': 'δ�����ȫñ',
    'worker_no_vest': 'δ�����ⱳ��',
    'worker_falling': '����ˤ��',
    'unsafe_operation': '����ȫ����',
    // Add more translations as needed
  };
  return translations[eventName] || eventName;
};

// ע�⣺��ʽ����ȫ�������������Ѿ������Ϸ�����ǿʵ����

// Grouped events for display
type EventGroupItem = { eventName: string; count: number };
type CategoryEventGroup = { category: string; events: EventGroupItem[]; totalCount: number };

const groupEventsForDisplay = (events: HighRiskEvent[] | LowRiskEvent[] | undefined): CategoryEventGroup[] => {
  if (!events || events.length === 0) return [];
  const categorized = new Map<string, EventGroupItem[]>();

  events.forEach(event => {
    const categoryName = event.category || 'δ����';
    const eventName = event.event || 'δ֪����';
    if (!categorized.has(categoryName)) {
      categorized.set(categoryName, []);
    }
    const categoryEvents = categorized.get(categoryName)!;
    let eventItem = categoryEvents.find(e => e.eventName === eventName);
    if (eventItem) {
      eventItem.count++;
    } else {
      categoryEvents.push({ eventName, count: 1 });
    }
  });

  const result: CategoryEventGroup[] = [];
  categorized.forEach((events, category) => {
    result.push({ category, events, totalCount: events.reduce((sum, e) => sum + e.count, 0) });
  });
  return result;
};

const groupedHighRiskEventsForDisplay = computed(() => groupEventsForDisplay(detectionResults.value?.high_risk_events));
const groupedLowRiskEventsForDisplay = computed(() => groupEventsForDisplay(detectionResults.value?.low_risk_events));

// ����һ�����������غ���ʾ�������ͼ��
const loadProcessedImage = (url: string) => {
  if (!url) return;

  console.log('���ش������ͼ��:', url);
  const processedImage = new Image();
  const cacheBuster = new Date().getTime();
  processedImage.onload = () => {
    if (detectionCanvasElement.value) {
      const ctx = detectionCanvasElement.value.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
        ctx.drawImage(
          processedImage,
          0, 0,
          detectionCanvasElement.value.width,
          detectionCanvasElement.value.height
        );
      }
    }
  };
  processedImage.onerror = (err) => {
    console.error('���ش������ͼ��ʧ��:', err);
    if (showDetectionBoxes.value) {
      drawDetectionBoxes(); // ����ʧ��ʱ���˵�ԭʼ����
    }
  };
  processedImage.src = `http://localhost:8000${url}?t=${cacheBuster}`;
};

// Modify the fetchApiLogs function to handle the missing endpoint
const fetchApiLogs = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/logs?type=qwen-vl-api');
    if (!response.ok) {
      console.error('Failed to fetch API logs');
      return;
    }
    
    const data = await response.json();
    if (data.logs) {
      // Filter logs containing the specific prefix
      const successLogs = data.logs.filter((log: string) => 
        log.includes('INFO:qwen-vl-api:���ɹ������ؽ��:'));
      
      if (successLogs.length > 0) {
        apiLogs.value = successLogs.map((log: string) => {
          // Extract just the result part after the prefix
          const resultMatch = log.match(/INFO:qwen-vl-api:���ɹ������ؽ��:(.*)/);
          return resultMatch ? resultMatch[1].trim() : log;
        });
      }
    }
  } catch (error) {
    console.error('Error fetching API logs:', error);
  }
};

// Start/stop polling for API logs
const startLogPolling = () => {
  if (!isPollingLogs.value) {
    isPollingLogs.value = true;
    // Poll every 5 seconds
    const pollingInterval = setInterval(() => {
      if (isPollingLogs.value) {
        fetchApiLogs();
      } else {
        clearInterval(pollingInterval);
      }
    }, 5000);
    
    // Initial fetch
    fetchApiLogs();
  }
};

// ����������ѯAPI��־�ĺ���
const startActiveLogPolling = () => {
  console.log('����������ѯAPI��־');
  const pollingInterval = setInterval(async () => {
    try {
      const response = await fetch('http://localhost:8000/api/logs?type=yolo_video_processor');
      if (response.ok) {
        const data = await response.json();
        if (data.logs && data.logs.length > 0) {
          // �������µ�Qwen API���سɹ���־
          const successLogs = data.logs.filter((log: string) => 
            log.includes('INFO:yolo_video_processor:Qwen API���سɹ�') || 
            log.includes('INFO:yolo_video_processor:Qwen API�������'));
          
          if (successLogs.length > 0) {
            // �������µ���־
            successLogs.slice(0, 3).forEach((log: string) => {
              addApiLog(log);
            });
            
            // ��������״̬
            lastUpdated.value = new Date();
            isHeaderUpdating.value = true;
            
            // 2���رն���
            setTimeout(() => {
              isHeaderUpdating.value = false;
            }, 2000);
          }
        }
      }
    } catch (error) {
      console.warn('��ѯAPI��־ʧ��:', error);
    }
  }, 1000); // ÿ����ѯһ��
  
  return pollingInterval;
};

// ����������ȡQwen��������ĺ���
const fetchQwenApiResults = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/logs?type=yolo_video_processor&limit=3');
    if (response.ok) {
      const data = await response.json();
      if (data.logs && data.logs.length > 0) {
        // �������µ�Qwen API���������־
        const qwenApiResultsLog = data.logs.find(log => 
          log.includes('INFO:yolo_video_processor:Qwen API�������:')
        );
        
        if (qwenApiResultsLog) {
          console.log('������ȡ�����µ�Qwen API�������');
          // ��Qwen API����������ӵ���־
          addApiLog(qwenApiResultsLog);
        }
      }
    }
  } catch (error) {
    console.error('��ȡQwen API���ʧ��:', error);
  }
};

// ����������ѯ�Ի�ȡ���µ�Qwen API�������
const startQwenApiResultsPolling = () => {
  // ÿ2����ѯһ��
  const pollingInterval = setInterval(fetchQwenApiResults, 2000);
  
  // �״�������ѯ
  fetchQwenApiResults();
  
  return pollingInterval;
};

onMounted(() => {
  // Initialize YS7 video player
  initializeYs7Player();
  
  // Initialize categories
  selectCategory('all');
  
  // Event listeners for video are added in the template now
  
  // Add a sample log for demonstration
  addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
  setTimeout(() => {
    addApiLog(`INFO:yolo_video_processor:Qwen API�������: {'success': True, 'detections': [{'category': '��', 'event_description': '', 'risk_level': '�з���', 'confidence_score': 0.7, 'bbox_2d': [654, 589, 673, 617], 'display_label': '', 'event': '��', 'confidence': 0.8, 'label': '��'}], 'description': '����������ֵ����⣺\n\n* ��ʩ���ֳ�Ӧ�ϸ����ذ�ȫ�����涨�������ֳ�������Ա��ǿ��ȫ������ѵ��\n* ����ʱ������ȷ������˷�����Ʒ(�����������ڰ�ȫñ)�����������⵼���˺��¹ʵķ����� \n* ʩ�����������ǿ�����ػ�е�豸�Ĳ���������ά���������ȣ�������Χ�������Եľ����ʶ���ѹ�������ע����÷�ֹ������ײ���߱����˵��������;\n * ��ǿ�ճ�Ѳ�����ƶ���ʵ��λ��ʱ������ȫ����Ԥ���¹ʷ���;  \n   \n���ϴ�ʩ�����ڽ��͸��లȫ�¹ʷ����ĸ��ʴӶ�����Ա�������Ʋ���ȫ�Լ��ٽ���Ŀ˳���ƽ����! \n\n��ע��ʵ�ʹ����п��ܴ��ڸ���ϸ����Ҫ��һ��ȷ�Ͼ��������������Դ�����', 'high_risk_events': [{"description": "����δ�����ȫñ��ҵ", "mitigation": "����Ҫ��ù��������ȫñ����ǿ��ȫ�������ֳ�Ѳ�顣"}], 'low_risk_events': [], 'raw_json': '{\n    "person": [\n        {\n            "category": "��",\n            "event_description": "",\n            "risk_level": "�з���",\n            "confidence_score": 0.7,\n            "bbox_2d": [654, 589, 673, 617],\n            "display_label": ""\n        }\n    ],\n    "vehicle": [],\n    "construction_machinery": [\n        {\n            "category": "���ػ�",\n            "event_description": "",\n            "risk_level": "�޷���",\n            "confidence_score": 0.9,\n            "bbox_2d": [-1,-1 , -1 ,-1]\n        },\n        {\n            "category": "����������е",\n            "event_description": "",\n            "risk_level": "�޷���",\n            "confidence_score": 0.9,\n            "bbox_2d": []\n        }\n    ],\n    "crane_operation_status": {},\n    "road_base_condition": {},\n    "slope_stability": {},\n    "pavement_condition": {},\n    "hard_isolation_and_protection_barriers": {},\n    "fire_hazard": {},\n    "smoke_detection": {}\n}', 'input_width': 1664, 'input_height': 928, 'image_path': 'static\\uploads\\9f19d038-ccbd-4ca1-a02c-3f948d6f66f6.jpg'}`);
  }, 500);
  
  // Start regular API log polling
  startLogPolling();
  
  // Start active polling specifically for Qwen API results
  const activePollingInterval = startActiveLogPolling();
  
  // Also start dedicated polling for Qwen API results
  const qwenApiResultsPollingInterval = startQwenApiResultsPolling();
  
  // Load sample Qwen results
  loadSampleQwenResults();
  
  // Clean up polling when component is unmounted
  onUnmounted(() => {
    if (activePollingInterval) {
      clearInterval(activePollingInterval);
    }
    if (qwenApiResultsPollingInterval) {
      clearInterval(qwenApiResultsPollingInterval);
    }
  });
});

onUnmounted(() => {
  // Clean up HLS player
  if (hlsPlayer) {
    hlsPlayer.destroy();
    hlsPlayer = null;
  }
  
  // �����ʱ��
  handlePauseOrEnd();

  // �ر�WebSocket����
  if (wsConnection) {
    console.log('���ж�أ��ر�WebSocket����');
    try {
      // ���͹ر���Ϣ
      if (wsConnection.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify({ action: 'close' }));
      }
      // �ر�����
      wsConnection.close();
      wsConnection = null;
    } catch (error) {
      console.error('�ر�WebSocket����ʱ����:', error);
    }
  }
  
  // Stop log polling
  isPollingLogs.value = false;
});

watch(detectionResults, (newValue) => { // Simplified watch, drawDetectionBoxes is called directly after results
    if (showDetectionBoxes.value) {
        if (newValue && (newValue.processed_url || newValue.visualized_image_url)) {
            // ����д������ͼ�񣬼��ز���ʾ
            const processedUrl = newValue.visualized_image_url || newValue.processed_url;
            if (processedUrl) {
                loadProcessedImage(processedUrl);
            } else {
                drawDetectionBoxes();
            }
        } else {
            drawDetectionBoxes();
        }
    }
}, { deep: true });

watch([showDetectionBoxes, showHighRiskBoxes, showLowRiskBoxes, showPersonBoxes, showVehicleBoxes, showMachineBoxes],
  () => {
    if (currentlyDisplayedLog.value) {
      // If we're showing a log on the video, redraw it
      drawLogDetectionBoxes(currentlyDisplayedLog.value);
    } else if (detectionResults.value) { 
      // Otherwise use regular detection results
      drawDetectionBoxes();
    }
  }
);

// YS7 related data
const isYs7Loading = ref(false);
const ys7Error = ref(false);
const ys7ErrorMessage = ref('');
let hlsPlayer: any = null;

// Add video buffer system for delayed playback
const videoBuffer = ref<{frame: string, timestamp: number}[]>([]);
const bufferSize = ref(10); // Number of frames to buffer (creates delay)
const isBuffering = ref(false);
const bufferInterval = ref<number | null>(null);
const isDelayedPlaybackActive = ref(false);

// Initialize YS7 Cloud video player
const initializeYs7Player = async () => {
  try {
    isYs7Loading.value = true;
    ys7Error.value = false;
    
    // YS7 authentication credentials
    const appKey = '8dcf58f3eff843a49ae4c60b55cd9c9b';
    const appSecret = 'a436053dec3df63157bdfe7100f76f88';
    const deviceSerial = '*********'; // Device serial number
    const channelNo = 3; // Channel number
    
    // Get access token
    const tokenParams = new URLSearchParams();
    tokenParams.append('appKey', appKey);
    tokenParams.append('appSecret', appSecret);
    
    const tokenResponse = await axios.post('https://open.ys7.com/api/lapp/token/get', tokenParams, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (tokenResponse.data.code !== '200') {
      throw new Error(`��ȡ��Ȩʧ��: ${tokenResponse.data.msg || '����AppKey��AppSecret'}`);
    }
    
    const accessToken = tokenResponse.data.data.accessToken;
    
    // Get stream URL
    const liveUrlParams = new URLSearchParams();
    liveUrlParams.append('accessToken', accessToken);
    liveUrlParams.append('deviceSerial', deviceSerial);
    liveUrlParams.append('channelNo', channelNo);
    liveUrlParams.append('protocol', '2'); // HLS protocol
    liveUrlParams.append('quality', '1'); // HD quality
    
    const liveUrlResponse = await axios.post('https://open.ys7.com/api/lapp/v2/live/address/get', liveUrlParams, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (liveUrlResponse.data.code !== '200') {
      throw new Error(`��ȡֱ����ַʧ��: ${liveUrlResponse.data.msg || '�����豸���кź�ͨ����'}`);
    }
    
    const hlsUrl = liveUrlResponse.data.data.url;
    videoSource.value = hlsUrl;
    const videoElement = videoPlayerElement.value;
    if (!videoElement) {
      throw new Error('Video element not found');
    }
    
    if (Hls.isSupported()) {
      if (hlsPlayer) {
        hlsPlayer.destroy();
      }
      
      hlsPlayer = new Hls({
        debug: false,
        maxLoadingRetry: 4,
        enableWorker: true,
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
        // Add lowLatencyMode to improve playback performance
        lowLatencyMode: true,
        // Increase chunk sizes to reduce network requests
        fragLoadPolicy: {
          default: {
            maxTimeToFirstByteMs: 10000,
            maxLoadTimeMs: 120000,
            timeoutRetry: 3
          }
        }
      });
      
      hlsPlayer.loadSource(hlsUrl);
      hlsPlayer.attachMedia(videoElement);

      hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest parsed, attempting to play video');
        // Force play the video element
        const playPromise = videoElement.play();
        if (playPromise !== undefined) {
          playPromise.then(() => {
            console.log('Video playback started successfully');
            // Only initialize YOLO after video has started successfully
            if (useYoloDetection.value) {
              console.log('Starting YOLO detection after successful video start');
              initializeWebSocket();
              handlePlay();
            }
          }).catch(playError => {
            console.warn('Autoplay prevented:', playError);
            // Try to play with user interaction later
            addApiLog(`WARN:qwen-vl-api:��Ƶ�Զ����ű��������ֹ��������Ƶ�����ֶ�����`);
          });
        }
      });
      
      // Error handling
      hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
        if (data.fatal) {
          switch(data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('Network error, attempting to recover');
              hlsPlayer.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('Media error, attempting to recover');
              hlsPlayer.recoverMediaError();
              break;
            default:
              console.error('Fatal error, cannot recover');
              ys7Error.value = true;
              ys7ErrorMessage.value = '��Ƶ����ʧ�ܣ�������ƵԴ';
              hlsPlayer.destroy();
              hlsPlayer = null;
              // Fall back to local video if available
              videoElement.src = './1747124126409.mp4';
              videoElement.play().catch(e => console.warn('Local video playback error:', e));
              break;
          }
        }
      });
    } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
      // For Safari
      videoElement.src = hlsUrl;
      videoElement.play().catch(e => console.warn('Safari playback error:', e));
    } else {
      throw new Error('�����������֧��HLS���ţ���ʹ���ִ������');
    }
    
    isYs7Loading.value = false;
  } catch (error) {
    console.error('YS7 initialization error:', error);
    isYs7Loading.value = false;
    ys7Error.value = true;
    ys7ErrorMessage.value = error.message || 'өʯ����Ƶ����ʧ��';
    
    // Fallback to sample video if available
    if (videoPlayerElement.value) {
    //   videoPlayerElement.value.src = './1747124126409.mp4';
    //   videoPlayerElement.value.play().catch(e => console.warn('Fallback video playback error:', e));
    // console.log('��ͣ');
    }
  }
};

// Start buffered playback system for delayed video with YOLO detection
const startBufferedPlayback = () => {
  if (isBuffering.value) return;
  
  isBuffering.value = true;
  isDelayedPlaybackActive.value = true;
  videoBuffer.value = [];
  
  // Pause the main video while we buffer frames
  if (videoPlayerElement.value) {
    videoPlayerElement.value.pause();
  }
  
//   console.log(`��ʼ������Ƶ֡�������С: ${bufferSize.value} ֡`);
  
  // Start capturing frames to fill the buffer
  const captureInterval = setInterval(() => {
    const frame = captureFrame();
    if (frame) {
      const timestamp = Date.now();
      videoBuffer.value.push({
        frame,
        timestamp
      });
      
      // Process frame with YOLO immediately (real-time detection)
      if (useYoloDetection.value && !isLoading.value) {
        // ���ʹ�ʱ�����ID��֡
        if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
          const frameId = Math.random().toString(36).substring(2, 15);
          wsConnection.send(JSON.stringify({
            frame: frame.split(',')[1],
            frameId: frameId,
            timestamp: timestamp,
            config: {
              segmentation: true,
              confidence: 0.25
            }
          }));
        }
      }
      
      // Once buffer is full, start playback
      if (videoBuffer.value.length >= bufferSize.value && !bufferInterval.value) {
        console.log(`�������� (${videoBuffer.value.length} ֡)����ʼ�ӳٲ���`);
        startBufferPlayback();
      }
    }
  }, 33); // Capture at ~30fps
  
  // Clear any existing interval
  if (bufferInterval.value) {
    clearInterval(bufferInterval.value);
    bufferInterval.value = null;
  }
  
  return captureInterval;
};

// Play frames from the buffer with a delay
const startBufferPlayback = () => {
  // Already playing from buffer
  if (bufferInterval.value) return;
  
  console.log('Starting delayed playback from buffer');
  
  // Display frames from buffer at regular intervals
  bufferInterval.value = window.setInterval(() => {
    if (videoBuffer.value.length > 0) {
      const oldestFrame = videoBuffer.value.shift();
      if (oldestFrame && detectionCanvasElement1.value) {
        // Display the delayed frame on canvas
       
      }
    }
  }, 16); // Display at ~60fps for smoother playback
};

// Process frame with YOLO and update detection overlay
const processFrameWithYOLO = async (frameDataUrl: string) => {
  try {
    // ��ȡ��Base64����
    const base64Data = frameDataUrl.split(',')[1];
    
    // ���͵�WebSocket���д���
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        frame: base64Data,
        model: "yolo11n-seg.pt",
        config: {
          segmentation: true,  // ���÷ָ�
          confidence: 0.25,    // ���Ŷ���ֵ
          visualize: true      // ȷ�����ÿ��ӻ�
        },
        timestamp: Date.now(), // ����ʱ�����׷��֡
        frameId: Math.random().toString(36).substring(2, 15) // ����Ψһ֡ID
      }));
    } else {
      // ���WebSocketδ���ӣ�������������
      console.log('WebSocketδ���ӣ�������������');
      initializeWebSocket();
    }
  } catch (error) {
    console.error('YOLO��������:', error);
  }
};

// ���Ͳ���ͼ������֤WebSocket����
const sendTestImage = () => {
  if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
    console.error('WebSocket����δ�򿪣��޷����Ͳ���ͼ��');
    return;
  }
  
  // ����һ���򵥵Ĳ���ͼ�� (20x20 ��ɫ����)
  const canvas = document.createElement('canvas');
  canvas.width = 20;
  canvas.height = 20;
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.fillStyle = 'red';
    ctx.fillRect(0, 0, 20, 20);
    const testImage = canvas.toDataURL('image/jpeg');
    
    // ��ȡBase64����
    const base64Data = testImage.split(',')[1];
    
    console.log('���Ͳ���ͼ������֤WebSocket����');
    wsConnection.send(JSON.stringify({
      action: 'test',
      frame: base64Data,
      model: 'yolo11n-seg.pt'
    }));
  }
};

// Initialize WebSocket for YOLO detection
const initializeWebSocket = () => {
  if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    console.log('WebSocket�����ӣ��������³�ʼ��');
    return;
  }
  
  if (wsConnection && wsConnection.readyState === WebSocket.CONNECTING) {
    console.log('WebSocket���������У����Ժ�...');
    return;
  }
  
  console.log('�����µ�WebSocket���� - ʹ��yolo11n-seg.ptģ�ͽ��п��ӻ�');
  addApiLog(`INFO:qwen-vl-api:WebSocket�����ѽ�����׼������YOLO��Ƶ����`);
  addApiLog(`INFO:yolo_video_processor:����YOLOģ��: ../src/models/yolo11n-seg.pt`);
  
  // ����֡��������ȷ��ƽ����API����
  frameCounter = 0;
  
  try {
    // �����µ�WebSocket���� - ������ӽ�ר������YOLO��Ƶ����
    wsConnection = new WebSocket('ws://localhost:8000/ws/yolo-video-process');

    wsConnection.onopen = () => {
      console.log('WebSocket�����ѽ���');
      addApiLog(`INFO:qwen-vl-api:YOLO�������Ѵ�����ʹ��ģ��: ../src/models/yolo11n-seg.pt`);
      
      // ����ģ��������Ϣ
      wsConnection.send(JSON.stringify({
        action: 'configure',
        model: 'yolo11n-seg.pt',
        config: {
          segmentation: true,
          confidence: 0.25,
          visualize: true,
          img_size: 640 // ���ô���ͼ��ĳߴ�
        }
      }));
      
      // �Ե�ǰ֡����YOLO����������������
      if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
        captureAndProcessFrame();
      }
      
      // ����UIָʾ����ʾYOLO��Ծ״̬
      const yoloBadge = document.querySelector('.yolo-badge');
      if (yoloBadge) {
        yoloBadge.style.animation = 'pulse 1s infinite';
        yoloBadge.style.backgroundColor = '#52c41a';
        yoloBadge.textContent = 'YOLO11x-seg ������';
      }
    };

    wsConnection.onerror = (error) => {
      console.error('WebSocket����:', error);
      addApiLog(`ERROR:qwen-vl-api:YOLO����������ʧ�ܣ������˷����Ƿ�����`);
      
      // �����Ӿ�ָʾ����ʾYOLO����ʧ��
      const yoloBadge = document.querySelector('.yolo-badge');
      if (yoloBadge) {
        yoloBadge.style.animation = 'none';
        yoloBadge.style.backgroundColor = '#ff4d4f';
        yoloBadge.textContent = 'YOLO����ʧ��';
      }
    };

    wsConnection.onclose = () => {
      console.log('WebSocket�����ѹر�');
      addApiLog(`INFO:qwen-vl-api:WebSocket�����ѶϿ�`);
      addApiLog(`INFO:qwen-vl-api:YOLO��Ƶ����WebSocket�����ѹر�`);
      wsConnection = null;
      
      // ����UI״̬
      const yoloBadge = document.querySelector('.yolo-badge');
      if (yoloBadge) {
        yoloBadge.style.animation = 'none';
        yoloBadge.style.backgroundColor = '#555';
        yoloBadge.textContent = 'YOLO11x-seg';
      }
      
      // ���ҳ������ʹ�ã�������������
      setTimeout(() => {
        if (useYoloDetection.value && isMonitoringActive.value) {
          console.log('������������WebSocket');
          initializeWebSocket();
        }
      }, 3000);
    };
    
    // �����ӷ��������յ���Ϣ
    wsConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.error) {
          console.error('���������ش���:', data.error);
          addApiLog(`ERROR:qwen-vl-api:${data.error}`);
          return;
        }

        // �������ص�YOLO�����
        if (data.processed_frame) {
          // ȷ���������֡������������Data URL
          let processedFrameDataUrl = data.processed_frame;
          if (!processedFrameDataUrl.startsWith('data:image')) {
            // ���ֻ�յ��˴�Base64������ǰ׺
            processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
          }
          
          // ��canvas����ʾ�������֡�����ڿ��ӻ�YOLO�����
          if (detectionCanvasElement1.value) {
            // ʹ�øĽ��汾��onload�������� (���ļ�ĩβ�Ѿ�����)
            if(lastImg.value!=processedFrameDataUrl){
              lastImg.value=processedFrameDataUrl
               canvasImg.src = processedFrameDataUrl;
            }
           
            
          }
           async function request_task() {
            // ��������Ѿ������������ٴ���
            if (processedTasks.has(data.taskid)) {
              return;
            }
            
            const res = await axios.get('http://localhost:8000/get_task?taskid='+data.taskid)
            if (res.data === 500) {
                // ������δ��ɣ�3����ٴγ���
                setTimeout(async () => {
                  await request_task()
                }, 3000);
                return;
            }
            
            // ��������Ѵ����������ظ�����
            processedTasks.set(data.taskid, true);
            
            // ���Map��������������
            if (processedTasks.size > 100) {
              const keys = Array.from(processedTasks.keys());
              const oldestKeys = keys.slice(0, 50);
              oldestKeys.forEach(key => processedTasks.delete(key));
            }
            
            data.qwen_analysis = res.data
            
            // ��⵽Qwen API���سɹ�����������UI��ʾ
            if (data.qwen_analysis) {
              // ���ȼ�¼����־ϵͳ���е���
              let logEntry; 
              if (typeof data.qwen_analysis === 'string') {
                // ������ַ�������ȡǰ500���ַ�
                logEntry = data.qwen_analysis.substring(0, 500) + (data.qwen_analysis.length > 500 ? '...' : '');
              } else {
                // ����Ƕ���תΪJSON�ַ���
                logEntry = JSON.stringify(data.qwen_analysis).substring(0, 500) + '...';
              }
              
              // ����־����������Ŀ
              addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
              addApiLog(`INFO:yolo_video_processor:Qwen API�������: ${
                typeof data.qwen_analysis === 'string' ? 
                  data.qwen_analysis : 
                  JSON.stringify(data.qwen_analysis)
              }`);
              
              // ֱ��ʹ�ý������UI
              try {
                let parsedResult;
                
                // ������ͬ���͵Ľ��
                if (typeof data.qwen_analysis === 'string') {
                  // ���Խ���JSON�ַ���
                  try {
                    // ����Python���Ĳ���ֵ��None
                    const preprocessedJson = data.qwen_analysis
                      .replace(/'/g, '"')
                      .replace(/True/g, 'true')
                      .replace(/False/g, 'false')
                      .replace(/None/g, 'null');
                    
                    // Ѱ����ЧJSON����ʼ�ͽ���λ��
                    let validJson = preprocessedJson;
                    if (preprocessedJson.includes('{') && preprocessedJson.includes('}')) {
                      const startPos = preprocessedJson.indexOf('{');
                      const endPos = preprocessedJson.lastIndexOf('}') + 1;
                      if (startPos !== -1 && endPos !== -1) {
                        validJson = preprocessedJson.substring(startPos, endPos);
                      }
                    }
                    
                    // �����JSON�ַ�����������
                    console.log('���Խ���Qwen API JSON�ַ���:', validJson.substring(0, 100) + '...');
                    parsedResult = JSON.parse(validJson);
                    console.log('�ɹ�����Qwen API���ΪJSON����');
                  } catch (parseError) {
                    // ����޷�������ʹ��ԭʼ�ı�����һ����������
                    console.error('�޷�����Qwen API���ΪJSON:', parseError);
                    parsedResult = {
                      description: data.qwen_analysis,
                      detections: data.objects || [],
                      high_risk_events: data.high_risk_events || [],
                      low_risk_events: data.low_risk_events || []
                    };
                  }
                } else if (typeof data.qwen_analysis === 'object' && data.qwen_analysis !== null) {
                  // ����Ѿ��Ƕ���ֱ��ʹ��
                  console.log('Qwen API������JSON����ֱ��ʹ��');
                  parsedResult = data.qwen_analysis;
                } else {
                  // �����������͵Ľ��
                  console.warn('Qwen API������δ֪��ʽ:', typeof data.qwen_analysis);
                  parsedResult = {
                    description: '���յ�δ֪��ʽ�ķ������',
                    detections: data.objects || [],
                    high_risk_events: data.high_risk_events || [],
                    low_risk_events: data.low_risk_events || []
                  };
                }
                
                // ȷ�������б�Ҫ�ֶ�
                const description = parsedResult.description || 
                                  parsedResult.safety_analysis || 
                                  parsedResult.analysis || 
                                  '�����������';
                
                // ����qwenResults״̬
                qwenResults.value = {
                  detections: parsedResult.detections || data.objects || [],
                  description: description,
                  high_risk_events: parsedResult.high_risk_events || data.high_risk_events || [],
                  low_risk_events: parsedResult.low_risk_events || data.low_risk_events || []
                };
                
                // ����ʱ����ʹ���UIˢ��
                lastUpdated.value = new Date();
                isHeaderUpdating.value = true;
                setTimeout(() => {
                  isHeaderUpdating.value = false;
                }, 2000);
                
                console.log('�Ѹ���Qwen API�����UI:', qwenResults.value);
              } catch (e) {
                console.error('����WebSocket��Qwen API���ʱ����:', e);
                // ����ʱ���Ը��򵥵ĸ��·�ʽ
                qwenResults.value = {
                  detections: data.objects || [],
                  description: typeof data.qwen_analysis === 'string' ? 
                    data.qwen_analysis.substring(0, 1000) : 
                    '�����������ʱ����',
                  high_risk_events: data.high_risk_events || [],
                  low_risk_events: data.low_risk_events || []
                };
                lastUpdated.value = new Date();
              }
            }
          }
          request_task().then(()=>console.log("��������"));
          // ���¼����
          if (data.objects && data.objects.length > 0) {
            latestDetections.value = data.objects;
          }
          if (data.high_risk_events) {
            latestHighRiskEvents.value = data.high_risk_events;
          }
          if (data.low_risk_events) {
            latestLowRiskEvents.value = data.low_risk_events;
          }
          
          // ����������һ֡ (�����Զ�ģʽ��)
          if (videoPlayerElement.value && !videoPlayerElement.value.paused && useYoloDetection.value) {
            // ʹ��requestAnimationFrame�Ը��õ����ܽ���֡����
            requestAnimationFrame(() => {
              captureAndProcessFrame();
            });
          }
        }
      } catch (error) {
        console.error('����WebSocket��Ϣʱ����:', error);
      }
    };
  } catch (error) {
    console.error('����WebSocketʱ����:', error);
    addApiLog(`ERROR:qwen-vl-api:����WebSocket����ʧ��: ${error.message}`);
  }
};

// ���Ӳ���ʹ�����ǰ֡�ĺ���
// Capture and send the current frame for YOLO processing, update Qwen results afterward
const captureAndProcessFrame = () => {
  if (!videoPlayerElement.value || videoPlayerElement.value.paused || !wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
    return;
  }
  
  // ����ǰ��Ƶ֡
  if (!detectionCanvasElement.value) return;
  
  const ctx = detectionCanvasElement.value.getContext('2d');
  if (!ctx) return;
  
  // ����canvas��С����Ƶ��ͬ
  if (detectionCanvasElement.value.width !== videoPlayerElement.value.videoWidth ||
      detectionCanvasElement.value.height !== videoPlayerElement.value.videoHeight) {
    detectionCanvasElement.value.width = videoPlayerElement.value.videoWidth;
    detectionCanvasElement.value.height = videoPlayerElement.value.videoHeight;
  }
  
  // ��canvas�ϻ��Ƶ�ǰ��Ƶ֡
  ctx.drawImage(videoPlayerElement.value, 0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
  
  // ��֡ת��Ϊbase64
  const frameDataUrl = detectionCanvasElement.value.toDataURL('image/jpeg', 0.8);
  
  // ����֡ID��ʱ���
  // ʹ�ü򵥵�����֡������ȷ��ÿ5֡����һ��API
  frameCounter++;
  const frameId = frameCounter;
  const timestamp = Date.now();
  
  // ͨ��WebSocket���͵����������д���
  if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    wsConnection.send(JSON.stringify({
      frame: frameDataUrl.split(',')[1], // ֻ����Base64����
      frameId: frameId,
      timestamp: timestamp,
      config: {
        segmentation: true, 
        confidence: 0.25
      }
    }));
  }
};

// Add toggle for delayed playback mode
const toggleDelayedPlayback = () => {
  if (isDelayedPlaybackActive.value) {
    // �ر��ӳٲ��ţ��ָ�������Ƶ��
    isDelayedPlaybackActive.value = false;
    
    // �������
    videoBuffer.value = [];
    
    // ��ʾ������Ƶ��
    if (videoPlayerElement.value && videoPlayerElement.value.paused) {
      videoPlayerElement.value.play().catch(e => console.warn('��Ƶ���Ŵ���:', e));
    }
    
    addApiLog(`INFO:qwen-vl-api:���л���ʵʱ��ģʽ��YOLO�������������ʾ`);
  } else {
    // �����ӳٲ��ţ�רע��YOLO�������
    isDelayedPlaybackActive.value = true;
    
    addApiLog(`INFO:qwen-vl-api:���л���YOLO���ӻ�ģʽ������ʾ���������Ƶ��`);
    
    // �����Ƶ���ڲ��ţ�����ʹ��YOLO����
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // ȷ������ʹ��YOLO���
      useYoloDetection.value = true;
    }
  }
};

// Stop delayed playback and return to normal
const stopDelayedPlayback = () => {
  isDelayedPlaybackActive.value = false;
  isBuffering.value = false;
  
  if (bufferInterval.value) {
    clearInterval(bufferInterval.value);
    bufferInterval.value = null;
  }
  
  // Clear buffer
  videoBuffer.value = [];
  
  // Resume normal video playback
  if (videoPlayerElement.value) {
    videoPlayerElement.value.play().catch(e => console.warn('Playback error:', e));
  }
  
  // Clear canvas
  if (detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// Add new ref to track monitoring state
const isMonitoringActive = ref(true);

// Add the toggle monitoring function
const toggleMonitoring = () => {
  isMonitoringActive.value = !isMonitoringActive.value;
  
  if (isMonitoringActive.value) {
    // Resume monitoring
    handlePlay();
  } else {
    // Stop monitoring but keep video playing
    if (frameProcessingInterval) {
      clearInterval(frameProcessingInterval);
      frameProcessingInterval = null;
    }
    
    // Also stop delayed playback if active
    if (isDelayedPlaybackActive.value) {
      stopDelayedPlayback();
    }
  }
};

// Add these helper functions for API log formatting
const tryParseJsonLog = (log: string) => {
  try {
    // First try to parse the log directly
    const parsed = parseJson(log);
    
    // Check if it has useful fields
    if (parsed && (
      parsed.detections || 
      parsed.description || 
      parsed.high_risk_events || 
      parsed.low_risk_events ||
      (parsed.raw_json && typeof parsed.raw_json === 'string')
    )) {
      return true;
    }
    
    // If raw_json is present in the parsed result, try to extract useful content from it
    if (parsed && parsed.raw_json) {
      try {
        // Clean and parse the raw_json field
        const cleanedRawJson = removeJsonComments(parsed.raw_json);
        const rawData = JSON.parse(cleanedRawJson);
        
        // Check for image_overall_safety_analysis_and_control_recommendations in raw data
        if (rawData && typeof rawData === 'object') {
          // If there's a safety analysis in the raw_json, it's useful
          if (rawData.image_overall_safety_analysis_and_control_recommendations) {
            return true;
          }
          
          // Check for presence of any object arrays - also useful
          if (rawData.person || rawData.vehicle || rawData.construction_machinery ||
              rawData.high_risk_events || rawData.low_risk_events) {
            return true;
          }
        }
      } catch (e) {
        console.warn('Failed to parse raw_json in tryParseJsonLog:', e);
      }
    }
    
    // If log contains safety analysis text, it's also useful for display
    if (typeof log === 'string' && 
        (log.includes('ͼ�����尲ȫ���շ�����ܿؽ���') || 
         log.includes('��ȫ����ܽ�') || 
         log.includes('�ܿش�ʩ������'))) {
      return true;
    }
    
    return false;
  } catch (e) {
    console.warn('Error in tryParseJsonLog:', e);
    
    // As a fallback, check if the log has safety analysis text
    if (typeof log === 'string' && 
        (log.includes('ͼ�����尲ȫ���շ�����ܿؽ���') || 
         log.includes('��ȫ����ܽ�') || 
         log.includes('�ܿش�ʩ������'))) {
      return true;
    }
    
    return false;
  }
};

const getDetectionCount = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    return (parsed.detections || []).length;
  } catch (e) {
    return 0;
  }
};

const getDetections = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    return parsed.detections || [];
  } catch (e) {
    return [];
  }
};

const getSafetyAnalysis = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    
    // ���ȳ��Դ�description�ֶλ�ȡ
    if (parsed.description) {
      return parsed.description;
    }
    
    // Ȼ���ԴӸ��ֿ��ܵ��ֶ��л�ȡ
    if (parsed.safety_analysis || parsed.recommendations || 
        parsed.image_overall_safety_analysis_and_control_recommendations) {
      return parsed.safety_analysis || parsed.recommendations || 
             parsed.image_overall_safety_analysis_and_control_recommendations;
    }
    
    // ����Դ�raw_json�в���
    if (parsed.raw_json) {
      try {
        const rawData = typeof parsed.raw_json === 'string' ? JSON.parse(parsed.raw_json) : parsed.raw_json;
        if (rawData.image_overall_safety_analysis_and_control_recommendations) {
          return rawData.image_overall_safety_analysis_and_control_recommendations;
        }
      } catch (e) {
        console.error('����raw_jsonʧ��', e);
      }
    }
    
    return '';
  } catch (e) {
    return '';
  }
};

const getRiskClass = (riskLevel: string) => {
  if (!riskLevel) return '';
  
  const level = riskLevel.toLowerCase();
  if (level.includes('��') || level.includes('high')) return 'high-risk';
  if (level.includes('��') || level.includes('medium')) return 'medium-risk';
  if (level.includes('��') || level.includes('low')) return 'low-risk';
  return '';
};

// Add new state for handling log visualization
const canShowLogOnVideo = computed(() => apiLogs.length > 0 && videoPlayerElement.value && detectionCanvasElement.value);
const currentlyDisplayedLog = ref<any>(null);

// Function to check if a log contains detection data with bounding boxes
const hasDetectionData = (log: string): boolean => {
  try {
    const parsed = parseJson(log);
    
    // Check for detections with bbox data
    if (parsed.detections && Array.isArray(parsed.detections)) {
      return parsed.detections.some((d: any) => d.bbox || d.bbox_2d);
    }
    
    // Also check raw_json field if it exists
    if (parsed.raw_json) {
      try {
        const rawData = typeof parsed.raw_json === 'string' ? JSON.parse(parsed.raw_json) : parsed.raw_json;
        // Check for any objects with bbox data
        for (const key in rawData) {
          if (Array.isArray(rawData[key])) {
            if (rawData[key].some((item: any) => item.bbox_2d && item.bbox_2d.length === 4)) {
              return true;
            }
          }
        }
      } catch (e) {
        console.error('Failed to parse raw_json:', e);
      }
    }
    
    return false;
  } catch (e) {
    return false;
  }
};

// Display the latest log on the video
const showLatestLogOnVideo = () => {
  if (apiLogs.length > 0) {
    showLogOnVideo(apiLogs[0]);
  }
};

// Display any log on the video
const showLogOnVideo = (log: string) => {
  try {
    const parsedLog = parseJson(log);
    currentlyDisplayedLog.value = parsedLog;
    
    // Draw bounding boxes from log data
    drawLogDetectionBoxes(parsedLog);
    
    // If the log contains text description, display it with large font
    const description = getSafetyAnalysis(log);
    if (description) {
      displayDescriptionOnVideo(description);
    }
  } catch (e) {
    console.error('Failed to show log on video:', e);
  }
};

// Draw bounding boxes from log data
const drawLogDetectionBoxes = (parsedLog: any) => {
  if (!detectionCanvasElement.value || !videoPlayerElement.value) return;
  
  const canvas = detectionCanvasElement.value;
  const video = videoPlayerElement.value;
  
  // Ensure canvas matches video dimensions
  if (canvas.width !== video.videoWidth || canvas.height !== video.videoHeight) {
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  }
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Clear existing content
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  let detections: any[] = [];
  
  // Extract detections from the log
  if (parsedLog.detections && Array.isArray(parsedLog.detections)) {
    detections = parsedLog.detections;
  }
  
  // Also check raw_json if available
  if (parsedLog.raw_json) {
    try {
      const rawData = typeof parsedLog.raw_json === 'string' ? JSON.parse(parsedLog.raw_json) : parsedLog.raw_json;
      // Extract detections from various categories
      for (const key in rawData) {
        if (Array.isArray(rawData[key])) {
          rawData[key].forEach((item: any) => {
            if (item.bbox_2d && item.bbox_2d.length === 4) {
              detections.push({
                ...item,
                category: item.category || key,
                label: item.category || key
              });
            }
          });
        }
      }
    } catch (e) {
      console.error('Failed to parse raw_json in drawLogDetectionBoxes:', e);
    }
  }
  
  // Draw each detection
  detections.forEach(detection => {
    const bbox = detection.bbox_2d || detection.bbox;
    if (!bbox || bbox.length !== 4) return;
    
    // Get coordinates normalized to canvas size
    const [x1, y1, x2, y2] = bbox;
    const sourceWidth = parsedLog.input_width || video.videoWidth;
    const sourceHeight = parsedLog.input_height || video.videoHeight;
    
    const drawX1 = (x1 / sourceWidth) * canvas.width;
    const drawY1 = (y1 / sourceHeight) * canvas.height;
    const boxWidth = ((x2 - x1) / sourceWidth) * canvas.width;
    const boxHeight = ((y2 - y1) / sourceHeight) * canvas.height;
    
    // Determine color based on risk level or category
    let color = 'rgba(128, 128, 128, 0.5)'; // Default gray
    let borderColor = 'gray';
    let lineDash: number[] = [];
    
    if (detection.risk_level) {
      const riskLevel = detection.risk_level.toLowerCase();
      if (riskLevel.includes('��') || riskLevel.includes('high')) {
        color = 'rgba(255, 0, 0, 0.3)'; borderColor = 'red'; lineDash = [5,3];
      } else if (riskLevel.includes('��') || riskLevel.includes('medium')) {
        color = 'rgba(255, 165, 0, 0.3)'; borderColor = 'orange';
      } else if (riskLevel.includes('��') || riskLevel.includes('low')) {
        color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
      }
    } else if (detection.category) {
      const category = detection.category.toLowerCase();
      if (category.includes('��') || category.includes('person')) {
        color = 'rgba(0, 0, 255, 0.3)'; borderColor = 'blue';
      } else if (category.includes('��') || category.includes('vehicle')) {
        color = 'rgba(0, 255, 0, 0.3)'; borderColor = 'green';
      } else if (category.includes('��е') || category.includes('�豸') || category.includes('machine')) {
        color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
      }
    }
    
    // Draw rectangle
    ctx.strokeStyle = borderColor;
    ctx.fillStyle = color;
    ctx.lineWidth = 3; // Thicker lines for visibility
    ctx.setLineDash(lineDash);
    ctx.beginPath();
    ctx.rect(drawX1, drawY1, boxWidth, boxHeight);
    ctx.stroke();
    ctx.fill();
    ctx.setLineDash([]);
    
    // Draw label with larger font
    const label = detection.label || detection.event || detection.category || 'δ֪';
    ctx.fillStyle = borderColor;
    ctx.font = 'bold 16px Arial'; // Larger font
    const textMetrics = ctx.measureText(label);
    ctx.fillRect(drawX1, drawY1 - 20, textMetrics.width + 6, 20);
    ctx.fillStyle = 'white';
    ctx.fillText(label, drawX1 + 3, drawY1 - 5);
    
    // Add confidence if available
    if (detection.confidence && typeof detection.confidence === 'number') {
      const confidenceText = `${Math.round(detection.confidence * 100)}%`;
      ctx.font = '14px Arial';
      ctx.fillStyle = 'white';
      ctx.fillText(confidenceText, drawX1 + 3, drawY1 - 25);
    }
  });
};

// Display description text on video with large font
const displayDescriptionOnVideo = (description: string) => {
  if (!detectionCanvasElement.value) return;
  
  const canvas = detectionCanvasElement.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Create a semi-transparent overlay for text
  ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
  ctx.fillRect(0, canvas.height - 150, canvas.width, 150);
  
  // Display text with larger font
  ctx.font = 'bold 18px Arial';
  ctx.fillStyle = 'white';
  
  // Truncate and format description to fit
  const maxLength = 150;
  let displayText = description.length > maxLength ? 
    description.substring(0, maxLength) + '...' : description;
  
  // Remove markdown formatting
  displayText = displayText.replace(/[*#_]/g, '');
  
  // Wrap text
  const words = displayText.split(' ');
  let line = '';
  let y = canvas.height - 120;
  
  words.forEach(word => {
    const testLine = line + word + ' ';
    const metrics = ctx.measureText(testLine);
    
    if (metrics.width > canvas.width - 40) {
      ctx.fillText(line, 20, y);
      line = word + ' ';
      y += 25;
    } else {
      line = testLine;
    }
  });
  
  ctx.fillText(line, 20, y);
};

// ����һ����������ȫ������ʾ���Ҳ������
const showSafetyInPanel = (log: string) => {
  try {
    const safetyAnalysis = getSafetyAnalysis(log);
    if (safetyAnalysis) {
      showSafetyRecommendation(safetyAnalysis);
      
      // ����һ����ʾ�ɹ�����ʾ
      const apiLogItem = document.querySelector('.api-log-item');
      if (apiLogItem) {
        const successIndicator = document.createElement('div');
        successIndicator.className = 'push-success-indicator';
        successIndicator.textContent = '? �����͵��Ҳ����';
        apiLogItem.appendChild(successIndicator);
        
        // 2��󵭳�
        setTimeout(() => {
          if (successIndicator && successIndicator.parentNode) {
            successIndicator.style.opacity = '0';
            setTimeout(() => {
              if (successIndicator.parentNode) {
                successIndicator.parentNode.removeChild(successIndicator);
              }
            }, 500);
          }
        }, 2000);
      }
    }
  } catch (e) {
    console.error('��ʾ��ȫ����ʧ��:', e);
  }
};

// ע�⣺��ȫ������ȡ���������Ѿ������Ϸ�����ǿʵ����

// Add this function after toggleDelayedPlayback
const toggleYoloDetection = () => {
  useYoloDetection.value = !useYoloDetection.value;
  
  if (useYoloDetection.value) {
    // ����YOLO���
    console.log('����YOLO11x-seg��⣬������Ƶ���ӻ�');
    addApiLog(`INFO:qwen-vl-api:������YOLO��⣬ʹ��yolo11n-seg.ptģ�ͽ���ʵʱ��Ƶ���ӻ�`);
    
    // ��ʼ��WebSocket����
    initializeWebSocket();
    
    // �����Ƶ���ڲ��ţ�������ʼYOLO���
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // ��ֹͣ��ǰ�ļ��
      if (frameProcessingInterval) {
        clearInterval(frameProcessingInterval);
        frameProcessingInterval = null;
      }
      
      // ����YOLO��⴦��
      handlePlay();
    }
  } else {
    // ����YOLO���
    console.log('����YOLO���');
    addApiLog(`INFO:qwen-vl-api:�ѽ���YOLO���`);
    
    // �ر�WebSocket����
    if (wsConnection) {
      wsConnection.close();
      wsConnection = null;
    }
    
    // ���Canvas�ϵļ���
    if (detectionCanvasElement.value) {
      const ctx = detectionCanvasElement.value.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
      }
    }
    
    // ֹͣ֡�������
    if (frameProcessingInterval) {
      clearInterval(frameProcessingInterval);
      frameProcessingInterval = null;
    }
  }
};

// Sample Qwen API results demo function
const loadSampleQwenResults = () => {
  // Sample data from the example in the user query
  const sampleJson = `{
    "detections": [
      {
        "category": "���� (boat)",
        "event_description": "ͼ����δ������ʾ��ֻ����ϵͳ��⵽���ܵĴ���ṹ��",
        "risk_level": "high",
        "confidence_score": 0.12,
        "bbox_2d": [0, 0, 1316, 728]
      }
    ],
    "description": "�ó���չʾ��һ��ҹ��ʩ�����أ�����Ϊһ�����͸ֽṹ����µ�ʩ���ֳ����������ж��������Դ���ṩ�˱�Ҫ�������������������˷������ϣ���Χ���л��������ӣ�������Ա��ȫͨ�С������пɼ�һЩ��е�豸�ͽ������ϣ����廷���Ե����򵫸��ӡ����ܱ�ע��'���� (boat)'������ͼ��������������δֱ����ʾ��ֻ�Ĵ��ڣ�������������ʵ�ʳ��������ļ������",
    "high_risk_events": [
      {
        "description": "ϵͳ���������壨���Ϊ'���� (boat)'���Ĵ��ڣ����ܵ��·����󵼡�",
        "mitigation": "���鸴�˼���㷨��ȷ���������Ƹ��ӳ����е�׼ȷ�ԺͿɿ��ԡ�ͬʱ���ֳ�������ԱӦ���ڼ�飬ȷ����Ǳ��Σ������ڡ�"
      }
    ],
    "low_risk_events": [
      {
        "description": "ҹ��ʩ���������㣬����ע��ƹ�Թ��������ĳ���Ӱ�켰��Դ���ġ�",
        "mitigation": "���ý����һ��۵������豸�������滮�������֣����ٹ���Ⱦ����Դ�˷ѡ�"
      },
      {
        "description": "ʩ���ֳ���������������ϣ������ڷ����ͱ������棬������ע����ϵ��ȹ��ԡ�",
        "mitigation": "���ڼ��������ϵ�״̬��ȷ����ƽ���������𣬼�ʱ�����𻵲��֡�"
      }
    ]
  }`;
  
  try {
    qwenResults.value = JSON.parse(sampleJson);
    console.log('���س�ʼQwen API���');
    lastUpdated.value = new Date();
    
    // ���ӵ�API��־��չʾ
    addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
    addApiLog(`INFO:yolo_video_processor:Qwen API�������: ${sampleJson}`);
    
    // ģ��10����ʵʱ����
    setTimeout(() => {
      console.log('ģ��10����Qwen API����');
      
      // ���º�Ľ������
      const updatedJson = `{
        "detections": [
          {
            "category": "��Ա",
            "event_description": "��ҵ���Ʋ��淶",
            "risk_level": "medium",
            "confidence_score": 0.85,
            "bbox_2d": [150, 60, 210, 180]
          },
          {
            "category": "��е",
            "event_description": "���ػ����۰뾶������Ա",
            "risk_level": "high",
            "confidence_score": 0.92,
            "bbox_2d": [320, 180, 450, 260]
          }
        ],
        "description": "�ó���չʾ��һ��ҹ��ʩ�����أ�����Ϊһ�����͸ֽṹ����µ�ʩ���ֳ����������ж��������Դ���ṩ�˱�Ҫ�������������������˷������ϣ���Χ���л��������ӣ�������Ա��ȫͨ�С������пɼ�һЩ��е�豸���������ڲ�����Ա�����ػ����۰뾶�ڻ�������ش�ȫ������",
        "high_risk_events": [
          {
            "description": "���ػ������뾶������Ա�",
            "mitigation": "��������Ա����Σ���������þ����߲�����ר�˼ල��"
          }
        ],
        "low_risk_events": [
          {
            "description": "������ҵ���Ʋ��淶",
            "mitigation": "�Թ��˽�����ȷ��ҵ������ѵ�����ⷢ��Ť�˵�ְҵ�˺���"
          }
        ]
      }`;
      
      // ��֪ͨAPI���سɹ�
      addApiLog(`INFO:yolo_video_processor:Qwen API���سɹ�`);
      
      // Ȼ�����ӷ������������UI����
      setTimeout(() => {
        addApiLog(`INFO:yolo_video_processor:Qwen API�������: ${updatedJson}`);
      }, 500);
    }, 10000);
    
  } catch (error) {
    console.error('��������JSONʧ��:', error);
  }
};

// �޸�canvasImg��onload����������ȷ����ȷ����ͼ����Ӧ�����ߴ�
canvasImg.onload = () => {
  const canvas = detectionCanvasElement1.value;
  if (canvas) {
    const ctx = canvas.getContext('2d');
    if (ctx) {
      // �������
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // �ڻ����Ͼ��л���ͼ�񣬱��ֱ���
      const imgWidth = canvasImg.width;
      const imgHeight = canvasImg.height;
      
      // �������ű�����ȷ��ͼ���ʺϻ���
      const scale = Math.min(
        canvas.width / imgWidth,
        canvas.height / imgHeight
      );
      
      // �������λ��
      const x = (canvas.width - imgWidth * scale) / 2;
      const y = (canvas.height - imgHeight * scale) / 2;
      
      // ����ͼ��
      ctx.drawImage(
        canvasImg,
        x, y,
        imgWidth * scale,
        imgHeight * scale
      );
    }
  }
};

// ��������״̬
const latestDetections = ref<DetectionObject[]>([]);
const latestHighRiskEvents = ref<DetectionObject[]>([]);
const latestLowRiskEvents = ref<DetectionObject[]>([]);

</script>

<style scoped>
.online-monitor-page {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 10px;
  min-height: calc(100vh - 70px); /* Adjust based on actual navbar height if known, minus padding */
  padding: 10px;
  background-color: var(--bg-darker, #000c17);
  overflow-y: auto; /* ����ҳ����� */
}

.left-sidebar,
.video-container,
.result-panel {
  background-color: var(--card-bg, #001f3d);
  border-radius: var(--radius-md, 8px);
  overflow: auto; /* Changed to auto for scrollability */
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color, #003a8c);
  color: var(--text-primary, #fff);
}

.left-sidebar p,
.result-panel p {
  padding: 15px;
  text-align: center;
}

.video-container {
  padding: 0; /* card-container will handle internal padding */
  position: sticky;
  top: 10px; /* ��ҳ�涥���ľ��룬��ҳ���padding��ͬ */
  z-index: 10; /* ȷ����Ƶ����������Ԫ���Ϸ� */
  align-self: flex-start; /* ��ֹ���������� */
  height: fit-content; /* �߶���Ӧ���� */
  max-height: calc(100vh - 20px); /* ȷ���������ӿڸ߶� */
  overflow-y: auto; /* ������ݹ��࣬�������� */
}

.card-container {
  width: 100%;
  height: auto; /* �߶���Ӧ���� */
  display: flex;
  flex-direction: column;
  background: var(--card-bg, #001f3d);
}

.card-header,
.card-footer {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color, #003a8c);
  background-color: var(--sidebar-header-bg, #002140);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-footer {
  border-top: 1px solid var(--border-color, #003a8c);
  border-bottom: none;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.card-body {
  flex-grow: 0; /* ��Ҫ�Զ���չ */
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: #000; /* Black background for video area */
  overflow: hidden; /* Clip contents */
  min-height: 360px; /* ������С�߶ȣ�����Ƶ�߶�һ�� */
}

.video-preview-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 ���߱ȣ��ɸ�����Ҫ���� */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* ��ֹ������� */
  background: #000;
}

.video-preview-wrapper video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain; /* ������Ƶ���� */
  border-radius: var(--radius-sm, 4px);
  display: block; /* ��ֹ�ײ����ּ�϶ */
  z-index: 1; /* ȷ����Ƶ�ڵײ� */
}

.video-preview-wrapper .detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2; /* ȷ��canvas����Ƶ֮�� */
  pointer-events: none; /* ���������͸����Ƶ���� */
}

.detection-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.image-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.detect-button {
  padding: 10px 15px;
  background-color: var(--primary-color, #1890ff);
  color: white;
  border: none;
  border-radius: var(--radius-sm, 4px);
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  flex-grow: 1;
}

.detect-button:hover {
  background-color: var(--primary-hover, #40a9ff);
}

.detect-button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.detect-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message {
  color: var(--danger-color, red);
  margin-top: 10px;
  font-size: 13px;
}

.toggle-button {
  padding: 8px 12px;
  border: 1px solid var(--border-light, #1d39c4);
  background-color: transparent;
  color: var(--primary-color, #1890ff);
  border-radius: var(--radius-sm, 4px);
  cursor: pointer;
  margin-right: 8px;
}

.toggle-button:last-child {
  margin-right: 0;
}

.toggle-button:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

/* Placeholder styles to give an idea of structure, real styles from App.vue would be more detailed */
.nav-section-placeholder,
.detection-options-container-placeholder,
.model-selector-placeholder,
.risk-analysis-header-placeholder,
.risk-events-container-placeholder,
.object-detection-panel-placeholder,
.stats-panel-horizontal-placeholder {
  border: 1px dashed var(--border-color, #003a8c);
  padding: 10px;
  margin: 10px;
  text-align: center;
  color: var(--text-secondary, #aaa);
  min-height: 50px;
}

/* Styles for sidebar (can be copied or adapted from App.vue if more detail is needed) */
.left-sidebar {
  padding: 10px;
  overflow-y: auto;
}
.nav-section { margin-bottom: 15px; }
.nav-button {
  display: flex; align-items: center; gap: 8px;
  background: var(--category-bg, #002140); border: 1px solid var(--border-light, #1d39c4);
  color: var(--text-primary, #fff); padding: 8px 12px; border-radius: var(--radius-sm, 4px);
  cursor: pointer; transition: background-color 0.2s; width: 100%; text-align: left; margin-bottom: 5px;
}
.nav-button:hover { background-color: var(--category-hover, #003a8c); }
.nav-button.active { background-color: var(--primary-color, #1890ff); border-color: var(--primary-color, #1890ff); }
.nav-icon { width: 18px; height: 18px; /* Add icons via CSS background if needed */ }

.detection-options-container .sidebar-header {
  color: var(--primary-color, #1890ff); font-size: 15px; margin-bottom: 10px;
  padding-bottom: 5px; border-bottom: 1px solid var(--border-color, #003a8c);
}
.detection-category { background: rgba(0,0,0,0.1); border-radius: var(--radius-sm, 4px); margin-bottom: 8px; }
.category-header {
  display: flex; justify-content: space-between; align-items: center;
  padding: 8px 10px; cursor: pointer; background: rgba(255,255,255,0.05);
}
.category-icon-wrapper { display: flex; align-items: center; gap: 8px; }
.category-label { font-size: 14px; }
.category-icon { width: 18px; height: 18px; /* Add icons */ }
.expand-icon { font-size: 12px; }
.category-options { padding: 10px; background: rgba(0,0,0,0.2); }
.option-item { margin-bottom: 5px; }
.custom-checkbox { display: flex; align-items: center; cursor: pointer; }
.custom-checkbox input[type="checkbox"] { margin-right: 8px; }
.option-text { font-size: 13px; }

/* Remove placeholder styles if they were general */
.nav-section-placeholder,
.detection-options-container-placeholder {
  display: none; /* Hide placeholders now that real content is added */
}

.result-panel {
  padding: 0; /* Let internal elements handle padding */
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  max-height: 100%; /* ȷ����岻���������߶� */
  position: sticky; /* ���ӹ̶���λ */
  top: 10px; /* ����Ƶ������ͬ�Ķ������� */
  align-self: flex-start; /* ��ֹ���������� */
  height: fit-content; /* �߶���Ӧ���� */
  max-height: calc(100vh - 20px); /* ȷ���������ӿڸ߶� */
}

.result-panel-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.risk-analysis-header {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color, #003a8c);
  background-color: var(--sidebar-header-bg, #002140);
  display: flex;
  align-items: center;
}
.risk-analysis-icon { width: 20px; height: 20px; margin-right: 8px; /* Add SVG/CSS icon */ }
.risk-analysis-title {
  font-size: 16px;
  font-weight: bold;
  flex: 1;
}

.risk-analysis-header.updating {
  background-color: rgba(24, 144, 255, 0.2);
  animation: headerPulse 2s ease-out;
}

@keyframes headerPulse {
  0% { background-color: rgba(24, 144, 255, 0.3); }
  100% { background-color: rgba(0, 33, 64, 1); }
}

.last-updated-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(24, 144, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  animation: pulse 1.5s;
}

@keyframes pulse {
  0% { background-color: rgba(24, 144, 255, 0.5); }
  100% { background-color: rgba(24, 144, 255, 0.2); }
}

.toggle-icon {
  font-size: 12px;
  color: #fff;
  margin-left: 5px;
}

.loading-results,
.error-display,
.no-detection-results {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary, #aaa);
}
.error-display p {
  color: var(--danger-color, red);
}

.risk-events-container, .safe-container {
  margin: 10px;
  padding: 15px;
  border-radius: var(--radius-md, 8px);
  border: 1px solid var(--border-color, #003a8c);
  background: rgba(0,0,0,0.1);
  overflow-y: visible; /* ����������������� */
  flex-shrink: 0; /* ��ֹ������ѹ�� */
}
.risk-events-container.high-risk { border-left: 3px solid var(--danger-color, red); }
.risk-events-container.low-risk { border-left: 3px solid var(--warning-color, orange); }
.risk-summary { text-align: center; margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid rgba(255,255,255,0.1); }
.risk-count { font-size: 28px; font-weight: bold; }
.risk-count.danger { color: var(--danger-color, red); }
.risk-count.warning { color: var(--warning-color, orange); }
.risk-label { font-size: 14px; opacity: 0.8; }
.risk-categories { margin-top: 10px; }
.risk-category { margin-bottom: 10px; }
.risk-category-header {
  display: flex; align-items: center; padding: 8px;
  background: rgba(255,255,255,0.05); border-radius: 4px 4px 0 0;
}
.risk-category-header .category-icon { width: 16px; height: 16px; margin-right: 8px; filter: invert(1); /* Basic icon styling */ }
.risk-category-header .category-name { flex-grow: 1; font-size: 14px; font-weight: 500; }
.risk-category-header .category-count { font-size: 13px; padding: 2px 6px; border-radius: 10px; color: white; }
.risk-category-header .category-count.danger { background-color: var(--danger-color, red); }
.risk-category-header .category-count.warning { background-color: var(--warning-color, orange); }

.risk-events { padding-left: 10px; }
.risk-event-item { display: flex; justify-content: space-between; align-items: center; padding: 6px 0; font-size: 13px; border-bottom: 1px dashed rgba(255,255,255,0.1);}
.risk-event-item:last-child { border-bottom: none; }
.risk-event-name .event-count { font-size: 0.8em; opacity: 0.7; margin-left: 5px;}
.risk-level { font-size: 12px; padding: 2px 6px; border-radius: 4px; text-transform: uppercase; color: white; }
.risk-high { background-color: var(--danger-color, red); }
.risk-low { background-color: var(--warning-color, orange); }

.safe-container { text-align: center; }
.safe-icon, .no-risk-icon { font-size: 30px; margin-bottom: 8px; }
.safe-message, .no-risk-message { font-size: 18px; font-weight: 500; margin-bottom: 5px; }
.safe-description { font-size: 13px; opacity: 0.7; }

.stats-panel-horizontal { display: flex; justify-content: space-around; padding: 10px; border-top: 1px solid var(--border-color, #003a8c); margin-top:10px;}
.stats-panel-horizontal .stats-item { text-align: center; }
.stats-panel-horizontal .stats-icon { width: 24px; height: 24px; margin-bottom: 4px; filter: invert(1); /* Basic icon styling */}
.stats-panel-horizontal .stats-label { font-size: 12px; opacity: 0.8; }
.stats-panel-horizontal .stats-value { font-size: 16px; font-weight: bold; color: var(--primary-color, #1890ff); }

/* Remove placeholder styles */
.risk-analysis-header-placeholder,
.risk-events-container-placeholder,
.object-detection-panel-placeholder,
.stats-panel-horizontal-placeholder {
  display: none;
}

/* Icon placeholders for categories - replace with actual icons */
.person-icon { background-color: var(--primary-color, blue); width:1em; height:1em; display:inline-block; border-radius:50%;}
.machine-icon { background-color: var(--warning-color, orange);width:1em; height:1em; display:inline-block; border-radius:50%; }
.vehicle-icon { background-color: var(--success-color, green); width:1em; height:1em; display:inline-block; border-radius:50%;}
/* Add other category icons similarly for .material-icon, .regulation-icon, .environment-icon */

.model-selector { position: relative; min-width: 130px; }
.selected-model {
  cursor: pointer; padding: 10px; border: 1px solid var(--border-color, #003a8c);
  border-radius: var(--radius-sm, 4px); background: rgba(0,0,0,0.2);
  display: flex; align-items: center; justify-content: space-between; height: 100%;
  color: var(--text-primary, #fff);
}
.model-icon { width:16px; height:16px; margin-right:5px; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M12 3L1 9l11 6 9-4.91V17h2V9L12 3zm0 3.97l7 3.83L12 13.97l-9-5 9-2zm-1 9.93v7l9-4V13l-9 3.9z'/%3E%3C/svg%3E"); filter: invert(1); }

/* YOLO���ģʽ��� */
.yolo-badge {
  display: inline-block;
  background-color: var(--success-color, #52c41a);
  color: white;
  font-size: 12px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  vertical-align: middle;
  animation: pulse 2s infinite;
}

.yolo-button {
  background-color: rgba(82, 196, 26, 0.15);
  border-color: var(--success-color, #52c41a);
  color: var(--success-color, #52c41a);
  position: relative;
}

.yolo-button:hover {
  background-color: rgba(82, 196, 26, 0.3);
}

.yolo-reload-button {
  background-color: rgba(24, 144, 255, 0.15);
  border-color: var(--primary-color, #1890ff);
  color: var(--primary-color, #1890ff);
}

.yolo-reload-button:hover {
  background-color: rgba(24, 144, 255, 0.3);
}

.toggle-button-hint {
  font-size: 10px;
  display: block;
  opacity: 0.7;
  margin-top: 2px;
}

.model-status {
  margin-left: 12px;
  font-size: 12px;
  color: #fff;
  opacity: 0.8;
}

.parallel-indicator {
  background-color: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 2px 6px;
  border-radius: 4px;
  animation: flash 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

@keyframes flash {
  0% { background-color: rgba(255, 215, 0, 0.2); }
  50% { background-color: rgba(255, 215, 0, 0.5); }
  100% { background-color: rgba(255, 215, 0, 0.2); }
}
.dropdown-icon { font-size: 10px; }
.model-dropdown {
  position: absolute; bottom: calc(100% + 5px); /* Open upwards */ left: 0; right: 0;
  background: var(--card-bg, #001f3d); border: 1px solid var(--border-color, #003a8c);
  border-bottom: none; border-radius: var(--radius-sm, 4px) var(--radius-sm, 4px) 0 0;
  z-index: 100; max-height: 150px; overflow-y: auto;
}
.model-option { padding: 8px 10px; cursor: pointer; border-bottom: 1px solid var(--border-color, #003a8c); color: var(--text-primary, #fff);}
.model-option:last-child { border-bottom: none; }
.model-option:hover { background-color: var(--category-hover, #003a8c); }
.model-option.active { background-color: var(--primary-color, #1890ff); }
.model-name { font-weight: bold; font-size: 13px; }
.model-description { font-size: 11px; opacity: 0.8; }

.detect-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9.5 9.5c0 .55.45 1 1 1h2c.55 0 1-.45 1-1V8h1c.55 0 1-.45 1-1s-.45-1-1-1h-1V5c0-.55-.45-1-1-1h-2c-.55 0-1 .45-1 1v2.5z'/%3E%3Cpath d='M20 12c0-2.54-1.19-4.81-3.04-6.27L16 0H8l-.95 5.73C5.19 7.19 4 9.45 4 12s1.19 4.81 3.05 6.27L8 24h8l.96-5.73C18.81 16.81 20 14.54 20 12zM12 2.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5zm0 19c-5.23 0-9.5-4.27-9.5-9.5S6.77 2.5 12 2.5s9.5 4.27 9.5 9.5-4.27 9.5-9.5 9.5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* Styles for placeholder in video footer */
.model-selector-placeholder {
  display: none; /* Remove if model selector is now implemented */
}

/* YS7 video styles */
.ys7-video {
  width: 100%;
  height: 100%;
  background-color: #000;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  border: none;
  display: block;
}

.ys7-loading-overlay,
.ys7-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.ys7-loading-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.ys7-error-overlay {
  background: rgba(245, 34, 45, 0.3);
}

.error-icon {
  width: 50px;
  height: 50px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23f5222d'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 15px;
}

.processing-indicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(0, 168, 255, 0.2);
  border-top: 3px solid #00a8ff;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.safety-analysis-container {
  margin: 10px;
  padding: 15px;
  border-radius: var(--radius-md, 8px);
  border: 1px solid var(--border-color, #003a8c);
  background: rgba(0,0,0,0.1);
  border-left: 3px solid var(--primary-color, #1890ff);
  flex-shrink: 0;
  max-height: 400px;
  overflow-y: auto;
  animation: fadeInUp 0.5s ease-out;
}

.safety-analysis-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.safety-analysis-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.safety-analysis-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-color, #1890ff);
}

.safety-analysis-content {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255,255,255,0.9);
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border-left: 2px solid var(--primary-color, #1890ff);
  white-space: pre-wrap;
}

.safety-analysis-content h1,
.safety-analysis-content h2,
.safety-analysis-content h3 {
  margin-top: 15px;
  margin-bottom: 8px;
  color: var(--primary-color, #1890ff);
}

.safety-analysis-content .safety-analysis-heading {
  color: var(--primary-color, #1890ff);
  font-size: 18px;
  font-weight: bold;
  padding: 8px 0;
  margin: 5px 0 15px 0;
  border-bottom: 1px solid var(--primary-color, #1890ff);
  text-align: center;
}

.safety-analysis-content ul {
  margin: 0;
  padding-left: 20px;
}

.safety-analysis-content li {
  margin-bottom: 8px;
  position: relative;
  padding-left: 5px;
}

.safety-analysis-content li:before {
  content: "?";
  color: var(--primary-color, #1890ff);
  position: absolute;
  left: -12px;
  font-weight: bold;
}

.safety-analysis-content strong {
  color: #fff;
  font-weight: bold;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 0 3px;
  border-radius: 3px;
}

.safety-analysis-content em {
  opacity: 0.8;
  font-style: italic;
}

.api-logs-container {
  margin: 10px;
  padding: 15px;
  border-radius: var(--radius-md, 8px);
  border: 1px solid var(--border-color, #003a8c);
  background: rgba(0,0,0,0.1);
  border-left: 3px solid var(--info-color, #1890ff);
  flex-shrink: 0;
}

.api-logs-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.api-logs-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.api-logs-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-color, #1890ff);
}

.api-logs-content {
  max-height: 300px;
  overflow-y: auto;
}

.api-log-item {
  padding: 12px;
  margin-bottom: 12px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

.api-log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
}

.api-log-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.api-log-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}

.api-log-status.success {
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
}

.api-log-details {
  padding: 5px 0;
}

.api-log-text {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: rgba(255, 255, 255, 0.85);
  font-family: monospace;
  max-height: 150px;
  overflow-y: auto;
}

.api-log-formatted {
  font-size: 13px;
}

.api-log-section {
  margin-bottom: 10px;
}

.api-log-section-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--primary-color, #1890ff);
}

.api-log-detection-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.api-log-detection-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  gap: 6px;
}

.detection-type {
  font-weight: 500;
}

.detection-type.high-risk {
  color: var(--danger-color, #ff4d4f);
}

.detection-type.medium-risk {
  color: var(--warning-color, #faad14);
}

.detection-type.low-risk {
  color: var(--success-color, #52c41a);
}

.detection-risk {
  font-size: 11px;
  padding: 1px 4px;
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.7);
}

.api-log-analysis {
  font-size: 12px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.85);
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border-left: 2px solid var(--primary-color, #1890ff);
  white-space: pre-wrap;
}

/* JSON Viewer Styles */
.api-log-json-viewer {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
  margin: 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.85);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  overflow: hidden;
}

.json-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(24, 144, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.json-viewer-type {
  font-weight: 600;
  color: #1890ff;
}

.json-viewer-actions {
  display: flex;
  gap: 8px;
}

.json-viewer-action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: background-color 0.2s;
}

.json-viewer-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.expand-icon, .collapse-icon {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
}

.json-viewer-content {
  padding: 12px;
  overflow-y: auto;
  max-height: 350px;
}

.json-tree {
  position: relative;
}

.json-tree-item {
  margin: 2px 0;
  position: relative;
}

.json-tree-key {
  padding: 2px 0;
  cursor: pointer;
  white-space: nowrap;
}

.json-tree-key:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.json-tree-toggle {
  display: inline-block;
  width: 12px;
  height: 12px;
  text-align: center;
  line-height: 12px;
  margin-right: 4px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 10px;
}

.json-key-name {
  color: #e6db74;
  margin-right: 4px;
}

.json-type {
  color: #66d9ef;
  margin-left: 4px;
}

.json-value {
  color: #a6e22e;
}

.json-string {
  color: #fd971f;
}

.json-number {
  color: #ae81ff;
}

.json-boolean {
  color: #f92672;
}

.json-null {
  color: #75715e;
}

.json-tree-children {
  margin-left: 16px;
  border-left: 1px dotted rgba(255, 255, 255, 0.2);
  padding-left: 8px;
}

/* Add styles for new functionality */
.api-log-action-btn {
  background: rgba(24, 144, 255, 0.2);
  color: var(--primary-color, #1890ff);
  border: 1px solid rgba(24, 144, 255, 0.5);
  border-radius: 4px;
  padding: 3px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-left: auto;
  transition: all 0.3s;
  display: block;
  width: fit-content;
  margin-top: 8px;
}

.api-log-action-btn:hover {
  background: rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.view-on-video-icon {
  font-size: 14px;
}

.api-log-text-large {
  font-size: 16px !important;
  line-height: 1.6;
}

.json-viewer-actions {
  display: flex;
  gap: 5px;
}

/* ������ʽ */
.highlight-high-risk {
  background-color: rgba(255, 0, 0, 0.2);
  color: #ff4d4f;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-medium-risk {
  background-color: rgba(250, 173, 20, 0.2);
  color: #faad14;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-low-risk {
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-warning {
  background-color: rgba(250, 173, 20, 0.2);
  color: #faad14;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-important {
  color: #fff;
  text-decoration: underline;
  font-weight: bold;
}

/* API��־������ʽ */
.api-log-actions {
  display: flex;
  margin-top: 10px;
  justify-content: flex-end;
}

.recommendation-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.push-success-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  transition: opacity 0.5s;
  z-index: 5;
}

.yolo-toggle-button {
  background-color: rgba(93, 109, 126, 0.2);
  border-color: var(--primary-color, #1890ff);
  color: var(--primary-color, #1890ff);
  position: relative;
  font-weight: bold;
}

.yolo-toggle-button:hover {
  background-color: rgba(93, 109, 126, 0.4);
  color: #fff;
}

/* ��ǿ��ȫ������ʽ */
.safety-analysis-container {
  margin-top: 15px;
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid var(--border-color);
}

.safety-analysis-heading {
  color: #1890ff;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(24, 144, 255, 0.3);
}

.safety-section {
  color: #1890ff;
  border-left: 3px solid #1890ff;
  padding-left: 10px;
}

.hazard-section {
  color: #ff4d4f;
  border-left: 3px solid #ff4d4f;
  padding-left: 10px;
}

.recommendation-section {
  color: #52c41a;
  border-left: 3px solid #52c41a;
  padding-left: 10px;
}

.safety-hazard {
  color: #ff4d4f;
  font-weight: bold;
}

.safety-recommendation {
  color: #52c41a;
  font-weight: bold;
}

/* ���¶��������ʽ */
.highlight-high-risk {
  background-color: rgba(255, 77, 79, 0.2);
  color: #ff4d4f;
  padding: 0 4px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-medium-risk {
  background-color: rgba(250, 173, 20, 0.2);
  color: #faad14;
  padding: 0 4px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-low-risk {
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  padding: 0 4px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-warning {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  padding: 0 4px;
  border-radius: 3px;
}

.highlight-important {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 0 4px;
  border-radius: 3px;
  font-weight: bold;
}

.safety-analysis-content {
  font-size: 14px;
  line-height: 1.6;
}

.safety-analysis-content ul, 
.safety-analysis-content ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.safety-analysis-content li {
  margin-bottom: 5px;
}

/* Platform-specific icon styles */
.road_structure-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM13.96 12.29l-2.75 3.54-1.96-2.36L6.5 17h11l-3.54-4.71z'/%3E%3C/path%3E%3C/svg%3E");
}

.vehicle_accident-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.85 6h10.29l1.08 3.11H5.77L6.85 6zM19 17H5v-5h14v5z'/%3E%3C/path%3E%3C/svg%3E");
}

/* Styles for the enhanced Qwen results panel */
.qwen-results-panel-main {
  margin-bottom: 20px;
  order: -1; /* Ensure it's first in flex layout */
  flex-grow: 1; /* Allow it to grow to take more space */
  max-height: unset; /* Remove height restriction */
  font-size: 16px; /* Increase font size */
  background: linear-gradient(135deg, rgba(0, 21, 41, 0.7), rgba(0, 58, 140, 0.4));
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: var(--radius-md, 8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  padding: 5px;
  position: relative;
  overflow: visible; /* Allow content to extend */
}

.qwen-results-panel-main::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 10%;
  right: 10%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.7), transparent);
}

/* Make description text larger and more visible */
.qwen-results-panel-main .description-box {
  font-size: 16px;
  line-height: 1.8;
  padding: 15px;
  background: rgba(0, 21, 41, 0.5);
  border-left: 3px solid var(--primary-color, #1890ff);
}

/* Enhance section headers */
.qwen-results-panel-main .section-header {
  background: linear-gradient(90deg, rgba(0, 58, 140, 0.8), rgba(0, 21, 41, 0.6));
  padding: 12px 15px;
}

.qwen-results-panel-main .section-title {
  font-size: 16px;
  font-weight: bold;
}

/* ��Ƶ������ʽ���Ż���ʾЧ�� */
.video-container {
  width: 100%;
  background-color: #001529;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-radius: var(--radius-md, 8px);
  overflow: hidden;
  margin-bottom: 15px;
}

.card-container {
  border: 1px solid var(--border-color, #003a8c);
  border-radius: var(--radius-md, 8px);
  background-color: var(--card-bg, #001f3d);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: rgba(0, 33, 65, 0.8);
  border-bottom: 1px solid var(--border-color, #003a8c);
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--text-primary, #fff);
}

.card-tools {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.card-body {
  padding: 15px;
  background-color: #000;
}

.card-footer {
  padding: 10px 15px;
  border-top: 1px solid var(--border-color, #003a8c);
  background-color: rgba(0, 33, 65, 0.8);
}

.video-preview-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 ���߱� */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background: #000;
  border-radius: var(--radius-md, 8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.video-preview-wrapper video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain; /* ������Ƶ���� */
  border-radius: var(--radius-sm, 4px);
  display: block;
  z-index: 1;
}

.video-preview-wrapper .detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2; /* ȷ��canvas����Ƶ֮�� */
  pointer-events: none; /* ���������͸����Ƶ���� */
}

/* ��Ƶ���غʹ���״̬���ǲ� */
.ys7-loading-overlay,
.ys7-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 10;
  border-radius: var(--radius-sm, 4px);
}

.processing-indicator {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(24, 144, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color, #1890ff);
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.processing-text,
.error-message {
  color: #fff;
  font-size: 14px;
  text-align: center;
}

.error-icon {
  font-size: 32px;
  color: #ff4d4f;
  margin-bottom: 15px;
  position: relative;
}

.error-icon:before {
  content: "!";
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: #ff4d4f;
  border-radius: 50%;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
}
</style>

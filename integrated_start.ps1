# Traffic Eyes 整合启动脚本
# 集成原有系统和新的视频处理系统

Write-Host "启动 Traffic Eyes 整合系统..." -ForegroundColor Green

# 设置环境变量，避免OpenCV错误
$env:OPENCV_VIDEOIO_PRIORITY_MSMF = "0"

# 创建必要的目录
$dirs = @(
    "logs", 
    "qwen-vl-backend/static/uploads",
    "processed_videos",
    "static_realtime"
)
foreach ($d in $dirs) {
    if (-not (Test-Path $d)) {
        Write-Host "创建目录: $d" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $d -Force | Out-Null
    }
}

# 检查Redis安装
$redis_server = "redis-server.exe"
$redis_found = $false
try {
    $found = Get-Command $redis_server -ErrorAction SilentlyContinue
    if ($found) {
        $redis_found = $true
        Write-Host "找到Redis: $($found.Source)" -ForegroundColor Green
    }
} catch {
    Write-Host "未找到Redis，将尝试使用嵌入式版本或安装..." -ForegroundColor Yellow
}

# 启动Redis
if (-not $redis_found) {
    $embedded = ".\redis-server.exe"
    if (Test-Path $embedded) {
        Write-Host "使用嵌入式Redis..." -ForegroundColor Cyan
        Start-Process -FilePath $embedded -ArgumentList "--port 6379" -NoNewWindow
    } else {
        $installer = "Redis-x64-3.0.504.msi"
        if (Test-Path $installer) {
            Write-Host "安装Redis MSI..." -ForegroundColor Cyan
            Start-Process msiexec.exe -ArgumentList "/i `"$installer`" /qn" -Wait
            Write-Host "启动Redis服务..." -ForegroundColor Green
            Start-Service -Name "Redis"
        } else {
            Write-Host "未找到Redis安装程序。请下载后重试。" -ForegroundColor Red
            exit 1
        }
    }
}

# 检查Python环境
$py = "python"
try {
    $ver = & $py --version 2>&1
    Write-Host "Python版本: $ver" -ForegroundColor Green
} catch {
    Write-Host "未检测到Python。请安装Python 3.8或更高版本。" -ForegroundColor Red
    exit 1
}

# 安装所需的Python包
$pkgs = @("fastapi","uvicorn","redis","websocket-client","numpy","opencv-python","ultralytics","pillow")
$toInstall = @()
foreach ($p in $pkgs) {
    $res = & $py -m pip show $p 2>&1
    if (-not $res) { $toInstall += $p }
}
if ($toInstall.Count -gt 0) {
    Write-Host "安装缺失的包: $($toInstall -join ', ')" -ForegroundColor Yellow
    & $py -m pip install $toInstall
}

# 确保YOLO模型文件存在
$models = @(
    @{Path="yolov8x-seg.pt"; Type="高质量检测和分割模型"},
    @{Path="yolov8n.pt"; Type="轻量级快速检测模型"}
)

foreach ($model in $models) {
    if (-not (Test-Path $model.Path)) {
        Write-Host "下载$($model.Type)..." -ForegroundColor Cyan
        & $py -c "from ultralytics import YOLO; YOLO('$($model.Path)')"
    } else {
        Write-Host "已找到$($model.Type): $($model.Path)" -ForegroundColor Green
    }
}

# 检查视频文件
$videoFiles = Get-ChildItem -Path . -Filter *.mp4 | Select-Object -ExpandProperty Name
if ($videoFiles.Count -eq 0) {
    Write-Host "未找到视频文件，将使用摄像头作为源" -ForegroundColor Yellow
    $videoSource = "camera"
} else {
    $videoSource = $videoFiles[0]
    Write-Host "找到视频文件: $videoSource" -ForegroundColor Yellow
}

# 创建简单的HTTP服务器来提供导航页面
function Start-NavigationServer {
    $httpPort = 8090
    
    # 创建一个临时的Python脚本来启动HTTP服务器
    $scriptPath = ".\temp_http_server.py"
    @"
import http.server
import socketserver
import webbrowser
import os
import threading
import time

PORT = 8090
Handler = http.server.SimpleHTTPRequestHandler

print(f"启动导航页面服务器在端口 {PORT}...")
httpd = socketserver.TCPServer(("", PORT), Handler)

# 在后台线程中启动浏览器
def open_browser():
    time.sleep(1)  # 等待服务器启动
    webbrowser.open(f"http://localhost:{PORT}/navigation.html")

threading.Thread(target=open_browser, daemon=True).start()

try:
    httpd.serve_forever()
except KeyboardInterrupt:
    pass
finally:
    httpd.server_close()
    print("导航页面服务器已关闭")
"@ | Out-File -FilePath $scriptPath -Encoding utf8

    # 启动HTTP服务器
    $server = Start-Process -FilePath $py -ArgumentList $scriptPath -PassThru -WindowStyle Hidden
    return $server
}

# 启动服务和监控
$pids = @()
try {
    # 1. 启动原有系统
    Write-Host "启动原有系统..." -ForegroundColor Cyan
    
    Write-Host "启动API服务 (8001)..." -ForegroundColor Cyan
    $api = Start-Process -FilePath $py -ArgumentList "api_server.py" -WorkingDirectory ".\qwen-vl-backend" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($api.HasExited) { Throw "API服务启动失败" }

    Write-Host "启动YOLO处理服务..." -ForegroundColor Cyan
    $proc = Start-Process -FilePath $py -ArgumentList "yolo_redis_processor.py" -WorkingDirectory ".\qwen-vl-backend" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($proc.HasExited) { Stop-Process -Id $api.Id; Throw "YOLO处理器启动失败" }

    Write-Host "启动主后端 (8000)..." -ForegroundColor Cyan
    $main = Start-Process -FilePath $py -ArgumentList "main.py" -WorkingDirectory ".\qwen-vl-backend" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 3
    if ($main.HasExited) { Stop-Process -Id $api.Id,$proc.Id; Throw "主后端启动失败" }

    $npm = Get-Command npm -ErrorAction SilentlyContinue
    if (-not $npm) {
        Write-Host "未找到npm。请安装Node.js并确保其在PATH中。" -ForegroundColor Red
        exit 1
    }
    Write-Host "启动前端..." -ForegroundColor Cyan
    # 保存当前目录
    $currentDir = Get-Location
    # 切换到根目录运行npm
    Set-Location -Path ".\"
    $fe = Start-Process -FilePath npm -ArgumentList "run dev" -PassThru -WindowStyle Hidden
    # 切回原来的目录
    Set-Location -Path $currentDir

    # 2. 启动新的视频处理系统
    Write-Host "启动新的视频处理系统..." -ForegroundColor Cyan
    
    Write-Host "启动离线视频处理服务 (8080)..." -ForegroundColor Cyan
    $offline = Start-Process -FilePath $py -ArgumentList "direct_video_processor.py --port 8080" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if ($offline.HasExited) { Throw "离线视频处理服务启动失败" }

    Write-Host "启动实时视频处理服务 (8081)..." -ForegroundColor Cyan
    if ($videoSource -eq "camera") {
        $realtime = Start-Process -FilePath $py -ArgumentList "realtime_video_processor.py --source 0 --port 8081" -PassThru -WindowStyle Hidden
    } else {
        $realtime = Start-Process -FilePath $py -ArgumentList "realtime_video_processor.py --source $videoSource --port 8081" -PassThru -WindowStyle Hidden
    }
    Start-Sleep -Seconds 2
    if ($realtime.HasExited) { Stop-Process -Id $offline.Id; Throw "实时视频处理服务启动失败" }
    
    # 3. 启动导航页面服务器
    Write-Host "启动导航页面服务器 (8090)..." -ForegroundColor Cyan
    $navServer = Start-NavigationServer
    
    Write-Host "所有服务已启动!" -ForegroundColor Green
    Write-Host "导航页面: http://localhost:8090/navigation.html" -ForegroundColor Magenta
    Write-Host "前端: http://localhost:5173" -ForegroundColor Magenta
    Write-Host "主后端: http://localhost:8000" -ForegroundColor Magenta
    Write-Host "API: http://localhost:8001" -ForegroundColor Magenta
    Write-Host "离线视频处理: http://localhost:8080" -ForegroundColor Magenta
    Write-Host "实时视频流: http://localhost:8081" -ForegroundColor Magenta

    $pids = @($api.Id, $proc.Id, $main.Id, $fe.Id, $offline.Id, $realtime.Id, $navServer.Id)
    $pids | Out-File -FilePath "running_processes.txt"

    Write-Host "按任意键停止所有服务..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

} finally {
    Write-Host "清理中..." -ForegroundColor Red
    foreach ($id in $pids) { Stop-Process -Id $id -Force -ErrorAction SilentlyContinue }
    if (Test-Path "running_processes.txt") { Remove-Item "running_processes.txt" -Force }
    if (Test-Path "temp_http_server.py") { Remove-Item "temp_http_server.py" -Force }
    Write-Host "所有服务已停止" -ForegroundColor Green
} 
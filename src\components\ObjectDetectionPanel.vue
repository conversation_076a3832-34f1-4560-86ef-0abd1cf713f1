<script setup lang="ts">
import { ref, watch, inject, onMounted, computed } from 'vue';
import type { Ref } from 'vue';
import { detectObjects, type DetectionResult, type DetectionObject } from '../services/DetectionService';

// Define props for the component
const props = defineProps({
  imagePreview: {
    type: String,
    default: ''
  },
  selectedOptions: {
    type: Array as () => string[],
    default: () => []
  }
});

// Get the detection results from the parent component
const detectionResults = inject<Ref<DetectionResult | null>>('detectionResults', ref(null));
const showDetectionBoxes = inject<Ref<boolean>>('showDetectionBoxes', ref(true));
const updateDetectionResults = inject<(results: DetectionResult) => void>('updateDetectionResults', (results) => {
  console.warn('updateDetectionResults not provided by parent');
});

// Define detection categories and colors
interface CategoryInfo {
  color: string;
  label: string;
}

const detectionCategories: Record<string, CategoryInfo> = {
  person: { color: '#FF5733', label: '人员' },
  vehicle: { color: '#33A1FF', label: '车辆' },
  box: { color: '#33FF57', label: '箱子' },
  misc: { color: '#F3FF33', label: '杂物' }
};

// Define risk categories
interface RiskEvent {
  id: string;
  label: string;
  count: number;
}

interface RiskCategory {
  id: string;
  label: string;
  events: RiskEvent[];
}

const riskCategories = ref<RiskCategory[]>([
  {
    id: 'person',
    label: '人员风险',
    events: [
      { id: 'no_helmet', label: '人员未带安全帽', count: 0 },
      { id: 'no_vest', label: '人员未穿反光背心', count: 0 },
      { id: 'no_safety_rope', label: '高空作业人员未系安全绳', count: 0 }
    ]
  },
  {
    id: 'machine',
    label: '机械风险',
    events: [
      { id: 'machine_tilt', label: '机械倾覆', count: 0 }
    ]
  },
  {
    id: 'material',
    label: '物料风险',
    events: [
      { id: 'material_unstable', label: '物料堆放不稳', count: 0 },
      { id: 'road_water', label: '路面积水', count: 0 }
    ]
  },
  {
    id: 'regulation',
    label: '法规风险',
    events: [
      { id: 'regulation_violation', label: '违反施工规范', count: 0 }
    ]
  },
  {
    id: 'environment',
    label: '环境风险',
    events: [
      { id: 'fire', label: '火情', count: 0 },
      { id: 'smoke', label: '烟雾', count: 0 }
    ]
  }
]);

// Statistics
const totalPeople = computed(() => {
  if (!detectionResults.value) return 0;
  return detectionResults.value.objects.filter(obj =>
    obj.category === 'person' ||
    obj.category === 'worker_with_helmet' ||
    obj.category === 'worker_no_helmet'
  ).length;
});

const totalVehicles = computed(() => {
  if (!detectionResults.value) return 0;
  return detectionResults.value.objects.filter(obj =>
    obj.category === 'vehicle' ||
    obj.category === 'car' ||
    obj.category === 'truck' ||
    obj.category === 'bus'
  ).length;
});

// Total high risk events
const totalHighRiskEvents = computed(() => {
  return riskCategories.value.reduce((total, category) => {
    return total + category.events.reduce((sum, event) => sum + event.count, 0);
  }, 0);
});

// Mock function to populate risk events based on detection results
// In a real application, this would be based on actual detection logic
const updateRiskEvents = () => {
  if (!detectionResults.value) return;

  // Reset all counts
  riskCategories.value.forEach(category => {
    category.events.forEach(event => {
      event.count = 0;
    });
  });

  // Example logic to populate risk events based on detection results
  detectionResults.value.objects.forEach(obj => {
    if (obj.category === 'worker_no_helmet') {
      const event = riskCategories.value[0].events.find(e => e.id === 'no_helmet');
      if (event) event.count++;
    }

    // Add more logic here based on your actual detection categories
    // This is just an example

    // Randomly add some risk events for demonstration
    if (Math.random() > 0.7) {
      const randomCategoryIndex = Math.floor(Math.random() * riskCategories.value.length);
      const category = riskCategories.value[randomCategoryIndex];
      const randomEventIndex = Math.floor(Math.random() * category.events.length);
      category.events[randomEventIndex].count++;
    }
  });
};

// Loading state
const isLoading = ref(false);
const error = ref('');

// Watch for changes in the image preview
watch(() => props.imagePreview, (newValue) => {
  if (newValue) {
    // When a new image is loaded, reset the detection results
    if (detectionResults.value) {
      detectionResults.value = null;
    }
  }
}, { immediate: true });

// Watch for changes in detection results
watch(() => detectionResults.value, (newValue) => {
  if (newValue) {
    updateRiskEvents();
  }
}, { deep: true });

// Add a function to get category color
const getCategoryColor = (categoryId: string): string => {
  const colors: Record<string, string> = {
    'person': '#1890ff',
    'machine': '#52c41a',
    'material': '#faad14',
    'regulation': '#722ed1',
    'environment': '#eb2f96'
  };
  return colors[categoryId] || '#1890ff';
};

// 格式化描述文本，将Markdown格式转换为HTML
function formatDescription(description: string): string {
  if (!description) return '';

  // 将Markdown标题转换为HTML，并处理空行格式
  let formattedText = description
    // 将风险分析标题转换为HTML
    .replace(/#### 风险分析/g, '<h4 class="description-heading">风险分析</h4>')
    // 确保"管控建议"标题前有空行，但"风险分析"标题后没有空行
    .replace(/#### 管控建议/g, '<h4 class="description-heading">管控建议</h4>')
    // 移除风险分析后的空行
    .replace(/<h4 class="description-heading">风险分析<\/h4>\s*<br>/g, '<h4 class="description-heading">风险分析</h4>')
    // 添加管控建议前的空行
    .replace(/<h4 class="description-heading">管控建议<\/h4>/g, '<br><h4 class="description-heading">管控建议</h4>')
    .replace(/\n\n/g, '<br>') // 处理其他换行
    .replace(/\n/g, ''); // 处理单个换行

  return formattedText;
}
</script>

<template>
  <div class="detection-panel-content">
    <div v-if="!detectionResults" class="no-results fade-in">
      <p v-if="!props.imagePreview">请上传图片并选择检测选项</p>
      <p class="error-message" v-if="error">{{ error }}</p>
    </div>

    <div v-else-if="detectionResults" class="results-container">
      <!-- Scene Description -->
      <div class="section-panel slide-in-up" style="animation-delay: 0.1s">
        <div class="section-header">
          <i class="section-icon description-icon"></i>
          <span>场景描述</span>
        </div>
        <div class="section-content">
          <div class="description-box">
            <div v-if="detectionResults.description" v-html="formatDescription(detectionResults.description)"></div>
            <p v-else>无场景描述</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.detection-panel-content {
  padding: 0;
  height: auto;
  min-height: 0;
  overflow-y: visible; /* 改为visible，让内容可以溢出 */
  display: flex;
  flex-direction: column;
  background-color: transparent;
  flex-shrink: 0; /* 防止被压缩 */
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  gap: 20px;
  padding: 20px;
  text-align: center;
  font-size: 1em;
}

.detect-button {
  padding: 12px 24px;
  background: linear-gradient(90deg, #1890ff, #096dd9);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-weight: bold;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  letter-spacing: 0.5px;
}

.detect-button:hover {
  background: linear-gradient(90deg, #40a9ff, #1890ff);
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(24, 144, 255, 0.5);
}

.detect-button:disabled {
  background: linear-gradient(90deg, #bfbfbf, #8c8c8c);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.results-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  height: auto;
  max-height: none; /* 移除最大高度限制 */
  padding: 0;
  flex-shrink: 0; /* 防止被压缩 */
}

.section-panel {
  background-color: rgba(0, 33, 64, 0.7);
  padding: 0;
  flex-shrink: 0;
  margin: 5px 10px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 58, 140, 0.3);
  backdrop-filter: blur(5px);
}

.section-header {
  font-size: 14px;
  font-weight: bold;
  color: white;
  text-align: left;
  background: linear-gradient(90deg, #002140, #003a8c);
  padding: 12px 15px;
  letter-spacing: 0.5px;
  position: relative;
  display: flex;
  align-items: center;
}

.section-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.description-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'%3E%3C/path%3E%3C/svg%3E");
}

.section-content {
  margin: 0;
  padding: 12px;
  line-height: 1.6;
  color: var(--text-primary);
}

.description-box {
  background: rgba(0, 21, 41, 0.3);
  border-radius: var(--radius-sm);
  padding: 15px;
  border: 1px solid rgba(0, 168, 255, 0.15);
  line-height: 1.6;
}

.description-box :deep(.description-heading) {
  color: var(--navbar-accent);
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 10px;
  position: relative;
  padding-bottom: 5px;
}

.description-box :deep(.description-heading)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, var(--navbar-accent), transparent);
}

.error-message {
  color: var(--danger-color);
  background-color: rgba(245, 34, 45, 0.1);
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--danger-color);
  margin-top: 8px;
  font-size: 13px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 21, 41, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.5);
  border-radius: 3px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.8);
}

/* Responsive styles */
@media (max-width: 768px) {
  .stat-item {
    padding: 12px;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }

  .stat-value {
    font-size: 24px;
  }
}
</style>
#!/usr/bin/env python3
"""
启动YOLO专用后端服务器
"""

import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """启动YOLO专用后端"""
    try:
        # 检查YOLO模型文件是否存在
        model_paths = [
            "../src/models/yolo11n-seg.pt",
            "../src/models/best.pt",
            "models/yolo11n-seg.pt",
            "models/best.pt"
        ]
        
        model_found = False
        for model_path in model_paths:
            if os.path.exists(model_path):
                logger.info(f"找到YOLO模型: {model_path}")
                model_found = True
                break
        
        if not model_found:
            logger.error("未找到任何YOLO模型文件，请确保模型文件存在于以下路径之一:")
            for path in model_paths:
                logger.error(f"  - {path}")
            return 1
        
        # 启动服务器
        logger.info("启动YOLO专用后端服务器...")
        logger.info("服务器地址: http://localhost:8001")
        logger.info("WebSocket地址: ws://localhost:8001/ws/yolo-only-process")
        
        import uvicorn
        from yolo_only_backend import app
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            reload=False,
            log_level="info"
        )
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        logger.error("请确保已安装所需依赖:")
        logger.error("  pip install fastapi uvicorn websockets")
        logger.error("  pip install ultralytics opencv-python pillow")
        return 1
    except KeyboardInterrupt:
        logger.info("服务器已被用户中断")
        return 0
    except Exception as e:
        logger.error(f"启动服务器时出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

# Traffic Eyes 项目合并描述

## 项目概述

Traffic Eyes 是一个基于 Qwen 2.5 VL 模型的交通图像分析系统，能够智能分析交通场景图像，识别交通状况、车辆、行人、交通标志和潜在的交通问题。系统采用前后端分离架构，前端使用 Vue 3 + TypeScript 开发，后端基于 FastAPI 和 Qwen 2.5 VL 模型实现。

## 技术栈

### 前端
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI 组件库**: Element Plus
- **路由**: Vue Router
- **状态管理**: Pinia
- **图表**: ECharts
- **HTTP 客户端**: Axios
- **Markdown 渲染**: markdown-it + highlight.js

### 后端
- **Web 框架**: FastAPI
- **ASGI 服务器**: Uvicorn
- **AI 模型接口**: DashScope API
- **图像处理**: Pillow, OpenCV
- **对象检测**: yolo11n-seg.pt 模型
- **环境变量管理**: python-dotenv

### AI 模型
- **主要模型**: Qwen 2.5 VL (通过 DashScope API 调用)
- **对象检测模型**: yolo11n-seg.pt

## 主要功能

1. **交通场景分析**
   - 上传交通场景图片进行分析
   - 检测图像中的车辆、行人、交通标志等对象
   - 显示检测结果和边界框
   - 生成场景描述和分析报告

2. **车辆事故风险检测**
   - 识别潜在的交通事故风险
   - 检测车辆碰撞风险
   - 评估交通拥堵情况
   - 识别路侧停车风险

3. **异常驾驶行为识别**
   - 检测超速驾驶行为
   - 识别车道偏离情况
   - 分析驾驶分心行为

4. **在线监控**
   - 实时监控交通场景
   - 持续分析视频流中的风险事件
   - 记录高风险和低风险事件

5. **交互式对话**
   - 与 AI 模型进行对话
   - 询问关于交通场景的详细信息
   - 获取安全建议和改进措施

## 最新实现的功能

1. **yolo11n-seg.pt 模型集成**
   - 新增车辆事故风险检测功能
   - 新增异常驾驶行为识别功能
   - 实现了类别映射机制，将检测结果映射到特定风险/行为类别
   - 添加了结果可视化功能

2. **在线监控功能**
   - 实现了 `/online-monitor-vehicle-accidents` 和 `/online-monitor-abnormal-driving` 端点
   - 支持实时视频流分析
   - 添加了事件记录和风险等级评估

3. **模型管理改进**
   - 添加了 `setup_models.py` 脚本，用于标准化模型文件位置
   - 实现了 `download_models.py` 脚本，用于自动下载所需模型
   - 优化了模型加载逻辑，支持多种模型路径查找

4. **用户界面优化**
   - 改进了检测结果展示方式
   - 添加了风险事件高亮显示
   - 实现了统计数据面板，显示检测到的人员、车辆和设备数量

5. **系统架构优化**
   - 实现了前后端代理配置，简化了 API 调用
   - 添加了健康检查端点，用于监控系统状态
   - 优化了错误处理机制，提高了系统稳定性

## API 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 健康检查，返回 API 状态 |
| `/analyze/image` | POST | 分析上传的图像 |
| `/analyze/image_url` | POST | 分析图像 URL |
| `/conversation` | POST | 与模型进行对话 |
| `/detect` | POST | 检测图像中的对象 |
| `/detect-vehicle-accidents` | POST | 检测车辆事故风险 |
| `/detect-abnormal-driving` | POST | 检测异常驾驶行为 |
| `/online-monitor-vehicle-accidents` | POST | 在线监控车辆事故风险 |
| `/online-monitor-abnormal-driving` | POST | 在线监控异常驾驶行为 |

## 项目结构

```
traffic-eyes/
├── public/                # 静态资源
│   └── favicon.ico        # 网站图标
├── src/                   # 前端源代码
│   ├── assets/            # 资源文件（图片、样式等）
│   ├── components/        # Vue组件
│   │   └── ObjectDetectionPanel.vue  # 对象检测面板组件
│   ├── router/            # 路由配置
│   ├── services/          # 服务层
│   │   └── DetectionService.ts       # 检测服务
│   ├── stores/            # 状态管理
│   ├── views/             # 页面视图
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── qwen-vl-backend/       # 后端代码
│   ├── static/            # 静态文件
│   ├── templates/         # 模板文件
│   ├── main.py            # 后端主程序
│   ├── detection_api.py   # 检测API
│   └── requirements.txt   # 依赖列表
├── index.html             # HTML入口文件
├── package.json           # 项目配置
├── vite.config.ts         # Vite配置
├── setup_models.py        # 模型文件标准化脚本
└── start.ps1              # 启动脚本
```

## 安装与运行

### 安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd qwen-vl-backend
pip install -r requirements.txt
```

### 配置环境变量

在 `qwen-vl-backend` 目录下创建 `.env` 文件，添加以下内容：

```
DASHSCOPE_API_KEY=your_api_key_here
```

### 准备模型文件

运行模型标准化脚本：

```bash
python setup_models.py
```

如果需要下载模型，可以运行：

```bash
cd qwen-vl-backend
python download_models.py
```

### 启动应用

使用启动脚本一键启动前后端：

```bash
# Windows
.\start.ps1
```

或者分别启动前后端：

```bash
# 启动后端
cd qwen-vl-backend
python main.py

# 启动前端（在另一个终端）
npm run dev
```

## 未来改进计划

1. 视频的逻辑处理需要修改，视频的api接入部分没有测试，因为每0.5秒发送一次请求还没处理完就刷新了，显示跟不上
2. api请求的文字需要进行优化排版
3. 智能对话并没有接入大模型


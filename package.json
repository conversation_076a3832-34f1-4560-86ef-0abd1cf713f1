{"name": "traffic-eyes", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.5", "ezuikit-js": "^8.1.9-beta.3", "highlight.js": "^11.11.1", "hls.js": "^1.6.2", "markdown-it": "^14.1.0", "pinia": "^3.0.1", "vue": "^3.3.4", "vue-router": "^4.5.0", "vue3-json-viewer": "^2.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@stagewise-plugins/vue": "^0.4.6", "@stagewise/toolbar-vue": "^0.1.2", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.18.5", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "typescript": "~5.2.0", "vite": "^4.4.11", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^1.8.19"}}
<template>
  <div class="online-monitor-page">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <div class="nav-section">
        <button
          v-for="category in riskCategories"
          :key="category.id"
          class="nav-button hover-lift"
          @click="selectCategory(category.id)"
          :class="{ active: selectedCategory === category.id }"
        >
          <span class="nav-icon" :class="category.id + '-icon'"></span>
          <span>{{ category.label }}</span>
        </button>
      </div>

      <div class="detection-options-container">
        <h3 class="sidebar-header">监测内容</h3>
        <div class="detection-options">
          <div
            v-for="(category, index) in filteredDetectionOptions"
            :key="category.id"
            class="detection-category slide-in-up"
            :style="{animationDelay: `${0.1 * index}s`}"
          >
            <div
              class="category-header"
              @click="toggleCategory(category.id)"
            >
              <div class="category-icon-wrapper">
                <span class="category-icon" :class="category.id + '-icon'"></span>
                <span class="category-label">{{ category.label }}</span>
              </div>
              <span class="expand-icon">{{ category.expanded ? '▼' : '▶' }}</span>
            </div>
            <div
              v-if="category.expanded"
              class="category-options"
            >
              <div
                v-for="option in category.options"
                :key="option.id"
                class="option-item"
              >
                <label class="option-label custom-checkbox">
                  <input
                    type="checkbox"
                    :checked="option.checked"
                    @change="toggleOption(category.id, option.id)"
                  />
                  <span class="option-text">{{ option.label }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Middle - Video and Detection Controls -->
    <div class="video-container">
      <div class="card-container">
        <div class="card-header">
          <span class="card-title">
            萤石云监控
          </span>
          <div class="card-tools">
            <button class="toggle-button hover-lift" @click="toggleMonitoring" v-if="videoPlayerElement && !videoPlayerElement.paused">
              {{ isMonitoringActive ? '停止监控' : '恢复监控' }}
            </button>
            <button class="toggle-button hover-lift" @click="toggleDetectionBoxes" v-if="videoSource && detectionResults">
              {{ showDetectionBoxes ? '隐藏检测框' : '显示检测框' }}
            </button>
            <button class="toggle-button hover-lift yolo-toggle-button" @click="toggleYoloDetection">
              {{ useYoloDetection ? '停用检测可视化' : '启用检测可视化' }}
            </button>
            <button class="toggle-button hover-lift yolo-button" @click="toggleDelayedPlayback" v-if="useYoloDetection">
              {{ isDelayedPlaybackActive ? '实时播放' : '延迟播放' }}
              <span class="toggle-button-hint">{{ isDelayedPlaybackActive ? '(实时检测但延迟显示)' : '(检测结果会即时显示)' }}</span>
            </button>
            <button class="toggle-button hover-lift yolo-reload-button" @click="initializeWebSocket" v-if="useYoloDetection && (!wsYoloConnection || wsYoloConnection.readyState !== 1)">
              重连YOLO
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="video-preview-wrapper">
            <!-- YS7 Cloud video player -->
            <video
              ref="videoPlayerElement"
              id="ys7-video-player"
              class="ys7-video video-player"
              controls
              autoplay
              muted
              @loadedmetadata="initializeCanvas"
              @play="handlePlay"
              @pause="handlePauseOrEnd"
              @ended="handlePauseOrEnd">
            </video>
            <canvas ref="detectionCanvasElement" class="detection-canvas"></canvas>
            
            <!-- Loading overlay for YS7 player -->
            <div class="ys7-loading-overlay" v-if="isYs7Loading">
              <div class="processing-indicator"></div>
              <div class="processing-text">加载萤石云视频中...</div>
            </div>
            
            <!-- Error overlay for YS7 player -->
            <div class="ys7-error-overlay" v-if="ys7Error">
              <div class="error-icon"></div>
              <div class="error-message">{{ ys7ErrorMessage }}</div>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <div class="detection-action">
            <div class="image-actions">
              <button class="detect-button" @click="triggerManualDetection" :disabled="isLoading || (videoPlayerElement && !videoPlayerElement.paused)">
                <div class="detect-button-content">
                  <i class="detect-icon" v-if="!isLoading"></i>
                  <span v-if="isLoading">检测中...</span>
                  <span v-else>{{ (videoPlayerElement && !videoPlayerElement.paused) ? '播放中自动检测' : '检测当前帧' }}</span>
                </div>
              </button>
              <div class="model-selector">
                <div class="selected-model" @click="isModelDropdownOpen = !isModelDropdownOpen">
                  <span class="model-icon"></span>
                  {{ useYoloDetection ? '智眼' : currentModel.name }}
                  <span class="dropdown-icon">▼</span>
                </div>
                <div class="model-dropdown" v-if="isModelDropdownOpen">
                  <div v-if="useYoloDetection" class="model-option active">
                    <div class="model-name">YOLO 检测模型</div>
                    <div class="model-description">使用yolo11n-seg.pt模型进行通用风险检测</div>
                  </div>
                  <div v-else
                    v-for="model in models"
                    :key="model.id"
                    class="model-option"
                    @click="handleModelChange(model.id); isModelDropdownOpen = false"
                    :class="{ active: currentModel.id === model.id }"
                  >
                    <div class="model-name">{{ model.name }}</div>
                    <div class="model-description">{{ model.description }}</div>
                  </div>
                </div>
              </div>
            </div>
            <p v-if="error" class="error-message slide-in-up">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel - Detection Results -->
    <div class="result-panel">
      <div class="risk-analysis-header" :class="{ 'updating': isHeaderUpdating }">
        <div class="risk-analysis-icon"></div>
        <span class="risk-analysis-title">{{ getRiskStatusTitle }}</span>
        <span v-if="lastUpdated" class="last-updated-time">{{ formatUpdateTime(lastUpdated) }}</span>
      </div>
      <div class="result-panel-content">
        <!-- Qwen API Results Panel - Moved to the top and made more prominent -->
        <QwenResultsPanel
          :results="qwenResults"
          :showRawJson="true"
          class="qwen-results-panel-main"
          :key="lastUpdated ? lastUpdated.getTime() : 'initial'"
        />

        <!-- API Logs Section -->
        <div class="api-logs-container" v-if="apiLogs.length > 0">
          <div class="api-logs-header">
            <div class="api-logs-icon"></div>
            <span class="api-logs-title">检测结果详情</span>
            <button class="api-log-action-btn" @click="showLatestLogOnVideo" v-if="canShowLogOnVideo">
              显示在视频上
            </button>
          </div>
          <div class="api-logs-content">
            <div v-for="(log, index) in apiLogs" :key="index" class="api-log-item">
              <div class="api-log-header">
                <span class="api-log-time">{{ new Date().toLocaleTimeString() }}</span>
                <span class="api-log-status success">成功</span>
            </div>
              <div class="api-log-details">
                <!-- Parse log content for more user-friendly display -->
                <div v-if="isJsonString(log)" class="api-log-formatted">
                  <template v-if="tryParseJsonLog(log)">
                    <div class="api-log-section">
                      <div class="api-log-section-title">检测到的目标 ({{ getDetectionCount(log) }})</div>
                      <div class="api-log-detection-items">
                        <div v-for="(detection, idx) in getDetections(log)" :key="idx" class="api-log-detection-item">
                          <div class="detection-type" :class="getRiskClass(detection.risk_level)">
                            {{ detection.category || detection.label || '未知目标' }}
                          </div>
                          <div class="detection-risk">{{ detection.risk_level || '无风险' }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="api-log-section" v-if="getSafetyAnalysis(log)">
                      <div class="api-log-section-title">安全分析与建议</div>
                      <div class="api-log-analysis api-log-text-large" v-html="formatSafetyAnalysis(getSafetyAnalysis(log))"></div>
                      <div class="api-log-actions">
                        <button @click="showSafetyInPanel(log)" class="api-log-action-btn">
                          <span class="action-icon recommendation-icon"></span>
                          在右侧面板中显示
                        </button>
                      </div>
                    </div>
                  </template>
                  <div v-else class="api-log-json-viewer">
                    <div class="json-viewer-header">
                      <div class="json-viewer-type">JSON 响应数据</div>
                      <div class="json-viewer-actions">
                        <button class="json-viewer-action-btn" @click="showLogOnVideo(log)" v-if="hasDetectionData(log)" title="在视频上显示">
                          <span class="view-on-video-icon">👁️</span>
                        </button>
                        <button class="json-viewer-action-btn" @click="expandAllJson(log)" title="展开全部">
                          <span class="expand-icon">+</span>
                        </button>
                        <button class="json-viewer-action-btn" @click="collapseAllJson(log)" title="折叠全部">
                          <span class="collapse-icon">-</span>
                        </button>
                      </div>
                    </div>
                    <div class="json-viewer-content">
                      <template v-if="isJsonString(log)">
                        <div class="json-tree">
                          <component :is="jsonTreeNodeComponentRef" 
                            :data="parseJson(log)" 
                            path="json-root">
                          </component>
                        </div>
                      </template>
                      <pre v-else class="api-log-text api-log-text-large">{{ log }}</pre>
                    </div>
                  </div>
                </div>
                <pre v-else class="api-log-text api-log-text-large">{{ log }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- Conditional rendering based on detection state -->
        <div v-if="showLoadingMessage" class="loading-results">
          <p>正在分析视频帧...</p>
        </div>
        <div v-else-if="error" class="error-display">
          <p>{{ error }}</p>
        </div>
        <div v-else-if="detectionResults">
          <!-- High Risk Events -->
          <div class="risk-events-container high-risk" v-if="groupedHighRiskEventsForDisplay.length > 0">
            <div class="risk-summary">
              <div class="risk-count danger">{{ totalHighRiskCount }}</div>
              <div class="risk-label">高风险事件</div>
            </div>
            <div class="risk-categories">
              <template v-for="categoryGroup in groupedHighRiskEventsForDisplay" :key="categoryGroup.category">
                <div class="risk-category">
                  <div class="risk-category-header high-risk">
                    <span class="category-icon" :class="categoryGroup.category.toLowerCase() + '-icon'"></span>
                    <span class="category-name">{{ categoryGroup.category }}</span>
                    <span class="category-count danger">{{ categoryGroup.totalCount }}</span>
                  </div>
                  <div class="risk-events">
                    <div v-for="eventItem in categoryGroup.events" :key="eventItem.eventName" class="risk-event-item high-risk">
                      <div class="risk-event-content">
                        <div class="risk-event-name">{{ translateEventName(eventItem.eventName) }}
                          <span v-if="eventItem.count > 1" class="event-count">x{{ eventItem.count }}</span>
                        </div>
                      </div>
                      <div class="risk-level risk-high">高风险</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- Low Risk Events -->
          <div class="risk-events-container low-risk" v-if="groupedLowRiskEventsForDisplay.length > 0">
            <div class="risk-summary">
              <div class="risk-count warning">{{ totalLowRiskCount }}</div>
              <div class="risk-label">低风险事件</div>
            </div>
            <div class="risk-categories">
              <template v-for="categoryGroup in groupedLowRiskEventsForDisplay" :key="categoryGroup.category">
                <div class="risk-category">
                  <div class="risk-category-header low-risk">
                    <span class="category-icon" :class="categoryGroup.category.toLowerCase() + '-icon'"></span>
                    <span class="category-name">{{ categoryGroup.category }}</span>
                    <span class="category-count warning">{{ categoryGroup.totalCount }}</span>
                  </div>
                  <div class="risk-events">
                    <div v-for="eventItem in categoryGroup.events" :key="eventItem.eventName" class="risk-event-item low-risk">
                      <div class="risk-event-content">
                        <div class="risk-event-name">{{ translateEventName(eventItem.eventName) }}
                          <span v-if="eventItem.count > 1" class="event-count">x{{ eventItem.count }}</span>
                        </div>
                      </div>
                      <div class="risk-level risk-low">低风险</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- Safety Analysis Section -->
          <div class="safety-analysis-container" v-if="detectionResults.image_overall_safety_analysis_and_control_recommendations || analysisDescription">
            <div class="safety-analysis-header">
              <div class="safety-analysis-icon"></div>
              <span class="safety-analysis-title">安全分析与建议</span>
            </div>
            <div class="safety-analysis-content" v-if="detectionResults.image_overall_safety_analysis_and_control_recommendations" v-html="formatSafetyAnalysis(detectionResults.image_overall_safety_analysis_and_control_recommendations)"></div>
            <div class="safety-analysis-content" v-else-if="analysisDescription" v-html="formatSafetyAnalysis(analysisDescription)"></div>
          </div>

          <!-- Safe Container -->
          <div class="safe-container" v-if="totalHighRiskCount === 0 && totalLowRiskCount === 0 && !analysisDescription">
            <div class="safe-icon">✓</div>
            <div class="safe-message">安全</div>
            <div class="safe-description">未检测到任何风险事件</div>
          </div>
        </div>
        <div v-else class="no-detection-results">
          <div class="no-risk-icon">ℹ️</div>
          <div class="no-risk-message">播放视频将自动开始检测。您也可以暂停视频并点击"检测当前帧"手动分析。</div>
        </div>

        <ObjectDetectionPanel
          :imagePreview="currentFramePreview"
          :selectedOptions="selectedOptions"
          :detectionResults="detectionResults"
        />
        <div class="stats-panel-horizontal" v-if="detectionResults">
          <div class="stats-item">
            <div class="stats-icon person-icon"></div>
            <div class="stats-info">
              <div class="stats-label">人员</div>
              <div class="stats-value">{{ getPersonCount() }}</div>
            </div>
          </div>
          <div class="stats-item">
            <div class="stats-icon vehicle-icon"></div>
            <div class="stats-info">
              <div class="stats-label">车辆</div>
              <div class="stats-value">{{ getVehicleCount() }}</div>
            </div>
          </div>
          <div class="stats-item">
            <div class="stats-icon machine-icon"></div>
            <div class="stats-info">
              <div class="stats-label">设备</div>
              <div class="stats-value">{{ getMachineCount() }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import ObjectDetectionPanel from './ObjectDetectionPanel.vue';
import QwenResultsPanel from './QwenResultsPanel.vue';
import { detectObjects } from '../services/DetectionService';
import type { DetectionResult, HighRiskEvent, LowRiskEvent, DetectionObject } from '../services/DetectionService';
import axios from 'axios';
import Hls from 'hls.js'; // Import hls.js from the installed package

// Define recursive component for JSON tree display
const JsonTreeNode = {
  name: 'JsonTreeNode',
  props: {
    data: {
      type: [Object, Array],
      required: true
    },
    path: {
      type: String,
      required: true
    }
  },
  template: `
    <div>
      <div v-for="(value, key) in data" :key="key" class="json-tree-item">
        <div class="json-tree-key" @click="toggleJsonNode(path + '-' + key)">
          <span v-if="isObject(value)" class="json-tree-toggle">▶</span>
          <span class="json-key-name">{{ key }}:</span>
          <span v-if="isObject(value)" class="json-type">{{ getJsonNodeType(value) }}</span>
          <span v-else class="json-value" :class="getJsonValueClass(value)">{{ formatJsonValue(value) }}</span>
        </div>
        <div :id="path + '-' + key" class="json-tree-children" v-if="isObject(value)">
          <json-tree-node :data="value" :path="path + '-' + key"></json-tree-node>
        </div>
      </div>
    </div>
  `,
  methods: {
    isObject(value) {
      return value !== null && (typeof value === 'object');
    },
    getJsonNodeType(value) {
      return Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`;
    },
    getJsonValueClass(value) {
      if (typeof value === 'string') return 'json-string';
      if (typeof value === 'number') return 'json-number';
      if (typeof value === 'boolean') return 'json-boolean';
      if (value === null) return 'json-null';
      return '';
    },
    formatJsonValue(value) {
      if (typeof value === 'string') return `"${value}"`;
      if (value === null) return 'null';
      return String(value);
    },
    toggleJsonNode(nodeId) {
      const node = document.getElementById(nodeId);
      if (node) {
        const isVisible = node.style.display !== 'none';
        node.style.display = isVisible ? 'none' : 'block';
        
        // Change toggle icon
        const toggleIcon = node.previousElementSibling.querySelector('.json-tree-toggle');
        if (toggleIcon) {
          toggleIcon.textContent = isVisible ? '▶' : '▼';
        }
      }
    }
  }
};

// Register component for Vue 3 composition API in script setup
import { defineComponent, h } from 'vue';

const jsonTreeNodeComponent = defineComponent({
  name: 'JsonTreeNode',
  props: {
    data: {
      type: [Object, Array],
      required: true
    },
    path: {
      type: String,
      required: true
    }
  },
  setup(props) {
    return () => {
      return h('div', {}, Object.entries(props.data).map(([key, value]) => {
        const isObj = value !== null && typeof value === 'object';
        
        const keyEl = h('div', {
          class: 'json-tree-key',
          onClick: () => {
            const nodeId = props.path + '-' + key;
            const node = document.getElementById(nodeId);
            if (node) {
              const isVisible = node.style.display !== 'none';
              node.style.display = isVisible ? 'none' : 'block';
              
              // Get toggle element
              const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
              if (toggleIcon) {
                toggleIcon.textContent = isVisible ? '▶' : '▼';
              }
            }
          }
        }, [
          isObj ? h('span', { class: 'json-tree-toggle' }, '▶') : null,
          h('span', { class: 'json-key-name' }, `${key}:`),
          isObj 
            ? h('span', { class: 'json-type' }, 
                Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`)
            : h('span', { 
                class: ['json-value', 
                  typeof value === 'string' ? 'json-string' : 
                  typeof value === 'number' ? 'json-number' : 
                  typeof value === 'boolean' ? 'json-boolean' : 
                  value === null ? 'json-null' : '']
              }, typeof value === 'string' ? `"${value}"` : value === null ? 'null' : String(value))
        ]);
        
        const valueEl = isObj ? h('div', {
          class: 'json-tree-children',
          id: props.path + '-' + key,
          style: { display: 'none' }
        }, [
          h(jsonTreeNodeComponent, {
            data: value,
            path: props.path + '-' + key
          })
        ]) : null;
        
        return h('div', { class: 'json-tree-item' }, [keyEl, valueEl].filter(Boolean));
      }));
    };
  }
});

// Define window.Hls interface for TypeScript
declare global {
  interface Window {
    EZUIKit?: any;
  }
}

// --- API Logs State ---
const apiLogs = ref<string[]>([]);
const isPollingLogs = ref(false);
const analysisDescription = ref<string>('');

// Qwen API results state
interface QwenDetection {
  category: string;
  event_description: string;
  risk_level: string;
  confidence_score: number;
  bbox_2d: number[];
}

interface QwenRiskEvent {
  description: string;
  mitigation: string;
}

interface QwenResultsData {
  detections: QwenDetection[];
  description: string;
  high_risk_events: QwenRiskEvent[];
  low_risk_events: QwenRiskEvent[];
}

const qwenResults = ref<QwenResultsData | null>(null);
const lastUpdated = ref<Date | null>(null);
const isHeaderUpdating = ref(false);

// 计算动态标题，根据Qwen结果变化
const getRiskStatusTitle = computed(() => {
  if (!qwenResults.value) return '风险统计';
  
  // 获取高/低风险事件数量
  const highRiskCount = qwenResults.value.high_risk_events?.length || 0;
  const lowRiskCount = qwenResults.value.low_risk_events?.length || 0;
  const detectionCount = qwenResults.value.detections?.length || 0;
  
  // 根据风险程度生成不同标题
  if (highRiskCount > 0) {
    return `风险统计 (${highRiskCount}项高风险)`;
  } else if (lowRiskCount > 0) {
    return `风险统计 (${lowRiskCount}项低风险)`;
  } else if (detectionCount > 0) {
    return `场景分析 (${detectionCount}个检测项)`;
  } else {
    return '场景安全分析';
  }
});

// 格式化更新时间
const formatUpdateTime = (time: Date): string => {
  return `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}:${time.getSeconds().toString().padStart(2, '0')}`;
};

// 监听qwenResults变化，触发风险分析头部更新动画
watch(() => qwenResults.value, (newVal, oldVal) => {
  if (newVal) {
    // 更新时间戳
    lastUpdated.value = new Date();
    
    // 触发头部更新动画
    isHeaderUpdating.value = true;
    setTimeout(() => {
      isHeaderUpdating.value = false;
    }, 2000);
  }
}, { deep: true });

// JSON viewer utilities
const isJsonString = (str: string): boolean => {
  try {
    // Check if it's a valid JSON string or an already parsed object
    if (typeof str === 'object') return true;
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

// Make the JSON Tree Node component available in the template
const jsonTreeNodeComponentRef = computed(() => jsonTreeNodeComponent);

const parseJson = (jsonStr: string): any => {
  try {
    if (typeof jsonStr === 'object') return jsonStr;
    
    // Clean JSON string by removing comments before parsing
    const cleanedJsonStr = removeJsonComments(jsonStr);
    return JSON.parse(cleanedJsonStr);
  } catch (e) {
    console.error('JSON parse error:', e);
    return { error: "Invalid JSON" };
  }
};

// Function to remove comments from JSON strings
const removeJsonComments = (jsonString: string): string => {
  if (!jsonString) return '';
  
  // Replace single-line comments (both // and /* */) with empty strings
  let cleanedString = jsonString
    .replace(/\/\/.*$/gm, '') // Remove single-line comments starting with //
    .replace(/\/\*[\s\S]*?\*\//gm, ''); // Remove multi-line comments /* */
    
  // Fix trailing commas that might be left after removing comments
  cleanedString = cleanedString
    .replace(/,(\s*[\]}])/g, '$1');
    
  return cleanedString;
};

const isObject = (value: any): boolean => {
  return value !== null && (typeof value === 'object');
};

const getJsonNodeType = (value: any): string => {
  return Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`;
};

const getJsonValueClass = (value: any): string => {
  if (typeof value === 'string') return 'json-string';
  if (typeof value === 'number') return 'json-number';
  if (typeof value === 'boolean') return 'json-boolean';
  if (value === null) return 'json-null';
  return '';
};

const formatJsonValue = (value: any): string => {
  if (typeof value === 'string') return `"${value}"`;
  if (value === null) return 'null';
  return String(value);
};

const toggleJsonNode = (nodeId: string): void => {
  const node = document.getElementById(nodeId);
  if (node) {
    const isVisible = node.style.display !== 'none';
    node.style.display = isVisible ? 'none' : 'block';
    
    // Change toggle icon
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = isVisible ? '▶' : '▼';
    }
  }
};

const expandAllJson = (jsonStr: string): void => {
  const jsonTree = document.querySelectorAll('.json-tree-children');
  jsonTree.forEach(node => {
    (node as HTMLElement).style.display = 'block';
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = '▼';
    }
  });
};

const collapseAllJson = (jsonStr: string): void => {
  const jsonTree = document.querySelectorAll('.json-tree-children');
  jsonTree.forEach(node => {
    (node as HTMLElement).style.display = 'none';
    const toggleIcon = node.previousElementSibling?.querySelector('.json-tree-toggle');
    if (toggleIcon) {
      toggleIcon.textContent = '▶';
    }
  });
};

// Parse Qwen API result from log
const parseQwenResult = (log: string): void => {
  try {
    // 匹配json部分 - 在```json和```之间或整个日志
    let jsonStr = log;
    
    if (log.includes('```json')) {
      // 如果包含Markdown代码块标记, 提取其中的JSON
      const jsonMatch = log.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        jsonStr = jsonMatch[1].trim();
      }
    } else if (log.includes('{') && log.includes('}')) {
      // 尝试直接从日志中提取JSON部分
      const startPos = log.indexOf('{');
      const endPos = log.lastIndexOf('}') + 1;
      if (startPos !== -1 && endPos !== -1) {
        jsonStr = log.substring(startPos, endPos);
      }
    }

    // 解析JSON
    const parsedData = JSON.parse(jsonStr);
    
    // 验证并更新qwenResults
    if (parsedData && (
      parsedData.detections || 
      parsedData.description || 
      parsedData.high_risk_events || 
      parsedData.low_risk_events
    )) {
      // 立即更新qwenResults和时间戳
      qwenResults.value = parsedData;
      lastUpdated.value = new Date(); // 更新时间戳
      console.log('Updated Qwen API results:', qwenResults.value, '时间:', formatUpdateTime(lastUpdated.value));
      
      // 触发更新事件，确保界面立即刷新
      setTimeout(() => {
        // 强制Vue更新DOM
        if (qwenResults.value) {
          const temp = {...qwenResults.value};
          qwenResults.value = temp;
        }
      }, 0);
    }
  } catch (error) {
    console.error('Failed to parse Qwen API result:', error);
  }
};

// Add a function to manually add logs for demonstration
const addApiLog = (log: string) => {
  // 无条件更新时间戳和触发头部刷新，确保UI始终反映最新状态
  lastUpdated.value = new Date();
  
  try {
    // 先处理常见的成功/返回消息，确保UI状态立即更新
    if (log.includes('INFO:yolo_video_processor:Qwen API返回成功')) {
      console.log('检测到Qwen API返回成功，立即更新UI显示');
      
      // 强制立即更新时间戳和更新状态
      lastUpdated.value = new Date();
      isHeaderUpdating.value = true;
      
      // 如果有现有结果，保留但添加加载指示
      if (qwenResults.value) {
        // 克隆当前结果，添加"正在更新"指示
        const updatedResults = { ...qwenResults.value };
        updatedResults.description = updatedResults.description + '\n\n【正在更新最新分析结果...】';
        qwenResults.value = updatedResults;
      }
      
      // 在2秒后重置头部更新状态
      setTimeout(() => {
        isHeaderUpdating.value = false;
      }, 2000);
    }

    // 处理Qwen API分析结果
    if (log.includes('INFO:yolo_video_processor:Qwen API分析结果:')) {
      console.log('检测到Qwen API分析结果，准备处理');
      
      // 提取结果部分
      const resultPrefix = 'INFO:yolo_video_processor:Qwen API分析结果: ';
      let jsonText = log.substring(log.indexOf(resultPrefix) + resultPrefix.length);
      
      // 调试信息
      console.log(`提取的结果文本长度: ${jsonText.length} 字符`);
      console.log(`结果文本前200个字符: ${jsonText.substring(0, 200)}`);
      
      try {
        // 预处理 - 处理可能带有Markdown格式的JSON
        
        // 如果包含Markdown代码块，提取JSON
        if (jsonText.includes('```json')) {
          const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch && jsonMatch[1]) {
            console.log('从Markdown代码块提取JSON');
            jsonText = jsonMatch[1].trim();
          }
        } 
        // 尝试提取JSON对象（如果文本中包含多余的前缀或后缀）
        else if (jsonText.includes('{') && jsonText.includes('}')) {
          console.log('尝试提取JSON对象');
          const startPos = jsonText.indexOf('{');
          const endPos = jsonText.lastIndexOf('}') + 1;
          if (startPos !== -1 && endPos !== -1) {
            jsonText = jsonText.substring(startPos, endPos);
          }
        }
        
        // 处理Python格式的JSON
        jsonText = jsonText
          .replace(/'/g, '"')     // 将单引号转换为双引号
          .replace(/True/g, 'true')   // Python的True转JS的true
          .replace(/False/g, 'false') // Python的False转JS的false
          .replace(/None/g, 'null');  // Python的None转JS的null
        
        // 尝试解析JSON
        let parsedJson;
        try {
          console.log('尝试解析JSON:', jsonText.substring(0, 100) + '...');
          parsedJson = JSON.parse(jsonText);
          console.log('成功解析JSON结构:', parsedJson);
        } catch (jsonError) {
          console.error('解析JSON失败:', jsonError);
          console.log('尝试进行更严格的JSON清理');
          
          // 尝试更强的修复：删除所有转义符号并找到有效的JSON对象
          try {
            // 删除可能干扰的转义字符
            const cleanedText = jsonText.replace(/\\n/g, ' ').replace(/\\"/g, '"').replace(/\\\\/g, '\\');
            
            // 寻找有效的JSON开始和结束
            const validStartPos = cleanedText.indexOf('{');
            const validEndPos = cleanedText.lastIndexOf('}') + 1;
            
            if (validStartPos !== -1 && validEndPos !== -1 && validEndPos > validStartPos) {
              const validJson = cleanedText.substring(validStartPos, validEndPos);
              console.log('提取潜在有效JSON:', validJson.substring(0, 100) + '...');
              parsedJson = JSON.parse(validJson);
              console.log('修复成功，解析到有效JSON');
            } else {
              throw new Error('无法找到有效的JSON对象');
            }
          } catch (deepError) {
            console.error('深度修复JSON失败:', deepError);
            
            // 如果所有尝试都失败，创建一个基本的结果对象并使用原始文本作为描述
            parsedJson = {
              detections: [],
              description: jsonText.includes('分析') ? 
                jsonText.replace(/^INFO:.*?:/, '') : // 删除日志前缀
                '接收到分析结果，但格式不正确',
              high_risk_events: [],
              low_risk_events: []
            };
            console.log('创建默认结构，使用原始文本作为描述');
          }
        }
        
        // 创建结构化的结果对象，确保所有字段都有默认值
        const qwenResultData = {
          detections: parsedJson.detections || [],
          description: parsedJson.description || parsedJson.safety_analysis || (typeof parsedJson === 'string' ? parsedJson : '场景分析完成'),
          high_risk_events: parsedJson.high_risk_events || [],
          low_risk_events: parsedJson.low_risk_events || []
        };
        
        // 更新状态
        qwenResults.value = qwenResultData;
        lastUpdated.value = new Date();
        isHeaderUpdating.value = true;
        
        setTimeout(() => {
          isHeaderUpdating.value = false;
        }, 2000);
        
        console.log('实时更新Qwen API结果:', qwenResults.value);
        
        // 将结果添加到日志
        apiLogs.value.unshift(log);
        
        // 更新检测结果
        updateDetectionResultsFromResponse(parsedJson);
      } catch (e) {
        console.error('处理Qwen API分析结果时出错:', e);
        
        // 出错时仍然更新UI状态，显示错误信息但提供更有用的描述
        qwenResults.value = {
          detections: [],
          description: `解析Qwen API结果时出错，原因可能是格式不符合预期。将尝试显示原始内容:\n\n${jsonText.substring(0, 500)}${jsonText.length > 500 ? '...' : ''}`,
          high_risk_events: [],
          low_risk_events: []
        };
        
        lastUpdated.value = new Date();
        isHeaderUpdating.value = true;
        setTimeout(() => { isHeaderUpdating.value = false; }, 2000);
        
        // 将日志添加到API日志列表
        apiLogs.value.unshift(log);
      }
    } else {
      // 处理其他日志
      apiLogs.value.unshift(log);
    }
  } catch (error) {
    // 捕获整个处理过程中的任何错误
    console.error('处理API日志失败:', error);
    apiLogs.value.unshift(log);
  }
  
  // 只保留最新的5条日志
  if (apiLogs.value.length > 5) {
    apiLogs.value = apiLogs.value.slice(0, 5);
  }
};

// Function to update detection results from API response
const updateDetectionResultsFromResponse = (response: any) => {
  if (!detectionResults.value) {
    detectionResults.value = {
      objects: [],
      summary: {},
      description: '',
      input_width: 0,
      input_height: 0,
      high_risk_events: [],
      low_risk_events: []
    };
  }
  
  // Update safety analysis
  if (response.image_overall_safety_analysis_and_control_recommendations) {
    detectionResults.value.image_overall_safety_analysis_and_control_recommendations = 
      response.image_overall_safety_analysis_and_control_recommendations;
  } else if (response.description) {
    detectionResults.value.description = response.description;
  }
  
  // Update detection objects
  if (response.detections && response.detections.length > 0) {
    detectionResults.value.objects = response.detections;
  }
  
  // Add risk events
  if (response.high_risk_events && response.high_risk_events.length > 0) {
    detectionResults.value.high_risk_events = response.high_risk_events;
  }
  
  if (response.low_risk_events && response.low_risk_events.length > 0) {
    detectionResults.value.low_risk_events = response.low_risk_events;
  }
  
  // Try to extract objects from raw_json if available
  if (response.raw_json && !detectionResults.value.objects.length) {
    try {
      const rawData = typeof response.raw_json === 'string' ? 
        parseJson(response.raw_json) : response.raw_json;
      
      let objects: any[] = [];
      
      // Extract different object types
      if (rawData.person && Array.isArray(rawData.person)) {
        objects = [...objects, ...rawData.person.map((p: any) => ({...p, category: '人员'}))];
      }
      
      if (rawData.vehicle && Array.isArray(rawData.vehicle)) {
        objects = [...objects, ...rawData.vehicle.map((v: any) => ({...v, category: '车辆'}))];
      }
      
      if (rawData.construction_machinery && Array.isArray(rawData.construction_machinery)) {
        objects = [...objects, ...rawData.construction_machinery.map((m: any) => ({...m, category: '机械'}))];
      }
      
      if (objects.length > 0) {
        detectionResults.value.objects = objects;
      }
    } catch (e) {
      console.warn('从raw_json提取对象失败:', e);
    }
  }
};

// 显示安全建议到右侧面板
const showSafetyRecommendation = (text: string) => {
  if (!text) return;
  
  // 确保文本格式正确，如果没有标题则添加
  let formattedText = text;
  if (!formattedText.includes('### 图像整体安全风险分析与管控建议') && 
      !formattedText.includes('### 安全隐患与建议')) {
    formattedText = '### 安全隐患与建议\n\n' + formattedText;
  }
  
  // 如果没有检测结果，创建一个
  if (!detectionResults.value) {
    detectionResults.value = {
      objects: [],
      summary: {},
      description: formattedText,
      input_width: 0,
      input_height: 0,
      high_risk_events: [],
      low_risk_events: [],
      image_overall_safety_analysis_and_control_recommendations: formattedText
    };
  } else {
    // 否则更新现有的检测结果
    detectionResults.value.description = formattedText;
    detectionResults.value.image_overall_safety_analysis_and_control_recommendations = formattedText;
    
    // 滚动到安全分析面板
    setTimeout(() => {
      const safetyPanel = document.querySelector('.safety-analysis-container');
      if (safetyPanel) {
        safetyPanel.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }
  
  // 清除canvas上的检测框，因为现在我们只关注安全分析
  if (showDetectionBoxes.value === false && detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// 增强格式化安全分析文本函数，专注于显示安全隐患和管控建议
const formatSafetyAnalysis = (content: string): string => {
  if (!content) return '';
  
  // 首先清理文本
  let cleanedContent = content
    .replace(/\\n/g, '\n')  // 处理JSON中的转义换行符
    .replace(/\\"/g, '"');  // 处理JSON中的转义引号
  
  // 突出显示关键词
  const keywordsHighlight = [
    { pattern: /高风险/g, replacement: '<span class="highlight-high-risk">高风险</span>' },
    { pattern: /中风险/g, replacement: '<span class="highlight-medium-risk">中风险</span>' },
    { pattern: /低风险/g, replacement: '<span class="highlight-low-risk">低风险</span>' },
    { pattern: /安全隐患/g, replacement: '<span class="highlight-warning">安全隐患</span>' },
    { pattern: /警告|危险/g, replacement: '<span class="highlight-warning">$&</span>' },
    { pattern: /必须|应该|需要|建议/g, replacement: '<span class="highlight-important">$&</span>' },
    { pattern: /存在(.{1,10}?)问题/g, replacement: '存在<span class="highlight-warning">$1</span>问题' },
    { pattern: /注意(.{1,10}?)安全/g, replacement: '<span class="highlight-important">注意$1安全</span>' }
  ];
  
  keywordsHighlight.forEach(item => {
    cleanedContent = cleanedContent.replace(item.pattern, item.replacement);
  });
  
  // 特殊处理安全分析标题
  cleanedContent = cleanedContent.replace(
    /### 图像整体安全风险分析与管控建议/g, 
    '<h3 class="safety-analysis-heading">图像整体安全风险分析与管控建议</h3>'
  );
  
  // 处理新的简化标题
  cleanedContent = cleanedContent.replace(
    /### 安全隐患与建议/g, 
    '<h3 class="safety-analysis-heading">安全隐患与建议</h3>'
  );
  
  // 处理其他Markdown标记
  let formatted = cleanedContent
    .replace(/\n/g, '<br>')                         // 换行
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 加粗
    .replace(/\*(.*?)\*/g, '<em>$1</em>')           // 斜体
    .replace(/#{3}([^<]*?)(?:\n|$)/g, '<h3>$1</h3>')   // h3标题 (排除已处理的特殊标题)
    .replace(/#{2}(.*?)(?:\n|$)/g, '<h2>$1</h2>')   // h2标题
    .replace(/#{1}(.*?)(?:\n|$)/g, '<h1>$1</h1>')   // h1标题
    .replace(/- (.*?)(?:\n|$)/g, '<li>$1</li>')     // 无序列表
    .replace(/<li>(.*?)<\/li>/g, '<ul><li>$1</li></ul>') // 将列表项包装在ul中
    .replace(/<\/ul><ul>/g, '');                    // 合并相邻的ul标签
  
  // 对于数字列表，单独处理，形成有序列表
  formatted = formatted.replace(/(\d+)\.\s+(.*?)(?:<br>|$)/g, '<ol start="$1"><li>$2</li></ol>');
  formatted = formatted.replace(/<\/ol><ol start="\d+">/g, '');
  
  // 增强对安全隐患和建议的样式
  formatted = formatted
    .replace(/<strong>(.*?隐患.*?)<\/strong>/g, '<strong class="safety-hazard">$1</strong>')
    .replace(/<strong>(.*?建议.*?)<\/strong>/g, '<strong class="safety-recommendation">$1</strong>')
    .replace(/<h3>(.*?安全.*?)<\/h3>/g, '<h3 class="safety-section">$1</h3>')
    .replace(/<h3>(.*?隐患.*?)<\/h3>/g, '<h3 class="hazard-section">$1</h3>')
    .replace(/<h3>(.*?建议.*?)<\/h3>/g, '<h3 class="recommendation-section">$1</h3>');
    
  return formatted;
};

// 提取安全建议的函数增强
const extractSafetyRecommendation = (log: string): string => {
  try {
    // 直接检查是否包含安全分析章节标题
    if (typeof log === 'string' && log.includes('### 图像整体安全风险分析与管控建议')) {
      const startIndex = log.indexOf('### 图像整体安全风险分析与管控建议');
      // 找出章节结束位置（通常是下一个标题或内容结束）
      let endIndex = log.length;
      const nextHeadingMatch = log.substring(startIndex + 1).match(/\n#{2,3}\s+/);
      if (nextHeadingMatch && nextHeadingMatch.index) {
        endIndex = startIndex + 1 + nextHeadingMatch.index;
      }
      
      return log.substring(startIndex, endIndex).trim();
    }
    
    // 尝试解析JSON（确保先清理注释）
    const cleanedLog = typeof log === 'string' ? removeJsonComments(log) : log;
    const parsed = typeof cleanedLog === 'string' ? parseJson(cleanedLog) : cleanedLog;
    let safetyContent = '';
    
    // 首先尝试从常见字段获取
    const possibleFields = [
      'description',
      'safety_analysis',
      'recommendations',
      'image_overall_safety_analysis_and_control_recommendations',
      'safety_recommendations',
      'analysis'
    ];
    
    for (const field of possibleFields) {
      if (parsed[field]) {
        safetyContent = parsed[field];
        // 如果内容不包含标题，并且这是整体安全分析字段，添加标题
        if (!safetyContent.includes('### 图像整体安全风险分析与管控建议')) {
          safetyContent = '### 图像整体安全风险分析与管控建议\n\n' + safetyContent;
        }
        return safetyContent;
      }
    }
    
    // 然后尝试从raw_json中查找
    if (parsed.raw_json) {
      try {
        const rawJsonCleaned = typeof parsed.raw_json === 'string' ? 
          removeJsonComments(parsed.raw_json) : JSON.stringify(parsed.raw_json);
        const rawData = typeof rawJsonCleaned === 'string' ? 
          JSON.parse(rawJsonCleaned) : rawJsonCleaned;
        
        for (const field of possibleFields) {
          if (rawData[field]) {
            safetyContent = rawData[field];
            // 如果内容不包含标题，添加标题
            if (!safetyContent.includes('### 图像整体安全风险分析与管控建议')) {
              safetyContent = '### 图像整体安全风险分析与管控建议\n\n' + safetyContent;
            }
            return safetyContent;
          }
        }
      } catch (e) {
        console.error('解析raw_json失败', e);
      }
    }
    
    // 尝试从原始字符串中提取安全分析部分
    if (typeof log === 'string') {
      // 首先尝试查找安全分析和管控建议章节
      if (log.includes('安全检查总结') || log.includes('管控措施及建议')) {
        // 尝试提取整个章节
        const safetySection = log.substring(log.indexOf('图像整体安全风险分析与管控建议'));
        if (safetySection.length > 50) {  // 确保内容有足够长度
          return safetySection;
        }
      }
      
      // 然后尝试使用正则表达式匹配
      const safetyPatterns = [
        /(?:安全分析|安全建议|管控建议|风险分析|图像整体安全[风险]*分析[及与]管控建议)[:：]([\s\S]+?)(?:\n\n|\Z)/i,
        /(?:安全检查总结|管控措施及建议)([\s\S]+?)(?:\n\n|\Z)/i
      ];
      
      for (const pattern of safetyPatterns) {
        const match = log.match(pattern);
        if (match && match[1]) {
          let extractedContent = match[1].trim();
          // 添加标题，如果不存在
          if (!extractedContent.includes('图像整体安全风险分析与管控建议')) {
            return '### 图像整体安全风险分析与管控建议\n\n' + extractedContent;
          }
          return extractedContent;
        }
      }
      
      // 最后检查长文本内容
      for (const key in parsed) {
        if (typeof parsed[key] === 'string' && 
            /安全|风险|建议|隐患|措施|analysis|safety|risk|recommendation/i.test(parsed[key]) &&
            parsed[key].length > 100) {
          safetyContent = parsed[key];
          // 添加标题，如果不存在
          if (!safetyContent.includes('### 图像整体安全风险分析与管控建议')) {
            safetyContent = '### 图像整体安全风险分析与管控建议\n\n' + safetyContent;
          }
          return safetyContent;
        }
      }
    }
    
    return '';
  } catch (e) {
    console.error('提取安全建议失败', e);
    
    // 最后尝试最简单的正则表达式提取
    if (typeof log === 'string') {
      const simplePattern = /(?:图像整体安全风险分析与管控建议[\s\S]*)/i;
      const match = log.match(simplePattern);
      if (match && match[0]) {
        return match[0].trim();
      }
    }
    return '';
  }
};

// --- Data structures for detection options (from App.vue) ---
interface DetectionOption {
  id: string;
  label: string;
  checked: boolean;
}

interface DetectionCategory {
  id: string;
  label: string;
  expanded: boolean;
  options: DetectionOption[];
}

const detectionOptions = ref<DetectionCategory[]>([
  {
    id: 'person',
    label: '人员监测',
    expanded: true,
    options: [
      { id: 'person_count', label: '人员统计', checked: false },
      { id: 'person_wear', label: '人员穿戴', checked: false },
      { id: 'person_status', label: '人员状态', checked: false },
      { id: 'hoisting_personnel', label: '吊装作业人员', checked: false },
      { id: 'high_altitude_personnel', label: '高空作业人员', checked: false }
    ]
  },
  {
    id: 'machine',
    label: '机械监测',
    expanded: true,
    options: [
      { id: 'vehicle_count', label: '车辆统计', checked: false },
      { id: 'vehicle_status', label: '车辆状态', checked: false },
      { id: 'equipment_status', label: '机械运行状态', checked: false },
      { id: 'hoisting_status', label: '吊装状态', checked: false }
    ]
  },
  {
    id: 'material',
    label: '物料监测',
    expanded: true,
    options: [
      { id: 'subgrade_monitoring', label: '路基监测', checked: false },
      { id: 'slope_monitoring', label: '边坡监测', checked: false },
      { id: 'pavement_monitoring', label: '路面监测', checked: false }
    ]
  },
  {
    id: 'regulation',
    label: '法规监测',
    expanded: true,
    options: [
      { id: 'operation_area_protection', label: '作业区防护', checked: false }
    ]
  },
  {
    id: 'environment',
    label: '环境监测',
    expanded: true,
    options: [
      { id: 'fire_detection', label: '火情监测', checked: false },
      { id: 'smoke_detection', label: '烟雾监测', checked: false }
    ]
  }
]);

const riskCategories = [
  {
    id: 'all',
    label: '全部风险监测',
    options: ['person_count', 'person_wear', 'person_status', 'hoisting_personnel', 'high_altitude_personnel',
              'vehicle_count', 'vehicle_status', 'equipment_status', 'hoisting_status',
              'subgrade_monitoring', 'slope_monitoring', 'pavement_monitoring',
              'operation_area_protection',
              'fire_detection', 'smoke_detection']
  },
  { id: 'general', label: '通用风险监测', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection']},
  { id: 'subgrade', label: '路基风险监测', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'subgrade_monitoring', 'slope_monitoring']},
  { id: 'pavement', label: '路面风险监测', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'pavement_monitoring']},
  { id: 'bridge', label: '桥梁风险监测', options: ['person_count', 'person_wear', 'person_status', 'vehicle_count', 'vehicle_status', 'equipment_status', 'fire_detection', 'smoke_detection', 'hoisting_personnel', 'high_altitude_personnel', 'hoisting_status', 'operation_area_protection']}
];

const selectedCategory = ref('all');

const filteredDetectionOptions = computed(() => {
  return detectionOptions.value;
});

const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId;
  const category = riskCategories.find(cat => cat.id === categoryId);
  if (category) {
    detectionOptions.value.forEach(detCat => {
      detCat.options.forEach(opt => {
        opt.checked = category.options.includes(opt.id);
      });
    });

    // 启用YOLO检测 - 对所有类别使用YOLO进行检测，不只是路面结构风险监测
    console.log('启用YOLO11x-seg检测，与通用检测同时运行');
    useYoloDetection.value = true;
    
    // 更新UI显示的YOLO模型名称
    const yoloBadge = document.querySelector('.yolo-badge');
    if (yoloBadge) {
      yoloBadge.textContent = 'YOLO11x-seg';
    }
    
    // 初始化WebSocket连接
    initializeWebSocket();
    
    // 默认启用延迟播放模式以保证YOLO检测结果可以显示在视频上
    if (!isDelayedPlaybackActive.value) {
      // 给提示
      addApiLog(`INFO:qwen-vl-api:YOLO检测已启用，使用延迟播放模式以保证检测结果与视频同步显示`);
      
      // 延迟500ms后启动延迟播放，确保WebSocket连接已建立
      setTimeout(() => {
        isDelayedPlaybackActive.value = true;
        
        // 如果视频正在播放，启动缓冲播放
        if (videoPlayerElement.value) {
          if (!videoPlayerElement.value.paused) {
            videoPlayerElement.value.pause();
          }
          startBufferedPlayback();
        }
      }, 500);
    }
    
    // 如果视频正在播放，立即开始YOLO检测
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // 先停止当前的检测
      if (frameProcessingInterval) {
        clearInterval(frameProcessingInterval);
        frameProcessingInterval = null;
      }

      // 启动YOLO检测
      handlePlay();
    }
  }
};

const toggleCategory = (categoryId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    category.expanded = !category.expanded;
  }
};

const toggleOption = (categoryId: string, optionId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    const option = category.options.find(opt => opt.id === optionId);
    if (option) {
      option.checked = !option.checked;
    }
  }
};

const selectedOptions = computed(() => {
  const options: string[] = [];
  detectionOptions.value.forEach(category => {
    category.options.forEach(option => {
      if (option.checked) {
        options.push(option.id);
      }
    });
  });
  return options;
});

// --- Video and Canvas State ---
// 设置默认视频源，可以是本地文件或网络URL
const videoSource = ref("./1747124126409.mp4");

// 可以在这里添加逻辑来动态设置视频源
// 例如从配置中读取或根据用户选择设置

// 标记是否使用YOLO检测
const useYoloDetection = ref(false);
const videoPlayerElement = ref<HTMLVideoElement | null>(null);
const detectionCanvasElement = ref<HTMLCanvasElement | null>(null);
const currentFramePreview = ref<string>('');

// --- Detection State & Results ---
const isLoading = ref(false);
const error = ref('');
const detectionResults = ref<DetectionResult | null>(null);
const showLoadingMessage = ref(false); // 新增状态用于控制加载提示的显示

// --- Model Selection (from App.vue) ---
const currentModel = ref({
  id: 'lite',
  name: '智眼 Lite',
  description: '轻量快速识别'
});
const models = [
  { id: 'pro', name: '智眼 Pro', description: '全能型建设风险识别', icon: 'model-pro-icon' },
  { id: 'lite', name: '智眼 Lite', description: '轻量快速识别', icon: 'model-lite-icon' }
];
const isModelDropdownOpen = ref(false);

const handleModelChange = (modelId: string) => {
  // 如果当前是YOLO检测模式，不允许切换模型
  if (useYoloDetection.value) {
    console.log('YOLO检测模式下不能切换模型');
    return;
  }

  const selectedModel = models.find(m => m.id === modelId);
  if (selectedModel) {
    currentModel.value = selectedModel;
  }
  // If detection is ongoing or video is playing, might need to re-trigger or notify
};

// --- Detection Box Toggles (from App.vue) ---
const showDetectionBoxes = ref(true);
const showHighRiskBoxes = ref(true);
const showLowRiskBoxes = ref(true);
const showPersonBoxes = ref(false); // Default to false as per App.vue logic
const showVehicleBoxes = ref(false);
const showMachineBoxes = ref(false);

const toggleDetectionBoxes = () => {
  showDetectionBoxes.value = !showDetectionBoxes.value;

  // 检查是否有处理后的图像
  if (detectionResults.value && (detectionResults.value.processed_url || detectionResults.value.visualized_image_url)) {
    const processedUrl = detectionResults.value.visualized_image_url || detectionResults.value.processed_url;
    if (processedUrl) {
      if (showDetectionBoxes.value) {
        // 如果用户要显示检测框，并且有处理后的图像，加载处理后的图像
        const processedImage = new Image();
        const cacheBuster = new Date().getTime();
        processedImage.onload = () => {
          if (detectionCanvasElement.value) {
            const ctx = detectionCanvasElement.value.getContext('2d');
            if (ctx) {
              ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
              ctx.drawImage(
                processedImage,
                0, 0,
                detectionCanvasElement.value.width,
                detectionCanvasElement.value.height
              );
            }
          }
        };
        processedImage.onerror = () => {
          console.error('加载处理后的图像失败');
          clearCanvas();
        };
        processedImage.src = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
      } else {
        // 如果用户要隐藏检测框，清除画布
        clearCanvas();
      }
      return;
    }
  }

  // 如果没有处理后的图像，使用原始检测框逻辑
  if (showDetectionBoxes.value && detectionResults.value) {
    drawDetectionBoxes(); // 使用常规检测框
  } else {
    clearCanvas();
  }
};

// Placeholder: toggleSpecificBoxes - if needed, copy from App.vue and adapt

const initializeCanvas = () => {
  if (videoPlayerElement.value && detectionCanvasElement.value) {
    const video = videoPlayerElement.value;
    const canvas = detectionCanvasElement.value;
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    console.log(`Canvas initialized to video dimensions: ${canvas.width}x${canvas.height}`);
  }
};

const clearCanvas = () => {
  if (detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// Fix the frame capture function to ensure proper base64 encoding
const captureFrame = (): string | null => {
  if (!videoPlayerElement.value) return null;
  const video = videoPlayerElement.value;
  
  try {
    // 使用离屏canvas来捕获视频帧
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth || 640;
    canvas.height = video.videoHeight || 360;
    
    // 确保视频尺寸有效
    if (canvas.width <= 0 || canvas.height <= 0) {
      console.error('无效的视频尺寸:', canvas.width, canvas.height);
      return null;
    }
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('无法获取canvas上下文');
      return null;
    }
    
    // 将视频帧绘制到canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // 使用较低的JPEG质量以减少数据量，提高传输效率
    // 返回完整的DataURL (例如: "data:image/jpeg;base64,/9j/4AAQ...")
    return canvas.toDataURL('image/jpeg', 0.7);
  } catch (error) {
    console.error('捕获视频帧时出错:', error);
    return null;
  }
};

const generatePrompt = (): string => { // Copied from App.vue, might need video-specific adjustments
  const promptMap: Record<string, string> = {
    'person_count': '人员数量及安全状态',
    'person_wear': '人员安全装备穿戴（安全帽/反光背心）',
    'person_status': '人员工作姿态风险（尤其是不安全姿势）',
    'hoisting_personnel': '吊装区人员安全（吊物下方/回转半径内）',
    'high_altitude_personnel': '高空作业人员坠落风险',
    'vehicle_count': '车辆分布与安全状态',
    'vehicle_status': '车辆行驶/停放风险',
    'equipment_status': '施工机械运行风险',
    'hoisting_status': '吊装作业安全（稳定性/吊索具/信号）',
    'subgrade_monitoring': '路基风险（塌陷/裂缝/沉降）',
    'slope_monitoring': '边坡风险（滑坡/崩塌/落石）',
    'pavement_monitoring': '路面风险（裂缝/坑洼/积水）',
    'operation_area_protection': '作业区防护措施（隔离/警示）',
    'fire_detection': '火灾风险',
    'smoke_detection': '烟雾风险'
  };
  const selectedPrompts: string[] = [];
  detectionOptions.value.forEach(category => {
    category.options.forEach(option => {
      if (option.checked && promptMap[option.id]) {
        selectedPrompts.push(promptMap[option.id]);
      }
    });
  });

  if (selectedPrompts.length === 0) {
    return `分析视频帧中的安全隐患：
1. 关注人员（防护装备、危险行为）、车辆和机械设备
2. 按风险等级（高/中/低）归类并简要描述每项隐患
3. 针对主要隐患提供简短、具体的管控建议

不要包含检测框或坐标信息。使用"### 安全隐患与建议"作为标题。`;
  }
  return `分析视频帧中的安全隐患，重点关注：
${selectedPrompts.map((prompt, index) => `${index + 1}. ${prompt}`).join('\n')}

直接输出：
1. 各类风险隐患的简要描述及风险等级
2. 针对性的管控建议（务实、可操作）

不要包含检测框或坐标信息。使用"### 安全隐患与建议"作为标题，使用Markdown格式。`;
};

// WebSocket连接
let wsConnection: WebSocket | null = null;
let frameCounter = 0; // 帧计数器，用于确保每5帧调用一次API

// 使用YOLO检测进行路面结构风险监测
const performYoloDetection = async (frameDataUrl: string): Promise<DetectionResult> => {
  console.log('使用YOLO检测模式进行路面结构风险监测');

  // 检查是否使用WebSocket进行实时处理
  if (useYoloDetection.value) {
    return await processFrameWithWebSocket(frameDataUrl);
  } else {
    // 使用HTTP API进行处理（原有逻辑）
    // 创建FormData对象用于发送到后端
    const formData = new FormData();
    formData.append('image', frameDataUrl);

    // 将选中的选项添加到FormData
    selectedOptions.value.forEach(option => {
      formData.append('options[]', option);
    });

    // 添加提示词
    const customPrompt = generatePrompt();
    formData.append('prompt', customPrompt);

    // 使用路面结构风险监测的API端点
    const apiEndpoint = '/online-monitor-road-defects';
    console.log(`使用路面结构风险监测API: ${apiEndpoint}`);

    // 发送请求到后端
    const response = await fetch(`http://localhost:8000${apiEndpoint}`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 解析响应
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || '检测失败，请重试');
    }

    // 将YOLO检测结果转换为DetectionResult格式
    const results: DetectionResult = {
      objects: data.defects.map((defect: any) => ({
        category: defect.category,
        confidence: defect.score,
        bbox: defect.bbox,
        risk_level: 'medium',
        label: defect.category
      })),
      summary: {},
      description: data.qwen_response,
      input_width: data.image_width,
      input_height: data.image_height,
      high_risk_events: [],
      low_risk_events: [],
      processed_url: data.result_url,
      visualized_image_url: data.result_url
    };

    // 计算每个类别的数量
    results.summary = results.objects.reduce((acc: Record<string, number>, obj: any) => {
      const category = obj.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    // 根据检测结果生成风险事件
    data.defects.forEach((defect: any) => {
      const isHighRisk = defect.score > 0.8;
      const event = {
        category: defect.category,
        event: `检测到${defect.category}`,
        risk_level: isHighRisk ? 'high' : 'low',
        confidence: defect.score,
        bbox_2d: defect.bbox
      };

      if (isHighRisk) {
        results.high_risk_events.push(event);
      } else {
        results.low_risk_events.push(event);
      }
    });

    return results;
  }
};

// 使用WebSocket处理视频帧
const processFrameWithWebSocket = (frameDataUrl: string): Promise<DetectionResult> => {
  return new Promise((resolve, reject) => {
    // 如果WebSocket连接不存在或已关闭，创建新连接
    if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
      console.log('创建新的WebSocket连接');
      wsConnection = new WebSocket('ws://localhost:8000/ws/yolo-video-process');

      wsConnection.onopen = () => {
        console.log('WebSocket连接已建立');
        // 连接建立后发送当前帧和模型配置
        sendFrameToWebSocket(frameDataUrl);
      };

      wsConnection.onerror = (error) => {
        console.error('WebSocket错误:', error);
        reject(new Error('WebSocket连接错误'));
      };

      wsConnection.onclose = () => {
        console.log('WebSocket连接已关闭');
        wsConnection = null;
      };

      // 处理从服务器接收的消息
      wsConnection.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.error) {
            console.error('服务器返回错误:', data.error);
            reject(new Error(data.error));
            return;
          }

          // 检查是否有分析结果数据
          if (data.qwen_results || data.analysis_results) {
            // 处理Qwen分析结果
            const analysisData = data.qwen_results || data.analysis_results;
            console.log('收到Qwen API分析结果，更新UI');
            
            // 触发UI更新
            addApiLog(`INFO:yolo_video_processor:Qwen API返回成功`);
            addApiLog(`INFO:yolo_video_processor:Qwen API分析结果: ${JSON.stringify(analysisData)}`);
            
            // 如果结果包含场景描述，立即更新
            if (analysisData.description) {
              qwenResults.value = {
                detections: analysisData.detections || [],
                description: analysisData.description,
                high_risk_events: analysisData.high_risk_events || [],
                low_risk_events: analysisData.low_risk_events || []
              };
              lastUpdated.value = new Date();
            }
          }

          if (data.processed_frame) {
            // 确保处理后的帧数据是完整的Data URL
            let processedFrameDataUrl = data.processed_frame;
            if (!processedFrameDataUrl.startsWith('data:image')) {
              // 如果只收到了纯Base64，添加前缀
              processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
            }
            
            // 只在调试时输出日志，避免控制台过载
            if (Math.random() < 0.05) { // Only log approximately 5% of frames
              console.log('YOLO视频流处理中...');
            }
            
            // 检查并保证帧的时间戳有效性
            // 忽略超过2秒的旧帧，防止显示过时的帧
            const currentTime = Date.now();
            const frameTimestamp = data.timestamp || currentTime;
            const frameAge = currentTime - frameTimestamp;
            
            if (frameAge > 2000) {
              console.log(`丢弃过时的帧，年龄: ${frameAge}ms`);
              return;
            }
            
            // 在canvas上显示处理后的帧，用于可视化YOLO检测结果
            if (detectionCanvasElement.value) {
              const processedImage = new Image();
              processedImage.onload = () => {
                const ctx = detectionCanvasElement.value!.getContext('2d');
                if (ctx) {
                  // 检查canvas尺寸是否与视频匹配
                  if (detectionCanvasElement.value!.width !== videoPlayerElement.value?.videoWidth ||
                      detectionCanvasElement.value!.height !== videoPlayerElement.value?.videoHeight) {
                    detectionCanvasElement.value!.width = videoPlayerElement.value?.videoWidth || 640;
                    detectionCanvasElement.value!.height = videoPlayerElement.value?.videoHeight || 360;
                  }
                  
                  // 清除画布
                  ctx.clearRect(0, 0, detectionCanvasElement.value!.width, detectionCanvasElement.value!.height);
                  
                  // 绘制处理后的图像（包含YOLO检测框）
                  ctx.drawImage(
                    processedImage,
                    0, 0,
                    detectionCanvasElement.value!.width,
                    detectionCanvasElement.value!.height
                  );
                }
              };
              processedImage.onerror = (err) => {
                console.error('加载处理后的图像失败:', err);
              };
              processedImage.src = processedFrameDataUrl;
            }
            
            // 如果此帧附带了Qwen分析结果但上面没处理到，在这里处理
            if (data.qwen_analysis && !data.qwen_results) {
              addApiLog(`INFO:yolo_video_processor:Qwen API分析结果: ${JSON.stringify(data.qwen_analysis)}`);
              
              if (typeof data.qwen_analysis === 'object') {
                qwenResults.value = {
                  detections: data.qwen_analysis.detections || [],
                  description: data.qwen_analysis.description || '场景分析中...',
                  high_risk_events: data.qwen_analysis.high_risk_events || [],
                  low_risk_events: data.qwen_analysis.low_risk_events || []
                };
                lastUpdated.value = new Date();
              }
            }
          }
        } catch (error) {
          console.error('处理WebSocket消息时出错:', error);
        }
      };
    } else {
      // 如果连接已存在且打开，直接发送帧
      sendFrameToWebSocket(frameDataUrl);
    }

    // 发送帧到WebSocket
    function sendFrameToWebSocket(frameData: string) {
      if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify({
          frame: frameData,
          model: "yolo11n-seg.pt", // 指定使用yolo11n-seg.pt模型
          config: {
            segmentation: true, // 启用分割
            confidence: 0.25  // 置信度阈值
          },
          timestamp: Date.now(), // 添加时间戳以追踪帧
          frameId: Math.random().toString(36).substring(2, 15) // 添加唯一帧ID
        }));
      } else {
        reject(new Error('WebSocket连接未打开'));
      }
    }
  });
};

// 使用标准API进行检测
const performStandardDetection = async (frameDataUrl: string): Promise<DetectionResult> => {
  const modelId = currentModel.value.id === 'pro' ? 'qwen2.5-vl-72b-instruct' : 'qwen2.5-vl-7b-instruct';
  const customPrompt = generatePrompt();
  console.log('使用标准检测模式，prompt:', customPrompt, 'model:', modelId);
  return await detectObjects(frameDataUrl, selectedOptions.value, customPrompt, modelId);
};

// 处理检测后的图像显示
const handleProcessedImage = (results: DetectionResult) => {
  // 处理WebSocket返回的YOLO处理后的帧
  if (results.yolo_processed_frame) {
    console.log('检测到WebSocket处理后的帧，直接显示');
    const processedImage = new Image();
    processedImage.onload = () => {
      // 在视频上方绘制处理后的图像
      if (videoPlayerElement.value && detectionCanvasElement.value) {
        const ctx = detectionCanvasElement.value.getContext('2d');
        if (ctx) {
          // 确保canvas尺寸与视频匹配
          if (detectionCanvasElement.value.width !== videoPlayerElement.value.videoWidth ||
              detectionCanvasElement.value.height !== videoPlayerElement.value.videoHeight) {
            detectionCanvasElement.value.width = videoPlayerElement.value.videoWidth;
            detectionCanvasElement.value.height = videoPlayerElement.value.videoHeight;
          }

          // 清除当前画布
          ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);

          // 绘制处理后的图像
          ctx.drawImage(
            processedImage,
            0, 0,
            detectionCanvasElement.value.width,
            detectionCanvasElement.value.height
          );

          // 不绘制检测框，因为处理后的图像已经包含了检测结果
          showDetectionBoxes.value = true;
        }
      }
    };
    processedImage.onerror = (err) => {
      console.error('加载WebSocket处理后的帧失败:', err);
      // 加载失败时使用常规检测框
      if (showDetectionBoxes.value) {
        drawDetectionBoxes();
      }
    };
    // 加载图像（WebSocket返回的已经是完整的base64数据）
    processedImage.src = results.yolo_processed_frame;
  }
  // 处理HTTP API返回的图像URL
  else if (results.processed_url || results.visualized_image_url) {
    const processedUrl = results.visualized_image_url || results.processed_url;
    if (processedUrl) {
      console.log('检测到处理后的图像URL，加载处理后的图像:', processedUrl);
      // 创建图像元素并加载处理后的图像
      const processedImage = new Image();
      const cacheBuster = new Date().getTime(); // 防止缓存
      processedImage.onload = () => {
        // 在视频上方绘制处理后的图像
        if (videoPlayerElement.value && detectionCanvasElement.value) {
          const ctx = detectionCanvasElement.value.getContext('2d');
          if (ctx) {
            // 确保canvas尺寸与视频匹配
            if (detectionCanvasElement.value.width !== videoPlayerElement.value.videoWidth ||
                detectionCanvasElement.value.height !== videoPlayerElement.value.videoHeight) {
              detectionCanvasElement.value.width = videoPlayerElement.value.videoWidth;
              detectionCanvasElement.value.height = videoPlayerElement.value.videoHeight;
            }

            // 清除当前画布
            ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);

            // 绘制处理后的图像
            ctx.drawImage(
              processedImage,
              0, 0,
              detectionCanvasElement.value.width,
              detectionCanvasElement.value.height
            );

            // 不绘制检测框，因为处理后的图像已经包含了检测结果
            showDetectionBoxes.value = true;
          }
        }
      };
      processedImage.onerror = (err) => {
        console.error('加载处理后的图像失败:', err);
        // 加载失败时使用常规检测框
        if (showDetectionBoxes.value) {
          drawDetectionBoxes();
        }
      };
      // 加载图像
      processedImage.src = `http://localhost:8000${processedUrl}?t=${cacheBuster}`;
    } else if (showDetectionBoxes.value) {
      // 没有处理后的图像，使用常规检测框
      drawDetectionBoxes();
    }
  } else if (showDetectionBoxes.value) {
    // 没有处理后的图像，使用常规检测框
    drawDetectionBoxes();
  }
};

// 主检测逻辑
const performDetectionLogic = async () => {
  const frameDataUrl = captureFrame(); // Captures from videoPlayerElement
  if (!frameDataUrl) {
    error.value = '无法捕获视频帧进行分析。';
    isLoading.value = false;
    return;
  }
  currentFramePreview.value = frameDataUrl; // For ObjectDetectionPanel

  if (selectedOptions.value.length === 0 && !useYoloDetection.value) { // Allow YOLO to run without specific options
    error.value = '请至少选择一个监测内容 (非YOLO模式时)。';
    detectionResults.value = null;
    clearCanvas(); // Clear detectionCanvasElement
    isLoading.value = false;
    return;
  }

  // Show loading message only on first manual detection or if no results yet
  if (!detectionResults.value || (videoPlayerElement.value && videoPlayerElement.value.paused)) {
    showLoadingMessage.value = true;
  }
  isLoading.value = true;
  error.value = '';

  try {
    let results: DetectionResult | null = null;

    if (useYoloDetection.value) {
      // If YOLO is active, and this is a manual trigger (e.g., on a paused frame),
      // we can send the current frame to YOLO for processing via WebSocket.
      // The result will be displayed on detectionCanvasElement via the WebSocket onmessage handler.
      // We might also want to get a Qwen analysis for this specific frame.
      console.log('YOLO active: Manual detection triggered. Sending frame via WebSocket.');
      processFrameWithYOLO(frameDataUrl); // Sends to WebSocket, display handled by onmessage
      
      // Optionally, also perform a standard detection for Qwen analysis on this specific frame
      // This is useful if the WebSocket YOLO stream doesn't include Qwen analysis or for a detailed one-off analysis
      console.log('YOLO active: Performing additional Qwen analysis for manual detection.');
      results = await performStandardDetection(frameDataUrl);
      if (results) {
        // Update Qwen specific parts of the UI
        if(!detectionResults.value) detectionResults.value = { objects: [], summary: {}, description: '', input_width: 0, input_height: 0, high_risk_events: [], low_risk_events: [] };
        detectionResults.value.description = results.description;
        detectionResults.value.high_risk_events = results.high_risk_events;
        detectionResults.value.low_risk_events = results.low_risk_events;
        showSafetyRecommendation(results.description);
        addApiLog(`INFO:qwen-vl-api (Manual YOLO Frame): 检测成功，返回结果: ${JSON.stringify({ description: results.description, high_risk_events: results.high_risk_events, low_risk_events: results.low_risk_events })}`);
      }
      console.log('YOLO manual detection: Frame sent. Qwen analysis (if any) complete.');

    } else {
      // Standard detection when YOLO is not active
      console.log('Standard detection: Performing Qwen analysis.');
      results = await performStandardDetection(frameDataUrl);
      detectionResults.value = results; // This will hold all results including bounding boxes for non-YOLO
      if (results) {
        addApiLog(`INFO:qwen-vl-api (Standard Manual): 检测成功，返回结果: ${JSON.stringify(results)}`);
        handleProcessedImage(results); // Display boxes on detectionCanvasElement for non-YOLO
      }
    }

    showLoadingMessage.value = false;

  } catch (err: any) {
    console.error('视频帧检测失败 (performDetectionLogic):', err);
    error.value = err.message || '视频帧检测失败，请重试。';
    if (!useYoloDetection.value) detectionResults.value = null; // Clear results only if not in YOLO mode, as YOLO might still update
    showLoadingMessage.value = false;
    if (!useYoloDetection.value) clearCanvas(); // Clear canvas only if not in YOLO mode
  } finally {
    isLoading.value = false;
  }
};

const triggerManualDetection = () => {
    if (videoPlayerElement.value && videoPlayerElement.value.paused) {
        performDetectionLogic();
    }
};

let frameProcessingInterval: number | null = null;
const realTimeDetectionFrameRate = 0.33; // FPS for real-time detection (1 frame every 3 seconds)

const processCurrentFrameForRealtime = () => {
    if (isLoading.value) return; // Skip if a detection is already in progress
    performDetectionLogic();
};

// Fix the handlePlay function to properly extract and send base64 data
const handlePlay = () => {
  if (!isMonitoringActive.value) {
    return;
  }

  if (frameProcessingInterval) {
    clearInterval(frameProcessingInterval);
    frameProcessingInterval = null;
  }

  // If YOLO detection is enabled, start the frame capture and processing loop.
  // The loop is driven by requestAnimationFrame within captureAndProcessFrame and wsConnection.onmessage.
  if (useYoloDetection.value) {
    if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
      console.log('YOLO active, but WebSocket not ready. Initializing WebSocket...');
      initializeWebSocket(); // This will call captureAndProcessFrame on successful connection
    } else {
      console.log('YOLO active and WebSocket ready. Starting frame processing.');
      requestAnimationFrame(captureAndProcessFrame); // Start the loop
    }
  }

  // Standard API detection logic (runs in parallel if conditions are met)
  // This part remains for non-YOLO related Qwen analysis, if applicable, or can be adjusted.
  const enableParallelStandardDetection = selectedCategory.value === 'pavement'; // Example condition

  if (enableParallelStandardDetection) {
    const captureRate = 5; // Frames per second for standard API calls
    frameProcessingInterval = window.setInterval(() => {
      if (videoPlayerElement.value && !videoPlayerElement.value.paused && !isLoading.value) {
        const frameDataUrl = captureFrame(); // Standard capture for API
        if (!frameDataUrl) return;

        console.log('Parallel: Performing standard API detection.');
        isLoading.value = true;
        performStandardDetection(frameDataUrl)
          .then(results => {
            console.log('Parallel: Standard API detection complete.');
            // Update relevant parts of detectionResults, focusing on Qwen analysis
            if (results) {
                if(!detectionResults.value) detectionResults.value = { objects: [], summary: {}, description: '', input_width: 0, input_height: 0, high_risk_events: [], low_risk_events: [] };
                detectionResults.value.description = results.description;
                detectionResults.value.high_risk_events = results.high_risk_events;
                detectionResults.value.low_risk_events = results.low_risk_events;
                // Avoid overwriting YOLO object detection if it's also running
                if (!useYoloDetection.value) {
                    detectionResults.value.objects = results.objects;
                    detectionResults.value.summary = results.summary;
                    detectionResults.value.input_width = results.input_width;
                    detectionResults.value.input_height = results.input_height;
                }
                showSafetyRecommendation(results.description);
                addApiLog(`INFO:qwen-vl-api (Parallel): 检测成功，返回结果: ${JSON.stringify({ description: results.description, high_risk_events: results.high_risk_events, low_risk_events: results.low_risk_events })}`);
                // Visual update for non-YOLO stream
                if (!useYoloDetection.value) {
                    handleProcessedImage(results);
                }
            }
          })
          .catch(err => {
            console.error('Parallel: Standard API detection failed:', err);
          })
          .finally(() => {
            isLoading.value = false;
          });
      }
    }, 1000 / captureRate);
    console.log(`Standard API detection interval started, ${captureRate} FPS for Qwen analysis.`);
  }
  
  console.log(`Video playback started. YOLO detection: ${useYoloDetection.value ? 'Active' : 'Inactive'}.`);
};

const handlePauseOrEnd = () => {
    if (frameProcessingInterval) {
        clearInterval(frameProcessingInterval);
        frameProcessingInterval = null;
    }
    // Optionally, perform one last detection on the paused frame if desired
    // if (videoPlayerElement.value && videoPlayerElement.value.paused) {
    //     performDetectionLogic();
    // }
};

const drawDetectionBoxes = () => {
  const canvas = detectionCanvasElement.value;
  const video = videoPlayerElement.value;

  if (!canvas || !video || !detectionResults.value) {
    clearCanvas();
    return;
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  // Ensure canvas size matches video intrinsic size for correct coordinate mapping
  if (canvas.width !== video.videoWidth || canvas.height !== video.videoHeight) {
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  }
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  if (!showDetectionBoxes.value) return;

  const { objects = [], high_risk_events = [], low_risk_events = [], input_width, input_height } = detectionResults.value;
  const sourceWidth = input_width || video.videoWidth;
  const sourceHeight = input_height || video.videoHeight;

  const allDetections: (DetectionObject | HighRiskEvent | LowRiskEvent)[] = [];
  if (showHighRiskBoxes.value) allDetections.push(...high_risk_events.map(e => ({ ...e, _isHighRisk: true })));
  if (showLowRiskBoxes.value) allDetections.push(...low_risk_events.map(e => ({ ...e, _isLowRisk: true })));

  objects.forEach(obj => {
    const isPerson = obj.category === '人员' || obj.category?.includes('person');
    const isVehicle = obj.category === '车辆' || obj.category?.includes('vehicle');
    const isMachine = obj.category === '机械' || obj.category === '设备' || obj.category?.includes('machine');

    if (
      (isPerson && showPersonBoxes.value) ||
      (isVehicle && showVehicleBoxes.value) ||
      (isMachine && showMachineBoxes.value)
    ) {
      // Avoid adding if it might be a duplicate from risk events (simple check, could be improved with IDs)
      if (!allDetections.some(riskEvent => riskEvent.bbox_2d.toString() === obj.bbox_2d.toString())) {
         allDetections.push(obj);
      }
    }
  });

  allDetections.forEach((item: any) => {
    const bbox = item.bbox_2d;
    if (!bbox || bbox.length !== 4) return;

    const [x1, y1, x2, y2] = bbox;
    const drawX1 = (x1 / sourceWidth) * canvas.width;
    const drawY1 = (y1 / sourceHeight) * canvas.height;
    const boxWidth = ((x2 - x1) / sourceWidth) * canvas.width;
    const boxHeight = ((y2 - y1) / sourceHeight) * canvas.height;

    let color = 'rgba(128, 128, 128, 0.5)'; // Default gray
    let borderColor = 'gray';
    let lineDash: number[] = [];

    if (item._isHighRisk || item.risk_level === 'high') {
      color = 'rgba(255, 0, 0, 0.3)'; borderColor = 'red'; lineDash = [5,3];
    } else if (item._isLowRisk || item.risk_level === 'low') {
      color = 'rgba(255, 165, 0, 0.3)'; borderColor = 'orange';
    } else if (item.category === '人员') {
      color = 'rgba(0, 0, 255, 0.3)'; borderColor = 'blue';
    } else if (item.category === '车辆') {
      color = 'rgba(0, 255, 0, 0.3)'; borderColor = 'green';
    } else if (item.category === '机械' || item.category === '设备') {
      color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
    }

    ctx.strokeStyle = borderColor;
    ctx.fillStyle = color;
    ctx.lineWidth = 2;
    ctx.setLineDash(lineDash);
    ctx.beginPath();
    ctx.rect(drawX1, drawY1, boxWidth, boxHeight);
    ctx.stroke();
    ctx.fill();
    ctx.setLineDash([]);

    const label = item.label || item.event || item.category || '未知';
    ctx.fillStyle = borderColor;
    ctx.font = 'bold 12px Arial';
    const textMetrics = ctx.measureText(label);
    ctx.fillRect(drawX1, drawY1 - 16, textMetrics.width + 4, 16);
    ctx.fillStyle = 'white';
    ctx.fillText(label, drawX1 + 2, drawY1 - 4);
  });
};

// --- Utility Functions for Results Display (from App.vue, simplified/adapted) ---
const totalHighRiskCount = computed(() => detectionResults.value?.high_risk_events?.length || 0);
const totalLowRiskCount = computed(() => detectionResults.value?.low_risk_events?.length || 0);

const getPersonCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '人员' || 
      obj.category === '人' || 
      obj.label === '人' || 
      (obj.category?.toLowerCase().includes('person')) ||
      (obj.label?.toLowerCase().includes('person'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '人员' || 
          obj.category === '人' || 
          obj.label === '人' || 
          (obj.category?.toLowerCase()?.includes('person')) ||
          (obj.label?.toLowerCase()?.includes('person'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          if (rawData.person && Array.isArray(rawData.person)) {
            return rawData.person.length;
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const getVehicleCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '车辆' || 
      obj.category === '车' || 
      (obj.category?.toLowerCase()?.includes('vehicle')) || 
      (obj.category?.toLowerCase()?.includes('car'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '车辆' || 
          obj.category === '车' || 
          (obj.category?.toLowerCase()?.includes('vehicle')) || 
          (obj.category?.toLowerCase()?.includes('car'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          if (rawData.vehicle && Array.isArray(rawData.vehicle)) {
            return rawData.vehicle.length;
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const getMachineCount = (): number => {
  // First check regular detection results
  if (detectionResults.value?.objects?.length) {
    return detectionResults.value.objects.filter(obj => 
      obj.category === '机械' || 
      obj.category === '设备' || 
      obj.category === '起重机' || 
      obj.category === '其他建筑机械' || 
      (obj.category?.toLowerCase()?.includes('machine')) || 
      (obj.category?.toLowerCase()?.includes('equipment'))
    ).length;
  }
  
  // If no results or no matches, check API logs
  if (apiLogs.value.length > 0) {
    try {
      const latestLog = JSON.parse(apiLogs.value[0]);
      
      // Check the detections array
      let count = 0;
      if (latestLog.detections) {
        count = latestLog.detections.filter((obj: any) => 
          obj.category === '机械' || 
          obj.category === '设备' || 
          obj.category === '起重机' || 
          obj.category === '其他建筑机械' || 
          (obj.category?.toLowerCase()?.includes('machine')) || 
          (obj.category?.toLowerCase()?.includes('equipment')) || 
          (obj.category?.toLowerCase()?.includes('construction'))
        ).length;
        
        if (count > 0) return count;
      }
      
      // Also check the raw_json field if it exists
      if (latestLog.raw_json && typeof latestLog.raw_json === 'string') {
        try {
          const rawData = JSON.parse(latestLog.raw_json);
          let machineCount = 0;
          // Check construction_machinery array
          if (rawData.construction_machinery && Array.isArray(rawData.construction_machinery)) {
            machineCount += rawData.construction_machinery.length;
          }
          // Return if we found any
          if (machineCount > 0) return machineCount;
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return count;
    } catch (e) {
      return 0;
    }
  }
  
  return 0;
};

const translateEventName = (eventName: string | undefined): string => {
  if (!eventName) return '未知事件';
  const translations: Record<string, string> = {
    'worker_no_helmet': '未佩戴安全帽',
    'worker_no_vest': '未穿反光背心',
    'worker_falling': '工人摔倒',
    'unsafe_operation': '不安全操作',
    // Add more translations as needed
  };
  return translations[eventName] || eventName;
};

// 注意：格式化安全分析函数现在已经移至上方的增强实现中

// Grouped events for display
type EventGroupItem = { eventName: string; count: number };
type CategoryEventGroup = { category: string; events: EventGroupItem[]; totalCount: number };

const groupEventsForDisplay = (events: HighRiskEvent[] | LowRiskEvent[] | undefined): CategoryEventGroup[] => {
  if (!events || events.length === 0) return [];
  const categorized = new Map<string, EventGroupItem[]>();

  events.forEach(event => {
    const categoryName = event.category || '未分类';
    const eventName = event.event || '未知风险';
    if (!categorized.has(categoryName)) {
      categorized.set(categoryName, []);
    }
    const categoryEvents = categorized.get(categoryName)!;
    let eventItem = categoryEvents.find(e => e.eventName === eventName);
    if (eventItem) {
      eventItem.count++;
    } else {
      categoryEvents.push({ eventName, count: 1 });
    }
  });

  const result: CategoryEventGroup[] = [];
  categorized.forEach((events, category) => {
    result.push({ category, events, totalCount: events.reduce((sum, e) => sum + e.count, 0) });
  });
  return result;
};

const groupedHighRiskEventsForDisplay = computed(() => groupEventsForDisplay(detectionResults.value?.high_risk_events));
const groupedLowRiskEventsForDisplay = computed(() => groupEventsForDisplay(detectionResults.value?.low_risk_events));

// 定义一个函数来加载和显示处理后的图像
const loadProcessedImage = (url: string) => {
  if (!url) return;

  console.log('加载处理后的图像:', url);
  const processedImage = new Image();
  const cacheBuster = new Date().getTime();
  processedImage.onload = () => {
    if (detectionCanvasElement.value) {
      const ctx = detectionCanvasElement.value.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
        ctx.drawImage(
          processedImage,
          0, 0,
          detectionCanvasElement.value.width,
          detectionCanvasElement.value.height
        );
      }
    }
  };
  processedImage.onerror = (err) => {
    console.error('加载处理后的图像失败:', err);
    if (showDetectionBoxes.value) {
      drawDetectionBoxes(); // 加载失败时回退到原始检测框
    }
  };
  processedImage.src = `http://localhost:8000${url}?t=${cacheBuster}`;
};

// Modify the fetchApiLogs function to handle the missing endpoint
const fetchApiLogs = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/logs?type=qwen-vl-api');
    if (!response.ok) {
      console.error('Failed to fetch API logs');
      return;
    }
    
    const data = await response.json();
    if (data.logs) {
      // Filter logs containing the specific prefix
      const successLogs = data.logs.filter((log: string) => 
        log.includes('INFO:qwen-vl-api:检测成功，返回结果:'));
      
      if (successLogs.length > 0) {
        apiLogs.value = successLogs.map((log: string) => {
          // Extract just the result part after the prefix
          const resultMatch = log.match(/INFO:qwen-vl-api:检测成功，返回结果:(.*)/);
          return resultMatch ? resultMatch[1].trim() : log;
        });
      }
    }
  } catch (error) {
    console.error('Error fetching API logs:', error);
  }
};

// Start/stop polling for API logs
const startLogPolling = () => {
  if (!isPollingLogs.value) {
    isPollingLogs.value = true;
    // Poll every 5 seconds
    const pollingInterval = setInterval(() => {
      if (isPollingLogs.value) {
        fetchApiLogs();
      } else {
        clearInterval(pollingInterval);
      }
    }, 5000);
    
    // Initial fetch
    fetchApiLogs();
  }
};

// 添加主动轮询API日志的函数
const startActiveLogPolling = () => {
  console.log('启动主动轮询API日志');
  const pollingInterval = setInterval(async () => {
    try {
      const response = await fetch('http://localhost:8000/api/logs?type=yolo_video_processor');
      if (response.ok) {
        const data = await response.json();
        if (data.logs && data.logs.length > 0) {
          // 查找最新的Qwen API返回成功日志
          const successLogs = data.logs.filter((log: string) => 
            log.includes('INFO:yolo_video_processor:Qwen API返回成功') || 
            log.includes('INFO:yolo_video_processor:Qwen API分析结果'));
          
          if (successLogs.length > 0) {
            // 处理最新的日志
            successLogs.slice(0, 3).forEach((log: string) => {
              addApiLog(log);
            });
            
            // 立即更新状态
            lastUpdated.value = new Date();
            isHeaderUpdating.value = true;
            
            // 2秒后关闭动画
            setTimeout(() => {
              isHeaderUpdating.value = false;
            }, 2000);
          }
        }
      }
    } catch (error) {
      console.warn('轮询API日志失败:', error);
    }
  }, 1000); // 每秒轮询一次
  
  return pollingInterval;
};

// 添加主动获取Qwen分析结果的函数
const fetchQwenApiResults = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/logs?type=yolo_video_processor&limit=3');
    if (response.ok) {
      const data = await response.json();
      if (data.logs && data.logs.length > 0) {
        // 查找最新的Qwen API分析结果日志
        const qwenApiResultsLog = data.logs.find(log => 
          log.includes('INFO:yolo_video_processor:Qwen API分析结果:')
        );
        
        if (qwenApiResultsLog) {
          console.log('主动获取到最新的Qwen API分析结果');
          // 将Qwen API分析结果添加到日志
          addApiLog(qwenApiResultsLog);
        }
      }
    }
  } catch (error) {
    console.error('获取Qwen API结果失败:', error);
  }
};

// 启动定期轮询以获取最新的Qwen API分析结果
const startQwenApiResultsPolling = () => {
  // 每2秒轮询一次
  const pollingInterval = setInterval(fetchQwenApiResults, 2000);
  
  // 首次立即查询
  fetchQwenApiResults();
  
  return pollingInterval;
};

onMounted(() => {
  // Initialize YS7 video player
  initializeYs7Player();
  
  // Initialize categories
  selectCategory('all');
  
  // Event listeners for video are added in the template now
  
  // Add a sample log for demonstration
  addApiLog(`INFO:yolo_video_processor:Qwen API返回成功`);
  setTimeout(() => {
    addApiLog(`INFO:yolo_video_processor:Qwen API分析结果: {'success': True, 'detections': [{'category': '人', 'event_description': '', 'risk_level': '中风险', 'confidence_score': 0.7, 'bbox_2d': [654, 589, 673, 617], 'display_label': '', 'event': '人', 'confidence': 0.8, 'label': '人'}], 'description': '针对上述发现的问题：\n\n* 在施工现场应严格遵守安全生产规定，并对现场工作人员加强安全管理培训；\n* 工作时必须正确佩戴个人防护用品(包括但不限于安全帽)，避免因意外导致伤害事故的发生； \n* 施工过程中需加强对起重机械设备的操作管理及维护保养力度，在其周围设立明显的警告标识提醒过往行人注意避让防止发生碰撞或者被砸伤的情况出现;\n * 加强日常巡查检查制度落实到位及时消除安全隐患预防事故发生;  \n   \n以上措施有助于降低各类安全事故发生的概率从而保障员工生命财产安全以及促进项目顺利推进完成! \n\n请注意实际工作中可能存在更多细节需要进一步确认具体情况进行针对性处理！', 'high_risk_events': [{"description": "工人未佩戴安全帽作业", "mitigation": "立即要求该工人佩戴安全帽，加强安全教育和现场巡查。"}], 'low_risk_events': [], 'raw_json': '{\n    "person": [\n        {\n            "category": "人",\n            "event_description": "",\n            "risk_level": "中风险",\n            "confidence_score": 0.7,\n            "bbox_2d": [654, 589, 673, 617],\n            "display_label": ""\n        }\n    ],\n    "vehicle": [],\n    "construction_machinery": [\n        {\n            "category": "起重机",\n            "event_description": "",\n            "risk_level": "无风险",\n            "confidence_score": 0.9,\n            "bbox_2d": [-1,-1 , -1 ,-1]\n        },\n        {\n            "category": "其他建筑机械",\n            "event_description": "",\n            "risk_level": "无风险",\n            "confidence_score": 0.9,\n            "bbox_2d": []\n        }\n    ],\n    "crane_operation_status": {},\n    "road_base_condition": {},\n    "slope_stability": {},\n    "pavement_condition": {},\n    "hard_isolation_and_protection_barriers": {},\n    "fire_hazard": {},\n    "smoke_detection": {}\n}', 'input_width': 1664, 'input_height': 928, 'image_path': 'static\\uploads\\9f19d038-ccbd-4ca1-a02c-3f948d6f66f6.jpg'}`);
  }, 500);
  
  // Start regular API log polling
  startLogPolling();
  
  // Start active polling specifically for Qwen API results
  const activePollingInterval = startActiveLogPolling();
  
  // Also start dedicated polling for Qwen API results
  const qwenApiResultsPollingInterval = startQwenApiResultsPolling();
  
  // Load sample Qwen results
  loadSampleQwenResults();
  
  // Clean up polling when component is unmounted
  onUnmounted(() => {
    if (activePollingInterval) {
      clearInterval(activePollingInterval);
    }
    if (qwenApiResultsPollingInterval) {
      clearInterval(qwenApiResultsPollingInterval);
    }
  });
});

onUnmounted(() => {
  // Clean up HLS player
  if (hlsPlayer) {
    hlsPlayer.destroy();
    hlsPlayer = null;
  }
  
  // 清除定时器
  handlePauseOrEnd();

  // 关闭WebSocket连接
  if (wsConnection) {
    console.log('组件卸载，关闭WebSocket连接');
    try {
      // 发送关闭消息
      if (wsConnection.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify({ action: 'close' }));
      }
      // 关闭连接
      wsConnection.close();
      wsConnection = null;
    } catch (error) {
      console.error('关闭WebSocket连接时出错:', error);
    }
  }
  
  // Stop log polling
  isPollingLogs.value = false;
});

watch(detectionResults, (newValue) => { // Simplified watch, drawDetectionBoxes is called directly after results
    if (showDetectionBoxes.value) {
        if (newValue && (newValue.processed_url || newValue.visualized_image_url)) {
            // 如果有处理后的图像，加载并显示
            const processedUrl = newValue.visualized_image_url || newValue.processed_url;
            if (processedUrl) {
                loadProcessedImage(processedUrl);
            } else {
                drawDetectionBoxes();
            }
        } else {
            drawDetectionBoxes();
        }
    }
}, { deep: true });

watch([showDetectionBoxes, showHighRiskBoxes, showLowRiskBoxes, showPersonBoxes, showVehicleBoxes, showMachineBoxes],
  () => {
    if (currentlyDisplayedLog.value) {
      // If we're showing a log on the video, redraw it
      drawLogDetectionBoxes(currentlyDisplayedLog.value);
    } else if (detectionResults.value) { 
      // Otherwise use regular detection results
      drawDetectionBoxes();
    }
  }
);

// YS7 related data
const isYs7Loading = ref(false);
const ys7Error = ref(false);
const ys7ErrorMessage = ref('');
let hlsPlayer: any = null;

// Add video buffer system for delayed playback
const videoBuffer = ref<{frame: string, timestamp: number}[]>([]);
const bufferSize = ref(5); // Number of frames to buffer (creates delay)
const isBuffering = ref(false);
const bufferInterval = ref<number | null>(null);
const isDelayedPlaybackActive = ref(false);

// Initialize YS7 Cloud video player
const initializeYs7Player = async () => {
  try {
    isYs7Loading.value = true;
    ys7Error.value = false;
    
    // YS7 authentication credentials
    const appKey = '8dcf58f3eff843a49ae4c60b55cd9c9b';
    const appSecret = 'a436053dec3df63157bdfe7100f76f88';
    const deviceSerial = '*********'; // Device serial number
    const channelNo = 3; // Channel number
    
    // Get access token
    const tokenParams = new URLSearchParams();
    tokenParams.append('appKey', appKey);
    tokenParams.append('appSecret', appSecret);
    
    const tokenResponse = await axios.post('https://open.ys7.com/api/lapp/token/get', tokenParams, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (tokenResponse.data.code !== '200') {
      throw new Error(`获取授权失败: ${tokenResponse.data.msg || '请检查AppKey和AppSecret'}`);
    }
    
    const accessToken = tokenResponse.data.data.accessToken;
    
    // Get stream URL
    const liveUrlParams = new URLSearchParams();
    liveUrlParams.append('accessToken', accessToken);
    liveUrlParams.append('deviceSerial', deviceSerial);
    liveUrlParams.append('channelNo', channelNo);
    liveUrlParams.append('protocol', '2'); // HLS protocol
    liveUrlParams.append('quality', '1'); // HD quality
    
    const liveUrlResponse = await axios.post('https://open.ys7.com/api/lapp/v2/live/address/get', liveUrlParams, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (liveUrlResponse.data.code !== '200') {
      throw new Error(`获取直播地址失败: ${liveUrlResponse.data.msg || '请检查设备序列号和通道号'}`);
    }
    
    const hlsUrl = liveUrlResponse.data.data.url;
    
    // Update video source and start playing
    videoSource.value = hlsUrl;
    
    // Initialize HLS player
    const videoElement = videoPlayerElement.value;
    
    if (!videoElement) {
      throw new Error('Video element not found');
    }
    
    if (Hls.isSupported()) {
      if (hlsPlayer) {
        hlsPlayer.destroy();
      }
      
      hlsPlayer = new Hls({
        debug: false,
        maxLoadingRetry: 4,
        enableWorker: true,
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
        // Add lowLatencyMode to improve playback performance
        lowLatencyMode: true,
        // Increase chunk sizes to reduce network requests
        fragLoadPolicy: {
          default: {
            maxTimeToFirstByteMs: 10000,
            maxLoadTimeMs: 120000,
            timeoutRetry: 3
          }
        }
      });
      
      hlsPlayer.loadSource(hlsUrl);
      hlsPlayer.attachMedia(videoElement);
      
      hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest parsed, attempting to play video');
        // Force play the video element
        const playPromise = videoElement.play();
        if (playPromise !== undefined) {
          playPromise.then(() => {
            console.log('Video playback started successfully');
            // 自动启用YOLO检测，无需用户手动点击按钮
            useYoloDetection.value = true;
            console.log('自动启用YOLO检测，开始处理视频流');
            addApiLog(`INFO:qwen-vl-api:已自动启用YOLO检测，开始处理视频流`);
            
            // 初始化WebSocket连接
            initializeWebSocket();
            
            // 开始处理帧
            handlePlay();
          }).catch(playError => {
            console.warn('Autoplay prevented:', playError);
            // Try to play with user interaction later
            addApiLog(`WARN:qwen-vl-api:视频自动播放被浏览器阻止，请点击视频区域手动播放`);
          });
        }
      });
      
      // Error handling
      hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
        if (data.fatal) {
          switch(data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('Network error, attempting to recover');
              hlsPlayer.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('Media error, attempting to recover');
              hlsPlayer.recoverMediaError();
              break;
            default:
              console.error('Fatal error, cannot recover');
              ys7Error.value = true;
              ys7ErrorMessage.value = '视频播放失败，请检查视频源';
              hlsPlayer.destroy();
              hlsPlayer = null;
              // Fall back to local video if available
              videoElement.src = './1747124126409.mp4';
              videoElement.play().catch(e => console.warn('Local video playback error:', e));
              break;
          }
        }
      });
    } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
      // For Safari
      videoElement.src = hlsUrl;
      videoElement.play().catch(e => console.warn('Safari playback error:', e));
    } else {
      throw new Error('您的浏览器不支持HLS播放，请使用现代浏览器');
    }
    
    isYs7Loading.value = false;
  } catch (error) {
    console.error('YS7 initialization error:', error);
    isYs7Loading.value = false;
    ys7Error.value = true;
    ys7ErrorMessage.value = error.message || '萤石云视频加载失败';
    
    // Fallback to sample video if available
    if (videoPlayerElement.value) {
      videoPlayerElement.value.src = './1747124126409.mp4';
      videoPlayerElement.value.play().catch(e => console.warn('Fallback video playback error:', e));
    }
  }
};

// Start buffered playback system for delayed video with YOLO detection
const startBufferedPlayback = () => {
  if (isBuffering.value) return;
  
  isBuffering.value = true;
  isDelayedPlaybackActive.value = true;
  videoBuffer.value = [];
  
  // Pause the main video while we buffer frames
  if (videoPlayerElement.value) {
    videoPlayerElement.value.pause();
  }
  
  console.log(`开始缓冲视频帧，缓冲大小: ${bufferSize.value} 帧`);
  
  // Start capturing frames to fill the buffer
  const captureInterval = setInterval(() => {
    const frame = captureFrame();
    if (frame) {
      const timestamp = Date.now();
      videoBuffer.value.push({
        frame,
        timestamp
      });
      
      // Process frame with YOLO immediately (real-time detection)
      if (useYoloDetection.value && !isLoading.value) {
        // 发送带时间戳和ID的帧
        if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
          const frameId = Math.random().toString(36).substring(2, 15);
          wsConnection.send(JSON.stringify({
            frame: frame.split(',')[1],
            frameId: frameId,
            timestamp: timestamp,
            config: {
              segmentation: true,
              confidence: 0.25
            }
          }));
        }
      }
      
      // Once buffer is full, start playback
      if (videoBuffer.value.length >= bufferSize.value && !bufferInterval.value) {
        console.log(`缓冲已满 (${videoBuffer.value.length} 帧)，开始延迟播放`);
        startBufferPlayback();
      }
    }
  }, 200); // Capture at 5fps
  
  // Clear any existing interval
  if (bufferInterval.value) {
    clearInterval(bufferInterval.value);
    bufferInterval.value = null;
  }
  
  return captureInterval;
};

// Play frames from the buffer with a delay
const startBufferPlayback = () => {
  // Already playing from buffer
  if (bufferInterval.value) return;
  
  console.log('Starting delayed playback from buffer');
  
  // Display frames from buffer at regular intervals
  bufferInterval.value = window.setInterval(() => {
    if (videoBuffer.value.length > 0) {
      const oldestFrame = videoBuffer.value.shift();
      if (oldestFrame && detectionCanvasElement.value) {
        // Display the delayed frame on canvas
        const ctx = detectionCanvasElement.value.getContext('2d');
        if (ctx) {
          // Draw the delayed frame on canvas
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, detectionCanvasElement.value!.width, detectionCanvasElement.value!.height);
            ctx.drawImage(img, 0, 0, detectionCanvasElement.value!.width, detectionCanvasElement.value!.height);
          };
          img.src = oldestFrame.frame;
        }
      }
    }
  }, 40); // Display at ~25fps for smooth playback
};

// Process frame with YOLO and update detection overlay
const processFrameWithYOLO = async (frameDataUrl: string) => {
  try {
    // 提取纯Base64数据
    const base64Data = frameDataUrl.split(',')[1];
    
    // 发送到WebSocket进行处理
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        frame: base64Data,
        model: "yolo11n-seg.pt",
        config: {
          segmentation: true,  // 启用分割
          confidence: 0.25,    // 置信度阈值
          visualize: true      // 确保启用可视化
        },
        timestamp: Date.now(), // 添加时间戳以追踪帧
        frameId: Math.random().toString(36).substring(2, 15) // 添加唯一帧ID
      }));
    } else {
      // 如果WebSocket未连接，尝试重新连接
      console.log('WebSocket未连接，尝试重新连接');
      initializeWebSocket();
    }
  } catch (error) {
    console.error('YOLO处理错误:', error);
  }
};

// 发送测试图像以验证WebSocket连接
const sendTestImage = () => {
  if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
    console.error('WebSocket连接未打开，无法发送测试图像');
    return;
  }
  
  // 创建一个简单的测试图像 (20x20 红色方块)
  const canvas = document.createElement('canvas');
  canvas.width = 20;
  canvas.height = 20;
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.fillStyle = 'red';
    ctx.fillRect(0, 0, 20, 20);
    const testImage = canvas.toDataURL('image/jpeg');
    
    // 提取Base64数据
    const base64Data = testImage.split(',')[1];
    
    console.log('发送测试图像以验证WebSocket连接');
    wsConnection.send(JSON.stringify({
      action: 'test',
      frame: base64Data,
      model: 'yolo11n-seg.pt'
    }));
  }
};

// Initialize WebSocket for YOLO detection
const initializeWebSocket = () => {
  // 调用新的初始化函数，它会初始化两个独立的WebSocket连接
  initializeWebSockets();
};

// Add toggle for delayed playback mode
const toggleDelayedPlayback = () => {
  if (isDelayedPlaybackActive.value) {
    // 关闭延迟播放，恢复正常视频流
    isDelayedPlaybackActive.value = false;
    
    // 清除缓冲
    videoBuffer.value = [];
    
    // 显示正常视频流
    if (videoPlayerElement.value && videoPlayerElement.value.paused) {
      videoPlayerElement.value.play().catch(e => console.warn('视频播放错误:', e));
    }
    
    addApiLog(`INFO:qwen-vl-api:已切换到实时流模式，YOLO检测结果将叠加显示`);
  } else {
    // 启用延迟播放，专注于YOLO处理结果
    isDelayedPlaybackActive.value = true;
    
    addApiLog(`INFO:qwen-vl-api:已切换到YOLO可视化模式，将显示处理后的视频流`);
    
    // 如果视频正在播放，继续使用YOLO处理
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // 确保正在使用YOLO检测
      useYoloDetection.value = true;
    }
  }
};

// Stop delayed playback and return to normal
const stopDelayedPlayback = () => {
  isDelayedPlaybackActive.value = false;
  isBuffering.value = false;
  
  if (bufferInterval.value) {
    clearInterval(bufferInterval.value);
    bufferInterval.value = null;
  }
  
  // Clear buffer
  videoBuffer.value = [];
  
  // Resume normal video playback
  if (videoPlayerElement.value) {
    videoPlayerElement.value.play().catch(e => console.warn('Playback error:', e));
  }
  
  // Clear canvas
  if (detectionCanvasElement.value) {
    const ctx = detectionCanvasElement.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
    }
  }
};

// Add new ref to track monitoring state
const isMonitoringActive = ref(true);

// Add the toggle monitoring function
const toggleMonitoring = () => {
  isMonitoringActive.value = !isMonitoringActive.value;
  
  if (isMonitoringActive.value) {
    // Resume monitoring
    handlePlay();
  } else {
    // Stop monitoring but keep video playing
    if (frameProcessingInterval) {
      clearInterval(frameProcessingInterval);
      frameProcessingInterval = null;
    }
    
    // Also stop delayed playback if active
    if (isDelayedPlaybackActive.value) {
      stopDelayedPlayback();
    }
  }
};

// Add these helper functions for API log formatting
const tryParseJsonLog = (log: string) => {
  try {
    // First try to parse the log directly
    const parsed = parseJson(log);
    
    // Check if it has useful fields
    if (parsed && (
      parsed.detections || 
      parsed.description || 
      parsed.high_risk_events || 
      parsed.low_risk_events ||
      (parsed.raw_json && typeof parsed.raw_json === 'string')
    )) {
      return true;
    }
    
    // If raw_json is present in the parsed result, try to extract useful content from it
    if (parsed && parsed.raw_json) {
      try {
        // Clean and parse the raw_json field
        const cleanedRawJson = removeJsonComments(parsed.raw_json);
        const rawData = JSON.parse(cleanedRawJson);
        
        // Check for image_overall_safety_analysis_and_control_recommendations in raw data
        if (rawData && typeof rawData === 'object') {
          // If there's a safety analysis in the raw_json, it's useful
          if (rawData.image_overall_safety_analysis_and_control_recommendations) {
            return true;
          }
          
          // Check for presence of any object arrays - also useful
          if (rawData.person || rawData.vehicle || rawData.construction_machinery ||
              rawData.high_risk_events || rawData.low_risk_events) {
            return true;
          }
        }
      } catch (e) {
        console.warn('Failed to parse raw_json in tryParseJsonLog:', e);
      }
    }
    
    // If log contains safety analysis text, it's also useful for display
    if (typeof log === 'string' && 
        (log.includes('图像整体安全风险分析与管控建议') || 
         log.includes('安全检查总结') || 
         log.includes('管控措施及建议'))) {
      return true;
    }
    
    return false;
  } catch (e) {
    console.warn('Error in tryParseJsonLog:', e);
    
    // As a fallback, check if the log has safety analysis text
    if (typeof log === 'string' && 
        (log.includes('图像整体安全风险分析与管控建议') || 
         log.includes('安全检查总结') || 
         log.includes('管控措施及建议'))) {
      return true;
    }
    
    return false;
  }
};

const getDetectionCount = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    return (parsed.detections || []).length;
  } catch (e) {
    return 0;
  }
};

const getDetections = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    return parsed.detections || [];
  } catch (e) {
    return [];
  }
};

const getSafetyAnalysis = (log: string) => {
  try {
    const parsed = JSON.parse(log);
    
    // 首先尝试从description字段获取
    if (parsed.description) {
      return parsed.description;
    }
    
    // 然后尝试从各种可能的字段中获取
    if (parsed.safety_analysis || parsed.recommendations || 
        parsed.image_overall_safety_analysis_and_control_recommendations) {
      return parsed.safety_analysis || parsed.recommendations || 
             parsed.image_overall_safety_analysis_and_control_recommendations;
    }
    
    // 最后尝试从raw_json中查找
    if (parsed.raw_json) {
      try {
        const rawData = typeof parsed.raw_json === 'string' ? JSON.parse(parsed.raw_json) : parsed.raw_json;
        if (rawData.image_overall_safety_analysis_and_control_recommendations) {
          return rawData.image_overall_safety_analysis_and_control_recommendations;
        }
      } catch (e) {
        console.error('解析raw_json失败', e);
      }
    }
    
    return '';
  } catch (e) {
    return '';
  }
};

const getRiskClass = (riskLevel: string) => {
  if (!riskLevel) return '';
  
  const level = riskLevel.toLowerCase();
  if (level.includes('高') || level.includes('high')) return 'high-risk';
  if (level.includes('中') || level.includes('medium')) return 'medium-risk';
  if (level.includes('低') || level.includes('low')) return 'low-risk';
  return '';
};

// Add new state for handling log visualization
const canShowLogOnVideo = computed(() => apiLogs.length > 0 && videoPlayerElement.value && detectionCanvasElement.value);
const currentlyDisplayedLog = ref<any>(null);

// Function to check if a log contains detection data with bounding boxes
const hasDetectionData = (log: string): boolean => {
  try {
    const parsed = parseJson(log);
    
    // Check for detections with bbox data
    if (parsed.detections && Array.isArray(parsed.detections)) {
      return parsed.detections.some((d: any) => d.bbox || d.bbox_2d);
    }
    
    // Also check raw_json field if it exists
    if (parsed.raw_json) {
      try {
        const rawData = typeof parsed.raw_json === 'string' ? JSON.parse(parsed.raw_json) : parsed.raw_json;
        // Check for any objects with bbox data
        for (const key in rawData) {
          if (Array.isArray(rawData[key])) {
            if (rawData[key].some((item: any) => item.bbox_2d && item.bbox_2d.length === 4)) {
              return true;
            }
          }
        }
      } catch (e) {
        console.error('Failed to parse raw_json:', e);
      }
    }
    
    return false;
  } catch (e) {
    return false;
  }
};

// Display the latest log on the video
const showLatestLogOnVideo = () => {
  if (apiLogs.length > 0) {
    showLogOnVideo(apiLogs[0]);
  }
};

// Display any log on the video
const showLogOnVideo = (log: string) => {
  try {
    const parsedLog = parseJson(log);
    currentlyDisplayedLog.value = parsedLog;
    
    // Draw bounding boxes from log data
    drawLogDetectionBoxes(parsedLog);
    
    // If the log contains text description, display it with large font
    const description = getSafetyAnalysis(log);
    if (description) {
      displayDescriptionOnVideo(description);
    }
  } catch (e) {
    console.error('Failed to show log on video:', e);
  }
};

// Draw bounding boxes from log data
const drawLogDetectionBoxes = (parsedLog: any) => {
  if (!detectionCanvasElement.value || !videoPlayerElement.value) return;
  
  const canvas = detectionCanvasElement.value;
  const video = videoPlayerElement.value;
  
  // Ensure canvas matches video dimensions
  if (canvas.width !== video.videoWidth || canvas.height !== video.videoHeight) {
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  }
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Clear existing content
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  let detections: any[] = [];
  
  // Extract detections from the log
  if (parsedLog.detections && Array.isArray(parsedLog.detections)) {
    detections = parsedLog.detections;
  }
  
  // Also check raw_json if available
  if (parsedLog.raw_json) {
    try {
      const rawData = typeof parsedLog.raw_json === 'string' ? JSON.parse(parsedLog.raw_json) : parsedLog.raw_json;
      // Extract detections from various categories
      for (const key in rawData) {
        if (Array.isArray(rawData[key])) {
          rawData[key].forEach((item: any) => {
            if (item.bbox_2d && item.bbox_2d.length === 4) {
              detections.push({
                ...item,
                category: item.category || key,
                label: item.category || key
              });
            }
          });
        }
      }
    } catch (e) {
      console.error('Failed to parse raw_json in drawLogDetectionBoxes:', e);
    }
  }
  
  // Draw each detection
  detections.forEach(detection => {
    const bbox = detection.bbox_2d || detection.bbox;
    if (!bbox || bbox.length !== 4) return;
    
    // Get coordinates normalized to canvas size
    const [x1, y1, x2, y2] = bbox;
    const sourceWidth = parsedLog.input_width || video.videoWidth;
    const sourceHeight = parsedLog.input_height || video.videoHeight;
    
    const drawX1 = (x1 / sourceWidth) * canvas.width;
    const drawY1 = (y1 / sourceHeight) * canvas.height;
    const boxWidth = ((x2 - x1) / sourceWidth) * canvas.width;
    const boxHeight = ((y2 - y1) / sourceHeight) * canvas.height;
    
    // Determine color based on risk level or category
    let color = 'rgba(128, 128, 128, 0.5)'; // Default gray
    let borderColor = 'gray';
    let lineDash: number[] = [];
    
    if (detection.risk_level) {
      const riskLevel = detection.risk_level.toLowerCase();
      if (riskLevel.includes('高') || riskLevel.includes('high')) {
        color = 'rgba(255, 0, 0, 0.3)'; borderColor = 'red'; lineDash = [5,3];
      } else if (riskLevel.includes('中') || riskLevel.includes('medium')) {
        color = 'rgba(255, 165, 0, 0.3)'; borderColor = 'orange';
      } else if (riskLevel.includes('低') || riskLevel.includes('low')) {
        color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
      }
    } else if (detection.category) {
      const category = detection.category.toLowerCase();
      if (category.includes('人') || category.includes('person')) {
        color = 'rgba(0, 0, 255, 0.3)'; borderColor = 'blue';
      } else if (category.includes('车') || category.includes('vehicle')) {
        color = 'rgba(0, 255, 0, 0.3)'; borderColor = 'green';
      } else if (category.includes('机械') || category.includes('设备') || category.includes('machine')) {
        color = 'rgba(255, 255, 0, 0.3)'; borderColor = 'yellow';
      }
    }
    
    // Draw rectangle
    ctx.strokeStyle = borderColor;
    ctx.fillStyle = color;
    ctx.lineWidth = 3; // Thicker lines for visibility
    ctx.setLineDash(lineDash);
    ctx.beginPath();
    ctx.rect(drawX1, drawY1, boxWidth, boxHeight);
    ctx.stroke();
    ctx.fill();
    ctx.setLineDash([]);
    
    // Draw label with larger font
    const label = detection.label || detection.event || detection.category || '未知';
    ctx.fillStyle = borderColor;
    ctx.font = 'bold 16px Arial'; // Larger font
    const textMetrics = ctx.measureText(label);
    ctx.fillRect(drawX1, drawY1 - 20, textMetrics.width + 6, 20);
    ctx.fillStyle = 'white';
    ctx.fillText(label, drawX1 + 3, drawY1 - 5);
    
    // Add confidence if available
    if (detection.confidence && typeof detection.confidence === 'number') {
      const confidenceText = `${Math.round(detection.confidence * 100)}%`;
      ctx.font = '14px Arial';
      ctx.fillStyle = 'white';
      ctx.fillText(confidenceText, drawX1 + 3, drawY1 - 25);
    }
  });
};

// Display description text on video with large font
const displayDescriptionOnVideo = (description: string) => {
  if (!detectionCanvasElement.value) return;
  
  const canvas = detectionCanvasElement.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Create a semi-transparent overlay for text
  ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
  ctx.fillRect(0, canvas.height - 150, canvas.width, 150);
  
  // Display text with larger font
  ctx.font = 'bold 18px Arial';
  ctx.fillStyle = 'white';
  
  // Truncate and format description to fit
  const maxLength = 150;
  let displayText = description.length > maxLength ? 
    description.substring(0, maxLength) + '...' : description;
  
  // Remove markdown formatting
  displayText = displayText.replace(/[*#_]/g, '');
  
  // Wrap text
  const words = displayText.split(' ');
  let line = '';
  let y = canvas.height - 120;
  
  words.forEach(word => {
    const testLine = line + word + ' ';
    const metrics = ctx.measureText(testLine);
    
    if (metrics.width > canvas.width - 40) {
      ctx.fillText(line, 20, y);
      line = word + ' ';
      y += 25;
    } else {
      line = testLine;
    }
  });
  
  ctx.fillText(line, 20, y);
};

// 添加一个函数将安全分析显示在右侧面板中
const showSafetyInPanel = (log: string) => {
  try {
    const safetyAnalysis = getSafetyAnalysis(log);
    if (safetyAnalysis) {
      showSafetyRecommendation(safetyAnalysis);
      
      // 添加一个表示成功的提示
      const apiLogItem = document.querySelector('.api-log-item');
      if (apiLogItem) {
        const successIndicator = document.createElement('div');
        successIndicator.className = 'push-success-indicator';
        successIndicator.textContent = '✓ 已推送到右侧面板';
        apiLogItem.appendChild(successIndicator);
        
        // 2秒后淡出
        setTimeout(() => {
          if (successIndicator && successIndicator.parentNode) {
            successIndicator.style.opacity = '0';
            setTimeout(() => {
              if (successIndicator.parentNode) {
                successIndicator.parentNode.removeChild(successIndicator);
              }
            }, 500);
          }
        }, 2000);
      }
    }
  } catch (e) {
    console.error('显示安全分析失败:', e);
  }
};

// 注意：安全建议提取函数现在已经移至上方的增强实现中

// Add this function after toggleDelayedPlayback
const toggleYoloDetection = () => {
  useYoloDetection.value = !useYoloDetection.value;
  
  if (useYoloDetection.value) {
    console.log('启用YOLO11x-seg检测，用于视频可视化');
    addApiLog(`INFO:qwen-vl-api:已启用YOLO检测，使用yolo11n-seg.pt模型进行实时视频可视化`);
    
    // Clear any existing non-YOLO detection boxes from the canvas
    clearCanvas(); 

    // If video is already playing, start/restart the YOLO processing loop.
    // If not playing, handlePlay will be called when user presses play.
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      handlePlay(); // This will initialize WebSocket if needed and start the capture loop
    } else {
      // If video is paused, ensure WebSocket is ready for when it starts
      if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
        initializeWebSocket();
      }
    }
  } else {
    console.log('禁用YOLO检测');
    addApiLog(`INFO:qwen-vl-api:已禁用YOLO检测`);
    
    // Close WebSocket connection if it's open
    if (wsConnection) {
      if (wsConnection.readyState === WebSocket.OPEN || wsConnection.readyState === WebSocket.CONNECTING) {
        wsConnection.close();
      }
      wsConnection = null;
    }
    
    // Clear the canvas where YOLO stream was displayed
    clearCanvas();
    
    // If standard detection was running via interval, it might need to be restarted or handled appropriately
    // For now, handlePlay will manage the standard detection interval if its conditions are met when video plays.
    // 清除Canvas上的检测框
    if (detectionCanvasElement.value) {
      const ctx = detectionCanvasElement.value.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, detectionCanvasElement.value.width, detectionCanvasElement.value.height);
      }
    }
    
    // 停止帧处理间隔
    if (frameProcessingInterval) {
      clearInterval(frameProcessingInterval);
      frameProcessingInterval = null;
    }
  }
};

// Sample Qwen API results demo function
const loadSampleQwenResults = () => {
  // Sample data from the example in the user query
  const sampleJson = `{
    "detections": [
      {
        "category": "其他 (boat)",
        "event_description": "图像中未明显显示船只，但系统检测到可能的船体结构。",
        "risk_level": "high",
        "confidence_score": 0.12,
        "bbox_2d": [0, 0, 1316, 728]
      }
    ],
    "description": "该场景展示了一个夜间施工场地，具体为一个大型钢结构框架下的施工现场。场地内有多个照明灯源，提供了必要的照明。地面上铺设了防护材料，周围设有护栏和梯子，用于人员安全通行。背景中可见一些机械设备和建筑材料，整体环境显得有序但复杂。尽管标注了'其他 (boat)'，但从图像内容来看，并未直接显示船只的存在，可能是误检或与实际场景不符的检测结果。",
    "high_risk_events": [
      {
        "description": "系统误检或不明物体（标记为'其他 (boat)'）的存在，可能导致分析误导。",
        "mitigation": "建议复核检测算法，确保其在类似复杂场景中的准确性和可靠性。同时，现场管理人员应定期检查，确认无潜在危险物存在。"
      }
    ],
    "low_risk_events": [
      {
        "description": "夜间施工照明充足，但需注意灯光对工人视力的长期影响及能源消耗。",
        "mitigation": "采用节能且护眼的照明设备，合理规划照明布局，减少光污染和能源浪费。"
      },
      {
        "description": "施工现场地面铺设防护材料，有助于防滑和保护地面，但仍需注意材料的稳固性。",
        "mitigation": "定期检查防护材料的状态，确保其平整、无破损，及时更换损坏部分。"
      }
    ]
  }`;
  
  try {
    qwenResults.value = JSON.parse(sampleJson);
    console.log('加载初始Qwen API结果');
    lastUpdated.value = new Date();
    
    // 添加到API日志中展示
    addApiLog(`INFO:yolo_video_processor:Qwen API返回成功`);
    addApiLog(`INFO:yolo_video_processor:Qwen API分析结果: ${sampleJson}`);
    
    // 模拟10秒后的实时更新
    setTimeout(() => {
      console.log('模拟10秒后的Qwen API更新');
      
      // 更新后的结果数据
      const updatedJson = `{
        "detections": [
          {
            "category": "人员",
            "event_description": "作业姿势不规范",
            "risk_level": "medium",
            "confidence_score": 0.85,
            "bbox_2d": [150, 60, 210, 180]
          },
          {
            "category": "机械",
            "event_description": "起重机吊臂半径内有人员",
            "risk_level": "high",
            "confidence_score": 0.92,
            "bbox_2d": [320, 180, 450, 260]
          }
        ],
        "description": "该场景展示了一个夜间施工场地，具体为一个大型钢结构框架下的施工现场。场地内有多个照明灯源，提供了必要的照明。地面上铺设了防护材料，周围设有护栏和梯子，用于人员安全通行。背景中可见一些机械设备，但场地内部有人员在起重机吊臂半径内活动，存在重大安全隐患。",
        "high_risk_events": [
          {
            "description": "起重机工作半径内有人员活动",
            "mitigation": "立即将人员撤离危险区域，设置警戒线并安排专人监督。"
          }
        ],
        "low_risk_events": [
          {
            "description": "工人作业姿势不规范",
            "mitigation": "对工人进行正确作业姿势培训，避免发生扭伤等职业伤害。"
          }
        ]
      }`;
      
      // 先通知API返回成功
      addApiLog(`INFO:yolo_video_processor:Qwen API返回成功`);
      
      // 然后添加分析结果，触发UI更新
      setTimeout(() => {
        addApiLog(`INFO:yolo_video_processor:Qwen API分析结果: ${updatedJson}`);
      }, 500);
    }, 10000);
    
  } catch (error) {
    console.error('解析样例JSON失败:', error);
  }
};

// Add state variables for the parallel processes
const yoloProcessActive = ref(true);  // Controls the YOLO visualization process
const apiRequestActive = ref(true);   // Controls the continuous API request process

// Add variables to manage the API request intervals
let apiRequestInterval: number | null = null;
const apiRequestDelay = 3000; // 3 seconds between API requests for scene analysis

// Update the captureAndProcessFrame function to use the dedicated YOLO WebSocket
const captureAndProcessFrame = () => {
  if (!useYoloDetection.value || !videoPlayerElement.value || videoPlayerElement.value.paused || videoPlayerElement.value.readyState < videoPlayerElement.value.HAVE_METADATA) {
    // If YOLO is not active, video is paused/not ready, or no player, stop the loop or don't start.
    return;
  }

  // Ensure WebSocket is connected and ready
  if (!wsYoloConnection || wsYoloConnection.readyState !== WebSocket.OPEN) {
    console.log('YOLO WebSocket not ready for sending frame, attempting to reconnect or waiting...');
    return;
  }

  // Only process frames for YOLO visualization if the process is active
  if (!yoloProcessActive.value) {
    // If the YOLO process is paused but we want to continue the loop
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      requestAnimationFrame(captureAndProcessFrame);
    }
    return;
  }

  // Use a temporary canvas to draw the current video frame for sending
  const tempCanvas = document.createElement('canvas');
  tempCanvas.width = videoPlayerElement.value.videoWidth;
  tempCanvas.height = videoPlayerElement.value.videoHeight;
  const ctx = tempCanvas.getContext('2d');

  if (!ctx || tempCanvas.width === 0 || tempCanvas.height === 0) {
    console.error('Failed to get temporary canvas context or video dimensions are zero.');
    // Retry next frame
    if (useYoloDetection.value && videoPlayerElement.value && !videoPlayerElement.value.paused) {
        requestAnimationFrame(captureAndProcessFrame);
    }
    return;
  }

  try {
    ctx.drawImage(videoPlayerElement.value, 0, 0, tempCanvas.width, tempCanvas.height);
    const frameDataUrl = tempCanvas.toDataURL('image/jpeg', 0.7); // Use 0.7 for quality/performance balance

    frameCounter++;
    const frameId = frameCounter; // Or a more unique ID if needed across sessions
    const timestamp = Date.now();

    // Send frame data to YOLO WebSocket server
    wsYoloConnection.send(JSON.stringify({
      frame: frameDataUrl.split(',')[1], // Send only Base64 part
      frameId: frameId,
      timestamp: timestamp,
      config: {
        segmentation: true, 
        confidence: 0.25,
        visualize: true // Request server to send back visualized frame
      }
    }));
  } catch (error) {
    console.error('Error capturing or sending video frame for YOLO visualization:', error);
    // Retry next frame even on error
    if (useYoloDetection.value && videoPlayerElement.value && !videoPlayerElement.value.paused) {
        requestAnimationFrame(captureAndProcessFrame);
    }
  }
};

// Add a new function to handle the API request process separately using the dedicated API WebSocket
const requestApiAnalysis = () => {
  if (!useYoloDetection.value || !videoPlayerElement.value || videoPlayerElement.value.paused) {
    return;
  }

  // Ensure API WebSocket is connected and ready
  if (!wsApiConnection || wsApiConnection.readyState !== WebSocket.OPEN) {
    console.log('API WebSocket not ready for API request, skipping this cycle');
    return;
  }

  // Only send API requests if the process is active
  if (!apiRequestActive.value) {
    return;
  }

  try {
    // Capture the current frame for API analysis
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = videoPlayerElement.value.videoWidth;
    tempCanvas.height = videoPlayerElement.value.videoHeight;
    const ctx = tempCanvas.getContext('2d');

    if (!ctx || tempCanvas.width === 0 || tempCanvas.height === 0) {
      console.error('Failed to get canvas context for API request');
      return;
    }

    ctx.drawImage(videoPlayerElement.value, 0, 0, tempCanvas.width, tempCanvas.height);
    const frameDataUrl = tempCanvas.toDataURL('image/jpeg', 0.8); // Higher quality for API analysis

    // Send frame data to API WebSocket server
    console.log('Sending frame for API analysis');
    wsApiConnection.send(JSON.stringify({
      frame: frameDataUrl.split(',')[1],
      frameId: `api-${Date.now()}`,
      timestamp: Date.now(),
      prompt: generatePrompt(),
      config: {
        requestApi: true,
        skipVisualization: true
      }
    }));

    addApiLog(`INFO:qwen-vl-api:发送场景分析请求`);
  } catch (error) {
    console.error('Error capturing or sending frame for API analysis:', error);
  }
};

// Initialize both WebSocket connections
const initializeWebSockets = () => {
  initializeYoloWebSocket();
  initializeApiWebSocket();
};

// Initialize WebSocket for YOLO visualization
const initializeYoloWebSocket = () => {
  if (wsYoloConnection && wsYoloConnection.readyState === WebSocket.OPEN) {
    console.log('YOLO WebSocket已连接，无需重新初始化');
    return;
  }
  
  if (wsYoloConnection && wsYoloConnection.readyState === WebSocket.CONNECTING) {
    console.log('YOLO WebSocket正在连接中，请稍候...');
    return;
  }
  
  console.log('创建YOLO WebSocket连接 - 连接到YOLO图像处理后端');
  addApiLog(`INFO:qwen-vl-api:正在连接YOLO图像处理后端: ${YOLO_BACKEND_URL}`);
  
  try {
    // 创建新的WebSocket连接到YOLO后端
    wsYoloConnection = new WebSocket(YOLO_BACKEND_URL);

    wsYoloConnection.onopen = () => {
      console.log('YOLO WebSocket连接已建立');
      addApiLog(`INFO:qwen-vl-api:已连接到YOLO图像处理后端`);
      
      // 发送模型配置信息
      wsYoloConnection.send(JSON.stringify({
        action: 'configure',
        model: 'yolo11n-seg.pt',
        config: {
          segmentation: true,
          confidence: 0.25,
          visualize: true,
          img_size: 640
        }
      }));
      
      // 开始YOLO处理
      if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
        captureAndProcessFrame();
      }
    };
    
    wsYoloConnection.onerror = (error) => {
      console.error('YOLO WebSocket错误:', error);
      addApiLog(`ERROR:qwen-vl-api:YOLO图像处理后端连接失败`);
    };
    
    wsYoloConnection.onclose = () => {
      console.log('YOLO WebSocket连接已关闭');
      addApiLog(`INFO:qwen-vl-api:YOLO图像处理后端连接已关闭`);
      wsYoloConnection = null;
    };
    
    wsYoloConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.error) {
          console.error('YOLO服务器返回错误:', data.error);
          return;
        }
        
        // 处理YOLO检测结果
        if (data.processed_frame) {
          // 确保处理后的帧数据是完整的Data URL
          let processedFrameDataUrl = data.processed_frame;
          if (!processedFrameDataUrl.startsWith('data:image')) {
            processedFrameDataUrl = 'data:image/jpeg;base64,' + processedFrameDataUrl;
          }
          
          // 在canvas上显示处理后的帧
          if (detectionCanvasElement.value) {
            const processedImage = new Image();
            processedImage.onload = () => {
              const ctx = detectionCanvasElement.value!.getContext('2d');
              if (ctx) {
                ctx.clearRect(0, 0, detectionCanvasElement.value!.width, detectionCanvasElement.value!.height);
                ctx.drawImage(
                  processedImage,
                  0, 0,
                  detectionCanvasElement.value!.width,
                  detectionCanvasElement.value!.height
                );
              }
            };
            processedImage.src = processedFrameDataUrl;
          }
          
          // 更新检测结果
          if (data.objects && data.objects.length > 0) {
            latestDetections.value = data.objects;
          }
          
          // 继续处理下一帧
          if (videoPlayerElement.value && !videoPlayerElement.value.paused && useYoloDetection.value) {
            requestAnimationFrame(captureAndProcessFrame);
          }
        }
      } catch (error) {
        console.error('处理YOLO WebSocket消息时出错:', error);
      }
    };
  } catch (error) {
    console.error('连接到YOLO后端时出错:', error);
    addApiLog(`ERROR:qwen-vl-api:连接到YOLO图像处理后端失败: ${error.message}`);
      // ... existing code ...
    };
    
    // ... existing code for wsYoloConnection ...
  } catch (error) {
    console.error('连接到YOLO后端时出错:', error);
    addApiLog(`ERROR:qwen-vl-api:连接到YOLO图像处理后端失败: ${error.message}`);
  }
};

// Initialize WebSocket for API requests
const initializeApiWebSocket = () => {
  if (wsApiConnection && wsApiConnection.readyState === WebSocket.OPEN) {
    console.log('API WebSocket已连接，无需重新初始化');
    return;
  }
  
  if (wsApiConnection && wsApiConnection.readyState === WebSocket.CONNECTING) {
    console.log('API WebSocket正在连接中，请稍候...');
    return;
  }
  
  console.log('创建API WebSocket连接 - 连接到API分析后端');
  addApiLog(`INFO:qwen-vl-api:正在连接API分析后端: ${API_BACKEND_URL}`);
  
  try {
    // 创建新的WebSocket连接到API后端
    wsApiConnection = new WebSocket(API_BACKEND_URL);

    wsApiConnection.onopen = () => {
      console.log('API WebSocket连接已建立');
      addApiLog(`INFO:qwen-vl-api:已连接到API分析后端`);
      
      // 发送配置信息
      wsApiConnection.send(JSON.stringify({
        action: 'configure',
        config: {
          model: 'qwen2.5-vl-7b-instruct',
          requestApi: true
        }
      }));
      
      // ... existing code ...
    };
    
    // ... existing code for wsApiConnection ...
  } catch (error) {
    console.error('连接到API后端时出错:', error);
    addApiLog(`ERROR:qwen-vl-api:连接到API分析后端失败: ${error.message}`);
    
    // 显示失败消息并尝试备用方案
    addApiLog(`INFO:qwen-vl-api:尝试使用HTTP API作为备用方案`);
    
    // 设置定时使用HTTP API作为备用
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      apiRequestActive.value = true;
      
      // 使用HTTP定时请求代替WebSocket
      if (apiRequestInterval) {
        clearInterval(apiRequestInterval);
      }
      
      apiRequestInterval = window.setInterval(() => {
        if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
          // 使用HTTP API作为备用
          const frameDataUrl = captureFrame();
          if (frameDataUrl) {
            isLoading.value = true;
            performStandardDetection(frameDataUrl)
              .then(results => {
                if (results) {
                  qwenResults.value = {
                    detections: results.detections || results.objects || [],
                    description: results.description || '场景分析完成',
                    high_risk_events: results.high_risk_events || [],
                    low_risk_events: results.low_risk_events || []
                  };
                  lastUpdated.value = new Date();
                  addApiLog(`INFO:qwen-vl-api:HTTP API请求成功 (备用方式)`);
                }
              })
              .catch(err => {
                console.error('HTTP API备用请求失败:', err);
                addApiLog(`ERROR:qwen-vl-api:HTTP API备用请求失败: ${err.message}`);
              })
              .finally(() => {
                isLoading.value = false;
              });
          }
        }
      }, apiRequestDelay);
    }
  }
};

// ... existing code ...

// 添加一个函数用于检查后端服务是否可用
const checkBackendAvailability = async () => {
  try {
    // 检查YOLO后端
    const yoloResponse = await fetch('http://localhost:8000/health');
    const yoloAvailable = yoloResponse.ok;
    
    // 检查API后端
    const apiResponse = await fetch('http://localhost:8001/health');
    const apiAvailable = apiResponse.ok;
    
    // 显示服务状态
    console.log(`YOLO后端状态: ${yoloAvailable ? '可用' : '不可用'}`);
    console.log(`API后端状态: ${apiAvailable ? '可用' : '不可用'}`);
    
    // 如果某个后端不可用，尝试使用备用地址
    if (!yoloAvailable) {
      addApiLog(`WARN:qwen-vl-api:YOLO图像处理后端不可用，尝试使用备用地址`);
      // 这里可以尝试备用YOLO服务地址
    }
    
    if (!apiAvailable) {
      addApiLog(`WARN:qwen-vl-api:API分析后端不可用，将使用HTTP API作为备用`);
      // 这里将使用HTTP API作为备用
    }
    
    return { yoloAvailable, apiAvailable };
  } catch (error) {
    console.error('检查后端可用性时出错:', error);
    return { yoloAvailable: false, apiAvailable: false };
  }
};

// 修改初始化逻辑，考虑后端可用性
const initializeBackends = async () => {
  // 检查后端可用性
  const { yoloAvailable, apiAvailable } = await checkBackendAvailability();
  
  // 初始化可用的后端连接
  if (yoloAvailable) {
    initializeYoloWebSocket();
  } else {
    addApiLog(`ERROR:qwen-vl-api:YOLO图像处理后端不可用，将无法显示实时检测结果`);
  }
  
  if (apiAvailable) {
    initializeApiWebSocket();
  } else {
    addApiLog(`WARN:qwen-vl-api:API分析后端不可用，尝试使用HTTP API作为备用`);
    // 设置备用的HTTP API请求
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      setupHttpApiBackup();
    }
  }
};

// 设置HTTP API备用机制
const setupHttpApiBackup = () => {
  apiRequestActive.value = true;
  
  // 使用HTTP定时请求代替WebSocket
  if (apiRequestInterval) {
    clearInterval(apiRequestInterval);
  }
  
  apiRequestInterval = window.setInterval(() => {
    if (videoPlayerElement.value && !videoPlayerElement.value.paused) {
      // 使用HTTP API作为备用
      const frameDataUrl = captureFrame();
      if (frameDataUrl) {
        performStandardDetection(frameDataUrl)
          .then(results => {
            if (results) {
              qwenResults.value = {
                detections: results.detections || results.objects || [],
                description: results.description || '场景分析完成',
                high_risk_events: results.high_risk_events || [],
                low_risk_events: results.low_risk_events || []
              };
              lastUpdated.value = new Date();
              addApiLog(`INFO:qwen-vl-api:HTTP API请求成功 (备用方式)`);
            }
          })
          .catch(err => {
            console.error('HTTP API备用请求失败:', err);
          });
      }
    }
  }, apiRequestDelay);
};

// 更新初始化WebSockets函数以考虑后端状态
const initializeWebSockets = () => {
  initializeBackends();
};

// Update initializeWebSocket function to initialize both connections
const initializeWebSocket = () => {
  initializeWebSockets();
};

// ... existing code ...

// 创建两个独立的WebSocket连接变量
let wsYoloConnection: WebSocket | null = null;  // 专用于YOLO图像处理
let wsApiConnection: WebSocket | null = null;   // 专用于API请求

// 定义后端服务地址
const YOLO_BACKEND_URL = 'ws://localhost:8000/ws/yolo-video-process'; // YOLO图像处理服务器
const API_BACKEND_URL = 'ws://localhost:8001/ws/api-analysis';        // API请求服务器
</script>

<style scoped>
.online-monitor-page {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 10px;
  min-height: calc(100vh - 70px); /* Adjust based on actual navbar height if known, minus padding */
  padding: 10px;
  background-color: var(--bg-darker, #000c17);
  overflow-y: auto; /* 允许页面滚动 */
}

.left-sidebar,
.video-container,
.result-panel {
  background-color: var(--card-bg, #001f3d);
  border-radius: var(--radius-md, 8px);
  overflow: auto; /* Changed to auto for scrollability */
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color, #003a8c);
  color: var(--text-primary, #fff);
}

.left-sidebar p,
.result-panel p {
  padding: 15px;
  text-align: center;
}

.video-container {
  padding: 0; /* card-container will handle internal padding */
  position: sticky;
  top: 10px; /* 与页面顶部的距离，与页面的padding相同 */
  z-index: 10; /* 确保视频容器在其他元素上方 */
  align-self: flex-start; /* 防止容器被拉伸 */
  height: fit-content; /* 高度适应内容 */
  max-height: calc(100vh - 20px); /* 确保不超过视口高度 */
  overflow-y: auto; /* 如果内容过多，允许滚动 */
}

.card-container {
  width: 100%;
  height: auto; /* 高度适应内容 */
  display: flex;
  flex-direction: column;
  background: var(--card-bg, #001f3d);
}

.card-header,
.card-footer {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color, #003a8c);
  background-color: var(--sidebar-header-bg, #002140);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-footer {
  border-top: 1px solid var(--border-color, #003a8c);
  border-bottom: none;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.card-body {
  flex-grow: 0; /* 不要自动扩展 */
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: #000; /* Black background for video area */
  overflow: hidden; /* Clip contents */
  min-height: 360px; /* 设置最小高度，与视频高度一致 */
}

.video-preview-wrapper {
  position: relative;
  width: 100%;
  height: auto; /* 高度适应内容 */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* 防止内容溢出 */
}

.video-preview-wrapper video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius-sm, 4px);
  display: block; /* 防止底部出现间隙 */
  z-index: 1; /* 确保视频在底层 */
}

.video-preview-wrapper .detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  /* Dimensions will be set by JS to match video */
  pointer-events: none; /* Canvas doesn't block video controls */
  z-index: 2; /* 确保canvas在视频上方 */
}

.detection-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.image-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.detect-button {
  padding: 10px 15px;
  background-color: var(--primary-color, #1890ff);
  color: white;
  border: none;
  border-radius: var(--radius-sm, 4px);
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  flex-grow: 1;
}

.detect-button:hover {
  background-color: var(--primary-hover, #40a9ff);
}

.detect-button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.detect-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message {
  color: var(--danger-color, red);
  margin-top: 10px;
  font-size: 13px;
}

.toggle-button {
  padding: 8px 12px;
  border: 1px solid var(--border-light, #1d39c4);
  background-color: transparent;
  color: var(--primary-color, #1890ff);
  border-radius: var(--radius-sm, 4px);
  cursor: pointer;
  margin-right: 8px;
}

.toggle-button:last-child {
  margin-right: 0;
}

.toggle-button:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

/* Placeholder styles to give an idea of structure, real styles from App.vue would be more detailed */
.nav-section-placeholder,
.detection-options-container-placeholder,
.model-selector-placeholder,
.risk-analysis-header-placeholder,
.risk-events-container-placeholder,
.object-detection-panel-placeholder,
.stats-panel-horizontal-placeholder {
  border: 1px dashed var(--border-color, #003a8c);
  padding: 10px;
  margin: 10px;
  text-align: center;
  color: var(--text-secondary, #aaa);
  min-height: 50px;
}

/* Styles for sidebar (can be copied or adapted from App.vue if more detail is needed) */
.left-sidebar {
  padding: 10px;
  overflow-y: auto;
}
.nav-section { margin-bottom: 15px; }
.nav-button {
  display: flex; align-items: center; gap: 8px;
  background: var(--category-bg, #002140); border: 1px solid var(--border-light, #1d39c4);
  color: var(--text-primary, #fff); padding: 8px 12px; border-radius: var(--radius-sm, 4px);
  cursor: pointer; transition: background-color 0.2s; width: 100%; text-align: left; margin-bottom: 5px;
}
.nav-button:hover { background-color: var(--category-hover, #003a8c); }
.nav-button.active { background-color: var(--primary-color, #1890ff); border-color: var(--primary-color, #1890ff); }
.nav-icon { width: 18px; height: 18px; /* Add icons via CSS background if needed */ }

.detection-options-container .sidebar-header {
  color: var(--primary-color, #1890ff); font-size: 15px; margin-bottom: 10px;
  padding-bottom: 5px; border-bottom: 1px solid var(--border-color, #003a8c);
}
.detection-category { background: rgba(0,0,0,0.1); border-radius: var(--radius-sm, 4px); margin-bottom: 8px; }
.category-header {
  display: flex; justify-content: space-between; align-items: center;
  padding: 8px 10px; cursor: pointer; background: rgba(255,255,255,0.05);
}
.category-icon-wrapper { display: flex; align-items: center; gap: 8px; }
.category-label { font-size: 14px; }
.category-icon { width: 18px; height: 18px; /* Add icons */ }
.expand-icon { font-size: 12px; }
.category-options { padding: 10px; background: rgba(0,0,0,0.2); }
.option-item { margin-bottom: 5px; }
.custom-checkbox { display: flex; align-items: center; cursor: pointer; }
.custom-checkbox input[type="checkbox"] { margin-right: 8px; }
.option-text { font-size: 13px; }

/* Remove placeholder styles if they were general */
.nav-section-placeholder,
.detection-options-container-placeholder {
  display: none; /* Hide placeholders now that real content is added */
}

.result-panel {
  padding: 0; /* Let internal elements handle padding */
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  max-height: 100%; /* 确保面板不超过容器高度 */
  position: sticky; /* 添加固定定位 */
  top: 10px; /* 与视频容器相同的顶部距离 */
  align-self: flex-start; /* 防止容器被拉伸 */
  height: fit-content; /* 高度适应内容 */
  max-height: calc(100vh - 20px); /* 确保不超过视口高度 */
}

.result-panel-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.risk-analysis-header {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color, #003a8c);
  background-color: var(--sidebar-header-bg, #002140);
  display: flex;
  align-items: center;
}
.risk-analysis-icon { width: 20px; height: 20px; margin-right: 8px; /* Add SVG/CSS icon */ }
.risk-analysis-title {
  font-size: 16px;
  font-weight: bold;
  flex: 1;
}

.risk-analysis-header.updating {
  background-color: rgba(24, 144, 255, 0.2);
  animation: headerPulse 2s ease-out;
}

@keyframes headerPulse {
  0% { background-color: rgba(24, 144, 255, 0.3); }
  100% { background-color: rgba(0, 33, 64, 1); }
}

.last-updated-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(24, 144, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  animation: pulse 1.5s;
}

@keyframes pulse {
  0% { background-color: rgba(24, 144, 255, 0.5); }
  100% { background-color: rgba(24, 144, 255, 0.2); }
}

.toggle-icon {
  font-size: 12px;
  color: #fff;
  margin-left: 5px;
}

.loading-results,
.error-display,
.no-detection-results {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary, #aaa);
}
.error-display p {
  color: var(--danger-color, red);
}

.risk-events-container, .safe-container {
  margin: 10px;
  padding: 15px;
  border-radius: var(--radius-md, 8px);
  border: 1px solid var(--border-color, #003a8c);
  background: rgba(0,0,0,0.1);
  overflow-y: visible; /* 允许内容溢出并滚动 */
  flex-shrink: 0; /* 防止容器被压缩 */
}
.risk-events-container.high-risk { border-left: 3px solid var(--danger-color, red); }
.risk-events-container.low-risk { border-left: 3px solid var(--warning-color, orange); }
.risk-summary { text-align: center; margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid rgba(255,255,255,0.1); }
.risk-count { font-size: 28px; font-weight: bold; }
.risk-count.danger { color: var(--danger-color, red); }
.risk-count.warning { color: var(--warning-color, orange); }
.risk-label { font-size: 14px; opacity: 0.8; }
.risk-categories { margin-top: 10px; }
.risk-category { margin-bottom: 10px; }
.risk-category-header {
  display: flex; align-items: center; padding: 8px;
  background: rgba(255,255,255,0.05); border-radius: 4px 4px 0 0;
}
.risk-category-header .category-icon { width: 16px; height: 16px; margin-right: 8px; filter: invert(1); /* Basic icon styling */ }
.risk-category-header .category-name { flex-grow: 1; font-size: 14px; font-weight: 500; }
.risk-category-header .category-count { font-size: 13px; padding: 2px 6px; border-radius: 10px; color: white; }
.risk-category-header .category-count.danger { background-color: var(--danger-color, red); }
.risk-category-header .category-count.warning { background-color: var(--warning-color, orange); }

.risk-events { padding-left: 10px; }
.risk-event-item { display: flex; justify-content: space-between; align-items: center; padding: 6px 0; font-size: 13px; border-bottom: 1px dashed rgba(255,255,255,0.1);}
.risk-event-item:last-child { border-bottom: none; }
.risk-event-name .event-count { font-size: 0.8em; opacity: 0.7; margin-left: 5px;}
.risk-level { font-size: 12px; padding: 2px 6px; border-radius: 4px; text-transform: uppercase; color: white; }
.risk-high { background-color: var(--danger-color, red); }
.risk-low { background-color: var(--warning-color, orange); }

.safe-container { text-align: center; }
.safe-icon, .no-risk-icon { font-size: 30px; margin-bottom: 8px; }
.safe-message, .no-risk-message { font-size: 18px; font-weight: 500; margin-bottom: 5px; }
.safe-description { font-size: 13px; opacity: 0.7; }

.stats-panel-horizontal { display: flex; justify-content: space-around; padding: 10px; border-top: 1px solid var(--border-color, #003a8c); margin-top:10px;}
.stats-panel-horizontal .stats-item { text-align: center; }
.stats-panel-horizontal .stats-icon { width: 24px; height: 24px; margin-bottom: 4px; filter: invert(1); /* Basic icon styling */}
.stats-panel-horizontal .stats-label { font-size: 12px; opacity: 0.8; }
.stats-panel-horizontal .stats-value { font-size: 16px; font-weight: bold; color: var(--primary-color, #1890ff); }

/* Remove placeholder styles */
.risk-analysis-header-placeholder,
.risk-events-container-placeholder,
.object-detection-panel-placeholder,
.stats-panel-horizontal-placeholder {
  display: none;
}

/* Icon placeholders for categories - replace with actual icons */
.person-icon { background-color: var(--primary-color, blue); width:1em; height:1em; display:inline-block; border-radius:50%;}
.machine-icon { background-color: var(--warning-color, orange);width:1em; height:1em; display:inline-block; border-radius:50%; }
.vehicle-icon { background-color: var(--success-color, green); width:1em; height:1em; display:inline-block; border-radius:50%;}
/* Add other category icons similarly for .material-icon, .regulation-icon, .environment-icon */

.model-selector { position: relative; min-width: 130px; }
.selected-model {
  cursor: pointer; padding: 10px; border: 1px solid var(--border-color, #003a8c);
  border-radius: var(--radius-sm, 4px); background: rgba(0,0,0,0.2);
  display: flex; align-items: center; justify-content: space-between; height: 100%;
  color: var(--text-primary, #fff);
}
.model-icon { width:16px; height:16px; margin-right:5px; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M12 3L1 9l11 6 9-4.91V17h2V9L12 3zm0 3.97l7 3.83L12 13.97l-9-5 9-2zm-1 9.93v7l9-4V13l-9 3.9z'/%3E%3C/svg%3E"); filter: invert(1); }

/* YOLO检测模式标记 */
.yolo-badge {
  display: inline-block;
  background-color: var(--success-color, #52c41a);
  color: white;
  font-size: 12px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  vertical-align: middle;
  animation: pulse 2s infinite;
}

.yolo-button {
  background-color: rgba(82, 196, 26, 0.15);
  border-color: var(--success-color, #52c41a);
  color: var(--success-color, #52c41a);
  position: relative;
}

.yolo-button:hover {
  background-color: rgba(82, 196, 26, 0.3);
}

.yolo-reload-button {
  background-color: rgba(24, 144, 255, 0.15);
  border-color: var(--primary-color, #1890ff);
  color: var(--primary-color, #1890ff);
}

.yolo-reload-button:hover {
  background-color: rgba(24, 144, 255, 0.3);
}

.toggle-button-hint {
  font-size: 10px;
  display: block;
  opacity: 0.7;
  margin-top: 2px;
}

.model-status {
  margin-left: 12px;
  font-size: 12px;
  color: #fff;
  opacity: 0.8;
}

.parallel-indicator {
  background-color: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 2px 6px;
  border-radius: 4px;
  animation: flash 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

@keyframes flash {
  0% { background-color: rgba(255, 215, 0, 0.2); }
  50% { background-color: rgba(255, 215, 0, 0.5); }
  100% { background-color: rgba(255, 215, 0, 0.2); }
}
.dropdown-icon { font-size: 10px; }
.model-dropdown {
  position: absolute; bottom: calc(100% + 5px); /* Open upwards */ left: 0; right: 0;
  background: var(--card-bg, #001f3d); border: 1px solid var(--border-color, #003a8c);
  border-bottom: none; border-radius: var(--radius-sm, 4px) var(--radius-sm, 4px) 0 0;
  z-index: 100; max-height: 150px; overflow-y: auto;
}
.model-option { padding: 8px 10px; cursor: pointer; border-bottom: 1px solid var(--border-color, #003a8c); color: var(--text-primary, #fff);}
.model-option:last-child { border-bottom: none; }
.model-option:hover { background-color: var(--category-hover, #003a8c); }
.model-option.active { background-color: var(--primary-color, #1890ff); }
.model-name { font-weight: bold; font-size: 13px; }
.model-description { font-size: 11px; opacity: 0.8; }

.detect-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9.5 9.5c0 .55.45 1 1 1h2c.55 0 1-.45 1-1V8h1c.55 0 1-.45 1-1s-.45-1-1-1h-1V5c0-.55-.45-1-1-1h-2c-.55 0-1 .45-1 1v2.5z'/%3E%3Cpath d='M20 12c0-2.54-1.19-4.81-3.04-6.27L16 0H8l-.95 5.73C5.19 7.19 4 9.45 4 12s1.19 4.81 3.05 6.27L8 24h8l.96-5.73C18.81 16.81 20 14.54 20 12zM12 2.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5zm0 19c-5.23 0-9.5-4.27-9.5-9.5S6.77 2.5 12 2.5s9.5 4.27 9.5 9.5-4.27 9.5-9.5 9.5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* Styles for placeholder in video footer */
.model-selector-placeholder {
  display: none; /* Remove if model selector is now implemented */
}

/* YS7 video styles */
.ys7-video {
  width: 100%;
  height: 100%;
  background-color: #000;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  border: none;
  display: block;
}

.ys7-loading-overlay,
.ys7-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.ys7-loading-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.ys7-error-overlay {
  background: rgba(245, 34, 45, 0.3);
}

.error-icon {
  width: 50px;
  height: 50px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23f5222d'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 15px;
}

.processing-indicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(0, 168, 255, 0.2);
  border-top: 3px solid #00a8ff;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.safety-analysis-container {
  margin: 10px;
  padding: 15px;
  border-radius: var(--radius-md, 8px);
  border: 1px solid var(--border-color, #003a8c);
  background: rgba(0,0,0,0.1);
  border-left: 3px solid var(--primary-color, #1890ff);
  flex-shrink: 0;
  max-height: 400px;
  overflow-y: auto;
  animation: fadeInUp 0.5s ease-out;
}

.safety-analysis-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.safety-analysis-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.safety-analysis-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-color, #1890ff);
}

.safety-analysis-content {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255,255,255,0.9);
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border-left: 2px solid var(--primary-color, #1890ff);
  white-space: pre-wrap;
}

.safety-analysis-content h1,
.safety-analysis-content h2,
.safety-analysis-content h3 {
  margin-top: 15px;
  margin-bottom: 8px;
  color: var(--primary-color, #1890ff);
}

.safety-analysis-content .safety-analysis-heading {
  color: var(--primary-color, #1890ff);
  font-size: 18px;
  font-weight: bold;
  padding: 8px 0;
  margin: 5px 0 15px 0;
  border-bottom: 1px solid var(--primary-color, #1890ff);
  text-align: center;
}

.safety-analysis-content ul {
  margin: 0;
  padding-left: 20px;
}

.safety-analysis-content li {
  margin-bottom: 8px;
  position: relative;
  padding-left: 5px;
}

.safety-analysis-content li:before {
  content: "•";
  color: var(--primary-color, #1890ff);
  position: absolute;
  left: -12px;
  font-weight: bold;
}

.safety-analysis-content strong {
  color: #fff;
  font-weight: bold;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 0 3px;
  border-radius: 3px;
}

.safety-analysis-content em {
  opacity: 0.8;
  font-style: italic;
}

.api-logs-container {
  margin: 10px;
  padding: 15px;
  border-radius: var(--radius-md, 8px);
  border: 1px solid var(--border-color, #003a8c);
  background: rgba(0,0,0,0.1);
  border-left: 3px solid var(--info-color, #1890ff);
  flex-shrink: 0;
}

.api-logs-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.api-logs-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.api-logs-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-color, #1890ff);
}

.api-logs-content {
  max-height: 300px;
  overflow-y: auto;
}

.api-log-item {
  padding: 12px;
  margin-bottom: 12px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

.api-log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
}

.api-log-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.api-log-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}

.api-log-status.success {
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
}

.api-log-details {
  padding: 5px 0;
}

.api-log-text {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: rgba(255, 255, 255, 0.85);
  font-family: monospace;
  max-height: 150px;
  overflow-y: auto;
}

.api-log-formatted {
  font-size: 13px;
}

.api-log-section {
  margin-bottom: 10px;
}

.api-log-section-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--primary-color, #1890ff);
}

.api-log-detection-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.api-log-detection-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  gap: 6px;
}

.detection-type {
  font-weight: 500;
}

.detection-type.high-risk {
  color: var(--danger-color, #ff4d4f);
}

.detection-type.medium-risk {
  color: var(--warning-color, #faad14);
}

.detection-type.low-risk {
  color: var(--success-color, #52c41a);
}

.detection-risk {
  font-size: 11px;
  padding: 1px 4px;
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.7);
}

.api-log-analysis {
  font-size: 12px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.85);
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border-left: 2px solid var(--primary-color, #1890ff);
  white-space: pre-wrap;
}

/* JSON Viewer Styles */
.api-log-json-viewer {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
  margin: 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.85);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  overflow: hidden;
}

.json-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(24, 144, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.json-viewer-type {
  font-weight: 600;
  color: #1890ff;
}

.json-viewer-actions {
  display: flex;
  gap: 8px;
}

.json-viewer-action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: background-color 0.2s;
}

.json-viewer-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.expand-icon, .collapse-icon {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
}

.json-viewer-content {
  padding: 12px;
  overflow-y: auto;
  max-height: 350px;
}

.json-tree {
  position: relative;
}

.json-tree-item {
  margin: 2px 0;
  position: relative;
}

.json-tree-key {
  padding: 2px 0;
  cursor: pointer;
  white-space: nowrap;
}

.json-tree-key:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.json-tree-toggle {
  display: inline-block;
  width: 12px;
  height: 12px;
  text-align: center;
  line-height: 12px;
  margin-right: 4px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 10px;
}

.json-key-name {
  color: #e6db74;
  margin-right: 4px;
}

.json-type {
  color: #66d9ef;
  margin-left: 4px;
}

.json-value {
  color: #a6e22e;
}

.json-string {
  color: #fd971f;
}

.json-number {
  color: #ae81ff;
}

.json-boolean {
  color: #f92672;
}

.json-null {
  color: #75715e;
}

.json-tree-children {
  margin-left: 16px;
  border-left: 1px dotted rgba(255, 255, 255, 0.2);
  padding-left: 8px;
}

/* Add styles for new functionality */
.api-log-action-btn {
  background: rgba(24, 144, 255, 0.2);
  color: var(--primary-color, #1890ff);
  border: 1px solid rgba(24, 144, 255, 0.5);
  border-radius: 4px;
  padding: 3px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-left: auto;
  transition: all 0.3s;
  display: block;
  width: fit-content;
  margin-top: 8px;
}

.api-log-action-btn:hover {
  background: rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.view-on-video-icon {
  font-size: 14px;
}

.api-log-text-large {
  font-size: 16px !important;
  line-height: 1.6;
}

.json-viewer-actions {
  display: flex;
  gap: 5px;
}

/* 高亮样式 */
.highlight-high-risk {
  background-color: rgba(255, 0, 0, 0.2);
  color: #ff4d4f;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-medium-risk {
  background-color: rgba(250, 173, 20, 0.2);
  color: #faad14;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-low-risk {
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-warning {
  background-color: rgba(250, 173, 20, 0.2);
  color: #faad14;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-important {
  color: #fff;
  text-decoration: underline;
  font-weight: bold;
}

/* API日志操作样式 */
.api-log-actions {
  display: flex;
  margin-top: 10px;
  justify-content: flex-end;
}

.recommendation-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231890ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.push-success-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  transition: opacity 0.5s;
  z-index: 5;
}

.yolo-toggle-button {
  background-color: rgba(93, 109, 126, 0.2);
  border-color: var(--primary-color, #1890ff);
  color: var(--primary-color, #1890ff);
  position: relative;
  font-weight: bold;
}

.yolo-toggle-button:hover {
  background-color: rgba(93, 109, 126, 0.4);
  color: #fff;
}

/* 增强安全分析样式 */
.safety-analysis-container {
  margin-top: 15px;
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid var(--border-color);
}

.safety-analysis-heading {
  color: #1890ff;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(24, 144, 255, 0.3);
}

.safety-section {
  color: #1890ff;
  border-left: 3px solid #1890ff;
  padding-left: 10px;
}

.hazard-section {
  color: #ff4d4f;
  border-left: 3px solid #ff4d4f;
  padding-left: 10px;
}

.recommendation-section {
  color: #52c41a;
  border-left: 3px solid #52c41a;
  padding-left: 10px;
}

.safety-hazard {
  color: #ff4d4f;
  font-weight: bold;
}

.safety-recommendation {
  color: #52c41a;
  font-weight: bold;
}

/* 重新定义高亮样式 */
.highlight-high-risk {
  background-color: rgba(255, 77, 79, 0.2);
  color: #ff4d4f;
  padding: 0 4px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-medium-risk {
  background-color: rgba(250, 173, 20, 0.2);
  color: #faad14;
  padding: 0 4px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-low-risk {
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  padding: 0 4px;
  border-radius: 3px;
  font-weight: bold;
}

.highlight-warning {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  padding: 0 4px;
  border-radius: 3px;
}

.highlight-important {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 0 4px;
  border-radius: 3px;
  font-weight: bold;
}

.safety-analysis-content {
  font-size: 14px;
  line-height: 1.6;
}

.safety-analysis-content ul, 
.safety-analysis-content ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.safety-analysis-content li {
  margin-bottom: 5px;
}

/* Platform-specific icon styles */
.road_structure-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM13.96 12.29l-2.75 3.54-1.96-2.36L6.5 17h11l-3.54-4.71z'/%3E%3C/path%3E%3C/svg%3E");
}

.vehicle_accident-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.85 6h10.29l1.08 3.11H5.77L6.85 6zM19 17H5v-5h14v5z'/%3E%3C/path%3E%3C/svg%3E");
}

/* Styles for the enhanced Qwen results panel */
.qwen-results-panel-main {
  margin-bottom: 20px;
  order: -1; /* Ensure it's first in flex layout */
  flex-grow: 1; /* Allow it to grow to take more space */
  max-height: unset; /* Remove height restriction */
  font-size: 16px; /* Increase font size */
  background: linear-gradient(135deg, rgba(0, 21, 41, 0.7), rgba(0, 58, 140, 0.4));
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: var(--radius-md, 8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  padding: 5px;
  position: relative;
  overflow: visible; /* Allow content to extend */
}

.qwen-results-panel-main::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 10%;
  right: 10%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.7), transparent);
}

/* Make description text larger and more visible */
.qwen-results-panel-main .description-box {
  font-size: 16px;
  line-height: 1.8;
  padding: 15px;
  background: rgba(0, 21, 41, 0.5);
  border-left: 3px solid var(--primary-color, #1890ff);
}

/* Enhance section headers */
.qwen-results-panel-main .section-header {
  background: linear-gradient(90deg, rgba(0, 58, 140, 0.8), rgba(0, 21, 41, 0.6));
  padding: 12px 15px;
}

.qwen-results-panel-main .section-title {
  font-size: 16px;
  font-weight: bold;
}
</style>
const fs = require('fs');

// Path to the file with encoding issues
const filePath = 'src/components/OnlineMonitor.vue';
const outputPath = 'src/components/OnlineMonitor.vue.fixed';

// Read the file with encoding issues
try {
  // Read the file content
  const data = fs.readFileSync(filePath, 'utf8');
  console.log('File read successfully');
  
  // Fix encoding issues - replace mojibake with proper Chinese characters
  let fixedContent = data;
  
  // Common mojibake replacements
  const replacements = {
    '锟斤拷': '安',
    '锟斤拷锟斤拷': '安全',
    '锟竭凤拷锟斤拷': '高风险',
    '锟酵凤拷锟斤拷': '低风险',
    '锟叫凤拷锟斤拷': '中风险',
    '锟斤拷员': '人员',
    '锟斤拷': '人',
    '锟斤拷锟斤拷': '车辆',
    '锟借备': '设备',
    '锟斤拷械': '机械'
  };
  
  // Apply replacements
  for (const [mojibake, chinese] of Object.entries(replacements)) {
    const regex = new RegExp(mojibake, 'g');
    fixedContent = fixedContent.replace(regex, chinese);
  }
  
  // Create a backup of the original file
  fs.writeFileSync(filePath + '.bak', data);
  console.log('Backup created at ' + filePath + '.bak');
  
  // Write fixed content to output file
  fs.writeFileSync(outputPath, fixedContent);
  console.log('Fixed file created at ' + outputPath);
  
} catch (err) {
  console.error('Error:', err);
} 
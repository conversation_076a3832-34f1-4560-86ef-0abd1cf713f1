# PowerShell script for parallel backend services
Write-Host "Installing dependencies..." -ForegroundColor Green
pip install -r requirements.txt

# Ensure upload directory exists
$uploadDir = "static/uploads"
if (-not (Test-Path $uploadDir)) {
    Write-Host "Creating upload directory: $uploadDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $uploadDir -Force | Out-Null
}

# Start both backend services
Write-Host "Starting Traffic Eyes dual backend services..." -ForegroundColor Cyan
Write-Host "1. Starting YOLO image processing backend (port 8000)..." -ForegroundColor Green
$yoloJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    python main.py
}

Write-Host "2. Starting API analysis backend (port 8001)..." -ForegroundColor Green
$apiJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    python api_server.py
}

# Display job status
Write-Host "Backend services starting..." -ForegroundColor Yellow
Start-Sleep -Seconds 2
Write-Host "YOLO backend status: $($yoloJob.State)" -ForegroundColor Cyan
Write-Host "API backend status: $($apiJob.State)" -ForegroundColor Cyan

# Wait for user to press a key to exit
Write-Host "`nBoth backend services are running. Press any key to stop services..." -ForegroundColor Magenta
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop jobs
Write-Host "Stopping backend services..." -ForegroundColor Yellow
Stop-Job -Job $yoloJob
Stop-Job -Job $apiJob
Remove-Job -Job $yoloJob
Remove-Job -Job $apiJob
Write-Host "Backend services stopped" -ForegroundColor Green 
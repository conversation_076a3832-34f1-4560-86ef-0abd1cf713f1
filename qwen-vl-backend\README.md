# Traffic Eyes

一个基于 Qwen 2.5 VL 模型的 AI 交通分析工具，能够智能分析交通图像，识别交通状况、车辆、行人、交通标志和潜在的交通问题。

## 功能特点

- **图像上传**：支持拖放和文件浏览两种方式上传交通图像
- **直观界面**：现代化的用户界面，提供实时图像预览
- **自定义提示**：允许用户输入自定义提示以获取针对性分析
- **实时分析**：快速处理图像并提供详细的交通分析结果
- **API 集成**：提供 RESTful API 接口，方便与其他应用集成

## 技术栈

### 后端
- **FastAPI**: 高性能的 Python Web 框架
- **Uvicorn**: ASGI 服务器
- **DashScope API**: 阿里云提供的 Qwen 模型 API 服务
- **Python-dotenv**: 环境变量管理
- **Pillow**: 图像处理库

### 前端
- **HTML/CSS/JavaScript**: 前端基础技术
- **Bootstrap 5**: UI 框架

### AI 模型
- **Qwen 2.5 VL 72B**: 通过 DashScope API 调用的多模态大语言模型

## 安装与设置

### 前提条件
- Python 3.8 或更高版本
- DashScope API 密钥 (可从阿里云获取)

### 安装步骤

1. 克隆仓库
   ```bash
   git clone https://github.com/yourusername/traffic-eyes.git
   cd traffic-eyes
   ```

2. 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

3. 创建 `.env` 文件并添加 API 密钥
   ```
   DASHSCOPE_API_KEY=your_api_key_here
   ```

### 运行应用

1. 启动 FastAPI 服务器
   ```bash
   python main.py
   ```

2. 在浏览器中访问
   ```
   http://localhost:8000
   ```

## API 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 返回主页 |
| `/health` | GET | 健康检查，返回 API 状态 |
| `/analyze/image` | POST | 分析上传的图像 |
| `/analyze/image_url` | POST | 分析图像 URL |
| `/conversation` | POST | 与模型进行对话 |

## 使用示例

### 分析交通图像

1. 打开应用主页
2. 拖放交通图像或点击"浏览文件"按钮选择图像
3. (可选) 在自定义提示框中输入特定的分析需求
4. 点击"分析图像"按钮
5. 等待分析完成，查看详细结果

## 项目结构

```
traffic-eyes/
├── main.py                # FastAPI 应用主文件
├── requirements.txt       # 项目依赖
├── .env                   # 环境变量 (需自行创建)
├── static/                # 静态文件
│   └── uploads/           # 上传的图像存储目录
└── templates/             # HTML 模板
    └── index.html         # 主页模板
```

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 致谢

- [Qwen 2.5 VL 模型](https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-qianwen-vl-2-5) - 提供强大的视觉语言能力
- [FastAPI](https://fastapi.tiangolo.com/) - 提供高性能的 API 框架
- [Bootstrap](https://getbootstrap.com/) - 提供现代化的 UI 组件 
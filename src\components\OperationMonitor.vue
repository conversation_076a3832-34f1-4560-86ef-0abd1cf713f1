<template>
  <div class="operation-monitor-page">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <div class="nav-section">
        <button
          v-for="category in riskCategories"
          :key="category.id"
          class="nav-button hover-lift"
          @click="selectCategory(category.id)"
          :class="{ active: selectedCategory === category.id }"
        >
          <span class="nav-icon" :class="category.id + '-icon'"></span>
          <span>{{ category.label }}</span>
        </button>
      </div>

      <div class="detection-options-container">
        <h3 class="sidebar-header">监测内容</h3>
        <div class="detection-options">
          <div
            v-for="(category, index) in filteredDetectionOptions"
            :key="category.id"
            class="detection-category slide-in-up"
            :style="{animationDelay: `${0.1 * index}s`}"
          >
            <div
              class="category-header"
              @click="toggleCategory(category.id)"
            >
              <div class="category-icon-wrapper">
                <span class="category-icon" :class="category.id + '-icon'"></span>
                <span class="category-label">{{ category.label }}</span>
              </div>
              <span class="expand-icon">{{ category.expanded ? '▼' : '▶' }}</span>
            </div>
            <div
              v-if="category.expanded"
              class="category-options"
            >
              <div
                v-for="option in category.options"
                :key="option.id"
                class="option-item"
              >
                <label class="option-label custom-checkbox">
                  <input
                    type="checkbox"
                    :checked="option.checked"
                    @change="toggleOption(category.id, option.id)"
                  />
                  <span class="option-text">{{ option.label }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频分析提示词设置 -->
      <div class="prompt-container">
        <h3 class="sidebar-header">分析配置</h3>
        <div class="prompt-editor">
          <label for="detection-prompt" class="prompt-label">分析提示词</label>
          <textarea
            id="detection-prompt"
            v-model="detectionPrompt"
            class="prompt-textarea"
            rows="6"
            placeholder="输入视频分析提示词..."
          ></textarea>
        </div>
      </div>
    </div>

    <!-- Middle - Video Feeds -->
    <div class="video-container">
      <div class="card-container">
        <div class="card-header">
          <span class="card-title">在线监控</span>
          <div class="card-tools">
            <button class="action-button hover-lift" @click="toggleAllMonitoring">
              <span class="button-icon" :class="monitoringActive ? 'stop-icon' : 'play-icon'"></span>
              {{ monitoringActive ? '停止监控' : '开始监控' }}
            </button>
            <div class="header-status" :class="monitoringActive ? 'online' : 'offline'">
              {{ monitoringActive ? '在线监测中' : '监测已停止' }}
            </div>
          </div>
        </div>
        <div class="card-body reduced-padding">
          <div class="video-grid">
            <div class="video-feed" v-for="(feed, index) in videoFeeds" :key="feed.id" :id="`feed-${feed.id}`">
              <div class="feed-header">
                <div class="feed-header-main">
                  <span class="feed-title">{{ feed.title }}</span>
                  <span class="feed-status" :class="feed.status">{{ feed.statusText }}</span>
                </div>
                <div class="feed-location-top">{{ feed.location }}</div>
              </div>
              <div class="feed-video">
                <!-- 萤石云视频播放器 - 使用iframe嵌入方式 -->
                <!-- 直接播放m3u8流 -->
                <video
                  v-if="feed.isYs7Feed && feed.isLive"
                  :src="getYs7IframeUrl(feed)"
                  class="video-element ys7-video"
                  controls
                  autoplay
                  muted
                  :id="`ys7-video-${feed.id}`"
                ></video>
                
                <!-- 视频源展示 -->
                <video
                  v-else-if="feed.videoUrl && typeof feed.videoUrl === 'string' && !feed.isLive && !feed.videoUrl.startsWith('ezopen://')"
                  :src="feed.videoUrl"
                  class="video-element"
                  controls
                  muted
                ></video>

                <!-- 静态图片展示 -->
                <img v-else-if="!feed.videoUrl || (typeof feed.videoUrl !== 'string' && !feed.isLive)" :src="feed.imageUrl" alt="Video feed" class="video-img" />

                <!-- 检测结果显示 -->
                <div class="detection-overlay" v-if="feed.detectionResults">
                  <!-- 可以在这里添加边界框显示 -->
                </div>

                <!-- 警告提示 -->
                <div class="feed-overlay" v-if="feed.alert">
                  <div class="alert-badge">{{ feed.alertText }}</div>
                </div>

                <!-- 视频加载中状态 -->
                <div class="processing-overlay" v-if="feed.isProcessing">
                  <div class="processing-indicator"></div>
                  <div class="processing-text">正在处理视频...</div>
                </div>

                <!-- 视频处理错误状态 -->
                <div class="error-overlay" v-if="feed.status === 'error'">
                  <div class="error-icon"></div>
                  <div class="error-message">{{ feed.alertText || '视频处理失败' }}</div>
                </div>

                <!-- 视频控制按钮 -->
                <div class="feed-controls">
                  <div class="feed-control-buttons">
                    <button class="control-button" @click="connectLiveCamera(feed.id)" title="启用摄像头">
                      <span class="button-icon camera-icon"></span>
                    </button>
                    <label class="control-button file-upload-label" title="上传视频文件">
                      <span class="button-icon upload-icon"></span>
                      <input type="file" accept="video/*" class="file-upload-input" @change="e => handleVideoUpload(feed.id, e)" />
                    </label>
                    <button
                      class="control-button"
                      @click="startVideoDetection(feed.id)"
                      :disabled="!feed.videoUrl || feed.isProcessing"
                      title="开始分析"
                    >
                      <span class="button-icon analyze-icon"></span>
                    </button>
                    <button class="control-button" @click="stopMonitoring(feed.id)" title="停止监控">
                      <span class="button-icon stop-icon"></span>
                    </button>
                  </div>
                </div>
              </div>
              <div class="feed-footer">
                <span class="feed-time">{{ currentTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right - Results Panel -->
    <div class="result-panel">
      <div class="risk-analysis-header">
        <div class="risk-analysis-icon"></div>
        <span class="risk-analysis-title">风险统计</span>
      </div>

      <!-- Statistics cards -->
      <div class="data-cards">
        <div class="data-card" v-for="(stat, index) in stats" :key="index">
          <div class="card-icon" :class="stat.icon"></div>
          <div class="card-content">
            <div class="card-title">{{ stat.title }}</div>
            <div class="card-value" :class="{'value-alert': stat.alert}">{{ stat.value }}</div>
          </div>
        </div>
      </div>

      <!-- Recent risk events -->
      <div class="risk-events-container high-risk" v-if="hasHighRiskEvents">
        <div class="risk-summary">
          <div class="risk-count danger">{{ getHighRiskCount() }}</div>
          <div class="risk-label">高风险事件</div>
        </div>

        <div class="recent-events">
          <div class="events-list">
            <div class="event-item" v-for="(event, index) in highRiskEvents" :key="index">
              <div class="event-time">{{ event.time }}</div>
              <div class="event-content">
                <div class="event-title">{{ event.title }}</div>
                <div class="event-desc">{{ event.description }}</div>
              </div>
              <div class="event-status danger">{{ event.levelText }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="risk-events-container low-risk" v-if="hasLowRiskEvents">
        <div class="risk-summary">
          <div class="risk-count warning">{{ getLowRiskCount() }}</div>
          <div class="risk-label">低风险事件</div>
        </div>

        <div class="recent-events">
          <div class="events-list">
            <div class="event-item" v-for="(event, index) in lowRiskEvents" :key="index">
              <div class="event-time">{{ event.time }}</div>
              <div class="event-content">
                <div class="event-title">{{ event.title }}</div>
                <div class="event-desc">{{ event.description }}</div>
              </div>
              <div class="event-status" :class="event.level">{{ event.levelText }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Safe container when no risk events -->
      <div class="safe-container" v-if="!hasHighRiskEvents && !hasLowRiskEvents">
        <div class="safe-icon">✓</div>
        <div class="safe-message">安全</div>
        <div class="safe-description">未检测到任何风险事件</div>
      </div>

      <!-- Statistical summary at the bottom -->
      <div class="stats-panel-horizontal">
        <div class="stats-item">
          <div class="stats-icon traffic-icon"></div>
          <div class="stats-info">
            <div class="stats-label">车流量</div>
            <div class="stats-value">{{ getCurrentTraffic() }}</div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-icon speed-icon"></div>
          <div class="stats-info">
            <div class="stats-label">平均车速</div>
            <div class="stats-value">{{ getAverageSpeed() }}</div>
          </div>
        </div>
        <div class="stats-item">
          <div class="stats-icon risk-icon"></div>
          <div class="stats-info">
            <div class="stats-label">风险指数</div>
            <div class="stats-value">{{ getRiskIndex() }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import axios from 'axios';
import EZUIKit from 'ezuikit-js';

// 定时器变量
let timeInterval: number | null = null;
let dataInterval: number | null = null;

// HLS播放器实例
let hlsPlayers = {};

// API endpoint configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

// 监控状态
const monitoringActive = ref(true);
const processingVideos = ref({});

// 监控视频数据
const videoFeeds = ref([
  {
    id: 'feed1',
    title: '高速公路监控点A',
    status: 'normal',
    statusText: '正常',
    location: '京津高速 K102+500',
    imageUrl: 'https://img.freepik.com/free-photo/empty-road-highway_1127-3054.jpg?w=360&t=st=1698213718~exp=1698214318~hmac=0ab35b01e5b29b3d6cca9c6ef455e3d8a895be3666f14f3e3c62962f0c9bd401',
    videoUrl: '1747124126409.mp4',
    isLive: false,
    isProcessing: false,
    processingId: null,
    alert: false,
    alertText: '',
    detectionResults: null
  },

  {
    id: 'feed3',
    title: '隧道监控点C',
    status: 'normal',
    statusText: '萤石云监控',
    location: '城市东西连接隧道 3号口',
    imageUrl: 'https://img.freepik.com/free-photo/highway-with-lights-night_1127-2179.jpg?w=360&t=st=1698213815~exp=1698214415~hmac=a219cf504da5bef142cce5e0e6011d8e4e894eaab0e02ece1c4e68a64d3b1f7b',
    videoUrl: 'ezopen://open.ys7.com/FX2683018/9.hd.live',
    isLive: true,
    isProcessing: false,
    processingId: null,
    alert: false,
    alertText: '',
    detectionResults: null,
    isYs7Feed: true  // 标记为萤石云摄像头
  },
  {
    id: 'feed4',
    title: '桥梁监控点D',
    status: 'alert',
    statusText: '警告',
    location: '跨江大桥 南向北',
    imageUrl: 'https://img.freepik.com/free-photo/bridge-with-illumination-night-city_1127-2088.jpg?w=360&t=st=1698213851~exp=1698214451~hmac=1e98c8a6d391dda0ba097a7a1a72b7ac29c1c1a152b64aa91f1fb02a232b33f4',
    videoUrl: null,
    isLive: false,
    isProcessing: false,
    processingId: null,
    alert: true,
    alertText: '拥堵风险',
    detectionResults: null
  }
]);

// 实时统计数据
const stats = ref([
  {
    title: '当前车流量',
    value: '1,246 辆/小时',
    icon: 'traffic-icon',
    alert: false
  },
  {
    title: '平均车速',
    value: '68 km/h',
    icon: 'speed-icon',
    alert: false
  },
  {
    title: '道路占用率',
    value: '76%',
    icon: 'occupation-icon',
    alert: true
  },
  {
    title: '事故风险指数',
    value: '中等',
    icon: 'risk-icon',
    alert: false
  }
]);

// 近期风险事件
const recentEvents = ref([
  {
    time: '10:23',
    title: '交通拥堵预警',
    description: '东环路与北二环交叉口出现车辆拥堵现象，建议绕行',
    level: 'warning',
    levelText: '中风险'
  },
  {
    time: '09:15',
    title: '道路施工影响',
    description: '南湖大道进行道路维修，占用两条车道，通行缓慢',
    level: 'info',
    levelText: '低风险'
  },
  {
    time: '08:30',
    title: '恶劣天气预警',
    description: '预计10:00后有大雾，能见度下降，建议车辆减速慢行',
    level: 'danger',
    levelText: '高风险'
  }
]);

// 高风险事件列表
const highRiskEvents = computed(() => {
  return recentEvents.value.filter(event => event.level === 'danger');
});

// 低风险事件列表
const lowRiskEvents = computed(() => {
  return recentEvents.value.filter(event => event.level === 'warning' || event.level === 'info');
});

// 是否有高风险事件
const hasHighRiskEvents = computed(() => {
  return highRiskEvents.value.length > 0;
});

// 是否有低风险事件
const hasLowRiskEvents = computed(() => {
  return lowRiskEvents.value.length > 0;
});

// 视频检测提示词
const detectionPrompt = ref(`
检测视频中所有帧的交通安全风险，识别人员、车辆和行为，以JSON格式返回结果。
特别关注危险驾驶行为（高风险）、交通拥堵（中风险）和其他安全隐患。
返回格式需包含：
1. 检测到的所有对象列表
2. 高风险事件列表
3. 低风险事件列表
4. 场景描述
`);

// 视频处理轮询状态
const pollingIntervals = ref({});

// 实时时间显示
const currentTime = ref('');

// 更新当前时间
const updateTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  currentTime.value = `${hours}:${minutes}:${seconds}`;
};

// 连接视频源
const connectVideoSource = async (feedId, sourceUrl) => {
  try {
    const feed = videoFeeds.value.find(f => f.id === feedId);
    if (!feed) return;

    feed.videoUrl = sourceUrl;
    feed.isLive = true;
    feed.status = 'normal';
    feed.statusText = '正常';
    feed.alert = false;
    feed.alertText = '';

    console.log(`Connected to video source: ${sourceUrl} for feed: ${feedId}`);
  } catch (error) {
    console.error(`Error connecting to video source: ${error}`);
  }
};

// 开始处理视频
const startVideoDetection = async (feedId) => {
  try {
    const feed = videoFeeds.value.find(f => f.id === feedId);
    if (!feed || !feed.videoUrl) return;

    // 更新处理状态
    feed.isProcessing = true;
    feed.status = 'processing';
    feed.statusText = '分析中';
    feed.alert = false; // 清除之前的错误状态

    console.log(`Sending video for detection with ${detectionPrompt.value.length} chars prompt`);

    try {
      // 发送视频到后端进行处理
      const response = await axios.post(`${API_BASE_URL}/api/video/process`, {
        video_input: feed.videoUrl,
        prompt: detectionPrompt.value,
        frame_interval: 30
      }, {
        timeout: 30000 // 30秒超时
      });

      console.log(`API response:`, response.data);

      if (!response.data.success) {
        throw new Error(response.data.error || '服务器返回错误');
      }

      // 获取处理ID
      const { video_id } = response.data;
      if (!video_id) {
        throw new Error('未收到处理ID');
      }

      feed.processingId = video_id;

      // 开始轮询处理状态
      pollingIntervals.value[feedId] = setInterval(() => {
        checkVideoProcessingStatus(feedId);
      }, 3000);

      console.log(`Started video detection for feed: ${feedId}, processing ID: ${video_id}`);
    } catch (apiError) {
      console.error(`API error: ${apiError.message}`);

      // 检查是否为网络错误
      if (apiError.message.includes('Network Error') || apiError.code === 'ECONNABORTED') {
        feed.status = 'error';
        feed.statusText = '网络错误';
        feed.alert = true;
        feed.alertText = '连接服务器失败，请检查网络或服务器状态';
      } else {
        feed.status = 'error';
        feed.statusText = '处理错误';
        feed.alert = true;
        feed.alertText = `视频处理失败: ${apiError.message || '未知错误'}`;
      }

      feed.isProcessing = false;
    }
  } catch (error) {
    console.error(`Error starting video detection: ${error}`);
    const feed = videoFeeds.value.find(f => f.id === feedId);
    if (feed) {
      feed.isProcessing = false;
      feed.status = 'error';
      feed.statusText = '处理错误';
      feed.alert = true;
      feed.alertText = `视频处理失败: ${error.message || '未知错误'}`;
    }
  }
};

// 检查视频处理状态
const checkVideoProcessingStatus = async (feedId) => {
  try {
    const feed = videoFeeds.value.find(f => f.id === feedId);
    if (!feed || !feed.processingId) return;

    try {
      // 请求处理状态
      const response = await axios.get(`${API_BASE_URL}/api/video/status/${feed.processingId}`, {
        timeout: 10000 // 10秒超时
      });
      const { status, error, elapsed_time } = response.data;

      console.log(`Status check for feed ${feedId}: ${status}, elapsed time: ${elapsed_time?.toFixed(1)}s`);

      // 根据状态更新UI
      if (status === 'completed') {
        // 清除轮询
        clearInterval(pollingIntervals.value[feedId]);

        // 更新状态
        feed.isProcessing = false;
        feed.status = 'normal';
        feed.statusText = '完成';
        feed.alert = false;

        try {
          // 获取处理结果
          const detailedResults = await axios.get(`${API_BASE_URL}/api/video/results/${feed.processingId}`, {
            timeout: 10000 // 10秒超时
          });
          feed.detectionResults = detailedResults.data;

          // 更新风险事件
          if (detailedResults.data && (detailedResults.data.frame_results || detailedResults.data.analysis)) {
            processDetectionResults(feedId, detailedResults.data);
          } else {
            console.warn('No valid results found in detection data');
            feed.alert = true;
            feed.alertText = '结果数据不完整';
            feed.status = 'warning';
            feed.statusText = '数据不完整';
          }

          console.log(`Video processing completed for feed: ${feedId}`);
        } catch (resultError) {
          console.error(`Error fetching results: ${resultError}`);
          feed.status = 'error';
          feed.statusText = '结果错误';
          feed.alert = true;
          feed.alertText = `获取结果失败: ${resultError.message || '未知错误'}`;
        }
      } else if (status === 'error') {
        // 清除轮询
        clearInterval(pollingIntervals.value[feedId]);

        // 更新状态
        feed.isProcessing = false;
        feed.status = 'error';
        feed.statusText = '处理错误';
        feed.alert = true;
        feed.alertText = `视频处理失败: ${error || '未知错误'}`;

        console.error(`Video processing error for feed: ${feedId}: ${error}`);
      } else if (status === 'processing' || status === 'initializing') {
        // 还在处理中，更新进度
        console.log(`Video processing in progress for feed: ${feedId}: ${status}, ${elapsed_time?.toFixed(1)}s`);
        // 确保不显示错误状态
        if (feed.status === 'error') {
          feed.status = 'processing';
          feed.statusText = '分析中';
          feed.alert = false;
        }
      } else if (status === 'not_found') {
        console.error(`Processing task not found for feed: ${feedId}`);
        clearInterval(pollingIntervals.value[feedId]);
        feed.isProcessing = false;
        feed.status = 'error';
        feed.statusText = '任务丢失';
        feed.alert = true;
        feed.alertText = '处理任务不存在';
      } else {
        console.warn(`Unknown status ${status} for feed: ${feedId}`);
      }
    } catch (statusError) {
      console.error(`Status check error: ${statusError.message}`);

      // 处理网络错误但不立即停止轮询，允许重试几次
      if (statusError.message.includes('Network Error') || statusError.code === 'ECONNABORTED') {
        feed.status = 'warning';
        feed.statusText = '连接中断';
        feed.alert = true;
        feed.alertText = '无法连接到服务器，正在重试...';

        // 连续失败次数计数
        feed.retryCount = (feed.retryCount || 0) + 1;

        // 如果连续失败超过3次，则停止轮询
        if (feed.retryCount > 3) {
          clearInterval(pollingIntervals.value[feedId]);
          feed.isProcessing = false;
          feed.status = 'error';
          feed.statusText = '连接失败';
          feed.alert = true;
          feed.alertText = '服务器连接失败，请稍后重试';
          console.error(`Max retry attempts reached for feed: ${feedId}`);
        }
      }
    }
  } catch (error) {
    console.error(`Error checking video status: ${error}`);
  }
};

// 处理检测结果并更新风险事件
const processDetectionResults = (feedId, results) => {
  try {
    const feed = videoFeeds.value.find(f => f.id === feedId);
    if (!feed) return;

    // 分析检测结果
    let highRiskCount = 0;
    let lowRiskCount = 0;
    const newEvents = [];

    // 统计检测信息
    let totalVehicles = 0;
    let totalSpeed = 0;
    let vehicleCount = 0;
    let riskLevel = '低';

    // 检查结果格式并根据需要进行适配
    const frameResults = results.frame_results || [];

    // 如果有analysis字段但没有frame_results，尝试从analysis中获取信息
    if ((!frameResults || frameResults.length === 0) && results.analysis) {
      console.log("Using analysis data for results");

      // 高风险事件
      if (results.analysis.high_risk_events && results.analysis.high_risk_events.length > 0) {
        highRiskCount += results.analysis.high_risk_events.length;

        // 添加到事件列表
        results.analysis.high_risk_events.forEach(event => {
          const timestamp = new Date(Date.now() - Math.random() * 3600000);
          const hours = timestamp.getHours().toString().padStart(2, '0');
          const minutes = timestamp.getMinutes().toString().padStart(2, '0');

          newEvents.push({
            time: `${hours}:${minutes}`,
            title: event.event || event.label || '高风险事件',
            description: `在${feed.location}发现${event.event || event.label || '高风险情况'}`,
            level: 'danger',
            levelText: '高风险',
            feedId: feed.id
          });
        });
      }

      // 低风险事件
      if (results.analysis.low_risk_events && results.analysis.low_risk_events.length > 0) {
        lowRiskCount += results.analysis.low_risk_events.length;

        // 添加到事件列表
        results.analysis.low_risk_events.forEach(event => {
          const timestamp = new Date(Date.now() - Math.random() * 3600000);
          const hours = timestamp.getHours().toString().padStart(2, '0');
          const minutes = timestamp.getMinutes().toString().padStart(2, '0');

          newEvents.push({
            time: `${hours}:${minutes}`,
            title: event.event || event.label || '低风险事件',
            description: `在${feed.location}发现${event.event || event.label || '低风险情况'}`,
            level: 'warning',
            levelText: '低风险',
            feedId: feed.id
          });
        });
      }

      // 统计所有检测到的车辆
      if (results.analysis.detections) {
        const vehicles = results.analysis.detections.filter(d =>
          d.category === '车辆' || d.category === 'vehicle' ||
          d.category?.toLowerCase().includes('car') ||
          d.category?.toLowerCase().includes('vehicle')
        );

        totalVehicles += vehicles.length;

        // 如果有车速信息
        vehicles.forEach(vehicle => {
          if (vehicle.speed) {
            totalSpeed += parseFloat(vehicle.speed);
            vehicleCount++;
          }
        });
      }
    } else {
      // 使用frame_results数据
      frameResults.forEach(frameResult => {
        // 高风险事件
        if (frameResult.high_risk_events && frameResult.high_risk_events.length > 0) {
          highRiskCount += frameResult.high_risk_events.length;

          // 添加到事件列表
          frameResult.high_risk_events.forEach(event => {
            const timestamp = new Date(Date.now() - Math.random() * 3600000);
            const hours = timestamp.getHours().toString().padStart(2, '0');
            const minutes = timestamp.getMinutes().toString().padStart(2, '0');

            newEvents.push({
              time: `${hours}:${minutes}`,
              title: event.event || event.label || '高风险事件',
              description: `在${feed.location}发现${event.event || event.label || '高风险情况'}`,
              level: 'danger',
              levelText: '高风险',
              feedId: feed.id
            });
          });
        }

        // 低风险事件
        if (frameResult.low_risk_events && frameResult.low_risk_events.length > 0) {
          lowRiskCount += frameResult.low_risk_events.length;

          // 添加到事件列表
          frameResult.low_risk_events.forEach(event => {
            const timestamp = new Date(Date.now() - Math.random() * 3600000);
            const hours = timestamp.getHours().toString().padStart(2, '0');
            const minutes = timestamp.getMinutes().toString().padStart(2, '0');

            newEvents.push({
              time: `${hours}:${minutes}`,
              title: event.event || event.label || '低风险事件',
              description: `在${feed.location}发现${event.event || event.label || '低风险情况'}`,
              level: 'warning',
              levelText: '低风险',
              feedId: feed.id
            });
          });
        }

        // 统计所有检测到的车辆
        if (frameResult.detections) {
          const vehicles = frameResult.detections.filter(d =>
            d.category === '车辆' || d.category === 'vehicle' ||
            d.category?.toLowerCase().includes('car') ||
            d.category?.toLowerCase().includes('vehicle')
          );

          totalVehicles += vehicles.length;

          // 如果有车速信息
          vehicles.forEach(vehicle => {
            if (vehicle.speed) {
              totalSpeed += parseFloat(vehicle.speed);
              vehicleCount++;
            }
          });
        }
      });
    }

    // 更新feed状态
    if (highRiskCount > 0) {
      feed.status = 'alert';
      feed.statusText = '警告';
      feed.alert = true;
      feed.alertText = `发现${highRiskCount}个高风险`;
      riskLevel = '高';
    } else if (lowRiskCount > 0) {
      feed.status = 'warning';
      feed.statusText = '注意';
      feed.alert = true;
      feed.alertText = `发现${lowRiskCount}个低风险`;
      riskLevel = '中';
    } else {
      feed.status = 'normal';
      feed.statusText = '正常';
      feed.alert = false;
      riskLevel = '低';
    }

    // 更新全局风险事件列表
    if (newEvents.length > 0) {
      // 添加新事件到开头，保持最多10个事件
      recentEvents.value = [...newEvents, ...recentEvents.value].slice(0, 10);
    }

    // 估计视频长度和FPS以便计算统计信息
    const videoMetadata = results.video_metadata || {
      frame_count: 300,  // 默认假设10秒视频
      fps: 30
    };

    // 处理帧数为0的情况
    const processingInfo = results.processing_info || {
      frame_interval: 30,
      frames_processed: frameResults.length || 1
    };

    // 更新右侧统计面板数据
    const framesPerHour = 3600 / ((processingInfo.frame_interval || 30) / (videoMetadata.fps || 30));
    const estimatedVehiclesPerHour = Math.round(totalVehicles * (framesPerHour / (processingInfo.frames_processed || 1)));

    // 更新车流量
    stats.value[0].value = `${estimatedVehiclesPerHour.toLocaleString()} 辆/小时`;

    // 更新平均车速
    const avgSpeed = vehicleCount > 0 ? Math.round(totalSpeed / vehicleCount) : 0;
    stats.value[1].value = `${avgSpeed || '--'} km/h`;

    // 更新道路占用率 (基于车辆数和当前帧的比例估算)
    const occupationPercentage = Math.min(Math.round((totalVehicles / (processingInfo.frames_processed || 1)) * 100), 100);
    stats.value[2].value = `${occupationPercentage}%`;
    stats.value[2].alert = occupationPercentage > 75;

    // 更新风险指数
    stats.value[3].value = `${riskLevel}等`;
    stats.value[3].alert = riskLevel === '高';

    console.log(`Processed detection results for feed: ${feedId}, high risks: ${highRiskCount}, low risks: ${lowRiskCount}`);
  } catch (error) {
    console.error(`Error processing detection results: ${error}`);
  }
};



// 接入摄像头视频源 - 使用iframe方式
const connectLiveCamera = async (feedId) => {
  try {
    const feed = videoFeeds.value.find(f => f.id === feedId);
    if (!feed) return;
    
    console.log(`连接萤石云摄像头，Feed ID: ${feedId}`);
    
    // 使用提供的设备序列号和通道号
    const deviceSerial = 'FX2683018'; // 设备序列号
    const channelNo = 3; // 通道号
    
    // 预设的appKey和appSecret
    const appKey = '8dcf58f3eff843a49ae4c60b55cd9c9b';
    const appSecret = 'a436053dec3df63157bdfe7100f76f88';
    
    console.log(`设备信息 - 序列号: ${deviceSerial}, 通道号: ${channelNo}`);
    console.log(`认证信息 - AppKey: ${appKey}`);
    
         // 更改"隧道监控点C"的feed对象以直接使用示例URL
     if (feedId === 'feed3') {
      console.log('设置隧道监控点C的萤石云视频流');
      
      try {
        // 获取accessToken - 使用正确的请求格式
        console.log('请求Token - AppKey:', appKey, 'AppSecret:', appSecret);
       
        // 尝试使用URL参数方式
        const params = new URLSearchParams();
        params.append('appKey', appKey);
        params.append('appSecret', appSecret);
        
        const tokenResponse = await axios.post('https://open.ys7.com/api/lapp/token/get', params, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });
        
        // 记录完整响应以便调试
        console.log('Token API响应:', tokenResponse.data);
        
        if (tokenResponse.data.code == '200') {
          const accessToken = tokenResponse.data.data.accessToken;
          console.log('成功获取AccessToken:', accessToken);
          
          // 获取直播地址 - 根据萤石云开放平台文档
          const liveUrlParams = new URLSearchParams();
          liveUrlParams.append('accessToken', accessToken);
          liveUrlParams.append('deviceSerial', deviceSerial);
          liveUrlParams.append('channelNo', channelNo);
          liveUrlParams.append('protocol', 2); // 使用HLS (2) 协议
          liveUrlParams.append('quality', 1); // 高清(2)画质
          
          console.log('请求直播地址 - 参数:', Object.fromEntries(liveUrlParams));
          
          const liveUrlResponse = await axios.post('https://open.ys7.com/api/lapp/v2/live/address/get', liveUrlParams, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          });
          
          console.log('直播地址API响应:', liveUrlResponse.data);
          
          if (liveUrlResponse.data.code == '200') {
            // 获取HLS地址
            const hlsUrl = liveUrlResponse.data.data.url;
            console.log('========== 获取到的HLS URL ==========');
            console.log(hlsUrl);
            console.log('=====================================');
            
            // 更新feed状态
       feed.isLive = true;
            feed.videoUrl = hlsUrl;
            feed.accessToken = accessToken;
       feed.status = 'normal';
       feed.statusText = '萤石云监控中';
       feed.alert = false;
       feed.alertText = '';
       
            // 设置延时来初始化HLS播放器
            setTimeout(() => {
              initializeHlsPlayer(feed);
            }, 500);
            
            console.log(`萤石云摄像头连接成功，Feed ID: ${feedId}`);
          } else {
            console.error('获取直播地址失败:', liveUrlResponse.data);
            throw new Error(`获取直播地址失败: ${liveUrlResponse.data.msg || '请检查设备序列号和通道号'}`);
          }
        } else {
          console.error('获取Token失败:', tokenResponse.data);
          throw new Error(`获取授权失败: ${tokenResponse.data.msg || '请检查AppKey和AppSecret'}`);
        }
      } catch (error) {
        console.error(`获取萤石云直播地址错误: ${error.message || error}`);
        feed.status = 'error';
        feed.statusText = '连接错误';
        feed.alert = true;
        feed.alertText = `萤石云摄像头连接失败: ${error.message || '未知错误'}`;
        
        // 如果API请求失败，使用后备的示例流
        console.log('Falling back to sample stream');
        const sampleStreamUrl = "https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8";
        feed.isLive = true;
        feed.videoUrl = sampleStreamUrl;
        feed.status = 'warning';
        feed.statusText = '示例流 (后备)';
        
        // 使用示例流初始化播放器
        setTimeout(() => {
          initializeHlsPlayer(feed);
        }, 500);
      }
      
       return;
     }
    
    try {
      // 获取accessToken - 使用正确的请求格式
      console.log('Requesting token with appKey:', appKey, 'and appSecret:', appSecret);
      
      // 尝试使用URL参数方式
      const params = new URLSearchParams();
      params.append('appKey', appKey);
      params.append('appSecret', appSecret);
      
      const tokenResponse = await axios.post('https://open.ys7.com/api/lapp/token/get', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      // 记录完整响应以便调试
      console.log('Token API response:', tokenResponse.data);
      
      if (tokenResponse.data.code == '200') {
        const accessToken = tokenResponse.data.data.accessToken;
        console.log('Successfully obtained YS7 access token:', accessToken);
        
        // 获取直播地址 - 根据萤石云开放平台文档
        const liveUrlParams = new URLSearchParams();
        liveUrlParams.append('accessToken', accessToken);
        liveUrlParams.append('deviceSerial', deviceSerial);
        liveUrlParams.append('channelNo', channelNo);
        liveUrlParams.append('protocol', 2); // 使用HLS (2) 协议
        liveUrlParams.append('quality', 2); // 高清(2)画质
        
        const liveUrlResponse = await axios.post('https://open.ys7.com/api/lapp/v2/live/address/get', liveUrlParams, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });
        
        console.log('Live URL response:', liveUrlResponse.data);
        
        if (liveUrlResponse.data.code == '200') {
          // 获取HLS地址
          const hlsUrl = liveUrlResponse.data.data.url;
          console.log('Got HLS URL:', hlsUrl);
        
        // 更新feed状态
        feed.isLive = true;
          feed.videoUrl = hlsUrl;
          feed.accessToken = accessToken;
        feed.status = 'normal';
        feed.statusText = '萤石云监控中';
        feed.alert = false;
        feed.alertText = '';
        
          // 设置延时来初始化HLS播放器
          setTimeout(() => {
            initializeHlsPlayer(feed);
          }, 500);
          
          console.log(`Connected to YS7 camera for feed: ${feedId} using HLS URL`);
        } else {
          throw new Error(`获取直播地址失败: ${liveUrlResponse.data.msg || '请检查设备序列号和通道号'}`);
        }
      } else {
        console.error('Token API error response:', tokenResponse.data);
        throw new Error(`获取授权失败: ${tokenResponse.data.msg || '请检查AppKey和AppSecret'}`);
      }
    } catch (error) {
      console.error(`Error connecting to YS7 camera: ${error.message || error}`);
      
      feed.status = 'error';
      feed.statusText = '连接错误';
      feed.alert = true;
      
      // 提供更详细的错误信息
      if (error.message && error.message.includes('token')) {
        feed.alertText = '萤石云授权失败，请检查AppKey和Secret';
      } else if (error.message && error.message.includes('直播地址')) {
        feed.alertText = '获取直播地址失败，请检查设备序列号和通道号';
      } else {
        feed.alertText = `萤石云摄像头连接失败: ${error.message || '未知错误'}`;
      }
      
      console.log(`YS7 connection failed for feed ${feedId}: ${error.message || error}`);
      throw error;
    }
  } catch (error) {
    console.error(`Error in connectLiveCamera: ${error.message || error}`);
    throw error;
  }
};

// 上传视频文件
const handleVideoUpload = async (feedId, event) => {
  try {
    const feed = videoFeeds.value.find(f => f.id === feedId);
    if (!feed) return;

    const file = event.target.files[0];
    if (!file) return;

    // 读取视频文件
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const base64Video = e.target.result;
        feed.videoUrl = base64Video;
        feed.isLive = false;
        feed.status = 'normal';
        feed.statusText = '视频已加载';

        // 自动开始处理
        startVideoDetection(feedId);
      } catch (err) {
        feed.status = 'error';
        feed.statusText = '加载错误';
        feed.alert = true;
        feed.alertText = '视频加载失败';
        console.error(`Error loading video: ${err}`);
      }
    };
    reader.readAsDataURL(file);

    console.log(`Video file uploaded for feed: ${feedId}`);
  } catch (error) {
    console.error(`Error uploading video: ${error}`);
  }
};

// 模拟数据变化 (简化版，当有真实数据时这个函数可以被删除)
const simulateDataChanges = () => {
  // 只在没有真实连接的情况下模拟变化
  const activeFeeds = videoFeeds.value.filter(feed => feed.isLive || feed.processingId);
  if (activeFeeds.length > 0) return;

  // 随机更新车流量
  const trafficVolume = Math.floor(Math.random() * 500) + 1000;
  stats.value[0].value = `${trafficVolume.toLocaleString()} 辆/小时`;

  // 随机更新平均车速
  const avgSpeed = Math.floor(Math.random() * 30) + 50;
  stats.value[1].value = `${avgSpeed} km/h`;

  // 随机更新道路占用率
  const occupation = Math.floor(Math.random() * 30) + 60;
  stats.value[2].value = `${occupation}%`;
  stats.value[2].alert = occupation > 75;

  // 随机模拟视频流状态变化
  const randomFeedIndex = Math.floor(Math.random() * videoFeeds.value.length);
  if (Math.random() > 0.7) {
    const statuses = ['normal', 'warning', 'alert'];
    const statusTexts = ['正常', '注意', '警告'];
    const randomStatusIndex = Math.floor(Math.random() * statuses.length);

    videoFeeds.value[randomFeedIndex].status = statuses[randomStatusIndex];
    videoFeeds.value[randomFeedIndex].statusText = statusTexts[randomStatusIndex];
    videoFeeds.value[randomFeedIndex].alert = randomStatusIndex > 0;

    if (randomStatusIndex > 0) {
      const alertTexts = ['车流量高', '车速异常', '拥堵风险', '异常停车'];
      videoFeeds.value[randomFeedIndex].alertText = alertTexts[Math.floor(Math.random() * alertTexts.length)];
    } else {
      videoFeeds.value[randomFeedIndex].alertText = '';
    }
  }
};

// 获取高风险事件数量
const getHighRiskCount = () => {
  return highRiskEvents.value.length;
};

// 获取低风险事件数量
const getLowRiskCount = () => {
  return lowRiskEvents.value.length;
};

// 获取当前车流量
const getCurrentTraffic = () => {
  return stats.value[0].value.split(' ')[0];
};

// 获取平均车速
const getAverageSpeed = () => {
  return stats.value[1].value.split(' ')[0];
};

// 获取风险指数
const getRiskIndex = () => {
  return stats.value[3].value;
};

// 获取萤石云直播URL
const getYs7IframeUrl = (feed) => {
  // 优先使用从API获取的直播地址
  if (feed.videoUrl && (feed.videoUrl.includes('.m3u8') || feed.videoUrl.includes('hls'))) {
    console.log(`==================== 视频URL信息 ====================`);
    console.log(`Feed ID: ${feed.id}`);
    console.log(`Feed Title: ${feed.title}`);
    console.log(`使用API提供的HLS地址: ${feed.videoUrl}`);
    console.log(`=====================================================`);
    return feed.videoUrl;
  }
  
  // 如果feed已经包含完整的认证URL，直接使用
  if (feed.authenticatedUrl) {
    console.log(`==================== 视频URL信息 ====================`);
    console.log(`Feed ID: ${feed.id}`);
    console.log(`Feed Title: ${feed.title}`);
    console.log(`使用预认证URL: ${feed.authenticatedUrl}`);
    console.log(`=====================================================`);
    return feed.authenticatedUrl;
  }
  
  // 如果有认证参数，添加到URL
  if (feed.authParams) {
    const fullUrl = `${feed.videoUrl}?${feed.authParams}`;
    console.log(`==================== 视频URL信息 ====================`);
    console.log(`Feed ID: ${feed.id}`);
    console.log(`Feed Title: ${feed.title}`);
    console.log(`使用认证参数URL: ${fullUrl}`);
    console.log(`=====================================================`);
    return fullUrl;
  }
  
  // 如果有accessToken但没有完整认证URL，构建带token的URL
  if (feed.accessToken) {
    const fullUrl = `${feed.videoUrl}?accessToken=${feed.accessToken}`;
    console.log(`==================== 视频URL信息 ====================`);
    console.log(`Feed ID: ${feed.id}`);
    console.log(`Feed Title: ${feed.title}`);
    console.log(`使用accessToken的URL: ${fullUrl}`);
    console.log(`=====================================================`);
    return fullUrl;
  }
  
  // 最后手段：硬编码一个测试URL用于开发
  if (feed.isYs7Feed && feed.id === 'feed3') {
    const testUrl = "https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8";
    console.log(`==================== 视频URL信息 ====================`);
    console.log(`Feed ID: ${feed.id}`);
    console.log(`Feed Title: ${feed.title}`);
    console.log(`使用测试URL: ${testUrl}`);
    console.log(`=====================================================`);
    return testUrl;
  }
  
  // 没有认证参数，尝试直接返回
  console.log(`==================== 视频URL信息 ====================`);
  console.log(`Feed ID: ${feed.id}`);
  console.log(`Feed Title: ${feed.title}`);
  console.log(`使用原始URL: ${feed.videoUrl}`);
  console.log(`=====================================================`);
  return feed.videoUrl;
};

// 停止监控
const stopMonitoring = (feedId) => {
  const feed = videoFeeds.value.find(f => f.id === feedId);
  if (!feed) return;

  // 清除轮询
  if (pollingIntervals.value[feedId]) {
    clearInterval(pollingIntervals.value[feedId]);
    delete pollingIntervals.value[feedId];
  }

  // 清除捕获
  if (feed.captureInterval) {
    clearInterval(feed.captureInterval);
    feed.captureInterval = null;
  }

  // 清理萤石云相关资源
  if (feed.isYs7Feed) {
    // 清除accessToken和关联的iframe
    feed.accessToken = null;
    console.log(`Stopped YS7 iframe for feed: ${feedId}`);
  }

  // 更新状态
  feed.isLive = false;
  feed.isProcessing = false;
  feed.status = 'stopped';
  feed.statusText = '已停止';
  feed.alert = false;
  feed.alertText = '';

  console.log(`Stopped monitoring for feed: ${feedId}`);
};

// 启动/停止所有监控
const toggleAllMonitoring = () => {
  monitoringActive.value = !monitoringActive.value;

  if (monitoringActive.value) {
    // 启动所有监控
    console.log("Starting all monitoring");
    initializeYs7Cameras();
  } else {
    // 停止所有监控
    videoFeeds.value.forEach(feed => {
      stopMonitoring(feed.id);
    });
    console.log("Stopped all monitoring");
  }
};

// 初始化萤石云摄像头
const initializeYs7Cameras = async () => {
  console.log('Initializing YS7 cameras');
  
  // 查找所有标记为萤石云摄像头的feed
  const ys7Feeds = videoFeeds.value.filter(feed => feed.isYs7Feed);
  
  if (ys7Feeds.length === 0) {
    console.log('No YS7 cameras configured');
    return;
  }
  
  console.log(`Found ${ys7Feeds.length} YS7 camera feeds to initialize`);
  
  // 在100ms后初始化摄像头，确保DOM已渲染
  setTimeout(async () => {
    for (const feed of ys7Feeds) {
      try {
        console.log(`Initializing YS7 camera for feed: ${feed.id} - ${feed.title}`);
        await connectLiveCamera(feed.id);
      } catch (error) {
        console.error(`Failed to initialize YS7 camera for feed ${feed.id}:`, error);
      }
    }
  }, 100);
};

// 导航类别数据
const riskCategories = [
  {
    id: 'road_structure',
    label: '路面结构风险监测',
    icon: 'road_structure-icon'
  },
  {
    id: 'vehicle_accident',
    label: '车辆事故风险',
    icon: 'vehicle_accident-icon'
  },
  {
    id: 'abnormal_driving',
    label: '异常驾驶行为',
    icon: 'abnormal_driving-icon'
  }
];

// 检测选项数据
const detectionOptions = ref([
  {
    id: 'road_structure',
    label: '路面结构风险',
    expanded: true,
    options: [
      { id: 'pavement_damage', label: '路面损坏检测', checked: false },
      { id: 'road_cracks', label: '路面裂缝检测', checked: false },
      { id: 'potholes', label: '坑洼检测', checked: false },
      { id: 'water_accumulation', label: '积水检测', checked: false },
      { id: 'road_subsidence', label: '路面沉降检测', checked: false },
      { id: 'roadside_damage', label: '路侧损坏检测', checked: false }
    ]
  },
  {
    id: 'vehicle_accident',
    label: '车辆事故风险',
    expanded: true,
    options: [
      { id: 'collision_risk', label: '碰撞风险检测', checked: false },
      { id: 'emergency_braking', label: '紧急制动检测', checked: false },
      { id: 'traffic_congestion', label: '交通拥堵检测', checked: false },
      { id: 'roadside_parking', label: '路侧停车检测', checked: false },
      { id: 'accident_scene', label: '事故现场检测', checked: false }
    ]
  },
  {
    id: 'abnormal_driving',
    label: '异常驾驶行为',
    expanded: true,
    options: [
      { id: 'speeding', label: '超速驾驶检测', checked: false },
      { id: 'lane_departure', label: '车道偏离检测', checked: false },
      { id: 'illegal_turning', label: '违规转弯检测', checked: false },
      { id: 'phone_use', label: '驾驶分心检测', checked: false },
      { id: 'fatigue_driving', label: '疲劳驾驶检测', checked: false },
      { id: 'signal_violation', label: '信号违规检测', checked: false }
    ]
  }
]);

// 当前选中的导航类别
const selectedCategory = ref('road_structure');

// 根据选中的导航类别过滤检测选项
const filteredDetectionOptions = computed(() => {
  return detectionOptions.value;
});

// 选择导航类别
const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId;

  // 根据选择的类别更新检测提示词
  switch(categoryId) {
    case 'road_structure':
      detectionPrompt.value = `
检测视频中所有帧的路面结构风险，识别路面裂缝、坑洼、积水等问题，以JSON格式返回结果。
特别关注路面损坏（高风险）、积水（中风险）和其他安全隐患。
返回格式需包含：
1. 检测到的所有路面结构问题
2. 高风险事件列表
3. 低风险事件列表
4. 场景描述
`;
      break;
    case 'vehicle_accident':
      detectionPrompt.value = `
检测视频中所有帧的车辆事故风险，识别碰撞风险、紧急制动、交通拥堵等情况，以JSON格式返回结果。
特别关注碰撞风险（高风险）、异常停车（中风险）和其他安全隐患。
返回格式需包含：
1. 检测到的所有车辆事故风险
2. 高风险事件列表
3. 低风险事件列表
4. 场景描述
`;
      break;
    case 'abnormal_driving':
      detectionPrompt.value = `
检测视频中所有帧的异常驾驶行为，识别超速驾驶、车道偏离、违规转弯等行为，以JSON格式返回结果。
特别关注疲劳驾驶（高风险）、使用手机（高风险）和其他安全隐患。
返回格式需包含：
1. 检测到的所有异常驾驶行为
2. 高风险事件列表
3. 低风险事件列表
4. 场景描述
`;
      break;
    default:
      detectionPrompt.value = `
检测视频中所有帧的交通安全风险，识别人员、车辆和行为，以JSON格式返回结果。
特别关注危险驾驶行为（高风险）、交通拥堵（中风险）和其他安全隐患。
返回格式需包含：
1. 检测到的所有对象列表
2. 高风险事件列表
3. 低风险事件列表
4. 场景描述
`;
  }
};

// 切换类别展开状态
const toggleCategory = (categoryId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    category.expanded = !category.expanded;
  }
};

// 切换选项选中状态
const toggleOption = (categoryId: string, optionId: string) => {
  const category = detectionOptions.value.find(cat => cat.id === categoryId);
  if (category) {
    const option = category.options.find(opt => opt.id === optionId);
    if (option) {
      option.checked = !option.checked;
    }
  }
};

// 组件挂载和卸载

onMounted(() => {
  // 初始化当前时间
  updateTime();

  // 每秒更新一次时间
  timeInterval = window.setInterval(updateTime, 1000);

  // 每10秒模拟数据变化
  dataInterval = window.setInterval(simulateDataChanges, 10000);

  // 动态加载HLS.js
  if (!window.Hls) {
    console.log('Loading HLS.js dynamically');
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/hls.js@latest';
    script.async = true;
    script.onload = () => {
      console.log('HLS.js loaded dynamically');
      initializeYs7Cameras();
    };
    script.onerror = () => {
      console.error('Failed to load HLS.js');
      // 尝试使用其他视频方式
      initializeYs7Cameras();
    };
    document.head.appendChild(script);
  } else {
    // HLS.js已加载，初始化萤石云摄像头
    initializeYs7Cameras();
  }

  // 检查萤石云SDK是否加载成功
  if (window.EZUIKit) {
    console.log('YS7 SDK loaded successfully');
  } else {
    console.warn('YS7 SDK not found, trying to load it');
    // 如果未能正确加载SDK，尝试动态加载
    const script = document.createElement('script');
    script.src = 'https://open.ys7.com/sdk/js/2.0/ezuikit.js';
    script.async = true;
    script.onload = () => {
      console.log('YS7 SDK loaded dynamically');
    };
    script.onerror = () => console.error('Failed to load YS7 SDK');
    document.head.appendChild(script);
  }
});

onUnmounted(() => {
  // 清除定时器
  if (timeInterval) window.clearInterval(timeInterval);
  if (dataInterval) window.clearInterval(dataInterval);

  // 清除所有视频处理轮询
  Object.keys(pollingIntervals.value).forEach(key => {
    clearInterval(pollingIntervals.value[key]);
  });

  // 清除HLS播放器实例
  Object.keys(hlsPlayers).forEach(key => {
    if (hlsPlayers[key]) {
      hlsPlayers[key].destroy();
      delete hlsPlayers[key];
    }
  });

  // 清除所有视频捕获和销毁YS7播放器
  videoFeeds.value.forEach(feed => {
    if (feed.captureInterval) {
      clearInterval(feed.captureInterval);
    }
    
    // 清理萤石云相关资源
    if (feed.isYs7Feed) {
      feed.accessToken = null;
      feed.isLive = false;
      console.log(`Cleaned up YS7 resources for feed: ${feed.id}`);
    }
  });
});

// 初始化HLS播放器
const initializeHlsPlayer = (feed) => {
  try {
    if (!feed || !feed.isLive || !feed.id) return;
    
    // 确保HLS.js可用
    if (typeof window.Hls === 'undefined') {
      console.error('HLS.js not available');
      feed.status = 'error';
      feed.statusText = '播放器加载失败';
      feed.alert = true;
      feed.alertText = 'HLS.js播放器加载失败';
      return;
    }
    
    const videoElement = document.getElementById(`ys7-video-${feed.id}`);
    if (!videoElement) {
      console.error(`Video element not found for feed ${feed.id}`);
      return;
    }
    
    // 获取视频URL
    const videoUrl = getYs7IframeUrl(feed);
    console.log(`Initializing HLS player for ${feed.id} with URL: ${videoUrl}`);
    
    // 清理旧的播放器实例
    if (hlsPlayers[feed.id]) {
      hlsPlayers[feed.id].destroy();
      delete hlsPlayers[feed.id];
    }
    
    // 创建新的播放器实例
    if (window.Hls.isSupported()) {
      const hls = new window.Hls({
        debug: false,
        // 增加错误恢复尝试次数
        maxLoadingRetry: 4,
        // 添加额外的媒体错误恢复
        enableWorker: true
      });
      
      hls.loadSource(videoUrl);
      hls.attachMedia(videoElement);
      
      // 成功解析流时
      hls.on(window.Hls.Events.MANIFEST_PARSED, () => {
        videoElement.play().catch(playError => {
          console.warn(`Autoplay prevented for feed ${feed.id}:`, playError);
          // 提示用户点击播放
          feed.alert = true;
          feed.alertText = '点击播放按钮开始播放';
          feed.status = 'warning';
          feed.statusText = '等待播放';
        });
        console.log(`HLS player initialized for feed ${feed.id}`);
      });
      
      // 错误处理
      hls.on(window.Hls.Events.ERROR, (event, data) => {
        // 输出详细错误信息
        console.error(`HLS player error for feed ${feed.id}:`, data);
        
        // 检查萤石云特定的编码类型错误
        if (data.type === window.Hls.ErrorTypes.MEDIA_ERROR && 
            data.details === window.Hls.ErrorDetails.BUFFER_APPEND_ERROR) {
          console.error(`Possible codec issue for feed ${feed.id}`);
          feed.status = 'error';
          feed.statusText = '视频编码不兼容';
          feed.alert = true;
          feed.alertText = '编码类型非H264，检查设备的视频编码并将视频编码类型修改为H264';
          
          // 播放器销毁
          hls.destroy();
          delete hlsPlayers[feed.id];
          
          // 显示帮助信息
          showCodecErrorHelp(feed);
          return;
        }
        
        if (data.fatal) {
          switch(data.type) {
            case window.Hls.ErrorTypes.NETWORK_ERROR:
              console.log(`Network error for feed ${feed.id}, attempting to recover`);
              feed.status = 'warning';
              feed.statusText = '网络错误，正在重试';
              hls.startLoad();
              break;
            case window.Hls.ErrorTypes.MEDIA_ERROR:
              console.log(`Media error for feed ${feed.id}, attempting to recover`);
              feed.status = 'warning';
              feed.statusText = '媒体错误，正在恢复';
              hls.recoverMediaError();
              break;
            default:
              console.error(`Fatal error for feed ${feed.id}, cannot recover`);
              feed.status = 'error';
              feed.statusText = '播放失败';
              feed.alert = true;
              feed.alertText = '视频播放失败，请检查视频源';
              hls.destroy();
              delete hlsPlayers[feed.id];
              
              // 尝试切换到备用流
              fallbackToSampleStream(feed);
              break;
          }
        }
      });
      
      hlsPlayers[feed.id] = hls;
    } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
      // 对于支持HLS的浏览器，直接设置src
      videoElement.src = videoUrl;
    } else {
      feed.status = 'error';
      feed.statusText = '浏览器不支持';
      feed.alert = true;
      feed.alertText = '您的浏览器不支持HLS播放，请使用现代浏览器';
    }
  } catch (error) {
    console.error(`Error initializing HLS player: ${error}`);
    const feed = videoFeeds.value.find(f => f.id === feed.id);
    if (feed) {
      feed.status = 'error';
      feed.statusText = '播放器错误';
      feed.alert = true;
      feed.alertText = `播放器初始化失败: ${error.message || '未知错误'}`;
    }
  }
};

// 显示编码错误帮助信息
const showCodecErrorHelp = (feed) => {
  // 添加一个覆盖层显示帮助信息
  const errorHelpDiv = document.createElement('div');
  errorHelpDiv.className = 'codec-error-help';
  errorHelpDiv.innerHTML = `
    <div class="error-help-content">
      <h3>视频编码类型错误</h3>
      <p>萤石云摄像头需要使用H264编码才能在网页中播放。</p>
      <p>解决方法:</p>
      <ul>
        <li>登录萤石云开放平台或萤石云APP</li>
        <li>在设备管理中找到设备【${feed.title}】</li>
        <li>进入设备设置 → 视频设置 → 视频编码</li>
        <li>将编码格式修改为H264</li>
        <li>保存设置并重启设备</li>
      </ul>
      <button class="close-help-btn">我知道了</button>
    </div>
  `;
  
  // 查找视频容器并添加帮助信息
  const videoContainer = document.getElementById(`feed-${feed.id}`);
  if (videoContainer) {
    videoContainer.appendChild(errorHelpDiv);
    
    // 添加关闭按钮事件
    const closeBtn = errorHelpDiv.querySelector('.close-help-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        errorHelpDiv.remove();
        // 尝试切换到备用流
        fallbackToSampleStream(feed);
      });
    }
  }
};

// 切换到备用示例流
const fallbackToSampleStream = (feed) => {
  if (!feed) return;
  
  console.log(`Switching to sample stream for feed ${feed.id}`);
  const sampleStreamUrl = "https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8";
  
  feed.videoUrl = sampleStreamUrl;
  feed.isLive = true;
  feed.status = 'warning';
  feed.statusText = '示例流 (备用)';
  feed.alert = true;
  feed.alertText = '已切换到示例流';
  
  // 延迟初始化备用播放器
  setTimeout(() => {
    initializeHlsPlayer(feed);
  }, 1000);
};
</script>

<style scoped>
.operation-monitor-page {
  display: grid;
  grid-template-columns: 320px 1fr 320px;
  grid-template-rows: 100%;
  gap: 15px;
  min-height: 100%;
  width: 100%;
  overflow-y: auto;
  position: relative;
}

/* Left Sidebar */
.left-sidebar {
  background: linear-gradient(135deg, #001529, #002140);
  overflow-y: auto;
  min-width: 320px;
  width: 320px;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
  height: 100%;
}

.left-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.7), transparent);
}

/* Navigation Section */
.nav-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
  padding: 15px 12px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.15);
  position: relative;
}

.nav-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  width: 70%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.7), transparent);
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 21, 41, 0.6);
  border: 1px solid rgba(0, 168, 255, 0.2);
  color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  text-align: left;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.1), transparent);
  transition: all 0.6s;
}

.nav-button:hover {
  background: rgba(0, 58, 140, 0.5);
  border-color: rgba(0, 168, 255, 0.4);
  transform: translateY(-2px);
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button.active {
  background: linear-gradient(90deg, rgba(0, 168, 255, 0.2), rgba(0, 58, 140, 0.6));
  border-color: rgba(0, 168, 255, 0.7);
  box-shadow: 0 0 10px rgba(0, 168, 255, 0.3);
}

.nav-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  filter: drop-shadow(0 0 2px rgba(0, 168, 255, 0.5));
}

.detection-options-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 12px 15px;
  max-height: calc(100% - 150px);
}

.sidebar-header {
  color: rgba(0, 168, 255, 0.9);
  font-size: 16px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.15);
  text-align: center;
  font-weight: bold;
  letter-spacing: 1px;
  text-shadow: 0 0 8px rgba(0, 168, 255, 0.3);
  position: relative;
}

.sidebar-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.7), transparent);
}

.detection-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
  max-height: 100%;
}

.detection-category {
  background: rgba(0, 21, 41, 0.4);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.1);
  transition: all 0.3s ease;
}

.detection-category:hover {
  border-color: rgba(0, 168, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  background: linear-gradient(90deg, rgba(0, 21, 41, 0.8), rgba(0, 58, 140, 0.4));
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.category-header:hover {
  background: linear-gradient(90deg, rgba(0, 21, 41, 0.6), rgba(0, 58, 140, 0.5));
  border-left-color: rgba(0, 168, 255, 0.7);
}

.category-icon-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  filter: drop-shadow(0 0 2px rgba(0, 168, 255, 0.5));
  transition: transform 0.3s ease;
}

.category-header:hover .category-icon {
  transform: scale(1.1);
}

.category-label {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.expand-icon {
  color: rgba(0, 168, 255, 0.7);
  font-size: 12px;
  transition: transform 0.3s ease;
}

.category-header:hover .expand-icon {
  transform: scale(1.2);
}

.category-options {
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(0, 168, 255, 0.1);
}

.option-item {
  margin-bottom: 8px;
}

.option-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.custom-checkbox input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid rgba(0, 168, 255, 0.4);
  border-radius: 3px;
  background: rgba(0, 21, 41, 0.6);
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 8px;
}

.custom-checkbox input[type="checkbox"]:hover {
  border-color: rgba(0, 168, 255, 0.7);
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.3);
}

.custom-checkbox input[type="checkbox"]:checked {
  background: rgba(0, 168, 255, 0.7);
  border-color: rgba(0, 168, 255, 0.7);
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

.custom-checkbox input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.option-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

/* Animation classes */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.5s ease-in-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Video Container */
.video-container {
  background: linear-gradient(135deg, #001529, #002140);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
  padding: 0px;
  padding-top: 5px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 168, 255, 0.2);
  height: 100%;
}

/* Card Container */
.card-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(0, 33, 64, 0.5);
  border-radius: 8px;
  overflow: auto;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  margin-top: -5px;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: rgba(0, 168, 255, 0.7);
  margin-right: 10px;
  border-radius: 2px;
}

.card-tools {
  display: flex;
  gap: 10px;
}

.card-body {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: auto;
}

.card-body.reduced-padding {
  padding: 10px;
  padding-top: 5px;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, minmax(min-content, auto));
  gap: 10px;
  width: 100%;
  height: 100%;
  align-content: start;
}

.video-feed {
  background: rgba(0, 21, 41, 0.7);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 168, 255, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  height: auto;
  min-height: 250px;
}

.video-feed:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 168, 255, 0.4);
}

.feed-header {
  padding: 8px 10px 6px;
  background: rgba(0, 33, 64, 0.8);
  border-bottom: 1px solid rgba(0, 168, 255, 0.2);
  display: flex;
  flex-direction: column;
}

.feed-header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.feed-location-top {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  padding-left: 2px;
}

.feed-title {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
}

.feed-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.feed-status.normal {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.feed-status.warning {
  background: rgba(250, 173, 20, 0.2);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.feed-status.alert {
  background: rgba(245, 34, 45, 0.2);
  color: #f5222d;
  border: 1px solid rgba(245, 34, 45, 0.3);
}

.feed-video {
  flex: none;
  position: relative;
  overflow: hidden;
  height: auto; /* 从固定高度改为自适应 */
}

.video-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}



.feed-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-badge {
  background: rgba(245, 34, 45, 0.8);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 0 15px rgba(245, 34, 45, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.feed-footer {
  padding: 4px 10px; /* 减少上下内边距 */
  background: rgba(0, 21, 41, 0.8);
  border-top: 1px solid rgba(0, 168, 255, 0.2);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.header-status {
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 12px;
  color: white;
}

.header-status.online {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

/* Result Panel */
.result-panel {
  background: linear-gradient(135deg, #001529, #002140);
  min-width: 320px;
  width: 320px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
  height: 100%;
  overflow-y: auto;
  flex-shrink: 0; /* 防止右侧面板被压缩 */
}

.result-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.7), transparent);
  z-index: 1;
}

.risk-analysis-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.9), rgba(0, 58, 140, 0.8));
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
}

.risk-analysis-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(0, 168, 255, 0.2), transparent 70%);
  z-index: 0;
}

.risk-analysis-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.5), transparent);
}

.risk-analysis-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 5px rgba(0, 168, 255, 0.5));
  position: relative;
  z-index: 1;
}

.risk-analysis-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  position: relative;
  z-index: 1;
  letter-spacing: 1px;
}

/* Data Cards */
.data-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 15px;
  margin-bottom: 10px;
}

.data-card {
  background: rgba(0, 33, 64, 0.6);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 168, 255, 0.15);
  transition: all 0.3s ease;
}

.data-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 168, 255, 0.3);
}

.card-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 3px rgba(0, 168, 255, 0.5));
}

.traffic-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z'%3E%3C/path%3E%3C/svg%3E");
}

.speed-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M20.38 8.57l-1.23 1.85a8 8 0 0 1-.22 7.58H5.07A8 8 0 0 1 15.58 6.85l1.85-1.23A10 10 0 0 0 3.35 19a2 2 0 0 0 1.72 1h13.85a2 2 0 0 0 1.74-1 10 10 0 0 0-.27-10.44zm-9.79 6.84a2 2 0 0 0 2.83 0l5.66-8.49-8.49 5.66a2 2 0 0 0 0 2.83z'%3E%3C/path%3E%3C/svg%3E");
}

.occupation-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM13.96 12.29l-2.75 3.54-1.96-2.36L6.5 17h11l-3.54-4.71z'/%3E%3C/path%3E%3C/svg%3E");
}

.risk-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z'%3E%3C/path%3E%3C/svg%3E");
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

.value-alert {
  color: #f5222d;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Risk Events */
.risk-events-container {
  margin: 15px;
  padding: 20px;
  background: rgba(0, 21, 41, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(0, 168, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.risk-events-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(0, 168, 255, 0.05), transparent 70%);
  z-index: 0;
}

.risk-events-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transform: translateY(-3px);
}

.risk-events-container.high-risk {
  border-color: rgba(245, 34, 45, 0.3);
  background: linear-gradient(135deg, rgba(245, 34, 45, 0.05), rgba(0, 21, 41, 0.5));
}

.risk-events-container.high-risk::before {
  background: radial-gradient(circle at top right, rgba(245, 34, 45, 0.1), transparent 70%);
}

.risk-events-container.low-risk {
  border-color: rgba(250, 173, 20, 0.3);
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.05), rgba(0, 21, 41, 0.5));
  margin-top: 20px;
}

.risk-events-container.low-risk::before {
  background: radial-gradient(circle at top right, rgba(250, 173, 20, 0.1), transparent 70%);
}

.risk-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
  z-index: 1;
}

.risk-count {
  font-size: 42px;
  font-weight: bold;
  text-shadow: 0 0 15px rgba(245, 34, 45, 0.5);
  margin-bottom: 5px;
  background: linear-gradient(to bottom, #fff, #ccc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.risk-count::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #f5222d, transparent);
}

.risk-count.danger {
  background: linear-gradient(to bottom, #ff4d4f, #cf1322);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.risk-count.warning {
  background: linear-gradient(to bottom, #faad14, #d48806);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.risk-label {
  font-size: 16px;
  color: #bdc3c7;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.recent-events {
  position: relative;
  z-index: 1;
}

.events-list {
  max-height: 200px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: flex-start;
  padding: 10px 0;
  border-bottom: 1px solid rgba(0, 168, 255, 0.1);
  margin-bottom: 5px;
}

.event-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.event-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  padding: 2px 6px;
  background: rgba(0, 33, 64, 0.6);
  border-radius: 4px;
  margin-right: 10px;
  white-space: nowrap;
}

.event-content {
  flex: 1;
}

.event-title {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 3px;
}

.event-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.event-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 10px;
  white-space: nowrap;
}

.event-status.info {
  background: rgba(24, 144, 255, 0.2);
  color: #1890ff;
  border: 1px solid rgba(24, 144, 255, 0.3);
}

.event-status.warning {
  background: rgba(250, 173, 20, 0.2);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.event-status.danger {
  background: rgba(245, 34, 45, 0.2);
  color: #f5222d;
  border: 1px solid rgba(245, 34, 45, 0.3);
}

/* Safe Container */
.safe-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 15px;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05), rgba(0, 21, 41, 0.3));
  border-radius: 8px;
  border: 1px solid rgba(82, 196, 26, 0.3);
  margin: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.safe-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(82, 196, 26, 0.1), transparent 70%);
  z-index: 0;
}

.safe-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transform: translateY(-3px);
}

.safe-icon {
  font-size: 48px;
  color: #52c41a;
  margin-bottom: 15px;
  text-shadow: 0 0 15px rgba(82, 196, 26, 0.5);
  position: relative;
  z-index: 1;
}

.safe-message {
  font-size: 28px;
  font-weight: bold;
  color: #52c41a;
  margin-bottom: 10px;
  text-shadow: 0 0 10px rgba(82, 196, 26, 0.3);
  position: relative;
  z-index: 1;
}

.safe-description {
  font-size: 15px;
  color: #bdc3c7;
  position: relative;
  z-index: 1;
  text-align: center;
}

/* Stats Panel Horizontal */
.stats-panel-horizontal {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: linear-gradient(90deg, rgba(0, 33, 64, 0.7), rgba(0, 21, 41, 0.7));
  border-top: 1px solid rgba(0, 58, 140, 0.3);
  position: relative;
  z-index: 10;
  margin-top: auto;
}

.stats-panel-horizontal .stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 8px 5px;
  position: relative;
}

.stats-panel-horizontal .stats-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.stats-panel-horizontal .stats-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.stats-panel-horizontal .stats-info {
  text-align: center;
  width: 100%;
}

.stats-panel-horizontal .stats-label {
  font-size: 12px;
  color: #bdc3c7;
  margin-bottom: 2px;
  white-space: nowrap;
}

.stats-panel-horizontal .stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
  text-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 1280px) {
  .operation-monitor-page {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    gap: 10px;
  }

  .left-sidebar, .result-panel {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
  }

  .data-cards {
    grid-template-columns: 1fr;
  }
}

/* Platform-specific icon styles */
.road_structure-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM13.96 12.29l-2.75 3.54-1.96-2.36L6.5 17h11l-3.54-4.71z'/%3E%3C/path%3E%3C/svg%3E");
}

.vehicle_accident-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.85 6h10.29l1.08 3.11H5.77L6.85 6zM19 17H5v-5h14v5z'/%3E%3C/path%3E%3C/svg%3E");
}

.abnormal_driving-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/path%3E%3C/svg%3E");
}

/* Category icons */
.person-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
}

.vehicle-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z'%3E%3C/path%3E%3C/svg%3E");
}

.machine-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M13 10h-2V8h2v2zm0-4h-2V1h2v5zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm-8.9-5h7.45c.75 0 1.41-.41 1.75-1.03L21 4.96 19.25 4l-3.7 7H8.53L4.27 2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2z'%3E%3C/path%3E%3C/svg%3E");
}

/* Video input controls */
.feed-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 21, 41, 0.7);
  border-radius: 20px;
  padding: 5px;
  display: flex;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.feed-video:hover .feed-controls {
  opacity: 1;
}

.feed-control-buttons {
  display: flex;
  gap: 5px;
}

.control-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 33, 64, 0.8);
  border: 1px solid rgba(0, 168, 255, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:hover {
  background: rgba(0, 58, 140, 0.8);
  transform: scale(1.1);
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button-icon {
  width: 18px;
  height: 18px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 2px rgba(0, 168, 255, 0.5));
}

.camera-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z'%3E%3C/path%3E%3C/svg%3E");
}

.upload-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z'%3E%3C/path%3E%3C/svg%3E");
}

.analyze-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'%3E%3C/path%3E%3C/svg%3E");
}

.stop-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M6 6h12v12H6z'%3E%3C/path%3E%3C/svg%3E");
}

.play-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a8ff'%3E%3Cpath d='M8 5v14l11-7z'%3E%3C/path%3E%3C/svg%3E");
}

.file-upload-label {
  position: relative;
  overflow: hidden;
  margin: 0;
}

.file-upload-input {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  padding: 0;
  cursor: pointer;
  opacity: 0;
  width: 100%;
  height: 100%;
}

/* Processing state */
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.processing-indicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(0, 168, 255, 0.2);
  border-top: 3px solid #00a8ff;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.processing-text {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Video element */
.video-element {
  width: 100%;
  height: 220px; /* 固定视频高度 */
  object-fit: cover; /* 改为cover，保持填充整个区域 */
  display: block; /* 移除底部空隙 */
}

.detection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* 视频处理错误状态 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(245, 34, 45, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.error-icon {
  width: 50px;
  height: 50px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23f5222d'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 15px;
}

.error-message {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
  text-align: center;
  max-width: 80%;
}

.action-button:hover {
  background: rgba(0, 58, 140, 0.8);
  border-color: rgba(0, 168, 255, 0.7);
  transform: translateY(-2px);
}

.action-button .button-icon {
  width: 14px;
  height: 14px;
}

/* Header status */
.header-status.offline {
  background: rgba(245, 34, 45, 0.2);
  color: #f5222d;
  border: 1px solid rgba(245, 34, 45, 0.3);
}

/* Prompt container */
.prompt-container {
  padding: 0 12px 15px;
  margin-top: 15px;
}

.prompt-editor {
  background: rgba(0, 21, 41, 0.4);
  border-radius: 4px;
  padding: 10px;
  border: 1px solid rgba(0, 168, 255, 0.1);
}

.prompt-label {
  display: block;
  margin-bottom: 5px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.prompt-textarea {
  width: 100%;
  padding: 8px;
  background: rgba(0, 21, 41, 0.6);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  color: #ffffff;
  font-size: 13px;
  line-height: 1.4;
  resize: vertical;
}

.prompt-textarea:focus {
  outline: none;
  border-color: rgba(0, 168, 255, 0.7);
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.3);
}

/* 萤石云视频播放器样式 */
.ys7-video {
  width: 100%;
  height: 100%;
  background-color: #000;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  border: none;
  display: block;
  object-fit: cover;
}

/* 编码错误帮助信息 */
.codec-error-help {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  padding: 20px;
}

.error-help-content {
  background: rgba(0, 33, 64, 0.9);
  border: 1px solid rgba(0, 168, 255, 0.4);
  border-radius: 8px;
  padding: 20px;
  max-width: 90%;
  color: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.error-help-content h3 {
  color: #f5222d;
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  text-align: center;
}

.error-help-content p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.error-help-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.error-help-content li {
  margin: 6px 0;
  font-size: 13px;
  line-height: 1.4;
}

.close-help-btn {
  background: linear-gradient(90deg, rgba(0, 58, 140, 0.8), rgba(0, 168, 255, 0.8));
  border: none;
  border-radius: 4px;
  color: white;
  padding: 8px 16px;
  margin-top: 15px;
  cursor: pointer;
  font-size: 14px;
  display: block;
  margin-left: auto;
  margin-right: auto;
  transition: all 0.3s ease;
}

.close-help-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 旧的播放器样式保留用于兼容 */
.ys7-player {
  width: 100%;
  height: 360px;
  background-color: #000;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes spin {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}
</style>
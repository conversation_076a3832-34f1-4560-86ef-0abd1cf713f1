#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import json
import base64
import redis
import socket
import requests
import threading
from pathlib import Path
import websocket
import traceback

print("Traffic Eyes 系统诊断工具")
print("=" * 50)

# 检查Redis服务
print("\n测试Redis连接...")
try:
    r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    if r.ping():
        print("✅ Redis连接成功")
    else:
        print("❌ Redis服务可访问但ping失败")
except Exception as e:
    print(f"❌ Redis连接失败: {str(e)}")
    print("请确保Redis服务已启动（使用管理员权限运行install_redis.ps1脚本）")
    sys.exit(1)

# 清除旧的测试数据
print("清除旧的测试数据...")
test_client_id = "test-client"
redis_keys = [
    "ezviz_frames", 
    f"client:{test_client_id}:status", 
    f"client:{test_client_id}:results"
]
for key in redis_keys:
    r.delete(key)

# 检查API服务器是否运行
print("\n测试API服务器(8001)...")
try:
    response = requests.get("http://localhost:8001/health", timeout=2)
    if response.status_code == 200:
        print(f"✅ API服务器健康检查通过: {response.json()}")
    else:
        print(f"❌ API服务器响应异常: {response.status_code}")
except Exception as e:
    print(f"❌ API服务器连接失败: {str(e)}")
    print("请确保API服务器已启动（运行start_advanced.ps1）")
    sys.exit(1)

# 检查WebSocket连接
print("\n测试API WebSocket连接...")
ws_connected = False
ws_message_received = False
ws_error = None

def on_ws_message(ws, message):
    global ws_message_received
    print(f"📩 收到WebSocket消息: {message[:100]}...")
    ws_message_received = True

def on_ws_error(ws, error):
    global ws_error
    print(f"❌ WebSocket错误: {error}")
    ws_error = error

def on_ws_open(ws):
    global ws_connected
    print("✅ WebSocket连接成功")
    ws_connected = True

def on_ws_close(ws, close_status_code, close_msg):
    print("WebSocket连接已关闭")

print("连接到YOLO结果WebSocket...")
try:
    ws = websocket.WebSocketApp(
        "ws://localhost:8001/ws/yolo-results",
        on_message=on_ws_message,
        on_error=on_ws_error,
        on_open=on_ws_open,
        on_close=on_ws_close
    )
    
    # 启动WebSocket连接线程
    ws_thread = threading.Thread(target=ws.run_forever)
    ws_thread.daemon = True
    ws_thread.start()
    
    # 等待连接建立
    start_time = time.time()
    while time.time() - start_time < 5:  # 最多等待5秒
        if ws_connected:
            break
        time.sleep(0.1)
    
    if not ws_connected:
        print("❌ WebSocket连接超时")
        if ws_error:
            print(f"   错误原因: {ws_error}")
    
    # 如果连接成功，发送测试消息
    if ws_connected:
        test_data = {
            "client_id": test_client_id,
            "results": {
                "test": True,
                "message": "这是一条测试消息"
            }
        }
        ws.send(json.dumps(test_data))
        print("✅ 已发送测试消息")
        
        # 等待服务器回复
        time.sleep(2)
        
except Exception as e:
    print(f"❌ WebSocket测试异常: {str(e)}")
    traceback.print_exc()

# 测试Redis帧队列
print("\n测试Redis视频帧处理队列...")

# 准备测试图像数据
try:
    # 尝试查找项目中的测试图像
    test_images = list(Path('.').glob('**/*.jpg')) + list(Path('.').glob('**/*.png'))
    if test_images:
        print(f"使用测试图像: {test_images[0]}")
        with open(test_images[0], 'rb') as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
    else:
        # 最小有效的1x1像素透明PNG
        print("未找到测试图像，使用默认1x1像素图像")
        img_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
except Exception as e:
    print(f"加载图像出错: {e}，使用默认图像")
    img_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="

# 发送测试帧数据
test_data = {
    "client_id": test_client_id,
    "frame": f"data:image/png;base64,{img_data}", 
    "frame_count": 1,
    "timestamp": int(time.time() * 1000)
}

# 添加到队列
r.rpush("ezviz_frames", json.dumps(test_data))
print("✅ 测试帧已添加到Redis队列")

# 发送通知
r.publish("new_ezviz_frame", json.dumps({
    "client_id": test_client_id,
    "timestamp": int(time.time() * 1000)
}))
print("✅ 已发布通知消息")

# 等待处理结果
print("\n等待处理结果...")
start_time = time.time()
result_received = False

while time.time() - start_time < 10:  # 最多等待10秒
    results = r.lrange(f"client:{test_client_id}:results", 0, -1)
    if results:
        print(f"✅ 收到结果: {results[0][:100]}...")
        result_received = True
        break
    time.sleep(0.5)

if not result_received:
    print("❌ 未收到处理结果，可能处理失败或超时")
    
# 检查YOLO处理器日志
print("\n查看YOLO处理器日志末尾内容...")
try:
    from subprocess import Popen, PIPE
    process = Popen(["Get-Content", "-Tail", "10", "yolo_processor.log"], 
                    stdout=PIPE, stderr=PIPE, shell=True)
    stdout, stderr = process.communicate()
    if stdout:
        print("\nYOLO处理器最新日志:")
        print(stdout.decode('utf-8', errors='replace'))
    else:
        print("无法读取日志文件")
except Exception as e:
    print(f"读取日志出错: {str(e)}")

# 关闭WebSocket连接
if ws_connected:
    ws.close()

print("\n诊断完成")
print("=" * 50)
if not result_received:
    print("系统诊断结果: 可能存在以下问题之一:")
    print("1. Redis服务未正确启动或配置")
    print("2. API服务器未正确配置WebSocket端点")
    print("3. YOLO处理器未启动或出现错误")
    print("4. Base64图像编码问题")
    print("\n请检查各组件状态并确保它们正确配置和启动") 
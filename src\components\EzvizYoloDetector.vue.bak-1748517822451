<template>
  <div class="ezviz-yolo-detector">
    <div class="video-container">
      <!-- 萤石云视频流 -->
      <div class="ezviz-player-container" ref="ezvizPlayerContainer"></div>
      
      <!-- 检测结果Canvas -->
      <canvas ref="detectionCanvas" class="detection-canvas"></canvas>
    </div>
    
    <div class="controls">
      <button 
        class="control-button"
        @click="toggleDetection"
        :disabled="!isModelLoaded || !isPlayerReady"
      >
        {{ isDetecting ? '停止YOLO检测' : '开始YOLO检测' }}
      </button>
      
      <div class="detection-stats" v-if="isModelLoaded">
        <div>模型: YOLOv8</div>
        <div>状态: {{ isModelLoaded ? '已加载' : '加载中...' }}</div>
        <div>播放器: {{ isPlayerReady ? '已就绪' : '未就绪' }}</div>
        <div v-if="isDetecting">FPS: {{ fps.toFixed(1) }}</div>
        <div v-if="detectionResults">
          检测到对象: {{ detectionResults.boxes ? detectionResults.boxes.length : 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import YoloTFJS from './YoloTFJS';
import { renderStats } from './utils/renderBox';

export default {
  name: 'EzvizYoloDetector',
  
  props: {
    // 萤石云设备序列号
    deviceSerial: {
      type: String,
      required: true
    },
    
    // 萤石云通道号
    channelNo: {
      type: Number,
      default: 1
    },
    
    // 萤石云认证信息
    accessToken: {
      type: String,
      required: true
    },
    
    // 检测阈值
    threshold: {
      type: Number,
      default: 0.25
    },
    
    // 模型路径
    modelPath: {
      type: String,
      default: '/models/yolov8n/model.json'
    },
    
    // 捕获帧间隔(毫秒)
    captureInterval: {
      type: Number,
      default: 100
    }
  },
  
  setup(props, { emit }) {
    // 引用
    const ezvizPlayerContainer = ref(null);
    const detectionCanvas = ref(null);
    
    // 状态
    const isModelLoaded = ref(false);
    const isPlayerReady = ref(false);
    const isDetecting = ref(false);
    const detectionResults = ref(null);
    const fps = ref(0);
    
    // 萤石云播放器实例
    let ezvizPlayer = null;
    
    // YOLOv8 模型实例
    const yolo = new YoloTFJS();
    
    // FPS计算相关
    let frameCount = 0;
    let lastTime = 0;
    let captureIntervalId = null;
    
    // 初始化
    onMounted(async () => {
      // 加载模型
      await loadModel();
      
      // 初始化萤石云播放器
      initEzvizPlayer();
    });
    
    // 清理资源
    onBeforeUnmount(() => {
      stopDetection();
      destroyEzvizPlayer();
      if (yolo) {
        yolo.dispose();
      }
    });
    
    // 加载YOLO模型
    const loadModel = async () => {
      try {
        const loaded = await yolo.loadModel(props.modelPath);
        isModelLoaded.value = loaded;
        emit('model-loaded', loaded);
      } catch (error) {
        console.error('加载YOLO模型失败:', error);
        emit('model-loaded', false);
      }
    };
    
    // 初始化萤石云播放器
    const initEzvizPlayer = () => {
      if (!ezvizPlayerContainer.value) return;
      
      // 确保全局变量EZUIKit存在
      if (typeof EZUIKit === 'undefined') {
        console.error('EZUIKit未加载，请确保已引入萤石云SDK');
        return;
      }
      
      try {
        // 创建萤石云播放器
        ezvizPlayer = new EZUIKit.EZUIKitPlayer({
          id: ezvizPlayerContainer.value,
          accessToken: props.accessToken,
          url: `ezopen://open.ezviz.com/${props.deviceSerial}/${props.channelNo}.live`,
          template: 'simple',
          autoplay: true,
          handleSuccess: () => {
            console.log('萤石云播放器初始化成功');
            isPlayerReady.value = true;
            emit('player-ready', true);
            
            // 调整canvas大小匹配视频
            if (detectionCanvas.value && ezvizPlayerContainer.value) {
              const videoElement = ezvizPlayerContainer.value.querySelector('video');
              if (videoElement) {
                detectionCanvas.value.width = videoElement.clientWidth;
                detectionCanvas.value.height = videoElement.clientHeight;
              }
            }
          },
          handleError: (err) => {
            console.error('萤石云播放器错误:', err);
            isPlayerReady.value = false;
            emit('player-error', err);
          }
        });
      } catch (error) {
        console.error('初始化萤石云播放器失败:', error);
        emit('player-error', error);
      }
    };
    
    // 销毁萤石云播放器
    const destroyEzvizPlayer = () => {
      if (ezvizPlayer) {
        try {
          ezvizPlayer.stop();
          ezvizPlayer = null;
        } catch (error) {
          console.error('销毁萤石云播放器失败:', error);
        }
      }
    };
    
    // 切换检测状态
    const toggleDetection = () => {
      if (isDetecting.value) {
        stopDetection();
      } else {
        startDetection();
      }
    };
    
    // 开始检测
    const startDetection = () => {
      if (!isModelLoaded.value || !isPlayerReady.value || isDetecting.value) return;
      
      isDetecting.value = true;
      lastTime = performance.now();
      frameCount = 0;
      
      // 开始定期捕获帧并检测
      captureIntervalId = setInterval(captureAndDetect, props.captureInterval);
      emit('detection-started');
    };
    
    // 停止检测
    const stopDetection = () => {
      if (!isDetecting.value) return;
      
      isDetecting.value = false;
      if (captureIntervalId) {
        clearInterval(captureIntervalId);
        captureIntervalId = null;
      }
      
      emit('detection-stopped');
    };
    
    // 捕获帧并检测
    const captureAndDetect = async () => {
      if (!isDetecting.value || !isPlayerReady.value || !detectionCanvas.value) return;
      
      try {
        // 获取视频元素
        const videoElement = ezvizPlayerContainer.value.querySelector('video');
        if (!videoElement) return;
        
        // 确保Canvas尺寸与视频匹配
        const ctx = detectionCanvas.value.getContext('2d');
        detectionCanvas.value.width = videoElement.clientWidth;
        detectionCanvas.value.height = videoElement.clientHeight;
        
        // 在Canvas上绘制当前视频帧
        ctx.drawImage(videoElement, 0, 0, detectionCanvas.value.width, detectionCanvas.value.height);
        
        // 执行检测
        detectionResults.value = await yolo.detect(
          detectionCanvas.value,
          detectionCanvas.value,
          props.threshold
        );
        
        // 渲染统计信息
        if (detectionResults.value) {
          renderStats(
            detectionCanvas.value,
            detectionResults.value,
            yolo.classes
          );
          
          // 发送检测结果事件
          emit('detection-results', detectionResults.value);
        }
        
        // 计算FPS
        frameCount++;
        const now = performance.now();
        const elapsed = now - lastTime;
        
        if (elapsed >= 1000) {
          fps.value = frameCount / (elapsed / 1000);
          frameCount = 0;
          lastTime = now;
        }
      } catch (error) {
        console.error('检测帧时出错:', error);
        stopDetection();
        emit('detection-error', error);
      }
    };
    
    return {
      ezvizPlayerContainer,
      detectionCanvas,
      isModelLoaded,
      isPlayerReady,
      isDetecting,
      detectionResults,
      fps,
      toggleDetection
    };
  }
};
</script>

<style scoped>
.ezviz-yolo-detector {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  background-color: #000;
}

.ezviz-player-container {
  position: absolute;
  width: 100%;
  height: 100%;
}

.detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.controls {
  display: flex;
  padding: 10px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  align-items: center;
}

.control-button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 15px;
}

.control-button:hover {
  background-color: #45a049;
}

.control-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.detection-stats {
  font-size: 14px;
  color: #333;
}

.detection-stats div {
  margin-bottom: 4px;
}
</style> 
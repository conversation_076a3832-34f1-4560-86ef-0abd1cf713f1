<template>
  <div class="chat-interface-container">
    <h2>智能对话界面</h2>
    <div class="chat-window">
      <div v-for="message in messages" :key="message.id" class="message-row" :class="['message', message.sender === 'user' ? 'user-message' : 'bot-message']">
        <div class="message-content">
          <p>{{ message.text }}</p>
          <span class="timestamp">{{ formatTimestamp(message.timestamp) }}</span>
        </div>
      </div>
    </div>
    <div class="input-area">
      <textarea v-model="userInput" @keyup.enter.prevent="sendMessage" placeholder="输入消息..."></textarea>
      <button @click="sendMessage">发送</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';

interface ChatMessage {
  id: number;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

const messages = ref<ChatMessage[]>([]);
const userInput = ref('');

const formatTimestamp = (date: Date) => {
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const sendMessage = async () => {
  const text = userInput.value.trim();
  if (!text) return;

  messages.value.push({
    id: Date.now(),
    text,
    sender: 'user',
    timestamp: new Date(),
  });

  const currentMessageText = userInput.value;
  userInput.value = '';
  scrollToBottom();

  // Simulate bot response - In a real app, call your backend API here
  // Example: const response = await fetch('/api/chat', { method: 'POST', body: JSON.stringify({ prompt: currentMessageText }) });
  // const data = await response.json();
  // const botText = data.reply;

  // Simulated delay and response
  setTimeout(() => {
    messages.value.push({
      id: Date.now() + 1, // Ensure unique ID
      text: `已收到: "${currentMessageText}". 我是一个模拟回复。`,
      sender: 'bot',
      timestamp: new Date(),
    });
    scrollToBottom();
  }, 1000);
};

const scrollToBottom = () => {
  nextTick(() => {
    const chatWindow = document.querySelector('.chat-window');
    if (chatWindow) {
      chatWindow.scrollTop = chatWindow.scrollHeight;
    }
  });
};

// Initial bot message or welcome message (optional)
messages.value.push({
  id: Date.now(),
  text: '您好！我是智能助手，有什么可以帮助您的吗？',
  sender: 'bot',
  timestamp: new Date(),
});

</script>

<style scoped>
.chat-interface-container {
  display: flex;
  flex-direction: column;
  min-height: 100%; /* Or a fixed height like 500px */
  width: 100%; /* Or a fixed width */
  max-width: 800px; /* Optional: to constrain width */
  margin: 0 auto; /* Center if max-width is used */
  background-color: var(--card-bg);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden; /* To contain children within rounded corners */
}

.chat-interface-container h2 {
  padding: 15px 20px;
  margin: 0;
  color: var(--primary-color);
  background-color: var(--card-header-bg, #f7f7f7); /* Add a fallback */
  border-bottom: 1px solid var(--border-color, #e0e0e0); /* Add a fallback */
  text-align: center;
}

.chat-window {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px; /* Space between messages */
}

.message-row {
  display: flex;
  max-width: 80%;
}

.message-content {
  padding: 10px 15px;
  border-radius: var(--radius-sm);
  word-wrap: break-word; /* Ensure long words break */
  display: flex;
  flex-direction: column;
}

.message-content p {
  margin: 0 0 5px 0;
}

.timestamp {
  font-size: 0.75em;
  color: var(--text-secondary);
  align-self: flex-end;
}

.user-message {
  align-self: flex-end;
  margin-left: auto; /* Push to the right */
}
.user-message .message-content {
  background-color: var(--primary-color-light, #cfe2ff); /* Fallback */
  color: var(--primary-color-text, #004085); /* Fallback */
}


.bot-message {
  align-self: flex-start;
  margin-right: auto; /* Push to the left */
}

.bot-message .message-content {
  background-color: var(--secondary-color-light, #e2e3e5); /* Fallback */
  color: var(--secondary-color-text, #383d41); /* Fallback */
}


.input-area {
  display: flex;
  padding: 10px;
  border-top: 1px solid var(--border-color, #e0e0e0); /* Fallback */
  background-color: var(--card-footer-bg, #fdfdfd); /* Fallback */
}

.input-area textarea {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid var(--border-color-input, #ccc); /* Fallback */
  border-radius: var(--radius-sm);
  resize: none; /* Prevent manual resizing */
  min-height: 40px; /* Minimum height */
  max-height: 120px; /* Maximum height before scrolling */
  overflow-y: auto; /* Allow scrolling within textarea if content exceeds max-height */
  font-family: inherit;
  font-size: 1em;
  margin-right: 10px;
}

.input-area button {
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 1em;
}

.input-area button:hover {
  background-color: var(--primary-color-dark);
}
</style>
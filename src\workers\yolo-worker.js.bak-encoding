/**
 * Worker for handling YOLO WebSocket communication
 * This keeps YOLO detection off the main thread
 */

// WebSocket connection
let ws = null;
let reconnectTimer = null;
let isConnecting = false;

// Connection config
const WS_URL = 'ws://localhost:8765';
const RECONNECT_DELAY = 2000; // 2 seconds

// Queue for frames to process when the connection is being established
let pendingFrames = [];
const MAX_PENDING_FRAMES = 5; // Limit queue size

// Handle incoming messages from the main thread
self.onmessage = function(e) {
  const { type, frameData, action } = e.data;
  
  switch (action) {
    case 'connect':
      // Initialize connection
      connect();
      break;
      
    case 'disconnect':
      // Close connection
      disconnect();
      break;
      
    case 'process':
      // Process a video frame
      if (frameData) {
        processFrame(frameData);
      }
      break;
      
    default:
      // Unknown action, just process if frameData exists
      if (frameData) {
        processFrame(frameData);
      }
      break;
  }
};

// Connect to WebSocket server
function connect() {
  if (ws && (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN)) {
    // Already connected or connecting
    return;
  }
  
  if (isConnecting) {
    return;
  }
  
  isConnecting = true;
  
  try {
    // Create WebSocket connection
    ws = new WebSocket(WS_URL);
    
    ws.onopen = function() {
      console.log('[YOLO Worker] WebSocket connection established');
      isConnecting = false;
      
      // Notify the main thread
      self.postMessage({
        type: 'connection_status',
        status: 'connected'
      });
      
      // Process any pending frames
      processPendingFrames();
      
      // Clear reconnect timer if it exists
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
      }
    };
    
    ws.onmessage = function(event) {
      try {
        // Parse and send results to main thread
        const result = JSON.parse(event.data);
        self.postMessage({
          type: 'result',
          data: result
        });
      } catch (error) {
        console.error('[YOLO Worker] Error parsing WebSocket message:', error);
        self.postMessage({
          type: 'error',
          error: 'Failed to parse WebSocket message'
        });
      }
    };
    
    ws.onerror = function(error) {
      console.error('[YOLO Worker] WebSocket error:', error);
      self.postMessage({
        type: 'connection_status',
        status: 'error',
        error: 'WebSocket connection error'
      });
    };
    
    ws.onclose = function() {
      console.log('[YOLO Worker] WebSocket connection closed');
      ws = null;
      isConnecting = false;
      
      // Notify the main thread
      self.postMessage({
        type: 'connection_status',
        status: 'disconnected'
      });
      
      // Try to reconnect after delay
      reconnectTimer = setTimeout(() => {
        connect();
      }, RECONNECT_DELAY);
    };
  } catch (error) {
    console.error('[YOLO Worker] Error creating WebSocket:', error);
    isConnecting = false;
    
    // Notify the main thread
    self.postMessage({
      type: 'connection_status',
      status: 'error',
      error: 'Failed to create WebSocket connection'
    });
    
    // Try to reconnect after delay
    reconnectTimer = setTimeout(() => {
      connect();
    }, RECONNECT_DELAY);
  }
}

// Disconnect from WebSocket server
function disconnect() {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
  
  if (ws) {
    ws.close();
    ws = null;
  }
  
  // Clear any pending frames
  pendingFrames = [];
}

// Process a video frame
function processFrame(frameData) {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    // Queue the frame for later if not too many pending
    if (pendingFrames.length < MAX_PENDING_FRAMES) {
      pendingFrames.push(frameData);
    }
    
    // Try to connect if not already connecting
    if (!isConnecting && (!ws || ws.readyState !== WebSocket.CONNECTING)) {
      connect();
    }
    return;
  }
  
  try {
    // Send the frame to the YOLO server
    ws.send(JSON.stringify({
      action: 'detect',
      image: frameData
    }));
  } catch (error) {
    console.error('[YOLO Worker] Error sending frame:', error);
    self.postMessage({
      type: 'error',
      error: 'Failed to send frame to YOLO server'
    });
  }
}

// Process any pending frames
function processPendingFrames() {
  if (pendingFrames.length === 0 || !ws || ws.readyState !== WebSocket.OPEN) {
    return;
  }
  
  while (pendingFrames.length > 0 && ws && ws.readyState === WebSocket.OPEN) {
    const frameData = pendingFrames.shift();
    processFrame(frameData);
  }
}

// Start connection immediately
connect(); 